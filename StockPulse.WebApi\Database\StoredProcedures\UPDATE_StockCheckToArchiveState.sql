﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].[UPDATE_StockCheckToArchiveState]
(
    @StockCheckIds varchar(max),
    @UserId INT
)
AS
BEGIN

SET NOCOUNT ON;


IF (dbo.[AuthenticateUserMultiStockChecks](@UserId, @StockCheckIds) = 0)
BEGIN 
    RETURN
END

    -- Temporary table to store the updated StockCheckIds and StatusId
    DECLARE @UpdatedStockChecks TABLE
    (
        StockCheckId INT,
        StatusId INT
    );

    -- Update StockChecks and capture the updated StockC<PERSON>ckId and StatusId
    UPDATE StockChecks
    SET
        IsActive = 0,
        StatusId = CASE
            WHEN StatusId < 3 THEN 3 
            ELSE StatusId 
        END
    OUTPUT INSERTED.Id, INSERTED.StatusId INTO @UpdatedStockChecks(StockCheckId, StatusId)
    WHERE Id IN (SELECT value FROM STRING_SPLIT(@StockCheckIds, ','));

    -- Log the status changes for all updated StockCheckIds
    INSERT INTO StatusChangeLogItems (StockCheckId, UserId, Date, StatusId)
    SELECT StockCheckId, @UserId, GETUTCDATE(), StatusId FROM @UpdatedStockChecks;

END

GO


