<div class="modal-header">
    <h4 id="modal-basic-title" class="modal-title">Bulk Resolve ({{ itemsToResolve.length }})</h4>
    <button type="button" class="close" aria-label="Close" (click)="onCancelButtonClick()">
        <span aria-hidden="true">&times;</span>
    </button>
</div>

<div class="modal-body">
    <table>
        <tr>
            <td>Resolution</td>
            <td>
                <div ngbDropdown container="body" placement="bottom-right" class="d-inline-block">
                    <button class="btn btn-primary" ngbDropdownToggle>
                        {{ resolution?.description ? resolution.description : 'Choose resolution' }}
                    </button>
                    <div ngbDropdownMenu class="dropdown-menu-left" aria-labelledby="dropdownBasic1">
                        <button *ngFor="let resolutionTypes of getResolutionTypes()" ngbDropdownItem
                            (click)="chooseResolution(resolutionTypes)">
                            {{ resolutionTypes.description }}
                        </button>
                    </div>
                </div>
            </td>
        </tr>
        <tr *ngIf="showBackupRow() ">
            <td>
              Backup required
            </td>
            <td>
              <div class="infoPanel">
                {{ resolution.backupRequired }}
              </div>
            </td>
          </tr>
        <tr>
            <td>Resolution Detail</td>
            <td>
                <textarea [(ngModel)]="resolutionNotes" placeholder="Add notes here"
                    class="notes subtleBoxShadow"></textarea>
            </td>
        </tr>
        <!-- <tr>
            <td>Is Now Resolved?</td>
            <td>
                <button class="custom-checkbox" [ngClass]="{ 'checked': resolutionResolved }"
                    (click)="resolutionResolved = !resolutionResolved">
                    <fa-icon *ngIf="resolutionResolved" [icon]="iconService.faCheck"></fa-icon>
                </button>
                <span *ngIf="backupRequired()" class="text-danger">
                    * Backup image required to mark as resolved
                </span>
            </td>
        </tr> -->
        <tr>
            <td>Backup Files</td>
            <td>
                <div id="backupContainer">
                    <!-- Drag drop area -->
                    <div id="fileDropArea" appDragAndDrop contenteditable="true" (filesDropped)="onFileDropped($event)"
                        (paste)="onFilePaste($event)">
                        <div id="instructionPopoverHolder">
                            <instructionRowPopoverStyle *ngIf="thereAreFiles()" [fullMessage]="instructionRowMessage()">
                            </instructionRowPopoverStyle>
                        </div>
                        <instructionRow *ngIf="!thereAreFiles()" [message]="instructionRowMessage()">
                        </instructionRow>

                        <div class="uploadFileWrapper mt-2">
                            <ng-container>
                                <input #fileDropRef id="file" type="file" multiple="multiple" class="chooseFileInput"
                                    (click)="$event.target.value = null" (change)="fileBrowseHandler($event)" />
                                <label for="file">
                                    <fa-icon [icon]="iconService.faUpload"></fa-icon>Choose <span
                                        *ngIf="thereAreFiles()">Additional</span> File(s)
                                </label>
                            </ng-container>
                        </div>
                    </div>

                    <!-- Files uploaded, but not yet saved -->
                    <span *ngIf="fileSizeExceeded" class="text-danger">
                        1 or more files exceeded the 10mb size limit and were not uploaded.
                    </span>
                    <div *ngIf="resolutionFiles.length" id="filesList">
                        <div *ngFor="let file of resolutionFiles; let i = index" class="singleFile">
                            <div class="d-flex align-items-center">
                                <img class="fileThumbnail" [src]="getThumbnail(file)">
                                <div class="info">
                                    <span class="name">{{ file.name }}</span>
                                    <br>
                                    <span class="size">{{ formatBytes(file?.size) }}</span>
                                </div>
                            </div>
                            <fa-icon [icon]="iconService.faTrash" width="20px" alt="delete" class="deleteIcon"
                                (click)="deleteFile(i)"></fa-icon>
                        </div>
                    </div>
                </div>
            </td>
        </tr>
    </table>
</div>

<div class="modal-footer">
    <button type="button" class="btn btn-success" [disabled]="preventSave()" (click)="onSaveButtonClick()">Save</button>
    <button type="button" class="btn btn-primary" (click)="onCancelButtonClick()">Close</button>
</div>