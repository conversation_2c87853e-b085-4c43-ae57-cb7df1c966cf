SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO  
  
CREATE OR ALTER PROCEDURE [dbo].[GET_ScansMatchedToRecItem]  
(  
    @StockCheckId INT = NULL,  
    @UserId INT = NULL  
)  
AS  
BEGIN  
  
SET NOCOUNT ON;  
    
DECLARE @isRegional INT;    
DECLARE @isTotal INT;    
DECLARE @StockCheckDate DateTime;  
DECLARE @DivisionId INT = NULL;  
DECLARE @DealerGroupId INT = NULL;  
DECLARE @SCId INT = NULL;  
  
  
IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END


  
SELECT   
@isRegional = IsRegional,   
@isTotal = IsTotal,   
@StockCheckDate = Date   
FROM StockChecks    
WHERE StockChecks.Id = @StockCheckId  
    

  
IF @isRegional = 0 AND @isTotal = 0  
  
    BEGIN  
   
 SET @SCId = @StockCheckId;  
     
    END  
  
ELSE IF @isRegional = 1 AND @isTotal = 0  
   
    BEGIN  
  
    SET @DivisionId = (SELECT DivisionId FROM StockChecks  
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId  
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id  
                        WHERE StockChecks.Id = @StockCheckId AND StockChecks.IsRegional = 1)  
  
    END  
  
ELSE IF @isRegional = 0 AND @isTotal = 1  
  
    BEGIN  
      
    SET @DealerGroupId = (SELECT DealerGroupId FROM StockChecks  
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId  
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id  
                        WHERE StockChecks.Id = @StockCheckId)  
  
    END  
  
  SELECT  
    scns.Id as ScanId,
    scns.UserId,
    scns.StockCheckId,
    LastEditedById,
    LocationId,
    scns.UnknownResolutionId,
    StockItemId,
    ReconcilingItemId,
    IsDuplicate,
    LastEditedDateTime,
    RegConfidence,
    VinConfidence,
    --IsEdited,
    scns.Longitude,
    scns.Latitude,
    ScanDateTime,
    scns.Comment as ScanComment,
    scns.Reg as ScanReg,
    scns.Vin as ScanVin,
    scns.Description as ScanDescription,
    CoordinatesJSON,
    HasVinImage,  
    locns.Description as 'LocationDescription',  
    usrs.Name as 'ScannerName',  
  
    rectypes.Id as 'ReconcilingItemTypeId',  
    rectypes.Description as 'ReconcilingItemTypeDescription',  
    recitems.Description as 'RecItemDescription',  
    recitems.Comment as 'RecItemComment',  
    recitems.Reference as 'RecItemReference',
  
    Sites.Description AS SiteName,
    Sites.Longitude as StockCheckLongitude,
    Sites.Latitude as StockCheckLatitude,

    CASE
        WHEN scns.IsRegEditedOnWeb = 1 THEN 'Web app'
        WHEN scns.IsRegEditedOnDevice = 1 THEN 'Mobile app'
        ELSE NULL
    END AS RegEditStatus,
    CASE
        WHEN scns.IsVinEditedOnWeb = 1 THEN 'Web app'
        WHEN scns.IsVinEditedOnDevice = 1 THEN 'Mobile app'
        ELSE NULL
    END AS VinEditStatus
    FROM dbo.Scans scns  
    INNER JOIN Locations locns on locns.Id = scns.LocationId  
    INNER JOIN Users usrs on usrs.Id = scns.UserId  
    INNER JOIN ReconcilingItems recitems on recitems.Id = scns.ReconcilingItemId  
    INNER JOIN ReconcilingItemTypes rectypes on rectypes.Id = recitems.ReconcilingItemTypeId  
    INNER JOIN StockChecks AS SC ON SC.Id=scns.StockCheckId  
    INNER JOIN Sites ON Sites.Id=SC.SiteId  
	INNER JOIN Divisions AS D ON D.Id=Sites.DivisionId  
	INNER JOIN DealerGroup AS DG ON D.DealerGroupId=DG.Id  
    WHERE ReconcilingItemId IS NOT NULL   
	AND scns.StockItemId IS NULL  
    --AND scns.StockCheckId = @StockCheckId  
	AND SC.Id = ISNULL(@SCId, SC.Id)  
	AND SC.Date = @StockCheckDate  
	AND D.Id = ISNULL(@DivisionId, D.Id)  
	AND DG.Id = ISNULL(@DealerGroupId, DG.Id)  

	UNION ALL

	SELECT  
    scns.Id,
	scns.UserId,
	scns.StockCheckId,
	LastEditedById,
	LocationId,
	scns.UnknownResolutionId,
    StockItemId,
	NULL AS ReconcilingItemId,
	0 AS IsDuplicate,
	LastEditedDateTime,
	RegConfidence,
    VinConfidence,
	--IsEdited,
    scns.Longitude,
	scns.Latitude,
	ScanDateTime,
	scns.Comment,
	scns.Reg,scns.
	Vin,
	scns.Description,
	CoordinatesJSON,
	HasVinImage,  
    locns.Description as 'LocationDescription',  
    usrs.Name as 'ScannerName',

	NULL AS 'ReconcilingItemTypeId',  
    'In stock at another site' AS 'ReconcilingItemTypeDescription',  
    'In stock at another site' AS 'RecItemDescription',  
    NULL as 'RecItemComment',  
    NULL as 'RecItemReference',  
  
    Sites.Description AS SiteName,
    Sites.Longitude as StockCheckLongitude,
    Sites.Latitude as StockCheckLatitude,
    CASE
        WHEN scns.IsRegEditedOnWeb = 1 THEN 'Web app'
        WHEN scns.IsRegEditedOnDevice = 1 THEN 'Mobile app'
        ELSE NULL
    END AS RegEditStatus,
    CASE
        WHEN scns.IsVinEditedOnWeb = 1 THEN 'Web app'
        WHEN scns.IsVinEditedOnDevice = 1 THEN 'Mobile app'
        ELSE NULL
    END AS VinEditStatus

    FROM scans scns     
    INNER JOIN StockItems stockitem on stockitem.Id = scns.StockItemId 
    INNER JOIN Locations locns on locns.Id = scns.LocationId  
    INNER JOIN Users usrs on usrs.Id = scns.UserId  
    --INNER JOIN ReconcilingItems recitems on recitems.Id = scns.ReconcilingItemId  
    --INNER JOIN ReconcilingItemTypes rectypes on rectypes.Id = recitems.ReconcilingItemTypeId  
    INNER JOIN StockChecks AS SC ON SC.Id=scns.StockCheckId  
    INNER JOIN Sites ON Sites.Id=SC.SiteId  
	INNER JOIN Divisions AS D ON D.Id=Sites.DivisionId  
	INNER JOIN DealerGroup AS DG ON D.DealerGroupId=DG.Id  
    WHERE scns.StockCheckId = @StockCheckId     
    AND stockItem.StockCheckId <> @StockCheckId  
  
  
END 

GO
