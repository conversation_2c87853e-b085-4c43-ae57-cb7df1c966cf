import { Component, OnInit, Output, Input, EventEmitter, ViewChild, ElementRef, HostListener } from "@angular/core";
import { DeleteButtonComponent } from '../../../_cellRenderers/deleteButton.component';
import { ReconcilingItemVM } from "../../../model/ReconcilingItemVM";
import { ConstantsService } from '../../../services/constants.service';
import { CphPipe } from '../../../cph.pipe';
import { CustomHeaderComponent } from '../../../_cellRenderers/customHeader.component';
import { Subscription } from 'rxjs';
import {  LoadItemsService } from "src/app/pages/loadItems/loadItems.service";
import { LogoService } from "src/app/services/logo.service";
import { GridOptions, SelectionChangedEvent } from "ag-grid-community";
import { FinancialLineVM } from "src/app/model/FinancialLineVM";




@Component({
  selector: 'TrialBalanceTable',
  templateUrl: './TrialBalanceTable.component.html',
  styleUrls: ['./TrialBalanceTable.component.scss']
})


export class TrialBalanceTableComponent implements OnInit {
  @ViewChild('tableContainer', { static: true }) tableContainer: ElementRef;
  @Output() deleteItem = new EventEmitter<{ financialLine: FinancialLineVM, controller: string, route: string }>();
  @Input() rowData: FinancialLineVM[];
  @Input() persistentRowData: FinancialLineVM[];

  @Output() filteredRowData = new EventEmitter<FinancialLineVM[]>();

  @HostListener('window:resize', [])
  private onresize(event) {
    if (this.gridApi) this.gridApi.sizeColumnsToFit();
  }

  mainTableGridOptions: GridOptions;
  public gridApi;
  subscription: Subscription;
  sidenavToggledSubscription: Subscription;
  
  constructor(
    public constants: ConstantsService,
    public cphPipe: CphPipe,
    public loadItemsService: LoadItemsService,
    public logo: LogoService
  ) { }

  ngOnDestroy() {
    if (this.subscription) this.subscription.unsubscribe()
    if (this.sidenavToggledSubscription) { this.sidenavToggledSubscription.unsubscribe(); }
  }

  ngOnInit(): void {
    this.initParams()
  }

  initParams() {
    this.loadItemsService.filter.valueChanges.subscribe(result => this.search(result));
    this.subscription = this.loadItemsService.updateMainTable.subscribe(res => {
      this.search(this.loadItemsService.filter.value)
      this.updateGrid()
    })

    this.setColumnDefinitions()

    this.sidenavToggledSubscription = this.constants.sidenavToggledEmitter.subscribe(() => {
      if (this.gridApi) {
        this.gridApi.sizeColumnsToFit();
      }
    })
  }

  setColumnDefinitions() {
    let gridScaleValue = this.tableContainer.nativeElement.clientWidth / 2150; //actual measurement 1310.  Added 40 for padding.

    this.mainTableGridOptions = {
      rowData: [],
      frameworkComponents: { agColumnHeader: CustomHeaderComponent },
      columnTypes: {
        "numberColumn": { cellClass: 'agAlignRight', cellRenderer: (params) => { return this.cphPipe.transform(params.value, 'number', 0) } },
        "labelColumn": { cellClass: 'agAlignCentre', filter: 'agTextColumnFilter', sortable: true },
        "date": { cellClass: 'agAlignCentre', cellRenderer:(params)=>this.cphPipe.transform(params.value,'date',0), filter: 'agTextColumnFilter' },
        "currencyColumn": { cellClass: 'agAlignRight', cellRenderer: (params) => { return this.cphPipe.transform(params.value, 'currency', 2) } },
      },
      getRowHeight: (params) => 20,
      onGridReady: (params) => this.onGridReady(params),
      defaultColDef: {
        sortable: true,
        resizable: true,
        
        floatingFilter: true
      },
      enableCellTextSelection: true,
      rowSelection: 'multiple',
      onSelectionChanged: (event) => this.onSelectionChanged(event),
      onFirstDataRendered: (event) => this.gridApi?.sizeColumnsToFit(),
      columnDefs: [
        //{ headerName: "", field: "index", cellClass: 'indexCell', valueGetter: (params) => params.node.rowIndex + 1 + ' >', autoHeight: true, width: 60 * gridScaleValue },
        { headerName: "Account Code", field: "accountCode", width: 600 * gridScaleValue, type: "labelColumn" },
        { headerName: "Account Name", field: "description", width: 600 * gridScaleValue, type: "labelColumn" },
        { headerName: "Value", field: "balance", width: 100 * gridScaleValue, type: 'currencyColumn' },
        { headerName: "File Name", field: "fileName", width: 200, type: 'labelColumn' },
        { headerName: "File Date", field: "fileDate", width: 200, type: 'date' },
        { headerName: "Load Date", field: "loadDate", width: 200, type: 'date' },
        { headerName: "User Name", field: "userName", width: 200, type: 'labelColumn' },
        { headerName: "", field: "", width: 60 * gridScaleValue, type: 'labelColumn', filter: null, cellRendererFramework: DeleteButtonComponent, cellRendererParams: { onClick: this.processDeleteClick.bind(this), } },
        { headerName: '', field: '', width: 40, headerCheckboxSelection: true, checkboxSelection: true }
      ]
    }
  }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridApi.sizeColumnsToFit();
  }

  processDeleteClick(financialLineVM: FinancialLineVM) {
    this.deleteItem.next({ financialLine: financialLineVM, controller: 'financiallines', route: 'financialline' });
  }

  search(text: string): ReconcilingItemVM[] {
    if (!this.rowData) { return }
    if (text == '') { this.rowData = this.persistentRowData; return }

    // this.rowData = this.rowData.filter(stockCheckItem => {
    //   const term = text.toLowerCase();
    //   return (
    //     stockCheckItem.description.toLowerCase().includes(term) ||
    //     (stockCheckItem.reg && stockCheckItem.reg.toLowerCase().includes(term)) ||
    //     (stockCheckItem.vin && stockCheckItem.vin.toLowerCase().includes(term))
    //   )
    // });

    this.filteredRowData.next(this.rowData);
  }

  updateGrid() {
    this.search('');
    this.gridApi.refreshCells();
  }

  generateExcel() {
    this.constants.loadItemsExcelDownload.emit();
  }

  onSelectionChanged(event: SelectionChangedEvent) {
    let selectedRows: any[] = event.api.getSelectedRows();
    let selectedSselectedFinancialLineIds: number[] = [];

    selectedRows.forEach(selectedRow => {
      selectedSselectedFinancialLineIds.push(selectedRow.id);
    });

    this.loadItemsService.selectedRowsParams = {
      controller: 'financiallines',
      route: 'financialline',
      ids: selectedSselectedFinancialLineIds
    }
  }
}
