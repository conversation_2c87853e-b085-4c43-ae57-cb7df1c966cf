<nav class="page-specific-navbar">
    <div class="page-title">
        <singleSitePickerWithSearch></singleSitePickerWithSearch>
    </div>

    <!-- Pick report type -->
    <div class="d-inline-block button-spacing" ngbDropdown>
        <button class="btn btn-primary" ngbDropdownToggle>
            {{ service.getChosenReportLabel() }}
        </button>
        <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
            <button *ngFor="let item of reportTypesArray()" 
                 ngbDropdownItem (click)="chooseReportType(item)">
                {{ service.getReportLabel(item) }}
            </button>
        </div>
    </div>

    <!-- Pick scanner -->
    <div *ngIf="displayTheShowPhotosSlider()" class="d-inline-block button-spacing" ngbDropdown>
        <button class="btn btn-primary" ngbDropdownToggle>
            <span *ngIf="service.chosenScanner">{{ service.chosenScanner.scannerName }} ({{ service.chosenScanner.scans }})</span>
            <span *ngIf="!service.chosenScanner">All Scanners ({{ service.totalScanCount }})</span>
        </button>
        <div id="scanners-dropdown" ngbDropdownMenu aria-labelledby="dropdownBasic1">
            <button *ngFor="let item of service.scansForExecs" 
              ngbDropdownItem (click)="chooseScanner(item)">
                <span>{{ item.scannerName }}</span>
                <span>{{ item.scans }}</span>
            </button>
        </div>
    </div>

    <!-- Slider switch to show photos -->
    <sliderSwitch *ngIf="displayTheShowPhotosSlider()" [defaultValue]="service.showPhotosOnEachItem" [text]="'Show Photos'" (toggle)="toggleShowPhotos()">
    </sliderSwitch>

    <statusAndBarChart></statusAndBarChart>
</nav>

<!-- Main Page -->
<div class="content-new">

    <instructionRow [message]="'Double click any item to see further details, or use the dropdown button above to choose other reports.'"></instructionRow>

    <div id="keyHolder" >

        <div id="key"  [ngClass]="{'showingPhotos':service.showPhotosOnEachItem}">
            <div *ngFor="let each of keyValues()" [ngClass]="each" class="imageOrBlobHolder">
                <div class="blob"></div>
                <div class="label">
                    {{ vehicleStateLabel(each) }}
                </div>
            </div>
        </div>

    </div>
    <!-- Each card showing a whole group     -->
    <div class="cph-card" *ngFor="let grouping of service.itemGroupings">

        <!-- The card header -->
        <div class="cph-card-header">
            <span class="cph-card-header-title">{{ grouping.label }} ({{ grouping.items.length }} vehicles)</span>
        </div>

        <!-- The card body -->
        <div class="cph-card-body">
            <ng-container *ngFor="let item of grouping.items">
                <!-- Each item -->
                <div class="imageOrBlobHolder" [openDelay]="600" [ngbPopover]="imagePopover" container="body" [disablePopover]="!displayTheShowPhotosSlider()"
                    placement="auto" triggers="mouseenter:mouseleave" popoverClass="scanImagePopover"
                    [ngClass]="blobHolderClass(item)" (dblclick)="openVehicleModal(item, grouping.items)">
                    <img *ngIf="service.showPhotosOnEachItem && displayTheShowPhotosSlider()" class="scanImage" [src]="item.scanImageThumbnail"
                        alt="Scan image">
                    <div class="blob" *ngIf="!service.showPhotosOnEachItem || !displayTheShowPhotosSlider()"></div>
                </div>
                <ng-template #imagePopover>
                    <img [src]=" item.scanImageLarge " alt="Scan image">
                </ng-template>
            </ng-container>
        </div>
    </div>
</div>