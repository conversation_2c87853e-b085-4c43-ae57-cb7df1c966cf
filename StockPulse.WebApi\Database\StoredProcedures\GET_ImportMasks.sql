﻿
/****** Object:  StoredProcedure [dbo].[GET_ImportMasks]    Script Date: 15/06/2021 12:32:44 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].[GET_ImportMasks]
(
    @UserId INT = NULL
)
AS
BEGIN

SET NOCOUNT ON

DECLARE @DealGroupId INT;
SET @DealGroupId = (select DealerGroupId from Users where Id = @UserId)

	
SELECT IM.[Id]
      ,IM.[Name]
      ,IM.[TopRowsToSkip]
      ,IM.[ColumnValueEqualsesJSON]
      ,IM.[ColumnValueDifferentFromsJSON]
      ,IM.[ColumnValueNotNullsJSON]
      ,IM.[ColumnsWeWantJSON]
      ,IM.[UserId]
      ,U.Name AS CreatedBy
      ,IM.[IsStandard]
	  ,IM.[IsMultiSite]
      ,IM.[IgnoreZeroValues]
  FROM [dbo].[ImportMasks] AS IM
  INNER JOIN Users AS U ON IM.UserId = U.Id
  WHERE U.DealerGroupId = @DealGroupId

	

END

GO


