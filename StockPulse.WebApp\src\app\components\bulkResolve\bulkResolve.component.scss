.modal-body {
    padding: 0 1em;
}

table {
    width: 100%;
    border-spacing: 0 1em;
    border-collapse: separate;
    
    td:nth-of-type(1) {
        white-space: nowrap;
        padding-right: 1em;
    }
}

textarea {
    width: 100%;
    border: none;
}

#fileDropArea {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    width: 100%;
    border: 1px dashed;
    padding: 1em;
    caret-color: transparent;

    &:focus-visible {
        outline: none !important;
    }
}

#filesList {
    padding: 1em;

    fa-icon.fileIcon {
        margin-right: 1em;
    }

    fa-icon.deleteIcon {
        color: var(--danger);
        cursor: pointer;
    }

    .singleFile {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        margin-bottom: 0.5em;

        .fileThumbnail {
            width: 20%;
            margin-right: 0.5em;
        }

        .info {
            max-width: 80%;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    }
}