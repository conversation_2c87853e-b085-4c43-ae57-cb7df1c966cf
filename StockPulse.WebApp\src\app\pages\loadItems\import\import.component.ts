import { Component, OnInit, ElementRef, ViewChild, } from "@angular/core";
import { SelectionsService } from '../../../services/selections.service';
import { ConstantsService } from '../../../services/constants.service';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ImportMask } from "../../../model/ImportMask";
import { ReconcilingItemVM } from "../../../model/ReconcilingItemVM";
import { ImportMaskVM } from "../../../model/ImportMaskVM";
import { Observable, Subscription } from 'rxjs';
import { IconService } from '../../../services/icon.service';
import * as XLSX from 'xlsx';
import { CphPipe } from '../../../cph.pipe';
import { SaveDataService } from 'src/app/services/saveData.service';
import {  LoadItemsService } from "../loadItems.service";
import { ApiAccessService } from "src/app/services/apiAccess.service";
import { ToastService } from "src/app/services/newToast.service";
import { StockItem } from "src/app/model/StockItem";
import { ReconciliationState } from "src/app/model/ReconciliationState";
import { FinancialLineVM } from "src/app/model/FinancialLineVM";
import { GetDataService } from "src/app/services/getData.service";
import { StockChecksService } from "../../stockChecks/stockChecks.service";

interface StockChecksForPicker {
  id: number;
  description: string;
  name: string;
  isSelected?: boolean;
}

export interface FileImportParams {
  FileName: string;
  FileDate: Date;
  LoadDate: Date;
}

@Component({
  selector: 'import',
  templateUrl: './import.component.html',
  styleUrls: ['./import.component.scss']
})


export class ImportItemsComponent implements OnInit {
  @ViewChild('importVehiclesModal', { static: true }) importVehiclesModal: ElementRef;
  @ViewChild('saveMaskChoiceModal', { static: true }) saveMaskChoiceModal: ElementRef;
  @ViewChild('saveMaskNameModal', { static: true }) saveMaskNameModal: ElementRef;
  @ViewChild('filterRowsModal', { static: true }) filterRowsModal: ElementRef;
  @ViewChild('importToAllStockChecksModal', { static: true }) importToAllStockChecksModal: ElementRef
  @ViewChild('maybeImportModal', { static: true }) maybeImportModal: ElementRef;
  @ViewChild('indexHeader') indexHeader!: ElementRef;

  //for excel import
  sourceDataArray: string[][];
  excelColumnHeadingRows: string[][]
  excelBodyData: string[][]
  excelColumnLetters: { letter: string, columnIsFiltered: boolean }[];
  fileName: string = '';
  reconcilingItemsToImport: ReconcilingItemVM[];
  stockItemsToImport: StockItem[];
  financialLinesToImport: FinancialLineVM[];
  //newlyImportedCount: number;
  excelColumnsHighlight: number[];
  newMapName: string;
  chosenColumnColumnValueEqualses: { colIndex: number, colLetter?: string, matchingValue: string }
  chosenColumnValueDifferentFroms?: { colIndex: number, colLetter?: string, matchingValue: string };
  chosenColumnValueNotNulls?: { colIndex: number, colLetter?: string };
  chosenColumnLetter: string;
  subscription: Subscription;
  subscription1: Subscription;
  subscription2: Subscription;

 // fileChosen: boolean = false;
  missingFieldsFromImport: string[];
  myImportMasks: ImportMask[] = [];
  othersImportMasks: ImportMask[] = [];
  standardImportMasks: ImportMask[] = [];
  importMasksUpdatedSubscription: Subscription;
  fileImportParams: FileImportParams;
  ignoreZeroValueRows: boolean = false;

  stockChecksForPicker: StockChecksForPicker[];
  stockChecksForPickerCopy: StockChecksForPicker[];
  stockChecksForPickerOriginal: StockChecksForPicker[];
  label: string;
  selectedStockChecks: StockChecksForPicker[];
  workSheetNames: string[];
  chosenWorksheetName: string;
  fileIsXl: boolean;
  searchString: string = '';

  constructor(
    public selections: SelectionsService,
    public constants: ConstantsService,
    public save: SaveDataService,
    public modalService: NgbModal,
    public icon: IconService,
    public cphPipe: CphPipe,
    public loadItemsService: LoadItemsService,
    public apiAccessService: ApiAccessService,
    public toastService: ToastService,
    public get: GetDataService,
    public stockChecksService: StockChecksService
  ) {

  }

  ngOnDestroy() {
    if (this.subscription) this.subscription.unsubscribe()
    if (this.subscription1) this.subscription1.unsubscribe()
    if (this.subscription2) this.subscription2.unsubscribe()
  }

  ngOnInit(): void {
    this.subscription = this.loadItemsService.chosenNewHeaderLettersEmitter.subscribe((update: { fieldName: string, letters: string }) => {
      this.onChangeChosenLetters(update.letters, update.fieldName)
    })

    this.subscription1 = this.loadItemsService.focusedOnHeaderLettersEmitter.subscribe((update: { fieldName: string, letters: string }) => {
      let lettersArray = update.letters.split(',');
      let numbersResult = this.constants.columnLettersToNumbers(lettersArray)
      this.loadItemsService.chosenImportMask.columnsWeWant[update.fieldName] = numbersResult;
      this.highlightColumnsInExcelTable(this.loadItemsService.chosenImportMask.columnsWeWant[update.fieldName])
    })

    this.importMasksUpdatedSubscription = this.constants.importMasksUpdatedEmitter.subscribe(res => {
      this.standardImportMasks = [];
      this.myImportMasks = [];
      this.othersImportMasks = [];
      this.getImportMasks();
    })

    //get import masks
    this.getImportMasks()
    this.generateStockChecksList();
  }


  getImportMasks() {
    this.apiAccessService.get('ImportMasks', 'GetImportMasks').subscribe((res: ImportMaskVM[]) => {
      this.constants.ImportMasks = [];
      this.standardImportMasks = [];
      this.myImportMasks = [];
      this.othersImportMasks = [];

      res.sort((a, b) => a.name.localeCompare(b.name)).forEach(item => {
        let proper: ImportMask = {
          id: item.id,
          name: item.name,
          topRowsToSkip: item.topRowsToSkip,
          columnValueEqualses: JSON.parse(item.columnValueEqualsesJSON),
          columnValueDifferentFroms: JSON.parse(item.columnValueDifferentFromsJSON),
          columnValueNotNulls: JSON.parse(item.columnValueNotNullsJSON),
          columnsWeWant: JSON.parse(item.columnsWeWantJSON),
          columnsWeWantAsLetters: null,
          createdBy: item.createdBy,
          isStandard: item.isStandard,
          isMultiSite: item.isMultiSite,
          ignoreZeroValues: item.ignoreZeroValues
        }
        this.constants.ImportMasks.push(proper)

        if (item.isStandard) {
          this.standardImportMasks.push(proper);
        } else if (this.selections.userId === item.userId) {
          this.myImportMasks.push(proper);
        } else {
          this.othersImportMasks.push(proper);
        }
      })
    })
  }

  cancelImport() {
    this.resetImportSettings()
    this.loadItemsService.showImportProcess = false;
  }



  saveRecItems(items: ReconcilingItemVM[], allSites?: boolean) {
    if (this.missingFieldsFromImport) return;

    // Filter out blank VINs (US)
    if (this.constants.currencySymbol === '$') {
      items = items.filter(x => x.vin !== null || x.vin !== '');
    }
    // Filter out blank VINs AND Reg (Non-US)
    else {
      items = items.filter(x => (x.vin !== null || x.vin !== '') && (x.reg !== null || x.reg !== ''));
    }

    if (allSites) {
      this.get.getStockCheckIdsForUserDealerGroup().subscribe((res: number[]) => {
        this.saveRecItemsAllActiveStockChecks(items, res);
      })
    } else {
      // User wants to continue with just this stock check
      this.saveRecItemsThisStockCheck(items);
    };

  }
  
  saveRecItemsThisStockCheck(items: ReconcilingItemVM[]) {
    const savingToast = this.toastService.loadingToast('Saving vehicles...');

    this.apiAccessService.addFileImport(this.fileImportParams).subscribe((fileImportId: number) => {
      this.save.bulkSaveNewReconcilingItems(items, this.loadItemsService.chosenReconcilingItemType.id, null, fileImportId).subscribe((results: StockItem[]) => {
        //success
        this.reconcilingItemsToImport = null; //reset for next time
        
        this.resetImportSettings();
        this.loadItemsService.bulkItemsLoaded.emit(true);
        this.loadItemsService.showImportProcess = false;
        savingToast.close();
        this.toastService.successToast(`Successfully added ${this.constants.pluralise(items.length, 'vehicle', 'vehicles')}`);
  
      }, e => {
        savingToast.close();
        this.toastService.errorToast(`Failed to save: ${e}`);
      }, () => {
  
      })
    }, error => {
      this.toastService.errorToast('Failed to save file import log');
      console.error('Failed to save file import log', error);
    });
  }

  saveRecItemsAllActiveStockChecks(items: ReconcilingItemVM[], stockCheckIds: number[]) {
    const savingToast = this.toastService.loadingToast('Saving vehicles...');

    let successCount: number = 0;
    let failCount: number = 0;

    this.apiAccessService.addFileImport(this.fileImportParams).subscribe((fileImportId: number) => {
      for (let i = 0; i < stockCheckIds.length; i++) {
        this.save.bulkSaveNewReconcilingItems(items, this.loadItemsService.chosenReconcilingItemType.id, stockCheckIds[i], fileImportId).subscribe((results: StockItem[]) => {
          successCount += 1;
          if ((successCount + failCount) === stockCheckIds.length) {
            this.finishBulkUploadProcess(savingToast, successCount, failCount, items.length);
          }
        }, e => {
          failCount += 1;
          if ((successCount + failCount) === stockCheckIds.length) {
            this.finishBulkUploadProcess(savingToast, successCount, failCount, items.length);
          }
        });
      }
    }, error => {
      this.toastService.errorToast('Failed to save file import log');
      console.error('Failed to save file import log', error);
    });
  }

  finishBulkUploadProcess(savingToast, successCount: number, failCount: number, totalItems: number) {
    this.reconcilingItemsToImport = null;
    this.loadItemsService.showImportProcess = false;
    this.resetImportSettings();

    this.loadItemsService.bulkItemsLoaded.emit(true);
    savingToast.close();
    
    if (successCount > 0) {
      this.toastService.successToast(`Successfully added ${this.constants.pluralise(totalItems, 'vehicle', 'vehicles')} to ${successCount} stock checks`);
    }

    if (failCount > 0) {
      this.toastService.successToast(`Failed to add ${this.constants.pluralise(totalItems, 'vehicle', 'vehicles')} to ${failCount} stock checks`);
    }
  }

  bulkSaveStockItems(newItemsRequestedToSave: StockItem[]) {
    // const savingToast = this.toastService.loadingToast('Saving vehicles...');

    const importingToast = this.toastService.loadingToast('Importing...');

    if (this.missingFieldsFromImport) return;

    let actuallyNewItems: StockItem[] = [];

    newItemsRequestedToSave.forEach(item => {
      if (Number.isNaN(item.dis)) {
        item.dis = 0
      }

      if (Number.isNaN(item.groupDIS)) {
        item.groupDIS = 0
      }

      if (Number.isNaN(item.stockValue)) {
        item.stockValue = 0
      }
      //if (
      //  (!item.reg) &&  //new item doesn't have a reg or if it does it doesn't match an existing one
      //  (!item.vin) //new item doesn't have a chassis or if it does, it doesn't match existing list
      //) {
      actuallyNewItems.push(item)

     
      //}
    })

    this.apiAccessService.addFileImport(this.fileImportParams).subscribe((fileImportId: number) => {
      
      // Filter out blank VINs (US)
      if (this.constants.currencySymbol === '$') {
        newItemsRequestedToSave = newItemsRequestedToSave.filter(x => x.vin !== null || x.vin !== '');
      }
      // Filter out blank VINs AND Reg (Non-US)
      else {
        newItemsRequestedToSave = newItemsRequestedToSave.filter(x => (x.vin !== null || x.vin !== '') && (x.reg !== null || x.reg !== ''));
      }

      const payload = {newStockItems:newItemsRequestedToSave,stockCheckId:this.selections.stockCheck.id, fileImportId: fileImportId};

      this.apiAccessService.addStockItems(payload).subscribe((results: number[]) => {
        // savingToast.close();
        //ok - success!
        this.toastService.successToast('Successfully added vehicles');
        this.resetImportSettings();
        this.loadItemsService.bulkItemsLoaded.emit(true);
        this.loadItemsService.showImportProcess = false;
        importingToast.close();
        //observer.next(actuallyNewItems);
        //observer.complete();
      }, e => {
        // savingToast.close();
        this.toastService.errorToast(`Failed to save: ${e}`);
        console.error('error ' + JSON.stringify(e))
        importingToast.close();
        //observer.error();
        //observer.complete();
      });
    }, error => {
      this.toastService.errorToast('Failed to save file import log');
      console.error('Failed to save file import log', error);
    });

  }


  bulkSaveFinancialLines(financialLinesToImport: FinancialLineVM[]) {

    if (this.missingFieldsFromImport) return;

    this.apiAccessService.addFileImport(this.fileImportParams).subscribe((fileImportId: number) => {
      this.apiAccessService.bulkSaveFinancialLines(financialLinesToImport, fileImportId).subscribe((results: any[]) => {
        //ok - success!
        this.toastService.successToast('Successfully added Trial Balance');
        this.resetImportSettings();
        this.loadItemsService.bulkItemsLoaded.emit(true);
        this.loadItemsService.showImportProcess = false;
      }, e => {
        this.toastService.errorToast(e);
        console.error('error ' + JSON.stringify(e))
      });
    }, error => {
      this.toastService.errorToast('Failed to save file import log');
      console.error('Failed to save file import log', error);
    });
  }



  //-----------------------------------------
  // Import process 

  resetImportSettings() {

    this.sourceDataArray = [];
    this.excelColumnHeadingRows = [];
    this.excelBodyData = [];
    this.excelColumnLetters = []
    this.reconcilingItemsToImport = null;
    this.stockItemsToImport = null;
    this.financialLinesToImport = null;
   // this.newlyImportedCount = 0
    //this.headerRowsCount = 1
    this.loadItemsService.chosenImportMask = null;
    this.loadItemsService.importFile = null;
    this.workSheetNames = null;
    this.chosenWorksheetName = null;
  }

  openExcelFile(file: string): any[][] {
    const wb: XLSX.WorkBook = XLSX.read(file, { type: 'binary' });

    // Save sheet names for later
    this.workSheetNames = wb.SheetNames;

    if (!this.chosenWorksheetName) {
      this.chosenWorksheetName = wb.SheetNames[0];
    }

    const ws: XLSX.WorkSheet = wb.Sheets[this.chosenWorksheetName];

    let createdDate: Date = new Date();
    if (wb.Props && wb.Props.CreatedDate) {
      createdDate = wb.Props.CreatedDate;
    }

    this.fileImportParams = {
      FileName: this.fileName,
      FileDate: createdDate,
      LoadDate: new Date()
    }

    /* save data */
    return <any[][]>(XLSX.utils.sheet_to_json(ws,  { header: 1, blankrows: true, raw: false }));
  }


  openCsvFile(file: string): any[][] {

    let result: Array<string>[] = []

    let rows = file.split('\n');
    
    if (rows[rows.length - 1] === "") {
      rows.pop(); // If CSV file has blank row at the end, remove it
    }

    rows.forEach(row => {
      let cells = this.constants.splitOn(row, ',');
      //trim off surrounding "
      cells.forEach((cell, i) => {
        if (cells[i].charAt(0) == '"') cells[i] = cells[i].substring(1);
        if (cells[i].charAt(cells[i].length - 1) == '"') cells[i] = cells[i].substring(0, cells[i].length - 1);
      })
      result.push(cells);
    })

    this.fileImportParams = {
      FileName: this.fileName,
      FileDate: new Date(),
      LoadDate: new Date()
    }

    return result;

  }


  onFileChange(evt: any) {
    let toastRef: any = this.toastService.loadingToast('Uploading file...');

    if (evt.target.value) {
      this.fileName = /[^\\]*$/.exec(evt.target.value)[0];
      //this.fileChosen = true;
    } else {
     // this.fileChosen = false;
    }

    /* wire up file reader */
    const target: DataTransfer = <DataTransfer>(evt.target);
    let inputFileType = target['value'].split(/\.(?=[^\.]+$)/)[1]

    if (target.files.length !== 1) {
      alert('Please select only 1 file');
      return
    }


    const reader: FileReader = new FileReader();
    reader.onload = (e: any) => {
      try {
        /* read workbook */
        this.loadItemsService.importFile = e.target.result;

        if (inputFileType.includes('xl')) {
          this.fileIsXl = true;
          this.sourceDataArray = this.openExcelFile(this.loadItemsService.importFile)
        } else if (inputFileType.includes('csv')) {
          this.fileIsXl = false;
          this.sourceDataArray = this.openCsvFile(this.loadItemsService.importFile);
          //this.sourceDataArray
        } else {
          this.toastService.errorToast('Invalid file type - Please upload an Excel or CSV file');
          this.toastService.closeToast(toastRef);
        }

        this.createExcelVisualisationTableData(toastRef)

      } catch (error) {
        this.toastService.closeToast(toastRef);
        this.toastService.errorToast('Problem loading file');
      }
    };

    setTimeout(() => {
      reader.readAsBinaryString(target.files[0]);
      this.reconcilingItemsToImport = null;
    }, 50)

  }





  createExcelVisualisationTableData(toastRef?: any) {
    this.toastService.loadingToast();

    // Step 1: Reduce data filtering
    const maxRows = 1000;  // Limit rows to handle
    const excelFirstxRows = this.sourceDataArray.slice(0, maxRows); // Use slice for better performance

    // Step 2: Optimize column letters creation
    let maxCols = 0;
    excelFirstxRows.forEach(row => {
        if (row.length > maxCols) maxCols = row.length;
    });

    this.excelColumnLetters = Array.from({ length: maxCols }, (_, i) => ({
        letter: this.constants.columnNumberToColumnLetterString(i + 1),
        columnIsFiltered: false,
    }));

    // Step 3: Clone array more efficiently
    this.excelBodyData = excelFirstxRows.map(row => [...row]);

    const headerRowsCount = this.loadItemsService.chosenImportMask?.topRowsToSkip || 0;
    this.excelColumnHeadingRows = this.excelBodyData.splice(0, headerRowsCount);

    // Step 4: Fill in any blank cells at the end
    this.excelBodyData.forEach(bodyRow => {
        while (bodyRow.length < maxCols) bodyRow.push('');
    });

    this.excelColumnHeadingRows.forEach(headerRow => {
        while (headerRow.length < maxCols) headerRow.push('');
    });

    // Step 5: Show excel table headers if we are currently filtering
    this.excelColumnLetters.forEach(colLetter => {
        this.checkIfColumnIsFiltered(colLetter);
    });

    setTimeout(() => {
        this.toastService.destroyToast();
        this.loadItemsService.updateMultiSiteEmitter.next(this.loadItemsService.multiSite);
        this.toastService.closeToast(toastRef);
    }, 20);

    document.getElementById("indexHeader")!.style.border = '1px';
    setTimeout(() => {
      document.getElementById("indexHeader")!.style.border = 'none';
    }, 100)
  }


  provideData(line: string[], columnIndexes: Array<number>): string {

    let combinedResult = '';

    if (columnIndexes === undefined) return combinedResult;

    columnIndexes.forEach((index, iteration) => {
      //if -1, indicates just want empty combinedResult
      if (index == -1) {
        combinedResult = ''
      } else {
        //otherwise do properly
        if (iteration > 0) {
          combinedResult = combinedResult + ' | ';
        }
        //have to remove 1 from given number as was created using excel definition of col heading which is 1 based and now need 0 based to interpret array
        let nextValue: string = line[index - 1]
        if (nextValue == 'undefined' || nextValue == 'null' || nextValue == undefined || nextValue == null) {
          nextValue = '';
          combinedResult = combinedResult.substring(0, combinedResult.length - 3);
        }
        if (combinedResult === ' | ') { combinedResult = ''; }
        combinedResult += nextValue
      }
    })

    return combinedResult;
  }

  sumData(line: string[], columnIndexes: number[]): number {

    let total = 0

    columnIndexes.forEach(index => {

      total += this.getFloatValue(line[index - 1])
    })

    return total
  }

  getFloatValue(input: string|number): number {
    if (input === undefined || input === "") {
      return 0;
    }
    
    if (typeof input != 'string') {
       return input;
    }
  
    // Using a regular expression to match all commas in the string and replace them with an empty string
    const result = input.replace(/,/g, '');
  
    return parseFloat(result);
  }


  parseImportedReport(toastRef?: any) {

    //do the processing
    let copyOfExcelData: string[][] = JSON.parse(JSON.stringify(this.sourceDataArray))
    copyOfExcelData.splice(0, this.loadItemsService.chosenImportMask.topRowsToSkip); //remove the header row

    let deletionRowIndexes = [];
    this.loadItemsService.filteredOutDueToBlanks = 0;

    try {
      //strip out unwanted rows

      for (let i = copyOfExcelData.length - 1; i > -1; i--) { //going backwards
        //does  include what we don't want
        this.loadItemsService.chosenImportMask.columnValueDifferentFroms.forEach(columnValueDifferentFrom => {
          try {

            if (copyOfExcelData[i][columnValueDifferentFrom.colIndex - 1] && copyOfExcelData[i][columnValueDifferentFrom.colIndex - 1].toString().includes(columnValueDifferentFrom.matchingValue)) { deletionRowIndexes.push(i) }
          }
          catch {
            deletionRowIndexes.push(i)
          }
        })

        //does cell include
        this.loadItemsService.chosenImportMask.columnValueEqualses.forEach(columnValueEquals => {
          try {
            if (!copyOfExcelData[i][columnValueEquals.colIndex - 1] || !copyOfExcelData[i][columnValueEquals.colIndex - 1].toString().includes(columnValueEquals.matchingValue)) { deletionRowIndexes.push(i) }
          }
          catch {
            deletionRowIndexes.push(i)
          }
        })

        //and do the not nulls
        this.loadItemsService.chosenImportMask.columnValueNotNulls.forEach(columnValueNotNull => {
          try {

            if (copyOfExcelData[i][columnValueNotNull.colIndex - 1] == null ||
              copyOfExcelData[i][columnValueNotNull.colIndex - 1] == ' ' ||
              copyOfExcelData[i][columnValueNotNull.colIndex - 1] == '') {
                deletionRowIndexes.push(i);
              }
          }
          catch {
            deletionRowIndexes.push(i)
          }
        })

        // Only applicable for reg / VIN
        if (this.loadItemsService.chosenImportMask.columnsWeWant.reg.length > 0 || this.loadItemsService.chosenImportMask.columnsWeWant.vin.length > 0) {
          if (this.constants.neverScanReg) {
            if (this.loadItemsService.chosenImportMask.columnsWeWant.vin.length > 0) {
              if (!copyOfExcelData[i][this.loadItemsService.chosenImportMask.columnsWeWant.vin[0]-1]) {
                this.loadItemsService.filteredOutDueToBlanks += 1;
                deletionRowIndexes.push(i);
              }
            }
          } else {
            if (this.loadItemsService.chosenImportMask.columnsWeWant.reg.length > 0 && this.loadItemsService.chosenImportMask.columnsWeWant.vin.length === 0) {
              if (!copyOfExcelData[i][this.loadItemsService.chosenImportMask.columnsWeWant.reg[0]-1]) {
                this.loadItemsService.filteredOutDueToBlanks += 1;
                deletionRowIndexes.push(i);
              }
            } else if (this.loadItemsService.chosenImportMask.columnsWeWant.reg.length === 0 && this.loadItemsService.chosenImportMask.columnsWeWant.vin.length > 0) {
              if (!copyOfExcelData[i][this.loadItemsService.chosenImportMask.columnsWeWant.vin[0]-1]) {
                this.loadItemsService.filteredOutDueToBlanks += 1;
                deletionRowIndexes.push(i);
              }
            } else {
              if (!copyOfExcelData[i][this.loadItemsService.chosenImportMask.columnsWeWant.reg[0]-1] && !copyOfExcelData[i][this.loadItemsService.chosenImportMask.columnsWeWant.vin[0]-1]) {
                this.loadItemsService.filteredOutDueToBlanks += 1;
                deletionRowIndexes.push(i);
              }
            }
          }
        }
      }
    }
    catch (e) {
      console.error('Error: ' + JSON.stringify(e))
      console.error(e);
    }


    let filteredImportData: string[][] = copyOfExcelData.filter((e, i) => deletionRowIndexes.indexOf(i) == -1)
    let trimChassis = (chassis: string) => {
      if (chassis.length > 8) {
        return chassis.slice(0, 0) + chassis.substring(chassis.length - 8, chassis.length)  //used to be (0,3)
      } else {
        return chassis
      }
    }

    //just grab the columns we want
    try {

      if (this.loadItemsService.chosenReconcilingItemType.description !== 'DMS Stock' && this.loadItemsService.chosenReconcilingItemType.description!=='Trial Balance' && this.loadItemsService.chosenReconcilingItemType.description!=='Agency Stock') {

        let vehiclesToImport: ReconcilingItemVM[] = []
        filteredImportData.forEach(line => {

          let reg = this.provideData(line, this.loadItemsService.chosenImportMask.columnsWeWant.reg)
          if (reg) {
            reg = reg.replace(/ /g, '')  //firrst slash is beginning of term to look for, up until next slash, then global
          }


          let vehicleToImport: ReconcilingItemVM = {
            reg: reg,
            vin: trimChassis(this.provideData(line, this.loadItemsService.chosenImportMask.columnsWeWant.vin)),
            description: this.provideData(line, this.loadItemsService.chosenImportMask.columnsWeWant.description),
            reference: this.provideData(line, this.loadItemsService.chosenImportMask.columnsWeWant.reference),
            comment: this.provideData(line, this.loadItemsService.chosenImportMask.columnsWeWant.comment),
            site: this.provideData(line, this.loadItemsService.chosenImportMask.columnsWeWant.site),
          };

          vehiclesToImport.push(vehicleToImport);
        })

        if (this.reconcilingItemsToImport) {
          //already exists so have to trigger the event emitter
          this.loadItemsService.updateImportTableEmitter.next(vehiclesToImport);
          this.reconcilingItemsToImport = vehiclesToImport;
        } else {
          this.reconcilingItemsToImport = vehiclesToImport;
        }

        if(toastRef){
          this.toastService.closeToast(toastRef);
        }

      }

      else if (this.loadItemsService.chosenReconcilingItemType.description === 'Agency Stock') {

        let vehiclesToImport: StockItem[] = []

        filteredImportData.forEach(line => {

          let reg = this.provideData(line, this.loadItemsService.chosenImportMask.columnsWeWant.reg)
          if (reg) {
            reg = reg.replace(/ /g, '')  //firrst slash is beginning of term to look for, up until next slash, then global
          }
          
          // For agency stock, certain values will always be zero
          let vehicleToImport: StockItem = {
            stockItemId:null,
            reg: reg,
            vin: trimChassis(this.provideData(line, this.loadItemsService.chosenImportMask.columnsWeWant.vin)),
            description: this.provideData(line, this.loadItemsService.chosenImportMask.columnsWeWant.description),
            dis: 0,
            groupDIS: 0,
            branch: this.provideData(line, this.loadItemsService.chosenImportMask.columnsWeWant.branch),
            reference: this.provideData(line, this.loadItemsService.chosenImportMask.columnsWeWant.reference),
            comment: this.provideData(line, this.loadItemsService.chosenImportMask.columnsWeWant.comment),
            stockType: 'Agency Stock',
            stockValue: 0,
            stockCheckId: this.selections.stockCheck.id,
            site: this.provideData(line, this.loadItemsService.chosenImportMask.columnsWeWant.site),
            state: ReconciliationState.AllItems,
            isAgencyStock: true,
            flooring: 0
          };
          vehiclesToImport.push(vehicleToImport);
        })

        if (this.stockItemsToImport) {
          //already exists so have to trigger the event emitter
          this.loadItemsService.updateImportTableEmitter.next(vehiclesToImport);
          this.stockItemsToImport = vehiclesToImport;
        } else {
          this.stockItemsToImport = vehiclesToImport;
        }

        if(toastRef){
          this.toastService.closeToast(toastRef);
        }

      }

      else if (this.loadItemsService.chosenReconcilingItemType.description === 'DMS Stock') {

        let vehiclesToImport: StockItem[] = [];

        filteredImportData.forEach(line => {
          let reg = this.provideData(line, this.loadItemsService.chosenImportMask.columnsWeWant.reg)
          if (reg) {
            reg = reg.replace(/ /g, '')  //firrst slash is beginning of term to look for, up until next slash, then global
          }


          let vehicleToImport: StockItem = {
            stockItemId:null,
            reg: reg,
            vin: trimChassis(this.provideData(line, this.loadItemsService.chosenImportMask.columnsWeWant.vin)),
            description: this.provideData(line, this.loadItemsService.chosenImportMask.columnsWeWant.description),
            dis: this.sumData(line, this.loadItemsService.chosenImportMask.columnsWeWant.dis),
            groupDIS: this.sumData(line, this.loadItemsService.chosenImportMask.columnsWeWant.groupDIS),
            branch: this.provideData(line, this.loadItemsService.chosenImportMask.columnsWeWant.branch),
            reference: this.provideData(line, this.loadItemsService.chosenImportMask.columnsWeWant.reference),
            comment: this.provideData(line, this.loadItemsService.chosenImportMask.columnsWeWant.comment),
            stockType: this.provideData(line, this.loadItemsService.chosenImportMask.columnsWeWant.stockType),
            stockValue: this.sumData(line, this.loadItemsService.chosenImportMask.columnsWeWant.stockValue),
            stockCheckId: this.selections.stockCheck.id,
            site: this.provideData(line, this.loadItemsService.chosenImportMask.columnsWeWant.site),
            state: ReconciliationState.AllItems,
            isAgencyStock: false,
            flooring: this.loadItemsService.chosenImportMask.columnsWeWant.flooring ? this.sumData(line, this.loadItemsService.chosenImportMask.columnsWeWant.flooring) : 0

          };

          if (this.ignoreZeroValueRows && (vehicleToImport.stockValue === 0 || vehicleToImport.stockValue === null)) { return; }

          vehiclesToImport.push(vehicleToImport);
        })

        if (this.stockItemsToImport) {
          //already exists so have to trigger the event emitter
          this.loadItemsService.updateImportTableEmitter.next(vehiclesToImport);
          this.stockItemsToImport = vehiclesToImport;
        } else {
          this.stockItemsToImport = vehiclesToImport;
        }

        if(toastRef){
          this.toastService.closeToast(toastRef);
        }

      }

      else if (this.loadItemsService.chosenReconcilingItemType.description === 'Trial Balance') {

        let financialLinesToImport: FinancialLineVM[] = []

        filteredImportData.forEach(line => {
          let accountCode = this.provideData(line, this.loadItemsService.chosenImportMask.columnsWeWant.accountCode)
          
          let financialLineToImport: FinancialLineVM = {
            accountCode: this.provideData(line, this.loadItemsService.chosenImportMask.columnsWeWant.accountCode),
            description: this.provideData(line, this.loadItemsService.chosenImportMask.columnsWeWant.description),
            balance: Number(this.provideData(line, this.loadItemsService.chosenImportMask.columnsWeWant.balance)),
            notes: '',
            isReconcilingAdj: false,
            site: this.provideData(line, this.loadItemsService.chosenImportMask.columnsWeWant.site),
          };
          financialLinesToImport.push(financialLineToImport);
        })

        if (this.financialLinesToImport) {
          //already exists so have to trigger the event emitter
          this.loadItemsService.updateImportTableEmitter.next(financialLinesToImport);
          this.financialLinesToImport = financialLinesToImport;
        } else {
          this.financialLinesToImport = financialLinesToImport;
        }

      }


    }
    catch (e) {
      this.toastService.errorToast('Failed to import, did you use the right map?');
      this.toastService.closeToast(toastRef);
    }


  }

  changeHeaderRow(change: number) {
    this.loadItemsService.chosenImportMask.topRowsToSkip += change;
    this.createExcelVisualisationTableData()
    this.parseImportedReport();
  }

  highlightColumnsInExcelTable(numbers: number[]) {
    this.excelColumnsHighlight = numbers;
  }

  checkCellHighlight(number: number) {
    return this.excelColumnsHighlight && this.excelColumnsHighlight.includes(number)
  }

  onChangeChosenLetters(letters: string, field: string) {
    let lettersParsed: string[] = letters.split(',');
    let numbers = this.constants.columnLettersToNumbers(lettersParsed);
    this.loadItemsService.chosenImportMask.columnsWeWant[field] = numbers;
    this.loadItemsService.chosenImportMask.columnsWeWantAsLetters[field] = letters;
    this.highlightColumnsInExcelTable(numbers);
    this.parseImportedReport();
  }


  possiblySaveImportMap() {

    this.modalService.open(this.saveMaskChoiceModal, { windowClass: 'saveModal', size: 'sm', keyboard: true, ariaLabelledBy: 'modal-basic-title' }).result.then((result) => {
      //have chosen to 'OK' selections


    }, (reason) => {
      //dismissed

    });
  }





  probablyOverwriteImportMask(importMask: ImportMask | null) {

    this.loadItemsService.chosenImportMask.id = importMask ? importMask.id : null
    this.newMapName = this.loadItemsService.chosenImportMask.name; 
    this.loadItemsService.chosenImportMask.isMultiSite = this.loadItemsService.multiSite;
    this.loadItemsService.chosenImportMask.ignoreZeroValues = this.ignoreZeroValueRows;


    this.modalService.open(this.saveMaskNameModal, { size: 'sm', keyboard: true, ariaLabelledBy: 'modal-basic-title' }).result.then((result) => {
      //have chosen to 'OK' selections
      let importMaskToSave: ImportMask = JSON.parse(JSON.stringify(this.loadItemsService.chosenImportMask))
      importMaskToSave.name = this.newMapName
      this.loadItemsService.chosenImportMask.name = this.newMapName;

      this.save.saveImportMap(importMaskToSave).subscribe(res => {
        if (!importMaskToSave.id) {
          //we saved 

          this.getImportMasks()

        } else {
          //are saving an existing so don't get given an object back
          this.getImportMasks()
        }

        this.toastService.successToast('Import map saved');
        this.modalService.dismissAll()
      }, e => {
        this.toastService.errorToast('Failed to save import map');
        console.error('failed to save import map ' + JSON.stringify(e))
      })


    }, (reason) => {
      //chose to cancel, do nothing

    });


  }



  chooseImportMask(importMask: ImportMask) {
    let toastRef: any = this.toastService.loadingToast('Loading import map...');

    setTimeout(() => {
      importMask.columnsWeWantAsLetters = {
        reg: [],
        vin: [],
        description: [],
        dis: [],
        groupDIS: [],
        reference: [],
        branch: [],
        comment: [],
        regPrevious: [],
        stockType: [],
        stockValue: [],
        site: [],
        flooring: [],
        accountCode: [],
        balance: []
      };

      ['reg', 'vin', 'site', 'description', 'dis', 'groupDIS', 'reference', 'branch', 'comment', 'regPrevious', 'stockType', 'stockValue', 'flooring', 'accountCode', 'balance'].forEach(key => {
        importMask.columnsWeWantAsLetters[key] = this.constants.columnNumbersToLetters(importMask.columnsWeWant[key])
      })
      this.loadItemsService.chosenImportMask = importMask;

      //add col letters to any existing choices
      this.loadItemsService.chosenImportMask.columnValueEqualses.forEach(item => {
        item.colLetter = this.constants.columnNumberToColumnLetterString(item.colIndex)
      })
      this.loadItemsService.chosenImportMask.columnValueDifferentFroms.forEach(item => {
        item.colLetter = this.constants.columnNumberToColumnLetterString(item.colIndex)
      })
      this.loadItemsService.chosenImportMask.columnValueNotNulls.forEach(item => {
        item.colLetter = this.constants.columnNumberToColumnLetterString(item.colIndex)
      })



      this.createExcelVisualisationTableData()
      
      this.loadItemsService.multiSite = importMask.isMultiSite;
      this.ignoreZeroValueRows = importMask.ignoreZeroValues;
      
      this.parseImportedReport(toastRef)
    }, 250)
  }


  newImportMask() {
    let toastRef: any = this.toastService.loadingToast('Creating import map...');

    setTimeout(() => {
      this.loadItemsService.chosenImportMask = {
        id: null,
        name: null,
        topRowsToSkip: 1,
        //userId: this.selections.userId,
        columnValueEqualses: [],
        columnValueDifferentFroms: [],
        columnValueNotNulls: [],
        columnsWeWant: {
          vin: [],
          reg: [],
          description: [],
          dis: [],
          groupDIS: [],
          reference: [],
          branch: [],
          comment: [],
          regPrevious: [],
          stockType: [],
          stockValue: [],
          flooring: [],
          site: [],
          accountCode: [],
        balance: []
        },
        columnsWeWantAsLetters: {
          vin: [],
          reg: [],
          description: [],
          dis: [],
          groupDIS: [],
          reference: [],
          branch: [],
          comment: [],
          regPrevious: [],
          stockType: [],
          stockValue: [],
          site: [],
          accountCode: [],
          flooring: [],
          balance: []
        },
        isStandard: false
      }


      this.createExcelVisualisationTableData()
      this.parseImportedReport(toastRef);
    }, 250)
  }


  editColumnFilter(columnLetter: string) {

    //create the objects indicating currently chosen filters
    this.chosenColumnLetter = columnLetter;

    let importMaskWas = JSON.parse(JSON.stringify(this.loadItemsService.chosenImportMask))

    this.chosenColumnColumnValueEqualses = this.loadItemsService.chosenImportMask.columnValueEqualses.find(x => x.colLetter === columnLetter) || {
      colIndex: this.constants.columnLettersToNumbers([this.chosenColumnLetter])[0],
      colLetter: this.chosenColumnLetter,
      matchingValue: '',
    }

    this.chosenColumnValueDifferentFroms = this.loadItemsService.chosenImportMask.columnValueDifferentFroms.find(x => x.colLetter === columnLetter) || {
      colIndex: this.constants.columnLettersToNumbers([this.chosenColumnLetter])[0],
      colLetter: this.chosenColumnLetter,
      matchingValue: '',
    }

    this.chosenColumnValueNotNulls = this.loadItemsService.chosenImportMask.columnValueNotNulls.find(x => x.colLetter === columnLetter)


    this.modalService.open(this.filterRowsModal, { size: 'sm', keyboard: true, ariaLabelledBy: 'modal-basic-title' }).result.then((result) => {
      //have chosen to 'OK' selections
      //let things stay as they are..

    }, (reason) => {
      //dismissed
      //reset import mask back to what it was
      this.loadItemsService.chosenImportMask = JSON.parse(JSON.stringify(importMaskWas))

      this.parseImportedReport()
    });

  }

  updateColumnValueEquals(colLetter: string, newValue) {

    this.chosenColumnColumnValueEqualses = {
      colIndex: this.constants.columnLettersToNumbers([colLetter])[0],
      colLetter: colLetter,
      matchingValue: newValue,
    }

    //remove column choice from existing overall object
    this.loadItemsService.chosenImportMask.columnValueEqualses = this.loadItemsService.chosenImportMask.columnValueEqualses.filter(x => x.colLetter !== colLetter)
    //if we have chosen something then now add the new choice in
    if (newValue) {
      this.loadItemsService.chosenImportMask.columnValueEqualses.push(this.chosenColumnColumnValueEqualses)
      this.excelColumnLetters.find(x => x.letter === colLetter).columnIsFiltered = true;
    } else {
      this.excelColumnLetters.find(x => x.letter === colLetter).columnIsFiltered = false;
    }

    let column = this.excelColumnLetters.find(x => x.letter === colLetter)
    this.checkIfColumnIsFiltered(column);
    this.parseImportedReport()
  }


  updateColumnValueDifferentFrom(colLetter: string, newValue) {

    this.chosenColumnValueDifferentFroms = {
      colIndex: this.constants.columnLettersToNumbers([colLetter])[0],
      colLetter: colLetter,
      matchingValue: newValue,
    }

    //remove column choice from existing overall object
    this.loadItemsService.chosenImportMask.columnValueDifferentFroms = this.loadItemsService.chosenImportMask.columnValueDifferentFroms.filter(x => x.colLetter !== colLetter)
    //if we have chosen something then now add the new choice in
    if (newValue) {
      this.loadItemsService.chosenImportMask.columnValueDifferentFroms.push(this.chosenColumnValueDifferentFroms)
      this.excelColumnLetters.find(x => x.letter === colLetter).columnIsFiltered = true;
    } else {
      this.excelColumnLetters.find(x => x.letter === colLetter).columnIsFiltered = false;
    }

    let column = this.excelColumnLetters.find(x => x.letter === colLetter)
    this.checkIfColumnIsFiltered(column);
    this.parseImportedReport()
  }

  updateColumnValueNotNull(colLetter: string, checked) {


    //remove column choice from existing overall object
    this.loadItemsService.chosenImportMask.columnValueNotNulls = this.loadItemsService.chosenImportMask.columnValueNotNulls.filter(x => x.colLetter !== colLetter)
    //if we have chosen something then now add the new choice in
    if (checked) {
      this.chosenColumnValueNotNulls = {
        colIndex: this.constants.columnLettersToNumbers([colLetter])[0],
        colLetter: colLetter,
      }
      this.loadItemsService.chosenImportMask.columnValueNotNulls.push(this.chosenColumnValueNotNulls)
    } else {
      this.chosenColumnValueNotNulls = null
    }

    let column = this.excelColumnLetters.find(x => x.letter === colLetter)
    this.checkIfColumnIsFiltered(column);
    this.parseImportedReport()
  }

  checkIfColumnIsFiltered(column: { letter: string, columnIsFiltered: boolean }) {
    if (!this.loadItemsService.chosenImportMask) return;
    column.columnIsFiltered = (
      this.loadItemsService.chosenImportMask.columnValueEqualses.map(x => x.colLetter).includes(column.letter) ||
      this.loadItemsService.chosenImportMask.columnValueDifferentFroms.map(x => x.colLetter).includes(column.letter) ||
      this.loadItemsService.chosenImportMask.columnValueNotNulls.map(x => x.colLetter).includes(column.letter)
    )
  }

  validateImport() {

    let rows: StockItem[] | ReconcilingItemVM[];
    let tbRows: FinancialLineVM[];

    if (this.stockItemsToImport) {
      rows = this.stockItemsToImport;
    } else if (this.financialLinesToImport) {
      tbRows = this.financialLinesToImport;
    } else {
      rows = this.reconcilingItemsToImport;
    }

    let missingFields: string[] = [];

    if (this.financialLinesToImport) {
      // If no Account Code or Account Name
      if (tbRows.filter(x => x.accountCode === '').length === tbRows.length && tbRows.filter(x => x.description === '').length === tbRows.length) {
        missingFields.push('Account Code and/or Account Name');
      }

      // If no Value
      if (tbRows.filter(x => x.balance === 0).length === tbRows.length) {
        missingFields.push('Value');
      }

      if (this.loadItemsService.multiSite) {
        if (tbRows.filter(x => x.site === '').length === tbRows.length) missingFields.push('Site');
      }
    } else {
      // If no Reg or VIN
      if (rows.filter(x => x.reg === '').length === rows.length && rows.filter(x => x.vin === '').length === rows.length) {
        if (this.constants.currencySymbol === "$") {
          missingFields.push('VIN');
        } else {
          missingFields.push('Reg and/or VIN');
        }
      }

      if (this.loadItemsService.multiSite) {
        if (rows.filter(x => x.site === '').length === rows.length) missingFields.push('Site');
      }
    }


    if (missingFields.length) {
      this.missingFieldsFromImport = missingFields;
      return true;
    }

    this.missingFieldsFromImport = null;
    return false;
  }

  selectMultiSite(): void {

    this.loadItemsService.multiSite = !this.loadItemsService.multiSite;

    setTimeout(() => {
      this.loadItemsService.updateMultiSiteEmitter.next(this.loadItemsService.multiSite);
    }, 20)

  }

  instructionRowMessage() {
    if (!this.loadItemsService.importFile) { return 'Choose an Excel file to upload'; }
    if (this.loadItemsService.importFile && !this.loadItemsService.chosenImportMask) { return 'Table shows data from Excel file.   Choose which import map to use to import this data, or create a new one.' }
    if (this.loadItemsService.chosenImportMask) { return `Complete the 3 steps below.  When you are happy, if you wish to save the Import map choose 'Save import map'.   Then choose 'Import Vehicle Details' on the right. ` }

  }


  bottomTableMessage(){
    return `3. To choose which column from the Excel file to import type in the column reference e.g. 'A'. To combine multiple columns use a comma e.g. 'A,C'.`
  }

  maybeShowSaveImportMask() {
    const chosenMap: ImportMask = this.loadItemsService.chosenImportMask;

    if (!chosenMap) { return false; }
    if (chosenMap.id && chosenMap.isStandard && this.selections.userRole !== 'SysAdministrator') { return false; }
    if (chosenMap.id && chosenMap.createdBy !== this.selections.usersName && this.selections.userRole !== 'SysAdministrator') { return false; }
    return true;
  }

  toggleIgnoreZeroValueRows() {
    this.ignoreZeroValueRows = !this.ignoreZeroValueRows;
    this.parseImportedReport();
  }

  maybeSaveRecItems() {
    if (this.loadItemsService.multiSite) {
      this.saveRecItemsThisStockCheck(this.reconcilingItemsToImport);
    } else {

      this.modalService.open(this.maybeImportModal, { size: 'sm', keyboard: true, ariaLabelledBy: 'modal-basic-title' }).result.then((result) => {
        //have chosen to 'OK' selections
        this.saveRecItemsAllActiveStockChecks(this.reconcilingItemsToImport, this.stockChecksForPicker.filter(x => x.isSelected).map(x => x.id));
      }, (reason) => {
        //dismissed
        this.generateStockChecksList();
      });

    }
  }

  generateStockChecksList() {
    this.stockChecksForPicker = [];

    this.stockChecksService.stockCheckVMs = this.stockChecksService.stockCheckVMs.sort((a,b) => a.site.localeCompare(b.site));

    this.stockChecksService.stockCheckVMs.forEach(sc => {
      this.stockChecksForPicker.push({
        id: sc.id,
        description: `${ sc.id } - ${ this.cphPipe.transform(sc.date, 'date', 0) } - ${sc.site}`,
        name: sc.site,
        isSelected: sc.id === this.selections.stockCheck.id
      });
    })

    this.stockChecksForPickerCopy = JSON.parse(JSON.stringify(this.stockChecksForPicker));
    this.stockChecksForPickerOriginal = JSON.parse(JSON.stringify(this.stockChecksForPicker));
    this.label = this.selections.stockCheck.site;
    this.selectedStockChecks = this.stockChecksForPicker.filter(x => x.isSelected);
  }

  toggleItem(item: StockChecksForPicker) {
    item.isSelected = !item.isSelected;
    this.stockChecksForPickerCopy.find(x => x.id === item.id).isSelected = item.isSelected;
    this.stockCheckChosenLabel();
  }

  toggleStockChecks() {
    if (this.stockChecksForPickerCopy.filter(x => x.isSelected).length === this.stockChecksForPickerCopy.length) {
      this.stockChecksForPicker.forEach(s => {
        s.isSelected = false;
      })
      this.stockChecksForPickerCopy.forEach(s => {
        s.isSelected = false;
      })
    } else {
      this.stockChecksForPicker.forEach(s => {
        s.isSelected = true;
      })
      this.stockChecksForPickerCopy.forEach(s => {
        s.isSelected = true;
      })
    }

    this.stockCheckChosenLabel();
  }

  clearSelection() {
    this.stockChecksForPicker = this.constants.clone(this.stockChecksForPickerOriginal);
    this.stockCheckChosenLabel();
  }

  stockCheckChosenLabel() {
    let selectedStockChecks: StockChecksForPicker[] = this.stockChecksForPickerCopy.filter(x => x.isSelected);
    this.selectedStockChecks = selectedStockChecks;

    if (selectedStockChecks.length === 0) return this.label = 'No stock checks selected';
    if (selectedStockChecks.length === 1) return this.label = selectedStockChecks[0].description;
    if (selectedStockChecks.length < 4) {
      let stockCheckNames: string = '';
      selectedStockChecks.forEach((sc, i) => {
        if (i > 0) stockCheckNames = stockCheckNames + ',';
        stockCheckNames = stockCheckNames + sc.name;
      })
      return this.label = stockCheckNames;
    }
    
    if (selectedStockChecks.length === this.stockChecksForPickerCopy.length) return this.label = 'All stock checks ';

    // More than 3 sites selected
    return this.label = selectedStockChecks.length + ' stock checks';
  }
  
  chooseNewWorksheet(worksheetName: string) {
    let toastRef: any = this.toastService.loadingToast('Changing worksheet...');
    this.loadItemsService.chosenImportMask = null;
    this.excelColumnsHighlight = null;

    this.chosenWorksheetName = worksheetName;

    if (this.fileIsXl) {
      this.sourceDataArray = this.openExcelFile(this.loadItemsService.importFile)
    } else {
      this.sourceDataArray = this.openCsvFile(this.loadItemsService.importFile);
    }

    this.createExcelVisualisationTableData(toastRef)
  }

  searchList() {
    let stockChecks: StockChecksForPicker[] = JSON.parse(JSON.stringify(this.stockChecksForPickerCopy));
    this.stockChecksForPicker = stockChecks.filter(s => s.description.toLocaleLowerCase().includes(this.searchString.toLocaleLowerCase()));
  }
}
