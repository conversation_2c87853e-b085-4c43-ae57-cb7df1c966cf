﻿using Dapper;
using StockPulse.WebApi.Dapper;
using StockPulse.WebApi.ViewModel;
using StockPulse.WebApi.Service;
using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;

//using Microsoft.Exchange.WebServices.Data;

namespace StockPulse.WebApi.DataAccess
{
    public interface IScanDataAccess
    {
        // Get all scans linked to a stockcheck
        Task<IEnumerable<ViewModel.Scan>> GetScans(int stockcheckId, int userId, int? limitToId = null);
        Task<IEnumerable<ScanWithResolution>> GetScansWithResolution(int stockcheckId, int userId);
        Task<IEnumerable<ScanMatchedToRecItem>> GetScansMatchedToRecItem(int stockcheckId, int? reconcilingItemTypeId, int userId);
        Task<IEnumerable<ScanRegDifference>> GetScanRegDifference(int stockcheckId, int userId);
        //Task<IEnumerable<RepeatUnknown>> GetRepeatUnknown(int stockCheckId, int userId);
        Task UpdateLocation(int newLocationId, int scanId, int userId);
        //Task<ScanFullDetail> GetFullScanDetail(int scanId, int userId);
        Task<ScanFullDetail> GetFullScanDetailAcrossSites(int scanId, int userId);
        Task<IEnumerable<ScanThatIsADuplicate>> GetDuplicateScans(int stockCheckId, int userId);
        Task<IEnumerable<ScanMatchedToOtherSiteStockItem>> GetScanMatchedToOtherSiteStockItem(int stockCheckId, int userId);
        Task<AddScanResult> CreateScan(int userId, ScanUpload scan, IDbConnection db, IDbTransaction tran);
        Task<int> AddMissingResolutionAsync(Resolution resolution, int userId);
        Task UpdateScanItemWithUnknownResolution(int stockCheckId, int userId, int scanId, int id, IDbConnection db, IDbTransaction tran);
        Task DeleteUnknownResolution(int resolutionId, int stockCheckId, int userId);
        Task UpdateUnknownResolutions(Resolution resolution, int userId, IDbConnection db, IDbTransaction tran);
        Task<int> AddUnknownResolutionImage(string fileName, int id, int stockCheckId, int userId, IDbConnection db, IDbTransaction tran);
        Task DeleteUnknownResolutionImage(int id, int imageId, int stockCheckId, int userId, IDbConnection db, IDbTransaction tran);
        Task DeleteScan(int scanId, int userId);
        Task UpdateScanNote(int scanId, string note, int userId);
        Task<IEnumerable<MatchItem>> GetMatchItems(int stockCheckId, int userId);
        Task<IEnumerable<MatchItem>> GetMatchItemsOtherSites(int stockCheckId, int userId);
        Task<int> UpdateScanReg(int scanId, string newReg, int userId, bool isMobileApp);
        Task<int> UpdateScanVin(int scanId, string newVin, int userId, bool isMobileApp);
        Task<ScanSeenBefore> CheckIfRegSeenBefore(string reg, int stockCheckId, int userId);
        Task<IEnumerable<int>> GetUnknownResolutionImageIds(int resolutionId);
        Task<ScanSeenBefore> CheckIfVinSeenBefore(string vin, int stockCheckId, int userId);
        Task<int> GetTotalScansCount(List<int> stockCheckIds, int userId);
    }

    public class ScanDataAccess : IScanDataAccess
    {
        private readonly IDapper dapper;
        private readonly IStockCheckDataAccess stockCheckDataAccess;

        public ScanDataAccess(IDapper dapper, IStockCheckDataAccess stockCheckDataAccess)
        {
            this.dapper = dapper;
            this.stockCheckDataAccess = stockCheckDataAccess;
        }

        public async Task<IEnumerable<ViewModel.Scan>> GetScans(int stockcheckId, int userId, int? limitToId = null)  //ok
        {
            //CoordinateSet stockCheckCoordinates = await stockCheckDataAccess.GetStockCheckCoordinates(stockcheckId);

            var paramList = new DynamicParameters();
            paramList.Add("StockCheckId", stockcheckId);
            paramList.Add("UserId", userId);
            if (limitToId != null) { paramList.Add("LimitToIds", limitToId); }


            var items = await dapper.GetAllAsync<ViewModel.Scan>("dbo.GET_Scans", paramList, System.Data.CommandType.StoredProcedure);
            foreach (var item in items)
            {
                item.DistanceFromDealershipInMiles = ConstantsService.CalculateScanDistance(item.StockCheckLongitude, item.StockCheckLatitude, item.Longitude, item.Latitude);
            }
            return items;
        }



        public async Task<IEnumerable<ScanWithResolution>> GetScansWithResolution(int stockcheckId, int userId)  //ok
        {
            //CoordinateSet stockCheckCoordinates = await stockCheckDataAccess.GetStockCheckCoordinates(stockcheckId);

            var paramList = new DynamicParameters();
            paramList.Add("StockCheckId", stockcheckId);
            paramList.Add("UserId", userId);

            var items = await dapper.GetAllAsync<ScanWithResolution>("dbo.GET_ScansWithResolution", paramList, System.Data.CommandType.StoredProcedure);

            foreach (var item in items)
            {
                item.DistanceFromDealershipInMiles = ConstantsService.CalculateScanDistance(item.StockCheckLongitude, item.StockCheckLatitude, item.Longitude, item.Latitude);
            }
            return items;
        }



        public async Task<IEnumerable<ScanMatchedToRecItem>> GetScansMatchedToRecItem(int stockcheckId, int? reconcilingItemTypeId, int userId) //ok
        {
            //CoordinateSet stockCheckCoordinates = await stockCheckDataAccess.GetStockCheckCoordinates(stockcheckId);

            var paramList = new DynamicParameters();
            paramList.Add("StockCheckId", stockcheckId);
            paramList.Add("UserId", userId);

            var items = await dapper.GetAllAsync<ScanMatchedToRecItem>("dbo.GET_ScansMatchedToRecItem", paramList, System.Data.CommandType.StoredProcedure);
            foreach (var item in items)
            {
                item.DistanceFromDealershipInMiles = ConstantsService.CalculateScanDistance(item.StockCheckLongitude, item.StockCheckLatitude, item.Longitude, item.Latitude);
            }
            return items;

        }

        public async Task<IEnumerable<ScanRegDifference>> GetScanRegDifference(int stockcheckId, int userId)  //ok
        {

            var paramList = new DynamicParameters();
            paramList.Add("StockCheckId", stockcheckId);
            paramList.Add("UserId", userId);

            return await dapper.GetAllAsync<ScanRegDifference>("dbo.GET_ScansWithRegDifference", paramList, System.Data.CommandType.StoredProcedure);

        }

        public async Task<IEnumerable<RepeatUnknown>> GetRepeatUnknown(int stockCheckId, int userId)  //ok
        {
            var paramList = new DynamicParameters();
            paramList.Add("StockCheckId", stockCheckId);
            paramList.Add("UserId", userId);

            return await dapper.GetAllAsync<RepeatUnknown>("dbo.GET_RepeatUnknown", paramList, System.Data.CommandType.StoredProcedure);

        }


        public async Task UpdateLocation(int newLocationId, int scanId, int userId)
        {
            var paramList = new DynamicParameters();
            paramList.Add("ScanId", scanId);
            paramList.Add("NewLocationId", newLocationId);
            paramList.Add("UserId", userId);

            await dapper.ExecuteAsync("dbo.UPDATE_Location", paramList, System.Data.CommandType.StoredProcedure);
        }

        public async Task<int> UpdateScanReg(int scanId, string newReg, int userId, bool isMobileApp)
        {
            var paramList = new DynamicParameters();
            paramList.Add("ScanId", scanId);
            paramList.Add("NewReg", newReg);
            paramList.Add("IsMobileApp", isMobileApp ? 1 : 0);
            paramList.Add("UserId", userId);
            return await dapper.GetAsync<int>("dbo.UPDATE_ScanReg", paramList, System.Data.CommandType.StoredProcedure);
        }

        public async Task<int> UpdateScanVin(int scanId, string newVin, int userId, bool isMobileApp)
        {
            var paramList = new DynamicParameters();
            paramList.Add("ScanId", scanId);
            paramList.Add("NewVin", newVin);
            paramList.Add("IsMobileApp", isMobileApp ? 1 : 0);
            paramList.Add("UserId", userId);
            return await dapper.GetAsync<int>("dbo.UPDATE_ScanVin", paramList, System.Data.CommandType.StoredProcedure);
        }

        public async Task UpdateScanNote(int scanId, string note, int userId)
        {
            var paramList = new DynamicParameters();
            paramList.Add("ScanId", scanId);
            paramList.Add("NewNote", note);
            paramList.Add("UserId", userId);
            await dapper.ExecuteAsync("dbo.UPDATE_ScanNote", paramList, System.Data.CommandType.StoredProcedure);
        }




        public async Task<ScanFullDetail> GetFullScanDetailAcrossSites(int scanId, int userId)
        {
            var paramList = new DynamicParameters();
            paramList.Add("ScanId", scanId);
            paramList.Add("UserId", userId);
            paramList.Add("SkipAuthCheck", 1);
            return await dapper.GetAsync<ScanFullDetail>("dbo.GET_ScanFullDetail", paramList, System.Data.CommandType.StoredProcedure);
        }

        public async Task<IEnumerable<ScanThatIsADuplicate>> GetDuplicateScans(int stockCheckId, int userId)  //ok
        {
            //CoordinateSet stockCheckCoordinates = await stockCheckDataAccess.GetStockCheckCoordinates(stockCheckId);

            var paramList = new DynamicParameters();
            paramList.Add("StockCheckId", stockCheckId);
            paramList.Add("UserId", userId);

            var items = await dapper.GetAllAsync<ScanThatIsADuplicate>("dbo.GET_DuplicateScans", paramList, System.Data.CommandType.StoredProcedure);
            foreach (var item in items)
            {
                item.DistanceFromDealershipInMiles = ConstantsService.CalculateScanDistance(item.StockCheckLongitude, item.StockCheckLatitude, item.Longitude, item.Latitude);
            }
            return items;

        }

        public async Task<IEnumerable<ScanMatchedToOtherSiteStockItem>> GetScanMatchedToOtherSiteStockItem(int stockCheckId, int userId)  //ok
        {
            //CoordinateSet stockCheckCoordinates = await stockCheckDataAccess.GetStockCheckCoordinates(stockCheckId);

            var paramList = new DynamicParameters();
            paramList.Add("StockCheckId", stockCheckId);
            paramList.Add("UserId", userId);

            var items = await dapper.GetAllAsync<ScanMatchedToOtherSiteStockItem>("dbo.GET_ScanMatchedToOtherSiteStockItem", paramList, System.Data.CommandType.StoredProcedure);
            foreach (var item in items)
            {
                item.DistanceFromDealershipInMiles = ConstantsService.CalculateScanDistance(item.StockCheckLongitude, item.StockCheckLatitude, item.Longitude, item.Latitude);
            }
            return items;

        }

        public async Task<AddScanResult> CreateScan(int userId, ScanUpload scan, IDbConnection db, IDbTransaction tran)
        {
            var paramList = new DynamicParameters();
            paramList.Add("StockCheckId", scan.StockCheckId);
            paramList.Add("UserId", userId);
            paramList.Add("LocationId", scan.LocationId);
            paramList.Add("RegConfidence", scan.RegConfidence);
            paramList.Add("VinConfidence", scan.VinConfidence);
            paramList.Add("Longitude", scan.Longitude);
            paramList.Add("Latitude", scan.Latitude);
            paramList.Add("ScanDateTime", scan.ScanDateTime);
            paramList.Add("HasVimImage", scan.HasVinImage);
            paramList.Add("Reg", scan.Reg);
            paramList.Add("Vin", scan.Vin);
            paramList.Add("InterpretedReg", scan.InterpretedReg);
            paramList.Add("InterpretedVin", scan.InterpretedVin);
            paramList.Add("IsRegEditedOnDevice", scan.IsRegEditedOnDevice);
            paramList.Add("IsVinEditedOnDevice", scan.IsVinEditedOnDevice);
            paramList.Add("Comment", scan.Comment);
            paramList.Add("Description", scan.Description);
            
            //paramList.Add("IsEdited", scan.IsEdited);


            try
            {
                return await dapper.InsertAsync<AddScanResult>("dbo.ADD_Scan", paramList, tran, db, CommandType.StoredProcedure);
            }
            catch (Exception ex)
            {
                { }
                throw new Exception(ex.Message, ex);
            }
        }

        public async Task<int> AddMissingResolutionAsync(Resolution resolution, int userId)
        {
            var paramList = new DynamicParameters();
            paramList.Add("StockCheckId", resolution.StockCheckId);
            paramList.Add("UserId", userId);
            paramList.Add("ResolutionTypeId", resolution.ResolutionTypeId);
            paramList.Add("ResolutionDate", DateTime.UtcNow);
            paramList.Add("Notes", resolution.Notes);
            paramList.Add("IsResolved", resolution.IsResolved);

            return await dapper.InsertAsync<int>("dbo.ADD_UnknownResolution", paramList, CommandType.StoredProcedure);
        }

        public async Task UpdateScanItemWithUnknownResolution(int stockCheckId, int userId, int scanId, int id, IDbConnection db, IDbTransaction tran)
        {
            var paramList = new DynamicParameters();
            paramList.Add("StockCheckId", stockCheckId);
            paramList.Add("UserId", userId);
            paramList.Add("UnknownResolutionId", id);
            paramList.Add("ScanId", scanId);

            await dapper.InsertAsync<int>("dbo.UPDATE_ScanWithUnknownResolution", paramList, tran, db, CommandType.StoredProcedure);
        }

        public async Task DeleteUnknownResolution(int resolutionId, int stockCheckId, int userId)
        {
            var paramList = new DynamicParameters();
            paramList.Add("UnknownResolutionId", resolutionId);
            paramList.Add("StockCheckId", stockCheckId);
            paramList.Add("UserId", userId);

            await dapper.ExecuteAsync("dbo.DELETE_UnknownResolution", paramList, CommandType.StoredProcedure);
        }

        public async Task UpdateUnknownResolutions(Resolution resolution, int userId, IDbConnection db, IDbTransaction tran)
        {
            var paramList = new DynamicParameters();
            paramList.Add("StockCheckId", resolution.StockCheckId);
            paramList.Add("UserId", userId);
            paramList.Add("UnknownResolutionId", resolution.ResolutionId);
            paramList.Add("ResolutionTypeId", resolution.ResolutionTypeId);
            paramList.Add("ResolutionDate", DateTime.UtcNow);
            paramList.Add("Notes", resolution.Notes);
            paramList.Add("IsResolved", resolution.IsResolved);

            try
            {

                await dapper.InsertAsync<int>("dbo.UPDATE_UnknownResolution", paramList, tran, db, CommandType.StoredProcedure);
            }
            catch
            {
                { }
            }
        }

        public async Task<int> AddUnknownResolutionImage(string fileName, int id, int stockCheckId, int userId, IDbConnection db, IDbTransaction tran)
        {
            var paramList = new DynamicParameters();
            paramList.Add("FileName", fileName);
            paramList.Add("StockCheckId", stockCheckId);
            paramList.Add("UserId", userId);
            paramList.Add("UnknownResolutionId", id);

            return await dapper.InsertAsync<int>("dbo.ADD_UnknownResolutionImage", paramList, tran, db, CommandType.StoredProcedure);
        }

        public async Task DeleteUnknownResolutionImage(int id, int imageId, int stockCheckId, int userId, IDbConnection db, IDbTransaction tran)
        {
            var paramList = new DynamicParameters();
            paramList.Add("StockCheckId", stockCheckId);
            paramList.Add("UserId", userId);
            paramList.Add("UnknownResolutionId", id);
            paramList.Add("ImageId", imageId);

            await dapper.InsertAsync<int>("dbo.DELETE_UnknownResolutionImage", paramList, tran, db, CommandType.StoredProcedure);
        }

        public async Task<IEnumerable<int>> GetUnknownResolutionImageIds(int resolutionId)
        {
            var paramList = new DynamicParameters();
            paramList.Add("ResolutionId", resolutionId);
            return await dapper.GetAllAsync<int>("dbo.GET_UnknownResolutionImageIds", paramList, System.Data.CommandType.StoredProcedure);
        }

        public async Task DeleteScan(int scanId, int userId)
        {
            var paramList = new DynamicParameters();

            paramList.Add("UserId", userId);
            paramList.Add("ScanId", scanId);

            try
            {
                await dapper.ExecuteAsync("dbo.DELETE_Scan", paramList, System.Data.CommandType.StoredProcedure);
            }
            catch
            {
                throw new Exception("Could not delete");
            }
        }

        public async Task<ScanSeenBefore> CheckIfRegSeenBefore(string reg, int stockCheckId, int userId)
        {
            return await dapper.GetAsync<ScanSeenBefore>("dbo.GET_AlreadyScannedCheck", new DynamicParameters(new { UserId = userId, stockCheckId, reg }), CommandType.StoredProcedure);
        }

        public async Task<ScanSeenBefore> CheckIfVinSeenBefore(string vin, int stockCheckId, int userId)
        {
            return await dapper.GetAsync<ScanSeenBefore>("dbo.GET_AlreadyScannedVinCheck", new DynamicParameters(new { UserId = userId, stockCheckId, vin }), CommandType.StoredProcedure);
        }


        public async Task<IEnumerable<MatchItem>> GetMatchItems(int stockCheckId, int userId)
        {
            return await dapper.GetAllAsync<MatchItem>("dbo.GET_ScanMatchItems", new DynamicParameters(new { stockCheckId, userId }), CommandType.StoredProcedure);
        }
        public async Task<IEnumerable<MatchItem>> GetMatchItemsOtherSites(int stockCheckId, int userId)
        {
            return await dapper.GetAllAsync<MatchItem>("dbo.GET_ScanMatchItemsOtherSites", new DynamicParameters(new { stockCheckId, userId }), CommandType.StoredProcedure);
        }

        public async Task<int> GetTotalScansCount(List<int> stockCheckIds, int userId)
        {
            var paramList = new DynamicParameters();
            paramList.Add("StockCheckIds", string.Join(',', stockCheckIds));
            paramList.Add("UserId", userId);
            return await dapper.GetAsync<int>("dbo.Get_TotalScansCount", paramList, CommandType.StoredProcedure);
        }






    }
}
