{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"stockpulseweb": {"root": "", "sourceRoot": "src", "projectType": "application", "prefix": "app", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "src/tsconfig.app.json", "assets": ["src/favicon-16x16.png", "src/assets", "src/config", "src/.well-known", "src/web.config"], "styles": ["node_modules/animate.css/animate.min.css", "src/styles.scss"], "scripts": [], "vendorChunk": true, "extractLicenses": false, "buildOptimizer": false, "sourceMap": true, "optimization": false, "namedChunks": true, "allowedCommonJsDependencies": ["file-saver", "jspdf", "html2canvas", "raf", "rgbcolor", "dompurify", "core-js/modules/es.array.iterator.js", "core-js/modules/es.array.index-of.js", "core-js/modules/es.array.reduce.js", "core-js/modules/es.array.reverse.js", "core-js/modules/es.promise.js", "core-js/modules/es.regexp.to-string.js", "core-js/modules/es.string.ends-with.js", "core-js/modules/es.string.includes.js", "core-js/modules/es.string.match.js", "core-js/modules/es.string.replace.js", "core-js/modules/es.string.split.js", "core-js/modules/es.string.starts-with.js", "core-js/modules/es.string.trim.js", "core-js/modules/web.dom-collections.iterator.js"]}, "configurations": {"production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "8mb", "maximumError": "11mb"}, {"type": "anyComponentStyle", "maximumWarning": "5kb", "maximumError": "10kb"}]}, "dev": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.dev.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "8mb", "maximumError": "11mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "4kb"}]}}, "defaultConfiguration": ""}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "stockpulseweb:build"}, "configurations": {"production": {"browserTarget": "stockpulseweb:build:production"}, "dev": {"browserTarget": "stockpulseweb:build:production"}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "stockpulseweb:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "src/tsconfig.spec.json", "karmaConfig": "src/karma.conf.js", "styles": ["src/styles.scss", "node_modules/bootstrap/dist/css/bootstrap.min.css"], "scripts": [], "assets": ["src/favicon.ico", "src/assets", "src/config", "src/web.config"]}}}}, "stockpulseweb-e2e": {"root": "e2e/", "projectType": "application", "prefix": "", "architect": {"e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "e2e/protractor.conf.js", "devServerTarget": "stockpulseweb:serve"}, "configurations": {"production": {"devServerTarget": "stockpulseweb:serve:production"}}}}}}, "cli": {"analytics": "3bc76fa7-1053-490a-bc20-9fd42887fe05"}}