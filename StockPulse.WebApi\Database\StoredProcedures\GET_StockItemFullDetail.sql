﻿
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO



CREATE OR ALTER PROCEDURE [dbo].[GET_StockItemFullDetail]
(
    @StockItemId INT = NULL	,
	@UserId INT = NULL,
	@SkipAuthCheck bit = 0
)
AS
BEGIN

SET NOCOUNT ON

IF (dbo.[AuthenticateUser](@UserId, (SELECT StockCheckId FROM dbo.StockItems WHERE Id=@StockItemId)) = 0 AND @SkipAuthCheck = 0) 
BEGIN 
    RETURN
END

DECLARE @DealerGroupId INT;
SET @DealerGroupId = (SELECT DealerGroupId FROM Users WHERE Id = @UserId)

DECLARE @StockCheckId INT = (SELECT StockCheckId FROM StockItems WHERE Id = @StockItemId)

--work out first instance of same stockItems
  SELECT
  si.Reg,si.Vin,
  MIN(si.Id) as Id
  INTO #UniqueIds
  FROM StockItems si
  INNER JOIN StockChecks  SC ON SC.Id=SI.StockCheckId 
  WHERE SC.Id = @StockCheckId
  AND si.IsDuplicate = 0
  GROUP BY si.Reg,si.Vin

  --find out everything about that first instance
  SELECT
  si.Id, si.StockCheckId, si.Reg,si.Vin,si.StockType,si.Comment,si.Reference
  INTO #firstItems
  FROM StockItems si
  INNER JOIN #uniqueIds u on u.id = si.id
  DROP TABLE #uniqueIds



select 
--normal stuff
s.Id as StockItemId,
s.Reg,
s.Vin,
s.Description,
DIS,
GroupDIS,
Branch,
s.StockType,
s.Comment,
s.Reference,
StockValue,
s.Flooring,
si.Description as SiteName,
CASE
	WHEN s.IsDuplicate = 1 THEN 'Duplicate'
	WHEN sc.Id IS NOT NULL AND sc.StockCheckId <> s.StockCheckId THEN 'MatchedToOtherSite'
	WHEN sc.Id IS NOT NULL AND sc.StockCheckId = s.StockCheckId THEN  'MatchedToStockOrScan'
	WHEN s.ReconcilingItemId IS NOT NULL THEN 'MatchedToReport'
	WHEN mr.Id IS NOT NULL AND mr.IsResolved = 1 THEN 'Resolved'
	ELSE 'OutstandingIssue'
END as [State],

--scan stuff
sc.Id as ScanId,
locns.Description as LocationDescription,
usrs.Name as ScannerName,
sc.RegConfidence as RegConfidence,
sc.VinConfidence as VinConfidence,
sc.Longitude as Longitude,
sc.Latitude as Latitude,
sc.ScanDateTime,
sc.Comment as ScanComment,
sc.Reg as ScanReg,
sc.Vin as ScanVin,
sc.Description as ScanDescription, 
sc.HasVinImage,
CASE
	WHEN sc.Id IS NOT NULL AND sc.StockCheckId <> s.StockCheckId THEN 'MatchedToOtherSite'
	WHEN sc.Id IS NOT NULL AND sc.StockCheckId = s.StockCheckId THEN  'MatchedToStockOrScan'
	ELSE NULL
END as ScanState,
othrSite.Description as ScanSiteName,


--reconciling item stuff
recitems.Id as ReconcilingItemId,
rit.Id as ReconcilingItemTypeId,
rit.[Description] as ReconcilingItemTypeDescription,
recitems.[Reg] AS ReconcilingItemReg,
recitems.[Vin] AS ReconcilingItemVin,
recitems.[Description] AS ReconcilingItemDesc,
recitems.[Comment] AS ReconcilingItemComment,
recitems.Reference AS ReconcilingItemRef,

--resolution stuff
mr.Id as ResolutionId,
rt.Id as ResolutionTypeId,
rt.Description as ResolutionTypeDescription,
rt.BackupRequired as ResolutionTypeBackup,
mr.IsResolved,
resUsers.Name as ResolvedBy,
mr.ResolutionDate as ResolutionDate,
mr.Notes as ResolutionNotes,
(select String_Agg(CONCAT(Id,'|',ISNULL([FileName],'FileName')),'::') from MissingResolutionImages where MissingResolutionId= mr.Id) as ResolutionImageIds,

--other site stuff
IIF(sc.Id IS NOT NULL AND sc.StockCheckId <> s.StockCheckId,othrSite.Description,'') as OtherSiteName,


--original item stuff if it's a duplicate
IIF(s.IsDuplicate=1,f.Id,null) as OriginalId,
IIF(s.IsDuplicate=1,f.StockType,null) as OriginalStockType,
IIF(s.IsDuplicate=1,f.Comment,null) as OriginalComment,
IIF(s.IsDuplicate=1,f.Reference,null) as OriginalReference,

s.StockCheckId


FROM stockitems s
LEFT JOIN StockChecks AS SCK ON S.StockCheckId = SCK.Id
LEFT JOIN Sites AS SI ON SCK.SiteId = SI.Id
LEFT JOIN Divisions AS D ON SI.DivisionId = D.Id

--Scans joins
LEFT JOIN Scans sc on sc.Id = s.ScanId 
LEFT JOIN Users usrs on usrs.Id = sc.UserId
LEFT JOIN Locations locns on locns.Id = sc.LocationId
LEFT JOIN StockChecks scanStockCheck on scanStockCheck.Id = sc.StockCheckId
LEFT JOIN Sites othrSite on othrSite.Id = scanStockCheck.SiteId

--resolution joins
LEFT JOIN MissingResolutions mr on mr.Id = s.MissingResolutionId
LEFT JOIN ResolutionTypes rt on rt.Id = mr.ResolutionTypeId
LEFT JOIN Users resUsers on resUsers.Id = mr.UserId

--reconciling item joins
LEFT JOIN ReconcilingItems recitems ON recitems.Id = s.ReconcilingItemId
LEFT JOIN ReconcilingItemTypes rit ON rit.Id = recitems.ReconcilingItemTypeId

--duplicate item joins
LEFT JOIN #firstItems f on (
	(f.Reg <> '' AND f.Reg = s.Reg) OR 
	(f.Vin <> '' AND f.Vin = s.Vin)
	) AND f.StockCheckId = s.StockCheckId

where s.Id =  @StockItemId 
AND D.DealerGroupId = @DealerGroupId

DROP TABLE #firstItems
	

END

GO


