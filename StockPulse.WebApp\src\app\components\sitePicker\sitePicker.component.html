<div class="d-inline-block" ngbDropdown container="body" [autoClose]="false">
    <button id="dropdown" class="btn btn-primary d-flex align-items-center justify-content-between" type="button"
        ngbDropdownToggle [disabled]="disabled" [ngStyle]="{ 'width.px': width }" (click)="generateSitesList()">
        {{ label }}
    </button>
    <div id="sitesDropdownMenu" aria-labelledby="dropdown" ngbDropdownMenu>
        <div id="sitesMenuItems">
            <input type="text" placeholder="Search..." [(ngModel)]="searchString"
                (ngModelChange)="searchList()">
            <ng-container *ngFor="let site of sites">
                <button *ngIf="site.id !== 0" class="btn btn-primary" (click)="toggleItem(site)">
                    <span *ngIf="site.isSelected" class="me-2">
                        <fa-icon [icon]="icon.faCheckSquare" class="checkboxIcon"></fa-icon>
                    </span>
                    <span *ngIf="!site.isSelected" class="me-2">
                        <fa-icon [icon]="icon.faSquare" class="checkboxIcon"></fa-icon>
                    </span>
                    {{ site.description }}
                    <span *ngIf="!site.isActive"> (Inactive)</span>
                </button>
            </ng-container>
        </div>
        <div id="selectAll">
            <button class="btn btn-primary" (click)="toggleSites()">
                All
            </button>
        </div>
        <div id="confirmCancelButtons">
            <button class="btn btn-primary toggleNoCaret" ngbDropdownToggle (click)="selectSites()">OK</button>
            <button class="btn btn-primary toggleNoCaret" ngbDropdownToggle (click)="clearSelection()">Cancel</button>
        </div>
    </div>
</div>