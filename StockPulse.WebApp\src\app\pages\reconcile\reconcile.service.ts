import { EventEmitter, Injectable, Renderer2 } from "@angular/core";
import { GridApi, GridOptions, SelectionChangedEvent } from "ag-grid-community";
import { CphPipe } from "src/app/cph.pipe";
import { BarNew } from "src/app/model/BarNew";
import { ItemToOpen } from "src/app/model/ItemToOpen";
import { WaterfallBarDetailItem } from "src/app/model/WaterfallBarDetailItem";
import { ApiAccessService } from "src/app/services/apiAccess.service";
import { ConstantsService } from "src/app/services/constants.service";
import { ExcelExportService } from "src/app/services/excelExport";
import { GetDataService } from "src/app/services/getData.service";
import { LogoService } from "src/app/services/logo.service";
import { ToastService } from "src/app/services/newToast.service";
import { SelectionsService } from "src/app/services/selections.service";
import { ColumnDefsService } from "./reconcileColumnDefs.service";
import { ReconcileExcelExportService } from "./reconcileExcelExport.service";


@Injectable({
  providedIn: 'root'
})
export class ReconcileService {

  chosenBar: BarNew
  detailItems: WaterfallBarDetailItem[];
  drawBarsFromZero: boolean = true;

  //grid stuff
  filterModel: any;
  public gridApi: GridApi;  
  gridOptions:GridOptions = {
    onGridReady:(params)=>this.onGridReady(params),
    rowData: null,
    onRowDoubleClicked:(params)=>this.loadItem(params), 
    defaultColDef: {
      floatingFilter: true,
    },
    enableCellTextSelection: true,
    headerHeight:50,
    floatingFiltersHeight:30,
    rowSelection: 'multiple',
    getRowHeight: (params) => this.chosenBar?.isStock ? 35 : 55,
    columnTypes: {
      "numberColumn": { cellClass: 'agAlignLeft', filter: 'agNumberColumnFilter' },
      "milesColumn": 
      { 
        cellClass: 'agAlignLeft', 
        filter: 'agNumberColumnFilter',
        cellStyle: { 
          display: 'flex', 
          alignItems: 'center',
          height: '100%'
        }, 
        cellRenderer: (params) => this.distanceCellRenderer(params) 
      },
      "labelColumn": { cellClass: 'agAlignLeft', filter: 'agTextColumnFilter', wrapText: true, autoHeight: true },
      "regVinColumn": { cellClass: 'agAlignCentre', filter: 'agTextColumnFilter', filterParams: { textFormatter: (value) => { 
        const strippedSpaces: string = value === null ? null : value.replace(/ /g, '').toLowerCase();
        return strippedSpaces;
      } }
      },
      "currencyColumn": { cellClass: 'agAlignRight', cellRenderer: (params) => { return this.cphPipe.transform(params.value, 'currency', 2) }, filter: 'agNumberColumnFilter' },
      "longlat": { cellClass: 'agAlignRight', cellRenderer: (params) => { return params.value === 0 ? 'Unknown' : this.cphPipe.transform(params.value, 'number', 4) }, filter: 'agNumberColumnFilter' },
    },
    columnDefs: null,
    onSelectionChanged: (event) => this.onSelectionChangedEvent(event),
    onCellKeyDown: (params) => {
      const key: string = params.event['key'];

      if (key === 'ArrowUp' || key === 'ArrowDown') {
        this.gridApi.deselectAll();
      } else if (key === 'Enter') {
        this.loadItem(params);
      }
    }
  };

  allIdsInList: ItemToOpen [];

  selectedRows: WaterfallBarDetailItem[];

  reloadBar: EventEmitter<void> = new EventEmitter();

  constructor(
    public getDataService: GetDataService,
    public selections: SelectionsService,
    public colDefsService: ColumnDefsService,
    public constants: ConstantsService,
    public toastService:ToastService,
    public cphPipe:CphPipe,
    public apiAccess: ApiAccessService,
    //public excelExport: ReconcileExcelExportService,
    public logo: LogoService, //this IS used
    public excelExportService:ExcelExportService
    //public api: ApiAccessService,
  ) {

  }





  getDetailItemData(bar: BarNew) {

    //if (!this.toastService.toastReference) {this.toastService.loadingToast();}
    this.toastService.loadingToast();

    this.getDataService.getWaterfallBarDetail(this.selections.stockCheck.id, bar.reconciliationState, bar.isScan, bar.reconciliationTypeId)
      .subscribe((res: WaterfallBarDetailItem[]) => {

        //process the incoming items
        res.map((item, index) => {
          if (item.scannedDate) { item.scannedDate = new Date(item.scannedDate) }
          if (item.resolutionDate) { item.resolutionDate = new Date(item.resolutionDate) }
          if (item.originalScannedDate) { item.originalScannedDate = new Date(item.originalScannedDate) }
          if (item.scanId) {
            const strings = this.constants.buildImageStrings(item.scanId, false)
            item.regImageLargeUrl = strings.regImageLargeUrl;
            item.regImageThumbnailUrl = strings.regImageThumbnailUrl;
          }
          item.index = index + 1;
        })

        const listOfThingsToOpen:ItemToOpen[]=[];
        if(bar.isStock){
          res.map(x => x.stockItemId).forEach(id=>{
            listOfThingsToOpen.push({id:id,isStockItem:true})
          })
        }else{
          res.map(x => x.scanId).forEach(id=>{
            listOfThingsToOpen.push({id:id,isStockItem:false})
          })
        }
        this.allIdsInList = listOfThingsToOpen;

        this.detailItems = res;
        this.gridOptions.columnDefs = this.colDefsService.provideColDefs(bar);

        if (this.gridApi) { //graph already exists
          setTimeout(() => {
            this.gridApi.setColumnDefs(this.gridOptions.columnDefs);
            this.gridApi.setRowData(this.detailItems);
            this.gridApi.sizeColumnsToFit();
            this.gridApi.setFilterModel(this.filterModel);
          }, 250)
        } else {
          this.gridOptions.rowData = this.detailItems;
        }

        this.constants.refreshPage.emit(false);
        this.toastService.destroyToast();


      })
  }


  rowHeightCalc(){
    return 55;
    // if(!this.chosenBar){return 55}
    // if(this.chosenBar.isScan){return 55}
    // return 33
  }
  

  loadItem(params:any) {
    let ids: ItemToOpen[] = [];
    
    if (this.chosenBar.isStock) {
      this.gridApi.forEachNodeAfterFilterAndSort(node => {
        ids.push({ id: node.data.stockItemId, isStockItem: true });
      })
    } else {
      this.gridApi.forEachNodeAfterFilterAndSort(node => {
        ids.push({ id: node.data.scanId, isStockItem: false });
      })
    }

    const item:WaterfallBarDetailItem = params.data;
    const id:number = this.chosenBar.isStock ? item.stockItemId : item.scanId;
    this.constants.vehicleModal.loadItemAndOpenModal(!!this.chosenBar.isStock, id, ids, this.chosenBar.description);
  }

  onGridReady(params) {
    this.gridApi = params.api;
    params.columnApi.autoSizeColumns(["index"], false);
    this.gridApi.sizeColumnsToFit();
  }

  distanceCellRenderer(params: any) {
    if (params.value === 'Unknown') return 'Unknown';
    if (params.value === 0) return '0 miles';
    return this.cphPipe.transform(params.value, 'miles', 2);
  }

  onSelectionChangedEvent(event: SelectionChangedEvent) {
    this.selectedRows = event.api.getSelectedNodes().map(x => x.data);
  }
}