@echo off
REM set /P db="Which database do you want to export from? "
REM set /P user="What is your username?"
REM set /P pass="What is your password?"

REM echo [ echo Hello %user%! 
set db= "stockpulseDemo"
set user= "richardprocter"
set pass= "PASSWORD"

 echo( & echo( & echo -------- Exporting -----------------  & echo(& echo(
   echo "divisionsexport" &  bcp divisions out "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\divisions.csv" -S cphi.database.windows.net -d  %db% -U %user% -P %pass% -e "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\error.txt" -q -c -t "|^|"
    echo "locations" &  bcp locations out "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\locations.csv" -S cphi.database.windows.net -d  %db% -U %user% -P %pass% -e "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\error.txt" -q -c -t "|^|"
    echo "resolutiontypesexport" &  bcp resolutiontypesexport out "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\resolutiontypes.csv" -S cphi.database.windows.net -d  %db% -U %user% -P %pass% -e "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\error.txt" -q -c -t "|^|"
    echo "reconcilingitemtypesexport" &  bcp reconcilingitemtypesexport out "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\reconcilingitemtypes.csv" -S cphi.database.windows.net -d  %db% -U %user% -P %pass% -e "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\error.txt" -q -c -t "|^|"
    echo "sitesexport" &  bcp sitesexport out "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\sites.csv" -S cphi.database.windows.net -d  %db% -U %user% -P %pass% -e "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\error.txt" -q -c -t "|^|"
    echo "statusesexport" &  bcp statusesexport out "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\statuses.csv" -S cphi.database.windows.net -d  %db% -U %user% -P %pass% -e "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\error.txt" -q -c -t "|^|"
    echo "stockchecksexport" &  bcp stockchecksexport out "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\stockchecks.csv" -S cphi.database.windows.net -d  %db% -U %user% -P %pass% -e "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\error.txt" -q -c -t "|^|"
    echo "scansexport" &  bcp scansexport out "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\scans.csv" -S cphi.database.windows.net -d  %db% -U %user% -P %pass% -e "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\error.txt" -q -c -t "|^|"
    echo "usersexport" &  bcp usersexport out "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\users.csv" -S cphi.database.windows.net -d  %db% -U %user% -P %pass% -e "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\error.txt" -q -c -t "|^|"
    echo "globalparamsexport" &  bcp globalparamsexport out "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\globalparams.csv" -S cphi.database.windows.net -d  %db% -U %user% -P %pass% -e "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\error.txt" -q -c -t "|^|"
    echo "usersitesexport" &  bcp usersitesexport out "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\usersites.csv" -S cphi.database.windows.net -d  %db% -U %user% -P %pass% -e "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\error.txt" -q -c -t "|^|"
    echo "sitelocationsexport" &  bcp sitelocationsexport out "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\sitelocations.csv" -S cphi.database.windows.net -d  %db% -U %user% -P %pass% -e "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\error.txt" -q -c -t "|^|"
    echo "sourcereportsexport" &  bcp sourcereportsexport out "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\sourcereports.csv" -S cphi.database.windows.net -d  %db% -U %user% -P %pass% -e "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\error.txt" -q -c -t "|^|"
    echo "stockitemsexport" &  bcp stockitemsexport out "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\stockitems.csv" -S cphi.database.windows.net -d  %db% -U %user% -P %pass% -e "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\error.txt" -q -c -t "|^|"
    echo "missingresolutionsexport" &  bcp missingresolutionsexport out "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\missingresolutions.csv" -S cphi.database.windows.net -d  %db% -U %user% -P %pass% -e "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\error.txt" -q -c -t "|^|"
    echo "missingresolutionimagesexport" &  bcp missingresolutionimagesexport out "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\missingresolutionimages.csv" -S cphi.database.windows.net -d  %db% -U %user% -P %pass% -e "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\error.txt" -q -c -t "|^|"
    echo "unknownresolutionsexport" &  bcp unknownresolutionsexport out "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\unknownresolutions.csv" -S cphi.database.windows.net -d  %db% -U %user% -P %pass% -e "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\error.txt" -q -c -t "|^|"
    echo "unknownresolutionimagesexport" &  bcp unknownresolutionimagesexport out "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\unknownresolutionimages.csv" -S cphi.database.windows.net -d  %db% -U %user% -P %pass% -e "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\error.txt" -q -c -t "|^|"
    echo "reconcilingitemsexport" &  bcp reconcilingitemsexport out "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\reconcilingitems.csv" -S cphi.database.windows.net -d  %db% -U %user% -P %pass% -e "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\error.txt" -q -c -t "|^|"
    echo "financiallinesexport" &  bcp financiallinesexport out "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\financiallines.csv" -S cphi.database.windows.net -d  %db% -U %user% -P %pass% -e "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\error.txt" -q -c -t "|^|"
    echo "importmasksexport" &  bcp importmasksexport out "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\importmasks.csv" -S cphi.database.windows.net -d  %db% -U %user% -P %pass% -e "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\error.txt" -q -c -t "|^|"




cmd /k 
@echo on