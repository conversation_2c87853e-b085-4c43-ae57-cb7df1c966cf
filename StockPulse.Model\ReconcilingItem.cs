﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using StockPulse.Model.Import;

namespace StockPulse.Model
{
    public class ReconcilingItem
    {
        [Key]
        public int Id { get; set; }

        public int ReconcilingItemTypeId { get; set; }
        [ForeignKey("ReconcilingItemTypeId")]
        public virtual ReconcilingItemType ReconcilingItemType { get; set; }

        public int StockCheckId { get; set; }
        [ForeignKey("StockCheckId")]
        public virtual StockCheck StockChecks { get; set; }

        public string Reg { get; set; }

        public string Vin { get; set; }

        public string Description { get; set; }

        public string Comment { get; set; }

        public string Reference { get; set; }


        public int SourceReportId { get; set; }
        [ForeignKey("SourceReportId")]
        public virtual SourceReport SourceReports { get; set; }


        public int? FileImportId { get; set; }
        [ForeignKey("FileImportId")]
        public virtual FileImport FileImports { get; set; }
    }

}