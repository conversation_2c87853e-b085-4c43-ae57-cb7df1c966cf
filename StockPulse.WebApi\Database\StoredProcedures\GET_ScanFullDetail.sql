﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


  
CREATE OR ALTER PROCEDURE [dbo].[GET_ScanFullDetail]  
(  
    @ScanId INT = NULL,  
    @UserId INT NULL,
	@SkipAuthCheck bit = 0
)  
AS  
BEGIN  
  
SET NOCOUNT ON  
  
IF (dbo.[AuthenticateUser](@UserId, (select StockCheckId from dbo.Scans where Id = @ScanId)) = 0 AND @SkipAuthCheck = 0)
BEGIN 
    RETURN
END

   
DECLARE @DealerGroupId INT;
SET @DealerGroupId = (SELECT DealerGroupId FROM Users WHERE Id = @UserId)

DECLARE @StockCheckId INT = (SELECT StockCheckId FROM Scans WHERE Id = @ScanId)

--work out first instance of same scans
  SELECT
  sc.Reg,sc.Vin,
  MIN(sc.Id) as Id
  INTO #UniqueIds
  FROM Scans sc
  INNER JOIN StockChecks  Sto ON Sto.Id=Sc.StockCheckId 
  WHERE Sto.Id = @StockCheckId
  AND sc.IsDuplicate = 0
  GROUP BY sc.Reg,sc.Vin

  --find out everything about that first instance
  SELECT
  sca.Id, sca.StockCheckId, sca.Reg,sca.Vin,loc.Description as LocationDescription,us.Name as ScannedBy,sca.ScanDateTime
  INTO #firstItems
  FROM Scans sca
  INNER JOIN #uniqueIds u on u.id = sca.id
  INNER JOIN Locations loc on loc.id = sca.LocationId
  INNER JOIN Users us on us.Id = sca.UserId
  DROP TABLE #uniqueIds



 SELECT   
        scns.Id as ScanId, 
      Locations.[Description] AS LocationDescription,
      Users.Name AS ScannerName  ,
      scns.[RegConfidence]  ,
      scns.[VinConfidence]  ,
      scns.Longitude,
      scns.Latitude,
      scns.ScanDateTime  ,
      scns.[Comment] as ScanComment ,
      scns.[Reg] as ScanReg ,
      scns.[Vin] as ScanVin ,
      scns.[Description] as ScanDescription ,
      scns.[HasVinImage]  ,
      CASE
	    WHEN scns.IsDuplicate = 1 THEN 'Duplicate'
	    WHEN sto.Id IS NOT NULL AND sto.StockCheckId <> scns.StockCheckId THEN 'MatchedToOtherSite'
	    WHEN sto.Id IS NOT NULL AND sto.StockCheckId = scns.StockCheckId THEN  'MatchedToStockOrScan'
	    WHEN scns.ReconcilingItemId IS NOT NULL THEN 'MatchedToReport'
	    WHEN res.Id IS NOT NULL AND res.IsResolved = 1 THEN 'Resolved'
      ELSE 'OutstandingIssue'
      END as ScanState,

      sites.Description as SiteName,

      --matching stock item detail
      sto.Id as StockItemId,
      sti.Reg,
      sti.Vin,
      sti.Description,
      sti.DIS,
      sti.GroupDIS,
      sti.Branch,
      sti.STockType,
      sti.Comment,
      sti.Reference,
      sti.StockValue,
      CASE
	    WHEN sto.Id IS NOT NULL AND sto.StockCheckId <> scns.StockCheckId THEN 'MatchedToOtherSite'
	        WHEN sto.Id IS NOT NULL AND sto.StockCheckId = scns.StockCheckId THEN  'MatchedToStockOrScan'
	    ELSE NULL
        END as State,

      
      --Reconciling item detail
      scns.[ReconcilingItemId]  ,
       rectypes.Id as ReconcilingItemTypeId,  
       recitems.[Reg] AS ReconcilingItemReg,
        recitems.[Vin] AS ReconcilingItemVin,
       rectypes.Description as ReconcilingItemTypeDescription,  
        recitems.Description as ReconcilingItemDesc,  
        recitems.Comment as ReconcilingItemComment,  
        recitems.Reference as ReconcilingItemReference  ,


        --Resolution Detail
      scns.[UnknownResolutionId] as ResolutionId ,
        restypes.Id as ResolutionTypeId,  
        restypes.Description as ResolutionTypeDescription,  
        restypes.BackupRequired as ResolutionTypeBackup, 
        res.IsResolved,  
        usrsRes.Name as ResolvedBy,  
        res.ResolutionDateTime as ResolutionDate,  
        res.Notes as ResolutionNotes,  
        (select String_Agg(CONCAT(Id,'|',ISNULL([FileName],'FileName')),'::') from UnknownResolutionImages where UnknownResolutionId= res.Id) as ResolutionImageIds,  

        --If matched to other site, the other site name
        IIF(sti.Id IS NOT NULL AND siScheck.id <> scns.StockCheckId, siSite.Description, '') as OtherSiteName,

        --original item stuff if it's a duplicate
        IIF(scns.IsDuplicate=1,f.Id,null) as OriginalId,
        IIF(scns.IsDuplicate=1,f.LocationDescription,null) as OriginalLocationDescription,
        IIF(scns.IsDuplicate=1,f.ScannedBy,null) as OriginalScannedBy,
        IIF(scns.IsDuplicate=1,f.ScanDateTime,null) as OriginalScannedDate,

	scns.[IsRegEditedOnDevice],
	scns.[IsRegEditedOnWeb],
	scns.[InterpretedReg],
	scns.[IsVinEditedOnDevice],
	scns.[IsVinEditedOnWeb],
	scns.[InterpretedVin],
    scns.[StockCheckId]
  
  
  FROM [dbo].[Scans] scns  
  INNER JOIN Users ON Users.Id=scns.UserId  
  INNER JOIN Locations ON Locations.Id=scns.LocationId  
  INNER JOIN StockChecks schek on schek.Id = scns.StockCheckId
  INNER JOIN Sites sites on sites.Id = schek.SiteId
  
  LEFT JOIN StockItems sto ON sto.Id = scns.StockItemId and (scns.IsDuplicate = 0)

  --Resolutions joins
  LEFT JOIN UnknownResolutions res on res.Id = scns.UnknownResolutionId   
  LEFT JOIN ResolutionTypes restypes on restypes.Id = res.ResolutionTypeId  
  LEFT JOIN Users usrsRes on usrsRes.Id = res.UserId  
  
  --Reconciling item joins
  LEFT JOIN ReconcilingItems recitems on  scns.ReconcilingItemId =recitems.Id   
  LEFT JOIN ReconcilingItemTypes rectypes on recitems.ReconcilingItemTypeId=rectypes.Id   

  --StockItem joins
  LEFT JOIN StockItems sti on sti.Id = scns.StockItemId
  LEFT JOIN StockChecks siScheck on siScheck.Id = sti.StockCheckId
  LEFT JOIN Sites siSite on siSite.Id = siScheck.SiteId

  --duplicate item joins
  LEFT JOIN #firstItems f on (
	(f.Reg <> '' AND f.Reg = scns.Reg) OR 
	(f.Vin <> '' AND f.Vin = scns.Vin)
	) AND f.StockCheckId = scns.StockCheckId

  WHERE scns.Id = @ScanId 
  AND Users.DealerGroupId = @DealerGroupId
  

   
   
  
END  
  
GO


