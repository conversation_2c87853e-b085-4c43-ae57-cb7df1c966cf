import { Injectable } from "@angular/core";
import { Router } from "@angular/router";
import { MsalService } from "@azure/msal-angular";
import { SelectionsService } from "./selections.service";
import { ConstantsService } from "./constants.service";


@Injectable({
  providedIn: 'root'
})
export class AuthenticationService {



  constructor(
    private router: Router,
    private selections:SelectionsService,
    private msalService: MsalService,
    private constants: ConstantsService
  ) {

  }


  logout() {
    this.selections.userRole = null;
    this.selections.stockCheck = null;
    localStorage.removeItem('refreshToken');
    localStorage.removeItem('accessToken');
    //this.msalService.logout(); //this should allow user to then pick a new ms account
    this.router.navigateByUrl('login')


  }



}