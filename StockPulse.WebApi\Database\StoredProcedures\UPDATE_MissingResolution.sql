﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].[UPDATE_MissingResolution]
(
    @StockCheckId INT,
	@UserId INT,
	@MissingResolutionId INT,
	@ResolutionTypeId INT,
	@ResolutionDate datetime,
	@Notes nvarchar(250),
	@IsResolved bit
)
AS
BEGIN

SET NOCOUNT ON;

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END


UPDATE [MissingResolutions] 
SET ResolutionTypeId = @ResolutionTypeId,
ResolutionDate = @ResolutionDate,
Notes = @Notes,
IsResolved = @IsResolved,
UserId = @UserId
WHERE Id = @MissingResolutionId


EXEC [dbo].[UPDATE_StockCheckLastUpdate] @StockCheckId, @UserId


END
	


GO


