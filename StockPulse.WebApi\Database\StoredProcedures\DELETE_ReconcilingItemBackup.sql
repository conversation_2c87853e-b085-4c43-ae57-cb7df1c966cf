SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].[DELETE_ReconcilingItemBackup]
(
    @StockCheckId INT,
	@UserId INT,
	@ReconcilingItemTypeId INT,
	@FileId INT
)
AS
BEGIN

SET NOCOUNT ON;

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END

DELETE [dbo].[ReconcilingItemBackups]
WHERE Id = @FileId AND ReconcilingItemTypeId = @ReconcilingItemTypeId

EXEC [dbo].[UPDATE_StockCheckLastUpdate] @StockCheckId, @UserId

END

GO
