﻿/*using StockPulse.WebApi.DataAccess;

using System.Collections.Generic;
using System.Threading.Tasks;

namespace StockPulse.WebApi.Service
{
    public interface ISourceReportService
    {
        Task<IEnumerable<SourceReport>> GetSourceReports(int userId);
    }

    public class SourceReportService: ISourceReportService
    {
        //properties of the service
        private readonly ISourceReportDataAccess sourceReportDataAccess;

        //constructor
        public SourceReportService(ISourceReportDataAccess sourceReportDataAccess)
        {
            this.sourceReportDataAccess = sourceReportDataAccess;
        }


        //methods of the service
        public async Task<IEnumerable<SourceReport>> GetSourceReports(int userId)
        {
            return await sourceReportDataAccess.GetSourceReports(userId);
        }
    }
}
*/