.popover.newUserProblem .popover-body {
    background: white !important;
}

.popover.newUserProblem.smallPopover {
    max-width: 40em !important;
}

.popover {
    box-shadow: 5px 5px 10px var(--shadowColour);
    background: rgba(255, 255, 255, 0.6)
}

.popover.solidBackground {
    background: rgba(255, 255, 255, 1)
}

.popover.hintPopover {
    max-width: 40em;
}

.popover.solid {
    background: rgba(255, 255, 255, 1)
}

.popover.smallPopover {
    max-width: 12em;

    .popover-body {
        text-align: center;

        .newLabel {
            text-align: center;
        }
    }
}

.popover {
    background: var(--primaryLight) !important;

    .arrow {
        display: none !important;
    }

    .popover-body {
        color: var(--bodyColour);
    }
}

.popover.account-popover,
.popover.training-slides-popover,
.popover.settings-popover {
    background-color: transparent;

    .popover-body {
        background-color: transparent;
        color: #000000;

        .popover-header {
            border-top-left-radius: 0;
            border-top-right-radius: 0;

            &::before {
                border-bottom: none;
            }
        }

        .popover-body-inner,
        .popover-footer {
            background-color: #FFFFFF;
        }
    }
}

.popover-header {
    background-color: var(--primaryLight) !important;
    color: white !important;
    margin: 0px;
}

.popover-body {
    line-height: 1.8em;
    background: rgba(255, 255, 255, 0)
}

#hint {
    width: 2em;
    color: white;
    background: royalblue;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 2em;
    margin: 0.5em;
}

.account-popover .popover-body,
.settings-popover .popover-body,
.training-slides-popover .popover-body {
    padding: 0;
    background-color: #FFFFFF;
    min-width: 200px;

    .popover-header {
        background-color: var(--primaryLight) !important;
    }

    .popover-body-inner {
        padding: 1em;

        table {
            width: 100%;
            table-layout: fixed;
        }
    }

    .popover-footer {
        padding: 0 1em 1em 1em;

        button {
            width: 100%;
        }
    }
}

.scanImagePopover.popover {
    background: none;
    border: 4px solid var(--primary);
    max-width: unset;

    .arrow {
        display: none;
    }

    .popover-body {
        padding: 0;

        img {
            object-fit: cover;
            height: 100%;
            max-height: 50vh;
        }
    }
}