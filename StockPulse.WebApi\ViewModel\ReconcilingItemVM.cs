﻿using System;

namespace StockPulse.WebApi.ViewModel
{
    public class ReconcilingItemVM
    {
        public int Id { get; set; }
        public string Reg { get; set; }
        public string Vin { get; set; }
        public string Description { get; set; }
        public string Comment { get; set; }
        public string Reference { get; set; }
        public int HasBeenScanned { get; set; }
        public int ScanId { get; set; }
        public string? FileName { get; set; }
        public DateTime? FileDate { get; set; }
        public DateTime? LoadDate { get; set; }
        public string? UserName { get; set; }
    }

    public class ReconcilingItemWithType : ReconcilingItemVM
    {
        public int ReconcilingItemTypeId { get; set; }
        public string ReconcilingItemTypeDescription { get; set; }
    }


    public class ReconcilingItemSave
    {
        public string Reg { get; set; }
        public string Vin { get; set; }
        public string Description { get; set; }
        public string Comment { get; set; }
        public string Reference { get; set; }
        public int ReconcilingItemTypeId { get; set; }
        public int SourceReportId { get; set; }
        public int StockCheckId { get; set; }
        

        // For multisite
        public bool MultiSite { get; set; }

        public string Site { get; set; }
        public int FileImportId {  get; set; }
        public string? FileName { get; set; }
        public DateTime? FileDate { get; set; }
        public DateTime? LoadDate { get; set; }
        public string? UserName { get; set; }
    }


}
