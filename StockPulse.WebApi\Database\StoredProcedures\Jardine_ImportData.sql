﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [import].[Jardine_ImportData]
(  
 @StockChecks varchar(max)  
)  
AS  
BEGIN  

    -- Begin Transaction
    BEGIN TRANSACTION;

    -- Lock the tables involved in the process to prevent concurrent access
    BEGIN TRY

        DECLARE @DealerGroupId INT;
        SET @DealerGroupId = 7 -- Jardine
        --------------------------------------------------------------------------------------------------

        DROP TABLE IF EXISTS #StockCheckIds
        SELECT Value as Id INTO #StockCheckIds from STRING_SPLIT(@StockChecks,',');


          --------------------------------------------------------------------------------------------------
        --Logic same across all imports-- this should be at a common place, - if you change it here , check and change for all----- Lithia UK, Lithia US and Marshalls

		--Check if Stockitems are linked to Scans of other stockchecks and the status of those stockchecks is not complted or approved. 
		------------------------------------------------------------------------------------------
		select DISTINCT s.Description, sc.Id, sc.StatusId, sn.StockItemId
		INTO #StockItemsLinkedToScansInOtherStockChecks
		from Scans sn
		INNER JOIN StockChecks sc ON sc.Id = sn.StockCheckId
		INNER JOIN Sites s ON s.Id = sc.SiteId
		INNER JOIN Divisions di ON di.Id = s.DivisionId
		INNER JOIN DealerGroup dg ON dg.Id = di.DealerGroupId
		INNER JOIN #StockCheckIds sci on sci.Id != sc.Id -- NOT IN 
		WHERE dg.Id = @DealerGroupId 
			and sn.StockItemid in (
				select si.Id
				FROM dbo.StockItems si
				INNER JOIN StockChecks sc ON sc.Id = si.StockCheckId
				INNER JOIN Sites s ON s.Id = sc.SiteId
				INNER JOIN Divisions di ON di.Id = s.DivisionId
				INNER JOIN DealerGroup dg ON dg.Id = di.DealerGroupId
				INNER JOIN #StockCheckIds sci on sci.Id = sc.Id
				WHERE dg.Id = @DealerGroupId 
			)

		
		IF EXISTS (SELECT 1 FROM #StockItemsLinkedToScansInOtherStockChecks where StatusId in (4,5))
		BEGIN
		
			--check if the stockitems which are mapped to other sites is available in the imports
			select siosc.StockItemId, si.Id, si.Reg as OrgReg, si.Vin as OrgVin, isi.Reg as NewReg, isi.Vin as NewVin
			INTO #StockItemsInExistingAndImportTable
				FROM #StockItemsLinkedToScansInOtherStockChecks siosc 
				INNER JOIN dbo.StockItems si on si.Id = siosc.StockItemId
				INNER JOIN StockChecks sc ON sc.Id = si.StockCheckId
				INNER JOIN Sites s ON s.Id = sc.SiteId
				INNER JOIN Divisions di ON di.Id = s.DivisionId
				INNER JOIN DealerGroup dg ON dg.Id = di.DealerGroupId
				INNER JOIN #StockCheckIds sci on sci.Id = sc.Id
				LEFT JOIN input.StockItems isi on isi.SiteId = s.Id and ISNULL(isi.Reg,'-1') = ISNULL(si.Reg,'-1') and ISNULL(isi.Vin,'-1') = ISNULL(si.Vin,'-1')
				WHERE dg.Id = @DealerGroupId and siosc.StatusId in (4,5)
			
			IF EXISTS (select * from #StockItemsInExistingAndImportTable where ISNULL(OrgReg,'-1') != ISNULL(NewReg,'-1') OR ISNULL(OrgVin,'-1') != ISNULL(NewVin,'-1'))
			BEGIN 

				DECLARE @siteNames nvarchar(max);
				DECLARE @errorMessage nvarchar(max);
				SELECT @siteNames = STRING_AGG(ConcatValue, ',') FROM ( SELECT DISTINCT CONCAT(Id, '(', Description, ')') AS ConcatValue FROM #StockItemsLinkedToScansInOtherStockChecks) AS DistinctValues;
				SELECT @errorMessage = CONCAT('Re-importing data for this Stock Check would affect the reconciliation of the following Completed/Approved Stock Checks: ',@siteNames, '. Please move these Stock Checks back to ''Scans Completed'' to re-import data for this Stock Check.');
				;THROW 51000, @errorMessage ,1
			END 

		END

		 
		--Removing links of Stockitems to Scans of other stockchecks 
		UPDATE sca
		SET sca.StockItemId = NULL
		FROM Scans sca
		INNER JOIN StockChecks sc ON sc.Id = sca.StockCheckId
		INNER JOIN Sites s ON s.Id = sc.SiteId
		INNER JOIN Divisions di ON di.Id = s.DivisionId
		INNER JOIN DealerGroup dg ON dg.Id = di.DealerGroupId
		INNER JOIN #StockItemsLinkedToScansInOtherStockChecks sci on sci.Id = sc.Id
		WHERE dg.Id = @DealerGroupId 
		------------------------------------------------------------------------------------------



        -- STOCKCHECK ITEMS
        UPDATE sca
        SET sca.StockItemId = NULL
        FROM Scans sca
        INNER JOIN StockChecks sc ON sc.Id = sca.StockCheckId
        INNER JOIN Sites s ON s.Id = sc.SiteId
        INNER JOIN Divisions di ON di.Id = s.DivisionId
        INNER JOIN DealerGroup dg ON dg.Id = di.DealerGroupId
        INNER JOIN #StockCheckIds sci on sci.Id = sc.Id
        WHERE dg.Id = @DealerGroupId 

        DELETE si
        FROM dbo.StockItems si WITH (TABLOCKX) -- Locking table
        INNER JOIN StockChecks sc ON sc.Id = si.StockCheckId
        INNER JOIN Sites s ON s.Id = sc.SiteId
        INNER JOIN Divisions di ON di.Id = s.DivisionId
        INNER JOIN DealerGroup dg ON dg.Id = di.DealerGroupId
        INNER JOIN #StockCheckIds sci on sci.Id = sc.Id
        WHERE dg.Id = @DealerGroupId
        
        -- RECONCILING ITEMS
        -- Make FKs null so we can do the delete
        UPDATE sca
        SET sca.ReconcilingItemId = NULL
        FROM Scans sca
        INNER JOIN ReconcilingItems r ON r.Id = sca.ReconcilingItemId
        INNER JOIN StockChecks sc ON sc.Id = r.StockCheckId
        INNER JOIN Sites s ON s.Id = sc.SiteId
        INNER JOIN Divisions di ON di.Id = s.DivisionId
        INNER JOIN DealerGroup dg ON dg.Id = di.DealerGroupId
        INNER JOIN #StockCheckIds sci on sci.Id = sc.Id
        WHERE dg.Id = @DealerGroupId 
        AND r.ReconcilingItemTypeId IN (71); 


        -- Delete existing items for StockChecks
        DELETE r
        FROM dbo.ReconcilingItems r WITH (TABLOCKX) -- Locking table
        INNER JOIN StockChecks sc ON sc.Id = r.StockCheckId
        INNER JOIN Sites s ON s.Id = sc.SiteId
        INNER JOIN Divisions di ON di.Id = s.DivisionId
        INNER JOIN DealerGroup dg ON dg.Id = di.DealerGroupId
        INNER JOIN #StockCheckIds sci on sci.Id = sc.Id
        WHERE dg.Id = @DealerGroupId
        AND ReconcilingItemTypeId IN (71);

        -- FL
        DELETE fl
        FROM dbo.FinancialLines fl WITH (TABLOCKX) -- Locking table
        INNER JOIN #StockCheckIds sci on sci.Id = fl.StockCheckId

        -- Declare variables to hold data from the cursor
        DECLARE @StockCheckId INT;
        DECLARE @SiteId INT;

        -- Declare the cursor
        DECLARE stockCheck_cursor CURSOR FOR
            SELECT Id FROM #StockCheckIds;

        -- Open the cursor
        OPEN stockCheck_cursor;

        -- Fetch the first row from the cursor
        FETCH NEXT FROM stockCheck_cursor INTO @StockCheckId;

        -- Loop until there are no more rows
        WHILE @@FETCH_STATUS = 0
        BEGIN

            -- Processing logic for each @StockCheckId
            SET @SiteId = (SELECT SiteId FROM StockChecks WHERE Id = @StockCheckId)

            -- FinancialLines
            INSERT INTO dbo.FinancialLines(Code, AccountDescription, Notes, IsExplanation, Balance, StockCheckId, FileImportId)
            SELECT Code, AccountDescription, NULL, 0, Balance, @StockCheckId, FileImportId
            FROM input.FinancialLines
            WHERE input.FinancialLines.SiteId = @SiteId
            AND input.FinancialLines.DealerGroupId = @DealerGroupId;

            -- Stock
            INSERT INTO dbo.StockItems(ScanId, ReconcilingItemId, MissingResolutionId, StockCheckId, SourceReportId, Reg, Vin, Description, DIS, GroupDIS, Branch, Comment, StockType, Reference, StockValue, IsDuplicate, IsAgencyStock, FileImportId)
            SELECT NULL, NULL, NULL, @StockCheckId, SourceReportId, Reg, SUBSTRING(Vin, LEN(Vin) - 7, 8), Description, DIS, GroupDIS, Branch, Comment, StockType, Reference, StockValue, 0, 0, FileImportId
            FROM input.StockItems
            WHERE input.StockItems.SiteId = @SiteId
            AND input.StockItems.DealerGroupId = @DealerGroupId;
            
            -- Wips
            INSERT INTO dbo.ReconcilingItems(ReconcilingItemTypeId, Reg, Vin, Description, Comment, Reference, SourceReportId, StockCheckId, FileImportId)
            SELECT ReconcilingItemTypeId, Reg, SUBSTRING(Vin, LEN(Vin) - 7, 8), Description, Comment, Reference, SourceReportId, @StockCheckId, FileImportId
            FROM input.ReconcilingItems
            WHERE input.ReconcilingItems.SiteId = @SiteId
            AND input.ReconcilingItems.ReconcilingItemTypeId = 71
            AND input.ReconcilingItems.DealerGroupId = @DealerGroupId;

            -- Fetch the next row from the cursor
            FETCH NEXT FROM stockCheck_cursor INTO @StockCheckId;

        END

        -- Close and deallocate the cursor
        CLOSE stockCheck_cursor;
        DEALLOCATE stockCheck_cursor;

        SELECT DISTINCT Id FROM #StockItemsLinkedToScansInOtherStockChecks -- StockcheckIds to be reconciled again.

        -- Commit the transaction
        COMMIT TRANSACTION;
    
    END TRY
    BEGIN CATCH
        -- Rollback in case of an error
        ROLLBACK TRANSACTION;
        THROW;
    END CATCH;

 END
GO
