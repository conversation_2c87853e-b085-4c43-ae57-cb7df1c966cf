﻿/****** Object:  UserDefinedFunction [dbo].[UserCanMoveStatusTo]    Script Date: 01/05/2021 12:19:29 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

ALTER FUNCTION [dbo].[UserCanMoveStatusTo] (@NewStatusId INT, @UserId INT,@StockCheckId INT)
RETURNS BIT
	BEGIN
		
		 --Business Logic:  Return true if:
		 --1. User is approver
		 --2. User is choosing to move to status 4 and it's currently below 4
		 Declare @UserRoleName varchar(20)
		 set @UserRoleName = (select  R.Name from users usrs
		inner join AspNetUsers U on U.LinkedPersonId = usrs.Id
		Inner join AspNetUserRoles UR on UR.UserId = U.Id
		inner join AspNetRoles R on R.Id = UR.RoleId
		WHERE usrs.id = @UserID
		)
		
		
		DECLARE @Res INT;
		Set @Res = 0;
		
		IF @UserRoleName='Approver' OR @UserRoleName='SysAdministrator' OR @UserRoleName='GeneralManager' OR
		 (
		 (SELECT StatusId from StockChecks sc WHERE sc.Id  = @StockCheckId) < 4 
			AND 
			@NewStatusId <5
		 )
			
			BEGIN
				 Set @Res = 1
		   END

		   RETURN @Res;
	END;
