import { Injectable } from "@angular/core";
import { HttpClient } from '@angular/common/http';

 
@Injectable()
export class AppConfig {
  constructor(private httpClient: HttpClient){

  }

  apiUrl: string;
  apiUrlUS: string;
  apiUrlUK: string;

  ensureInit(): Promise<any> {
    return new Promise((r, e) => {
      
      this.httpClient.get("./config/config.json")
        .subscribe(
        (content: AppConfig) => {
          Object.assign(this, content);
          r(this);
        },
        reason => e(reason));
    });
  }
}