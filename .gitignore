.vs/
StockPulseP/angular/node_modules
packages/
StockPulseP/bin/
StockPulseP/obj/
dev
live
obj/
StockPulseP/obj/*
packages/
/PlateRecogniserService/Configs
/StockPulseP/angular/dist

StockPulse.WebApi/bin/*
StockPulse.WebApi/obj/*

StockPulse.Loader/bin/*
StockPulse.Loader/obj/*

StockPulse.WebApi/bin/*
/StockPulse.WebApp/dist
/StockPulse.WebApp/node_modules
/StockPulse.WebApp/.angular
StockPulseP
/StockPulse.WebApp/dist
/TestProject1/bin/*
StockPulse.Repository/bin/*
StockPulse.Model/bin/*
/StockPulse.Model/bin/Debug/net8.0/StockPulse.Model.dll
/StockPulse.Model/bin/Debug/net8.0/StockPulse.Model.pdb
/StockPulse.Repository/bin/Debug/net8.0/StockPulse.Model.dll
/StockPulse.Repository/bin/Debug/net8.0/StockPulse.Model.pdb
/StockPulse.Repository/bin/Debug/net8.0/StockPulse.Repository.dll
/StockPulse.Repository/bin/Debug/net8.0/StockPulse.Repository.pdb
