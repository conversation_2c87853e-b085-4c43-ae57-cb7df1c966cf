<nav class="page-specific-navbar">
    <div class="page-title">
        Print Labels
    </div>

    <div class="d-inline-block" #dropdown="ngbDropdown" container="body" ngbDropdown>
        <button id="dropdownTrigger" class="btn btn-primary" ngbDropdownToggle>
            {{ service.selectedSite ? service.selectedSite.description : 'Select a site' }}
        </button>
        <div ngbDropdownMenu aria-labelledby="dropdownTrigger">
            <input type="text" placeholder="Search..." [(ngModel)]="service.searchString"
                (ngModelChange)="searchList()">
            <button *ngFor="let site of service.sitesCopy" [ngClass]="{ 'active': service.selectedSite?.id === site.id }"
                ngbDropdownItem (click)="service.loadStockForSite(site)">
                {{ site.description }}
            </button>
        </div>
    </div>

    <button class="btn btn-primary mx-2" (click)="openManualPrintModal()">
        Manual Print
    </button>

    <div class="uploadFileWrapper">
        <ng-container *ngIf="!file">
          <input class="chooseFileInput" id="file" type="file" (change)="onFileChange($event)" />
          <label for="file" class="btn"><fa-icon [icon]="icon.faUpload"></fa-icon>Print From File</label>
        </ng-container>
        <ng-container *ngIf="file">
          <span class="fileName">File: {{ fileName | slice:0:50 }}{{ fileName.length > 50 ? '...' : '' }}</span>
          <button class="btn btn-primary" (click)="file = null">Choose Different File</button>
        </ng-container>
      </div>

    <ng-container *ngIf="service.selectedRows || file">
        <div class="mx-2">
            Copies
            <input type="number" min="1" max="3" [(ngModel)]="service.copies" (ngModelChange)="validateCopies()" />
        </div>

        <button *ngIf="!file" class="btn btn-primary" [disabled]="service.copies < 1" (click)="service.generateLabels(true)">
            Print {{ service.selectedRows.length * service.copies }} labels
        </button>

        <button *ngIf="file" class="btn btn-primary" [disabled]="service.copies < 1" (click)="service.generateLabels()">
            Print {{ service.labelsFromModal?.length * service.copies }} labels
        </button>
    </ng-container>
</nav>

<div class="content-new">
    <labelPrinterTable *ngIf="service.rowData" class="h-100"></labelPrinterTable>
</div>

<div *ngIf="service.showIframe" id="iframeContainer">
    <button (click)="closeIframe()">
        <fa-icon [icon]="icon.faTimesCircle"></fa-icon>
    </button>
</div>