﻿
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].[UPDATE_UnknownResolution]
(
    @StockCheckId INT,
	@UserId INT,
	@UnknownResolutionId INT,
	@ResolutionTypeId INT,
	@ResolutionDate datetime,
	@Notes nvarchar(250),
	@IsResolved bit
)
AS
BEGIN

SET NOCOUNT ON;

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
	RETURN
END


UPDATE [UnknownResolutions] 
SET ResolutionTypeId = @ResolutionTypeId,
ResolutionDateTime = @ResolutionDate,
Notes = @Notes,
IsResolved = @IsResolved,
UserId = @UserId
WHERE Id = @UnknownResolutionId

EXEC [dbo].[UPDATE_StockCheckLastUpdate] @StockCheckId, @UserId



END
	
  
	


GO


