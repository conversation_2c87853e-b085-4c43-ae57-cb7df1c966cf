﻿using PlateRecognizer;
using RestSharp;
using System.Linq;
using System.Net;
using System.Threading;
using System.Threading.Tasks;
using static StockPulse.WebApi.Service.RecogniseService;

namespace StockPulse.WebApi.Service
{
    public interface IPlateService
    {
        Task<PlateReaderResult> ProcessScanAsync(byte[] bytes, bool useApiA, string plateType, CancellationToken cancellationToken);
    }
    public class PlateService: IPlateService
    {

        public async Task<PlateReaderResult> ProcessScanAsync(byte[] bytes, bool useApiA, string plateType, CancellationToken cancellationToken)
        {
            var client = new RestClient("https://api.platerecognizer.com");
            var request = new RestRequest("v1/plate-reader");
            
            Parameter region = Parameter.CreateParameter("region", plateType.ToLower(), ParameterType.HttpHeader);
            request.Parameters.AddParameter(region);

            //if picture we are processing had an id that is even we use the first key else the second, as each is limited to 8 lookups/s
            string token = useApiA ? "c095fb5409ebbfb9b3c16950eba640cf7fb7e206" : "bad6529dbd67930c828be036ebccf7ca10048cf1";
            request.AddHeader("Authorization", $"Token {token}");

            ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;

            request.AddFile("upload", bytes, "Foo.jpeg", "image/jpeg");
            
            var response = await client.ExecutePostAsync(request, cancellationToken);

            if (response.StatusCode == HttpStatusCode.TooManyRequests)
            {
                await Task.Delay(300);
                return await ProcessScanAsync(bytes, useApiA, plateType, cancellationToken);
            }

            
            PlateReaderResult plateReaderResult = JsonSerializer<PlateReaderResult>.DeSerialize(response.Content);
            plateReaderResult.DidSucceeed = response.IsSuccessful;
            //now add quality to the result plates
            if (plateReaderResult.Results.Count > 0)
            {

            foreach (var scoreAndPlate in plateReaderResult.Results.First().ScoresAndPlates)
            {
                    scoreAndPlate.Plate = scoreAndPlate.parsePlate(scoreAndPlate.Plate);
                    scoreAndPlate.Score = scoreAndPlate.provideAdjustedScore(scoreAndPlate.Score,scoreAndPlate.Plate);
            }
            }
            return plateReaderResult;
        }
    }



}
