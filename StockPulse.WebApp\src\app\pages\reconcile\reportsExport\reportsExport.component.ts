import { Component, OnInit } from '@angular/core';
import { CphPipe } from 'src/app/cph.pipe';
import { ScanWithResolution } from 'src/app/model/ScanWithResolution';
import { SheetToExtract } from 'src/app/model/SheetToExtract';
import { StockItem } from 'src/app/model/StockItem';
import { StockItemWithResolution } from 'src/app/model/StockItemWithResolution';
import { ExcelExportService } from 'src/app/services/excelExport';
import { GetDataService } from 'src/app/services/getData.service';
import { LogoService } from 'src/app/services/logo.service';
import { SelectionsService } from 'src/app/services/selections.service';
import { ConstantsService } from 'src/app/services/constants.service';
import { ImageStrings } from 'src/app/model/ImageStrings';
import { ToastService } from 'src/app/services/newToast.service';
import { Subscription } from 'rxjs';

interface File {
  label: string;
  stockItems?: StockItemWithResolution[];
  scanItems?: ScanWithResolution[];
}

@Component({
  selector: 'reportsExport',
  templateUrl: './reportsExport.component.html',
  styleUrls: ['./reportsExport.component.scss']
})
export class ReportsExportComponent implements OnInit {
  missings: StockItemWithResolution[];
  unknowns: ScanWithResolution[];
  unknownUnresolvedLength: number;
  missingUnresolvedLength: number;
  stockTypeFiles: File[];
  locationFiles: File[];
  pageRefreshSubscription: Subscription;

  constructor(
    public logoService: LogoService,
    public excelService: ExcelExportService,
    public getDataService: GetDataService,
    public selectionsService: SelectionsService,
    public pipeService: CphPipe,
    public constantsService: ConstantsService,
    private toastService:ToastService,
  ) {

  }

  ngOnInit(): void {
    this.getResolvedMissings();
    this.getResolvedUnknowns();
    
    this.pageRefreshSubscription = this.constantsService.refreshPage.subscribe(res => {
      this.getResolvedMissings();
      this.getResolvedUnknowns();
    })
  }

  ngOnDestroy() {
    if (this.pageRefreshSubscription) { this.pageRefreshSubscription.unsubscribe(); }
  }

  getResolvedMissings() {
    this.getDataService.getResolvedMissings(this.selectionsService.stockCheck.id).subscribe((res: StockItemWithResolution[]) => {
      this.missings = res;
      this.missingUnresolvedLength = this.missings.filter(u => u.isResolved === false).length;
      this.formatMissings();
    })
  }

  formatMissings() {
    let stockTypeFiles: File[] = [];
    let stockTypes: string[] = [];

    this.missings.forEach(missing => {
      if (missing.stockType === ''){ missing.stockType = 'Unspecified stock type';}
      if (!stockTypes.find(x => x == missing.stockType)) {stockTypes.push(missing.stockType);}
    })

    stockTypes.forEach(stockType => {
      stockTypeFiles.push({
        label: stockType,
        stockItems: this.missings.filter(x => x.stockType === stockType)
      })
    })

    this.stockTypeFiles = stockTypeFiles.sort((a, b) => a.label.localeCompare(b.label));;
  }

  getResolvedUnknowns() {
    this.getDataService.getResolvedUnknowns(this.selectionsService.stockCheck.id).subscribe((res: ScanWithResolution[]) => {
      this.unknowns = res;
      this.unknownUnresolvedLength = this.unknowns.filter(u => u.isResolved === false).length;
      this.formatUnknowns();
    })
  }

  getUnresolvedItemsCount(items: ScanWithResolution[]){
    return items.filter(i => i.isResolved === false).length;
  }

  formatUnknowns() {
    let locationFiles: File[] = [];
    let locations: string[] = [];

    this.unknowns.forEach(vehicle => {
      locations.push(vehicle.locationDescription);
    })

    let uniqueLocations: string[] = [...new Set(locations)];

    uniqueLocations.forEach(location => {
      locationFiles.push({
        label: location,
        scanItems: this.unknowns.filter(x => x.locationDescription === location)
      })
    })

    this.locationFiles = locationFiles.sort((a, b) => a.label.localeCompare(b.label));;
  }

  downloadFile(file: File, isStock: boolean) {
    return isStock ? this.createMissingsExcel(file.stockItems.filter(s => s.isResolved === false), file.label) : this.createUnknownsExcel(file.scanItems.filter(s => s.isResolved === false), file.label);
  }

  downloadAllFiles(isStock: boolean) {
    var items: any[] = [];
    if (isStock) {
      this.stockTypeFiles.forEach(file => {
        items = items.concat(file.stockItems.filter(s => s.isResolved === false));
      })

      this.createMissingsExcel(items, 'All Stock Types - Missing Unresolved')
    } else {
      this.locationFiles.forEach(file => {
        items = items.concat(file.scanItems.filter(s => s.isResolved === false));
      })

      this.createUnknownsExcel(items, 'All Locations - Unknown Unresolved')
    }
  }

  createMissingsExcel(missings: StockItemWithResolution[], fileName: string) {
    
    let sheets: SheetToExtract[] = [];
    let rows = [];
    let headers = [];
    if (this.constantsService.currencySymbol == '$') {
      headers = ['Id', 'Stock Number', 'Description', 'Stock Value', 'Flooring Balance', 'Days in Stock', 'Branch', 'Stock Type', 'Notes', 'Reg', 'Chassis', 'Reason Type', 'Explanation', 'Is Resolved?', 'Backup'];
    }
    else{ 
      headers = ['Id', 'Stock Number', 'Description', 'Stock Value', 'Days in Stock', 'Branch', 'Stock Type', 'Notes', 'Reg', 'Chassis', 'Reason Type', 'Explanation', 'Is Resolved?', 'Backup'];
    }

    missings.forEach(missing => {
      let rowDetail = [];
      rowDetail.push(missing.stockItemId);
      rowDetail.push(missing.reference);
      rowDetail.push(missing.description);
      rowDetail.push(missing.stockValue);

      if (this.constantsService.currencySymbol == '$'){
        rowDetail.push(missing.flooring);
      }

      rowDetail.push(missing.dis);
      rowDetail.push(missing.branch);
      rowDetail.push(missing.stockType);
      rowDetail.push(missing.comment);
      rowDetail.push(missing.reg);
      rowDetail.push(missing.vin);
      rowDetail.push(missing.resolutionTypeDescription);
      rowDetail.push(missing.resolutionNotes);
      rowDetail.push(missing.isResolved);
      rowDetail.push(missing.resolutionImageIds ? 'Yes' : 'No');
      rows.push(rowDetail);
    })

    let sheet: SheetToExtract = {
      headers: [headers],
      rows: rows,
      footers: [],
      tableName: fileName,
      columnWidths: (this.constantsService.currencySymbol == '$') ? [10, 25, 75, 15, 15, 15, 25, 12, 39, 16, 16, 40, 55, 12, 15]: [10, 25, 75, 15, 15, 25, 12, 39, 16, 16, 40, 55, 12, 15],
      columnTypes: (this.constantsService.currencySymbol == '$') ? ['label', 'label', 'label', 'label', 'label', 'label', 'label', 'label', 'label', 'label', 'label', 'label', 'label', 'label', 'label']: ['label', 'label', 'label', 'label', 'label', 'label', 'label', 'label', 'label', 'label', 'label', 'label', 'label', 'label'],
      vehicleImages: [],
      initialColumnsToHighlight: 1
    }

    sheets.push(sheet);

    this.excelService.exportSheetsToExcel(sheets, 30, false, false);
  }

  async createUnknownsExcel(missings: ScanWithResolution[], fileName: string) {

    const toastRef = this.toastService.loadingToast('Generating Excel file...');
    let sheets: SheetToExtract[] = [];
    let rows = [];
    let headers = ['Scan Id', 'Scan Date', 'Scan Time', 'Reg', 'Chassis', 'Scanned by', 'Location', 'Description', 'Image', 'Reason Type', 'Explanation', 'Is Resolved?'];

    for (let i = 0; i < missings.length; i++) {
      let scan = missings[i];

      let rowDetail = [];
      rowDetail.push(scan.scanId);
      rowDetail.push(this.pipeService.transform(scan.scanDateTime, 'dateMed', 0));
      rowDetail.push(this.pipeService.transform(scan.scanDateTime, 'time', 0));
      rowDetail.push(scan.scanReg);
      rowDetail.push(scan.scanVin);
      rowDetail.push(scan.scannerName);
      rowDetail.push(scan.locationDescription);
      rowDetail.push(scan.scanDescription);
      rowDetail.push('');
      rowDetail.push(scan.resolutionTypeDescription);
      rowDetail.push(scan.resolutionNotes);
      rowDetail.push(scan.isResolved);
      rows.push(rowDetail);
    }

    let sheet: SheetToExtract = {
      headers: [headers],
      rows: rows,
      footers: [],
      tableName: fileName,
      columnWidths: [10, 15, 15, 15, 20, 30, 20, 40, 25, 30, 40, 15],
      columnTypes: ['label', 'label', 'label', 'label', 'label', 'label', 'label', 'label', 'label', 'label', 'label', 'label'],
      vehicleImages: await this.getScanImages(missings),
      initialColumnsToHighlight: 1
    }

    sheets.push(sheet);

    toastRef.close();
    this.excelService.exportSheetsToExcel(sheets, 140, true, true);
  }

  async getScanImages(missings: ScanWithResolution[]) {
    let vehicleImages: string[] = [];

    for (let scan of missings) {
      let imageStrings: ImageStrings = this.constantsService.buildImageStrings(scan.scanId, false);
  
      if (imageStrings.regImageThumbnailUrl) {
        let base64: string = await urlToBase64(imageStrings.regImageThumbnailUrl);
        vehicleImages.push(base64);
      }
    }

    return vehicleImages;
  
    async function urlToBase64(url: string): Promise<string> {
      const data: Response = await fetch(url);
      const blob: Blob = await data.blob();
      const reader: FileReader = new FileReader();
      return new Promise<string>((resolve, reject) => {
        reader.onload = () => {
          resolve(reader.result as string);
        };
        reader.onerror = reject;
        reader.readAsDataURL(blob);
      });
    }
  }
}
