﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using StockPulse.Repository.Database;

#nullable disable

namespace StockPulse.Repository.Migrations
{
    [DbContext(typeof(StockpulseContext))]
    partial class StockpulseContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasDefaultSchema("dbo")
                .HasAnnotation("ProductVersion", "8.0.6")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("StockPulse.Model.DealerGroup", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Description")
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<int?>("NominatedUserId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("NominatedUserId")
                        .IsUnique()
                        .HasFilter("[NominatedUserId] IS NOT NULL");

                    b.ToTable("DealerGroup", "dbo");
                });

            modelBuilder.Entity("StockPulse.Model.Division", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("DealerGroupId")
                        .HasColumnType("int");

                    b.Property<string>("Description")
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.HasKey("Id");

                    b.HasIndex("DealerGroupId");

                    b.ToTable("Divisions", "dbo");
                });

            modelBuilder.Entity("StockPulse.Model.ErrorReport", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Report")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("ErrorReports", "dbo");
                });

            modelBuilder.Entity("StockPulse.Model.FinancialLine", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("AccountDescription")
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<decimal>("Balance")
                        .HasColumnType("decimal(15, 3)");

                    b.Property<string>("Code")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int?>("FileImportId")
                        .HasColumnType("int");

                    b.Property<bool>("IsExplanation")
                        .HasColumnType("bit");

                    b.Property<string>("Notes")
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<int>("StockCheckId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("FileImportId");

                    b.HasIndex("StockCheckId");

                    b.ToTable("FinancialLines", "dbo");
                });

            modelBuilder.Entity("StockPulse.Model.GlobalParam", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool>("BoolValue")
                        .HasColumnType("bit");

                    b.Property<DateTime>("DateValue")
                        .HasColumnType("datetime2");

                    b.Property<int>("DealerGroupId")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<decimal>("NumberValue")
                        .HasColumnType("decimal(15, 3)");

                    b.Property<string>("StringValue")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("Id");

                    b.HasIndex("DealerGroupId");

                    b.ToTable("GlobalParams", "dbo");
                });

            modelBuilder.Entity("StockPulse.Model.Import.FileImport", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("FileDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("FileName")
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<DateTime>("LoadDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("LoadedByUserId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("LoadedByUserId");

                    b.ToTable("FileImports", "import");
                });

            modelBuilder.Entity("StockPulse.Model.Import.SiteDescriptionDictionary", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("DealerGroupId")
                        .HasColumnType("int");

                    b.Property<string>("Description")
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<bool>("IsPrimarySiteId")
                        .HasColumnType("bit");

                    b.Property<int>("SiteId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("DealerGroupId");

                    b.HasIndex("SiteId");

                    b.ToTable("SiteDescriptionDictionary", "import");
                });

            modelBuilder.Entity("StockPulse.Model.ImportMask", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ColumnValueDifferentFromsJSON")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("ColumnValueEqualsesJSON")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("ColumnValueNotNullsJSON")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("ColumnsWeWantJSON")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<bool>("IgnoreZeroValues")
                        .HasColumnType("bit");

                    b.Property<bool>("IsMultiSite")
                        .HasColumnType("bit");

                    b.Property<bool>("IsStandard")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<int>("TopRowsToSkip")
                        .HasColumnType("int");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("ImportMasks", "dbo");
                });

            modelBuilder.Entity("StockPulse.Model.Input.FinancialLine", b =>
                {
                    b.Property<string>("AccountDescription")
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<decimal>("Balance")
                        .HasColumnType("decimal(15, 3)");

                    b.Property<string>("Code")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int>("DealerGroupId")
                        .HasColumnType("int");

                    b.Property<int?>("FileImportId")
                        .HasColumnType("int");

                    b.Property<int>("Id")
                        .HasColumnType("int");

                    b.Property<int>("SiteId")
                        .HasColumnType("int");

                    b.HasIndex("DealerGroupId");

                    b.HasIndex("FileImportId");

                    b.HasIndex("SiteId");

                    b.ToTable("FinancialLines", "input");
                });

            modelBuilder.Entity("StockPulse.Model.Input.ReconcilingItem", b =>
                {
                    b.Property<string>("Comment")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<int>("DealerGroupId")
                        .HasColumnType("int");

                    b.Property<string>("Description")
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<int?>("FileImportId")
                        .HasColumnType("int");

                    b.Property<int>("Id")
                        .HasColumnType("int");

                    b.Property<int>("ReconcilingItemTypeId")
                        .HasColumnType("int");

                    b.Property<string>("Reference")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Reg")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int>("SiteId")
                        .HasColumnType("int");

                    b.Property<int>("SourceReportId")
                        .HasColumnType("int");

                    b.Property<string>("Vin")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasIndex("DealerGroupId");

                    b.HasIndex("FileImportId");

                    b.HasIndex("ReconcilingItemTypeId");

                    b.HasIndex("SiteId");

                    b.HasIndex("SourceReportId");

                    b.ToTable("ReconcilingItems", "input");
                });

            modelBuilder.Entity("StockPulse.Model.Input.StockItem", b =>
                {
                    b.Property<string>("Branch")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Comment")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<int?>("DIS")
                        .HasColumnType("int");

                    b.Property<int>("DealerGroupId")
                        .HasColumnType("int");

                    b.Property<string>("Description")
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<int?>("FileImportId")
                        .HasColumnType("int");

                    b.Property<decimal>("Flooring")
                        .HasColumnType("decimal(15, 3)");

                    b.Property<int?>("GroupDIS")
                        .HasColumnType("int");

                    b.Property<int>("Id")
                        .HasColumnType("int");

                    b.Property<string>("Reference")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Reg")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int>("SiteId")
                        .HasColumnType("int");

                    b.Property<int>("SourceReportId")
                        .HasColumnType("int");

                    b.Property<string>("StockType")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<decimal>("StockValue")
                        .HasColumnType("decimal(15, 3)");

                    b.Property<string>("Vin")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasIndex("DealerGroupId");

                    b.HasIndex("FileImportId");

                    b.HasIndex("SiteId");

                    b.HasIndex("SourceReportId");

                    b.ToTable("StockItems", "input");
                });

            modelBuilder.Entity("StockPulse.Model.Location", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.HasKey("Id");

                    b.ToTable("Locations", "dbo");
                });

            modelBuilder.Entity("StockPulse.Model.LogMessage", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("AddedCount")
                        .HasColumnType("int");

                    b.Property<int>("ChangedCount")
                        .HasColumnType("int");

                    b.Property<int>("DealerGroup_Id")
                        .HasColumnType("int");

                    b.Property<int>("ErrorCount")
                        .HasColumnType("int");

                    b.Property<string>("FailNotes")
                        .HasMaxLength(99999)
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("FinalPartsSeconds")
                        .HasColumnType("int");

                    b.Property<int>("FinishCount")
                        .HasColumnType("int");

                    b.Property<DateTime?>("FinishDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("InterpretFileSeconds")
                        .HasColumnType("int");

                    b.Property<bool>("IsCompleted")
                        .HasColumnType("bit");

                    b.Property<string>("Job")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int>("ProcessedCount")
                        .HasColumnType("int");

                    b.Property<int>("RemovedCount")
                        .HasColumnType("int");

                    b.Property<DateTime>("SourceDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("StartCount")
                        .HasColumnType("int");

                    b.Property<int>("UpdateDbSeconds")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("DealerGroup_Id");

                    b.ToTable("LogMessages", "dbo");
                });

            modelBuilder.Entity("StockPulse.Model.MaintenanceTable", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.HasKey("Id");

                    b.ToTable("MaintenanceTables", "dbo");
                });

            modelBuilder.Entity("StockPulse.Model.MissingResolution", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool>("IsResolved")
                        .HasColumnType("bit");

                    b.Property<string>("Notes")
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<DateTime?>("ResolutionDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("ResolutionTypeId")
                        .HasColumnType("int");

                    b.Property<string>("StockcheckIdAndReference")
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("ResolutionTypeId");

                    b.HasIndex("UserId");

                    b.ToTable("MissingResolutions", "dbo");
                });

            modelBuilder.Entity("StockPulse.Model.MissingResolutionImage", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("FileName")
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<int>("MissingResolutionId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("MissingResolutionId");

                    b.ToTable("MissingResolutionImages", "dbo");
                });

            modelBuilder.Entity("StockPulse.Model.PrintLog", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime2");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.Property<string>("Vin")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("PrintLogs", "dbo");
                });

            modelBuilder.Entity("StockPulse.Model.ReconcilingItem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Comment")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("Description")
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<int?>("FileImportId")
                        .HasColumnType("int");

                    b.Property<int>("ReconcilingItemTypeId")
                        .HasColumnType("int");

                    b.Property<string>("Reference")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Reg")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int>("SourceReportId")
                        .HasColumnType("int");

                    b.Property<int>("StockCheckId")
                        .HasColumnType("int");

                    b.Property<string>("Vin")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("Id");

                    b.HasIndex("FileImportId");

                    b.HasIndex("ReconcilingItemTypeId");

                    b.HasIndex("SourceReportId");

                    b.HasIndex("StockCheckId");

                    b.ToTable("ReconcilingItems", "dbo");
                });

            modelBuilder.Entity("StockPulse.Model.ReconcilingItemBackup", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("FileName")
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<int>("ReconcilingItemTypeId")
                        .HasColumnType("int");

                    b.Property<int>("StockCheckId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("ReconcilingItemTypeId");

                    b.HasIndex("StockCheckId");

                    b.ToTable("ReconcilingItemBackups", "dbo");
                });

            modelBuilder.Entity("StockPulse.Model.ReconcilingItemType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("DealerGroupId")
                        .HasColumnType("int");

                    b.Property<string>("Description")
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<bool>("ExplainsMissingVehicle")
                        .HasColumnType("bit");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<int?>("SortOrder")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("DealerGroupId");

                    b.ToTable("ReconcilingItemTypes", "dbo");
                });

            modelBuilder.Entity("StockPulse.Model.ResolutionType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("BackupRequired")
                        .HasMaxLength(5000)
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("DealerGroupId")
                        .HasColumnType("int");

                    b.Property<string>("Description")
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<bool>("ExplainsMissingVehicle")
                        .HasColumnType("bit");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.HasKey("Id");

                    b.HasIndex("DealerGroupId");

                    b.ToTable("ResolutionTypes", "dbo");
                });

            modelBuilder.Entity("StockPulse.Model.Scan", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Comment")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("CoordinatesJSON")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("Description")
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<bool>("HasVinImage")
                        .HasColumnType("bit");

                    b.Property<string>("InterpretedReg")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("InterpretedVin")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<bool>("IsDuplicate")
                        .HasColumnType("bit");

                    b.Property<bool>("IsRegEditedOnDevice")
                        .HasColumnType("bit");

                    b.Property<bool>("IsRegEditedOnWeb")
                        .HasColumnType("bit");

                    b.Property<bool>("IsVinEditedOnDevice")
                        .HasColumnType("bit");

                    b.Property<bool>("IsVinEditedOnWeb")
                        .HasColumnType("bit");

                    b.Property<int?>("LastEditedById")
                        .HasColumnType("int");

                    b.Property<DateTime?>("LastEditedDateTime")
                        .HasColumnType("datetime");

                    b.Property<decimal>("Latitude")
                        .HasPrecision(9, 4)
                        .HasColumnType("decimal(15, 3)");

                    b.Property<int>("LocationId")
                        .HasColumnType("int");

                    b.Property<decimal>("Longitude")
                        .HasPrecision(9, 4)
                        .HasColumnType("decimal(15, 3)");

                    b.Property<int?>("ReconcilingItemId")
                        .HasColumnType("int");

                    b.Property<string>("Reg")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<decimal>("RegConfidence")
                        .HasColumnType("decimal(15, 3)");

                    b.Property<DateTime?>("SaveDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("ScanDateTime")
                        .HasColumnType("datetime");

                    b.Property<int>("StockCheckId")
                        .HasColumnType("int");

                    b.Property<int?>("StockItemId")
                        .HasColumnType("int");

                    b.Property<int?>("UnknownResolutionId")
                        .HasColumnType("int");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.Property<string>("Vin")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<decimal>("VinConfidence")
                        .HasColumnType("decimal(15, 3)");

                    b.HasKey("Id");

                    b.HasIndex("LastEditedById");

                    b.HasIndex("LocationId");

                    b.HasIndex("ReconcilingItemId");

                    b.HasIndex("StockCheckId");

                    b.HasIndex("StockItemId");

                    b.HasIndex("UnknownResolutionId");

                    b.HasIndex("UserId", "ScanDateTime", "Reg", "Vin")
                        .IsUnique()
                        .HasFilter("[Reg] IS NOT NULL AND [Vin] IS NOT NULL");

                    b.ToTable("Scans", "dbo");
                });

            modelBuilder.Entity("StockPulse.Model.Site", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Description")
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<int>("DivisionId")
                        .HasColumnType("int");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<decimal>("Latitude")
                        .HasPrecision(9, 4)
                        .HasColumnType("decimal(15, 3)");

                    b.Property<decimal>("Longitude")
                        .HasPrecision(9, 4)
                        .HasColumnType("decimal(15, 3)");

                    b.Property<bool>("OverrideLongLat")
                        .HasColumnType("bit");

                    b.HasKey("Id");

                    b.HasIndex("DivisionId");

                    b.ToTable("Sites", "dbo");
                });

            modelBuilder.Entity("StockPulse.Model.SiteLocation", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("LocationId")
                        .HasColumnType("int");

                    b.Property<int>("SiteId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("LocationId");

                    b.HasIndex("SiteId");

                    b.ToTable("SiteLocations", "dbo");
                });

            modelBuilder.Entity("StockPulse.Model.SourceReport", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("DateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Filename")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("SourceReports", "dbo");
                });

            modelBuilder.Entity("StockPulse.Model.Status", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Description")
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.HasKey("Id");

                    b.ToTable("Statuses", "dbo");
                });

            modelBuilder.Entity("StockPulse.Model.StatusChangeLogItem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime2");

                    b.Property<int>("StatusId")
                        .HasColumnType("int");

                    b.Property<int>("StockCheckId")
                        .HasColumnType("int");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("StatusId");

                    b.HasIndex("StockCheckId");

                    b.HasIndex("UserId");

                    b.ToTable("StatusChangeLogItems", "dbo");
                });

            modelBuilder.Entity("StockPulse.Model.StockCheck", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int?>("ApprovedByAccountantId")
                        .HasColumnType("int");

                    b.Property<int?>("ApprovedByGMId")
                        .HasColumnType("int");

                    b.Property<int?>("ApprovedById")
                        .HasColumnType("int");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime");

                    b.Property<bool>("HasSignoffImage")
                        .HasColumnType("bit");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsRegional")
                        .HasColumnType("bit");

                    b.Property<bool>("IsTotal")
                        .HasColumnType("bit");

                    b.Property<DateTime>("LastUpdated")
                        .HasColumnType("datetime");

                    b.Property<int>("MissingOs")
                        .HasColumnType("int");

                    b.Property<int>("Missings")
                        .HasColumnType("int");

                    b.Property<DateTime>("ReconciliationApprovedDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("ReconciliationCompletedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("ScannedInStock")
                        .HasColumnType("int");

                    b.Property<int>("SiteId")
                        .HasColumnType("int");

                    b.Property<int>("StatusId")
                        .HasColumnType("int");

                    b.Property<int>("UnknownOs")
                        .HasColumnType("int");

                    b.Property<int>("Unknowns")
                        .HasColumnType("int");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("ApprovedByAccountantId");

                    b.HasIndex("ApprovedByGMId");

                    b.HasIndex("ApprovedById");

                    b.HasIndex("SiteId");

                    b.HasIndex("StatusId");

                    b.HasIndex("UserId");

                    b.ToTable("StockChecks", "dbo");
                });

            modelBuilder.Entity("StockPulse.Model.StockItem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Branch")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Comment")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<int?>("DIS")
                        .HasColumnType("int");

                    b.Property<string>("Description")
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<int?>("FileImportId")
                        .HasColumnType("int");

                    b.Property<decimal>("Flooring")
                        .HasColumnType("decimal(15, 3)");

                    b.Property<int?>("GroupDIS")
                        .HasColumnType("int");

                    b.Property<bool>("IsAgencyStock")
                        .HasColumnType("bit");

                    b.Property<int?>("MissingResolutionId")
                        .HasColumnType("int");

                    b.Property<int?>("ReconcilingItemId")
                        .HasColumnType("int");

                    b.Property<string>("Reference")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Reg")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int?>("ScanId")
                        .HasColumnType("int");

                    b.Property<int>("SourceReportId")
                        .HasColumnType("int");

                    b.Property<int>("StockCheckId")
                        .HasColumnType("int");

                    b.Property<string>("StockType")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<decimal>("StockValue")
                        .HasColumnType("decimal(15, 3)");

                    b.Property<string>("Vin")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("Id");

                    b.HasIndex("FileImportId");

                    b.HasIndex("MissingResolutionId");

                    b.HasIndex("ReconcilingItemId");

                    b.HasIndex("ScanId");

                    b.HasIndex("SourceReportId");

                    b.HasIndex("StockCheckId");

                    b.ToTable("StockItems", "dbo");
                });

            modelBuilder.Entity("StockPulse.Model.UnknownResolution", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool>("IsResolved")
                        .HasColumnType("bit");

                    b.Property<string>("Notes")
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<DateTime>("ResolutionDateTime")
                        .HasColumnType("datetime");

                    b.Property<int?>("ResolutionTypeId")
                        .HasColumnType("int");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("ResolutionTypeId");

                    b.HasIndex("UserId");

                    b.ToTable("UnknownResolutions", "dbo");
                });

            modelBuilder.Entity("StockPulse.Model.UnknownResolutionImage", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("FileName")
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<int>("UnknownResolutionId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("UnknownResolutionId");

                    b.ToTable("UnknownResolutionImages", "dbo");
                });

            modelBuilder.Entity("StockPulse.Model.User", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("CurrentDevice")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int>("DealerGroupId")
                        .HasColumnType("int");

                    b.Property<string>("EmployeeNumber")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Name")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("Id");

                    b.HasIndex("DealerGroupId");

                    b.ToTable("Users", "dbo");
                });

            modelBuilder.Entity("StockPulse.Model.UserPreference", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("Person_Id")
                        .HasColumnType("int");

                    b.Property<string>("Preference")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("PreferenceName")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("Id");

                    b.ToTable("UserPreferences", "dbo");
                });

            modelBuilder.Entity("StockPulse.Model.UserSite", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool>("IsDefault")
                        .HasColumnType("bit");

                    b.Property<int>("SiteId")
                        .HasColumnType("int");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("SiteId");

                    b.HasIndex("UserId");

                    b.ToTable("UserSites", "dbo");
                });

            modelBuilder.Entity("StockPulse.RegPicture", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<byte[]>("ImageBytes")
                        .HasMaxLength(99999)
                        .HasColumnType("varbinary(max)");

                    b.Property<bool>("IsDone")
                        .HasColumnType("bit");

                    b.Property<int>("Priority")
                        .HasColumnType("int");

                    b.Property<DateTime>("RequestDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Result")
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.HasKey("Id");

                    b.ToTable("RegPictures", "dbo");
                });

            modelBuilder.Entity("StockPulse.RegScanStat", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool>("DidSucceed")
                        .HasColumnType("bit");

                    b.Property<bool>("FromLocallySaved")
                        .HasColumnType("bit");

                    b.Property<int>("Priority")
                        .HasColumnType("int");

                    b.Property<DateTime>("ScanDate")
                        .HasColumnType("datetime");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.Property<decimal>("WaitTime")
                        .HasColumnType("decimal(15, 3)");

                    b.HasKey("Id");

                    b.ToTable("RegScanStats", "dbo");
                });

            modelBuilder.Entity("StockPulse.Model.DealerGroup", b =>
                {
                    b.HasOne("StockPulse.Model.User", "User")
                        .WithOne()
                        .HasForeignKey("StockPulse.Model.DealerGroup", "NominatedUserId");

                    b.Navigation("User");
                });

            modelBuilder.Entity("StockPulse.Model.Division", b =>
                {
                    b.HasOne("StockPulse.Model.DealerGroup", "DealerGroup")
                        .WithMany()
                        .HasForeignKey("DealerGroupId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DealerGroup");
                });

            modelBuilder.Entity("StockPulse.Model.FinancialLine", b =>
                {
                    b.HasOne("StockPulse.Model.Import.FileImport", "FileImport")
                        .WithMany()
                        .HasForeignKey("FileImportId");

                    b.HasOne("StockPulse.Model.StockCheck", "StockChecks")
                        .WithMany()
                        .HasForeignKey("StockCheckId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("FileImport");

                    b.Navigation("StockChecks");
                });

            modelBuilder.Entity("StockPulse.Model.GlobalParam", b =>
                {
                    b.HasOne("StockPulse.Model.DealerGroup", "DealerGroup")
                        .WithMany()
                        .HasForeignKey("DealerGroupId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DealerGroup");
                });

            modelBuilder.Entity("StockPulse.Model.Import.FileImport", b =>
                {
                    b.HasOne("StockPulse.Model.User", "LoadedByUser")
                        .WithMany()
                        .HasForeignKey("LoadedByUserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("LoadedByUser");
                });

            modelBuilder.Entity("StockPulse.Model.Import.SiteDescriptionDictionary", b =>
                {
                    b.HasOne("StockPulse.Model.DealerGroup", "DealerGroup")
                        .WithMany()
                        .HasForeignKey("DealerGroupId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("StockPulse.Model.Site", "Sites")
                        .WithMany()
                        .HasForeignKey("SiteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DealerGroup");

                    b.Navigation("Sites");
                });

            modelBuilder.Entity("StockPulse.Model.ImportMask", b =>
                {
                    b.HasOne("StockPulse.Model.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("StockPulse.Model.Input.FinancialLine", b =>
                {
                    b.HasOne("StockPulse.Model.DealerGroup", "DealerGroup")
                        .WithMany()
                        .HasForeignKey("DealerGroupId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("StockPulse.Model.Import.FileImport", "FileImport")
                        .WithMany()
                        .HasForeignKey("FileImportId");

                    b.HasOne("StockPulse.Model.Site", "Sites")
                        .WithMany()
                        .HasForeignKey("SiteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DealerGroup");

                    b.Navigation("FileImport");

                    b.Navigation("Sites");
                });

            modelBuilder.Entity("StockPulse.Model.Input.ReconcilingItem", b =>
                {
                    b.HasOne("StockPulse.Model.DealerGroup", "DealerGroup")
                        .WithMany()
                        .HasForeignKey("DealerGroupId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("StockPulse.Model.Import.FileImport", "FileImport")
                        .WithMany()
                        .HasForeignKey("FileImportId");

                    b.HasOne("StockPulse.Model.ReconcilingItemType", "ReconcilingItemType")
                        .WithMany()
                        .HasForeignKey("ReconcilingItemTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("StockPulse.Model.Site", "Sites")
                        .WithMany()
                        .HasForeignKey("SiteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("StockPulse.Model.SourceReport", "SourceReports")
                        .WithMany()
                        .HasForeignKey("SourceReportId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DealerGroup");

                    b.Navigation("FileImport");

                    b.Navigation("ReconcilingItemType");

                    b.Navigation("Sites");

                    b.Navigation("SourceReports");
                });

            modelBuilder.Entity("StockPulse.Model.Input.StockItem", b =>
                {
                    b.HasOne("StockPulse.Model.DealerGroup", "DealerGroup")
                        .WithMany()
                        .HasForeignKey("DealerGroupId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("StockPulse.Model.Import.FileImport", "FileImport")
                        .WithMany()
                        .HasForeignKey("FileImportId");

                    b.HasOne("StockPulse.Model.Site", "Sites")
                        .WithMany()
                        .HasForeignKey("SiteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("StockPulse.Model.SourceReport", "SourceReports")
                        .WithMany()
                        .HasForeignKey("SourceReportId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DealerGroup");

                    b.Navigation("FileImport");

                    b.Navigation("Sites");

                    b.Navigation("SourceReports");
                });

            modelBuilder.Entity("StockPulse.Model.LogMessage", b =>
                {
                    b.HasOne("StockPulse.Model.DealerGroup", "DealerGroup")
                        .WithMany()
                        .HasForeignKey("DealerGroup_Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DealerGroup");
                });

            modelBuilder.Entity("StockPulse.Model.MissingResolution", b =>
                {
                    b.HasOne("StockPulse.Model.ResolutionType", "ResolutionType")
                        .WithMany()
                        .HasForeignKey("ResolutionTypeId");

                    b.HasOne("StockPulse.Model.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ResolutionType");

                    b.Navigation("User");
                });

            modelBuilder.Entity("StockPulse.Model.MissingResolutionImage", b =>
                {
                    b.HasOne("StockPulse.Model.MissingResolution", "MissingResolution")
                        .WithMany()
                        .HasForeignKey("MissingResolutionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("MissingResolution");
                });

            modelBuilder.Entity("StockPulse.Model.PrintLog", b =>
                {
                    b.HasOne("StockPulse.Model.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("StockPulse.Model.ReconcilingItem", b =>
                {
                    b.HasOne("StockPulse.Model.Import.FileImport", "FileImports")
                        .WithMany()
                        .HasForeignKey("FileImportId");

                    b.HasOne("StockPulse.Model.ReconcilingItemType", "ReconcilingItemType")
                        .WithMany()
                        .HasForeignKey("ReconcilingItemTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("StockPulse.Model.SourceReport", "SourceReports")
                        .WithMany()
                        .HasForeignKey("SourceReportId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("StockPulse.Model.StockCheck", "StockChecks")
                        .WithMany()
                        .HasForeignKey("StockCheckId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("FileImports");

                    b.Navigation("ReconcilingItemType");

                    b.Navigation("SourceReports");

                    b.Navigation("StockChecks");
                });

            modelBuilder.Entity("StockPulse.Model.ReconcilingItemBackup", b =>
                {
                    b.HasOne("StockPulse.Model.ReconcilingItemType", "ReconcilingItemType")
                        .WithMany()
                        .HasForeignKey("ReconcilingItemTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("StockPulse.Model.StockCheck", "StockCheck")
                        .WithMany()
                        .HasForeignKey("StockCheckId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ReconcilingItemType");

                    b.Navigation("StockCheck");
                });

            modelBuilder.Entity("StockPulse.Model.ReconcilingItemType", b =>
                {
                    b.HasOne("StockPulse.Model.DealerGroup", "DealerGroup")
                        .WithMany()
                        .HasForeignKey("DealerGroupId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DealerGroup");
                });

            modelBuilder.Entity("StockPulse.Model.ResolutionType", b =>
                {
                    b.HasOne("StockPulse.Model.DealerGroup", "DealerGroup")
                        .WithMany()
                        .HasForeignKey("DealerGroupId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DealerGroup");
                });

            modelBuilder.Entity("StockPulse.Model.Scan", b =>
                {
                    b.HasOne("StockPulse.Model.User", "LastEditedBy")
                        .WithMany()
                        .HasForeignKey("LastEditedById");

                    b.HasOne("StockPulse.Model.Location", "Locations")
                        .WithMany()
                        .HasForeignKey("LocationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("StockPulse.Model.ReconcilingItem", "ReconcilingItems")
                        .WithMany()
                        .HasForeignKey("ReconcilingItemId");

                    b.HasOne("StockPulse.Model.StockCheck", "StockChecks")
                        .WithMany()
                        .HasForeignKey("StockCheckId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("StockPulse.Model.StockItem", "StockItems")
                        .WithMany()
                        .HasForeignKey("StockItemId");

                    b.HasOne("StockPulse.Model.UnknownResolution", "UnknownResolutions")
                        .WithMany()
                        .HasForeignKey("UnknownResolutionId");

                    b.HasOne("StockPulse.Model.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("LastEditedBy");

                    b.Navigation("Locations");

                    b.Navigation("ReconcilingItems");

                    b.Navigation("StockChecks");

                    b.Navigation("StockItems");

                    b.Navigation("UnknownResolutions");

                    b.Navigation("User");
                });

            modelBuilder.Entity("StockPulse.Model.Site", b =>
                {
                    b.HasOne("StockPulse.Model.Division", "Divisions")
                        .WithMany()
                        .HasForeignKey("DivisionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Divisions");
                });

            modelBuilder.Entity("StockPulse.Model.SiteLocation", b =>
                {
                    b.HasOne("StockPulse.Model.Location", "Locations")
                        .WithMany()
                        .HasForeignKey("LocationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("StockPulse.Model.Site", "Sites")
                        .WithMany()
                        .HasForeignKey("SiteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Locations");

                    b.Navigation("Sites");
                });

            modelBuilder.Entity("StockPulse.Model.SourceReport", b =>
                {
                    b.HasOne("StockPulse.Model.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("StockPulse.Model.StatusChangeLogItem", b =>
                {
                    b.HasOne("StockPulse.Model.Status", "Status")
                        .WithMany()
                        .HasForeignKey("StatusId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("StockPulse.Model.StockCheck", "StockCheck")
                        .WithMany()
                        .HasForeignKey("StockCheckId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("StockPulse.Model.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Status");

                    b.Navigation("StockCheck");

                    b.Navigation("User");
                });

            modelBuilder.Entity("StockPulse.Model.StockCheck", b =>
                {
                    b.HasOne("StockPulse.Model.User", "ApprovedByAccountant")
                        .WithMany()
                        .HasForeignKey("ApprovedByAccountantId");

                    b.HasOne("StockPulse.Model.User", "ApprovedByGM")
                        .WithMany()
                        .HasForeignKey("ApprovedByGMId");

                    b.HasOne("StockPulse.Model.User", "ApprovedBy")
                        .WithMany()
                        .HasForeignKey("ApprovedById");

                    b.HasOne("StockPulse.Model.Site", "Sites")
                        .WithMany()
                        .HasForeignKey("SiteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("StockPulse.Model.Status", "Statuses")
                        .WithMany()
                        .HasForeignKey("StatusId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("StockPulse.Model.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ApprovedBy");

                    b.Navigation("ApprovedByAccountant");

                    b.Navigation("ApprovedByGM");

                    b.Navigation("Sites");

                    b.Navigation("Statuses");

                    b.Navigation("User");
                });

            modelBuilder.Entity("StockPulse.Model.StockItem", b =>
                {
                    b.HasOne("StockPulse.Model.Import.FileImport", "FileImport")
                        .WithMany()
                        .HasForeignKey("FileImportId");

                    b.HasOne("StockPulse.Model.MissingResolution", "MissingResolution")
                        .WithMany()
                        .HasForeignKey("MissingResolutionId");

                    b.HasOne("StockPulse.Model.ReconcilingItem", "ReconcilingItem")
                        .WithMany()
                        .HasForeignKey("ReconcilingItemId");

                    b.HasOne("StockPulse.Model.Scan", "Scan")
                        .WithMany()
                        .HasForeignKey("ScanId");

                    b.HasOne("StockPulse.Model.SourceReport", "SourceReport")
                        .WithMany()
                        .HasForeignKey("SourceReportId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("StockPulse.Model.StockCheck", "StockCheck")
                        .WithMany()
                        .HasForeignKey("StockCheckId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("FileImport");

                    b.Navigation("MissingResolution");

                    b.Navigation("ReconcilingItem");

                    b.Navigation("Scan");

                    b.Navigation("SourceReport");

                    b.Navigation("StockCheck");
                });

            modelBuilder.Entity("StockPulse.Model.UnknownResolution", b =>
                {
                    b.HasOne("StockPulse.Model.ResolutionType", "ResolutionTypes")
                        .WithMany()
                        .HasForeignKey("ResolutionTypeId");

                    b.HasOne("StockPulse.Model.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ResolutionTypes");

                    b.Navigation("User");
                });

            modelBuilder.Entity("StockPulse.Model.UnknownResolutionImage", b =>
                {
                    b.HasOne("StockPulse.Model.UnknownResolution", "UnknownResolution")
                        .WithMany()
                        .HasForeignKey("UnknownResolutionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("UnknownResolution");
                });

            modelBuilder.Entity("StockPulse.Model.User", b =>
                {
                    b.HasOne("StockPulse.Model.DealerGroup", "DealerGroup")
                        .WithMany()
                        .HasForeignKey("DealerGroupId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DealerGroup");
                });

            modelBuilder.Entity("StockPulse.Model.UserSite", b =>
                {
                    b.HasOne("StockPulse.Model.Site", "Site")
                        .WithMany()
                        .HasForeignKey("SiteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("StockPulse.Model.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Site");

                    b.Navigation("User");
                });
#pragma warning restore 612, 618
        }
    }
}
