﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace StockPulse.WebApi.Migrations
{
    /// <inheritdoc />
    public partial class changetablestobeDGspecificv2 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_FinancialLines_FileImports_FileImportId",
                schema: "dbo",
                table: "FinancialLines");

            migrationBuilder.DropForeignKey(
                name: "FK_ReconcilingItems_FileImports_FileImportId",
                schema: "dbo",
                table: "ReconcilingItems");

            migrationBuilder.DropForeignKey(
                name: "FK_ReconcilingItems_ReconcilingItemTypes_ReconcilingItemTypeId",
                schema: "dbo",
                table: "ReconcilingItems");

            migrationBuilder.DropForeignKey(
                name: "FK_ReconcilingItems_SourceReports_SourceReportId",
                schema: "dbo",
                table: "ReconcilingItems");

            migrationBuilder.DropForeignKey(
                name: "FK_StockItems_FileImports_FileImportId",
                schema: "dbo",
                table: "StockItems");

            migrationBuilder.DropForeignKey(
                name: "FK_StockItems_SourceReports_SourceReportId",
                schema: "dbo",
                table: "StockItems");

            migrationBuilder.DropTable(
                name: "FinancialLines",
                schema: "import");

            migrationBuilder.DropTable(
                name: "ReconcilingItems",
                schema: "import");

            migrationBuilder.DropTable(
                name: "StockItems",
                schema: "import");

            migrationBuilder.CreateTable(
                name: "MMGFinancialLines",
                schema: "import",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false),
                    Code = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    AccountDescription = table.Column<string>(type: "nvarchar(250)", maxLength: 250, nullable: true),
                    Balance = table.Column<decimal>(type: "decimal(15,3)", nullable: false),
                    SiteId = table.Column<int>(type: "int", nullable: false),
                    FileImportId = table.Column<int>(type: "int", nullable: true)
                },
                constraints: table =>
                {
                    table.ForeignKey(
                        name: "FK_MMGFinancialLines_FileImports_FileImportId",
                        column: x => x.FileImportId,
                        principalSchema: "import",
                        principalTable: "FileImports",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_MMGFinancialLines_Sites_SiteId",
                        column: x => x.SiteId,
                        principalSchema: "dbo",
                        principalTable: "Sites",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "MMGReconcilingItems",
                schema: "import",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false),
                    ReconcilingItemTypeId = table.Column<int>(type: "int", nullable: false),
                    Reg = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Vin = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Description = table.Column<string>(type: "nvarchar(250)", maxLength: 250, nullable: true),
                    Comment = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    Reference = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    SourceReportId = table.Column<int>(type: "int", nullable: false),
                    SiteId = table.Column<int>(type: "int", nullable: false),
                    FileImportId = table.Column<int>(type: "int", nullable: true)
                },
                constraints: table =>
                {
                    table.ForeignKey(
                        name: "FK_MMGReconcilingItems_FileImports_FileImportId",
                        column: x => x.FileImportId,
                        principalSchema: "import",
                        principalTable: "FileImports",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_MMGReconcilingItems_ReconcilingItemTypes_ReconcilingItemTypeId",
                        column: x => x.ReconcilingItemTypeId,
                        principalSchema: "dbo",
                        principalTable: "ReconcilingItemTypes",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_MMGReconcilingItems_Sites_SiteId",
                        column: x => x.SiteId,
                        principalSchema: "dbo",
                        principalTable: "Sites",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_MMGReconcilingItems_SourceReports_SourceReportId",
                        column: x => x.SourceReportId,
                        principalSchema: "dbo",
                        principalTable: "SourceReports",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "MMGStockItems",
                schema: "import",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false),
                    SourceReportId = table.Column<int>(type: "int", nullable: false),
                    Reg = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Vin = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Description = table.Column<string>(type: "nvarchar(250)", maxLength: 250, nullable: true),
                    DIS = table.Column<int>(type: "int", nullable: true),
                    GroupDIS = table.Column<int>(type: "int", nullable: true),
                    Branch = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Comment = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    StockType = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Reference = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    StockValue = table.Column<decimal>(type: "decimal(15,3)", nullable: false),
                    SiteId = table.Column<int>(type: "int", nullable: false),
                    FileImportId = table.Column<int>(type: "int", nullable: true)
                },
                constraints: table =>
                {
                    table.ForeignKey(
                        name: "FK_MMGStockItems_FileImports_FileImportId",
                        column: x => x.FileImportId,
                        principalSchema: "import",
                        principalTable: "FileImports",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_MMGStockItems_Sites_SiteId",
                        column: x => x.SiteId,
                        principalSchema: "dbo",
                        principalTable: "Sites",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_MMGStockItems_SourceReports_SourceReportId",
                        column: x => x.SourceReportId,
                        principalSchema: "dbo",
                        principalTable: "SourceReports",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_MMGFinancialLines_FileImportId",
                schema: "import",
                table: "MMGFinancialLines",
                column: "FileImportId");

            migrationBuilder.CreateIndex(
                name: "IX_MMGFinancialLines_SiteId",
                schema: "import",
                table: "MMGFinancialLines",
                column: "SiteId");

            migrationBuilder.CreateIndex(
                name: "IX_MMGReconcilingItems_FileImportId",
                schema: "import",
                table: "MMGReconcilingItems",
                column: "FileImportId");

            migrationBuilder.CreateIndex(
                name: "IX_MMGReconcilingItems_ReconcilingItemTypeId",
                schema: "import",
                table: "MMGReconcilingItems",
                column: "ReconcilingItemTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_MMGReconcilingItems_SiteId",
                schema: "import",
                table: "MMGReconcilingItems",
                column: "SiteId");

            migrationBuilder.CreateIndex(
                name: "IX_MMGReconcilingItems_SourceReportId",
                schema: "import",
                table: "MMGReconcilingItems",
                column: "SourceReportId");

            migrationBuilder.CreateIndex(
                name: "IX_MMGStockItems_FileImportId",
                schema: "import",
                table: "MMGStockItems",
                column: "FileImportId");

            migrationBuilder.CreateIndex(
                name: "IX_MMGStockItems_SiteId",
                schema: "import",
                table: "MMGStockItems",
                column: "SiteId");

            migrationBuilder.CreateIndex(
                name: "IX_MMGStockItems_SourceReportId",
                schema: "import",
                table: "MMGStockItems",
                column: "SourceReportId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "MMGFinancialLines",
                schema: "import");

            migrationBuilder.DropTable(
                name: "MMGReconcilingItems",
                schema: "import");

            migrationBuilder.DropTable(
                name: "MMGStockItems",
                schema: "import");

            migrationBuilder.CreateTable(
                name: "FinancialLines",
                schema: "import",
                columns: table => new
                {
                    FileImportId = table.Column<int>(type: "int", nullable: true),
                    SiteId = table.Column<int>(type: "int", nullable: false),
                    AccountDescription = table.Column<string>(type: "nvarchar(250)", maxLength: 250, nullable: true),
                    Balance = table.Column<decimal>(type: "decimal(15,3)", nullable: false),
                    Code = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Id = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.ForeignKey(
                        name: "FK_FinancialLines_FileImports_FileImportId",
                        column: x => x.FileImportId,
                        principalSchema: "import",
                        principalTable: "FileImports",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_FinancialLines_Sites_SiteId",
                        column: x => x.SiteId,
                        principalSchema: "dbo",
                        principalTable: "Sites",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "ReconcilingItems",
                schema: "import",
                columns: table => new
                {
                    FileImportId = table.Column<int>(type: "int", nullable: true),
                    ReconcilingItemTypeId = table.Column<int>(type: "int", nullable: false),
                    SiteId = table.Column<int>(type: "int", nullable: false),
                    SourceReportId = table.Column<int>(type: "int", nullable: false),
                    Comment = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    Description = table.Column<string>(type: "nvarchar(250)", maxLength: 250, nullable: true),
                    Id = table.Column<int>(type: "int", nullable: false),
                    Reference = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Reg = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Vin = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.ForeignKey(
                        name: "FK_ReconcilingItems_FileImports_FileImportId",
                        column: x => x.FileImportId,
                        principalSchema: "import",
                        principalTable: "FileImports",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_ReconcilingItems_ReconcilingItemTypes_ReconcilingItemTypeId",
                        column: x => x.ReconcilingItemTypeId,
                        principalSchema: "dbo",
                        principalTable: "ReconcilingItemTypes",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_ReconcilingItems_Sites_SiteId",
                        column: x => x.SiteId,
                        principalSchema: "dbo",
                        principalTable: "Sites",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_ReconcilingItems_SourceReports_SourceReportId",
                        column: x => x.SourceReportId,
                        principalSchema: "dbo",
                        principalTable: "SourceReports",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "StockItems",
                schema: "import",
                columns: table => new
                {
                    FileImportId = table.Column<int>(type: "int", nullable: true),
                    SiteId = table.Column<int>(type: "int", nullable: false),
                    SourceReportId = table.Column<int>(type: "int", nullable: false),
                    Branch = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Comment = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    DIS = table.Column<int>(type: "int", nullable: true),
                    Description = table.Column<string>(type: "nvarchar(250)", maxLength: 250, nullable: true),
                    GroupDIS = table.Column<int>(type: "int", nullable: true),
                    Id = table.Column<int>(type: "int", nullable: false),
                    Reference = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Reg = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    StockType = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    StockValue = table.Column<decimal>(type: "decimal(15,3)", nullable: false),
                    Vin = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.ForeignKey(
                        name: "FK_StockItems_FileImports_FileImportId",
                        column: x => x.FileImportId,
                        principalSchema: "import",
                        principalTable: "FileImports",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_StockItems_Sites_SiteId",
                        column: x => x.SiteId,
                        principalSchema: "dbo",
                        principalTable: "Sites",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_StockItems_SourceReports_SourceReportId",
                        column: x => x.SourceReportId,
                        principalSchema: "dbo",
                        principalTable: "SourceReports",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_FinancialLines_FileImportId",
                schema: "import",
                table: "FinancialLines",
                column: "FileImportId");

            migrationBuilder.CreateIndex(
                name: "IX_FinancialLines_SiteId",
                schema: "import",
                table: "FinancialLines",
                column: "SiteId");

            migrationBuilder.CreateIndex(
                name: "IX_ReconcilingItems_FileImportId",
                schema: "import",
                table: "ReconcilingItems",
                column: "FileImportId");

            migrationBuilder.CreateIndex(
                name: "IX_ReconcilingItems_ReconcilingItemTypeId",
                schema: "import",
                table: "ReconcilingItems",
                column: "ReconcilingItemTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_ReconcilingItems_SiteId",
                schema: "import",
                table: "ReconcilingItems",
                column: "SiteId");

            migrationBuilder.CreateIndex(
                name: "IX_ReconcilingItems_SourceReportId",
                schema: "import",
                table: "ReconcilingItems",
                column: "SourceReportId");

            migrationBuilder.CreateIndex(
                name: "IX_StockItems_FileImportId",
                schema: "import",
                table: "StockItems",
                column: "FileImportId");

            migrationBuilder.CreateIndex(
                name: "IX_StockItems_SiteId",
                schema: "import",
                table: "StockItems",
                column: "SiteId");

            migrationBuilder.CreateIndex(
                name: "IX_StockItems_SourceReportId",
                schema: "import",
                table: "StockItems",
                column: "SourceReportId");
        }
    }
}
