﻿/****** Object:  StoredProcedure [dbo].[GET_LocationsForStockCheck]    Script Date: 11/3/21 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].GET_LocationsForStockCheck
(
    @StockCheckId int null,
	@UserId int null
)
AS
BEGIN

SET NOCOUNT ON

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END

	
SELECT L.[Id]      ,L.[Description]
FROM [dbo].Locations L
inner join SiteLocations SL on SL.LocationId = L.Id
inner join Sites S on S.Id = SL.SiteId
WHERE SiteId = (select SiteId from StockChecks where Id =  @StockCheckId)

END
	



GO



--To use this run 
--exec [GET_LocationsForStockCheck] @StockCheckId = 99, @UserId =1