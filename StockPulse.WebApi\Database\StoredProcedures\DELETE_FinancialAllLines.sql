﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


  
CREATE OR ALTER PROCEDURE [dbo].[DELETE_FinancialAllLines]
(  
    @StockCheckId INT = NULL,  
    @UserId INT = NULL
)  
AS  
BEGIN  
  
SET NOCOUNT ON;  
  
IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END

   
DELETE FROM [dbo].[FinancialLines]
WHERE StockCheckId = @StockCheckId


EXEC [dbo].[UPDATE_StockCheckLastUpdate] @StockCheckId, @UserId  
  
  
END  
  
GO


