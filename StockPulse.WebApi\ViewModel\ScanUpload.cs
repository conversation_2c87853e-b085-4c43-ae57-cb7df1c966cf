﻿using System;

namespace StockPulse.WebApi.ViewModel
{
    public class ScanUpload
    {
        //Scan
        public int Id { get; set; }
        public int UploadId { get; set; }
        public int StockCheckId { get; set; }
        public int LocationId { get; set; }
        public decimal RegConfidence { get; set; }
        public decimal VinConfidence { get; set; }
        public decimal Longitude { get; set; }
        public decimal Latitude { get; set; }
        public DateTime ScanDateTime { get; set; }
        public string Comment { get; set; }
        public string Reg { get; set; }
        public string Vin { get; set; }
        public string InterpretedReg { get; set; }
        public string InterpretedVin { get; set; }
        public bool IsVinEditedOnDevice { get; set; }
        public bool IsRegEditedOnDevice { get; set; }
        public string Description { get; set; }
        public bool HasVinImage { get; set; }
        public string ImageBase64 { get; set; }
        public string ImageBase64Sm { get; set; }
        public string VinImagebase64 { get; set; }

    }
}
