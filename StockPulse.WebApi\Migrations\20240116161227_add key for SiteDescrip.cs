﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace StockPulse.WebApi.Migrations
{
    /// <inheritdoc />
    public partial class addkeyforSiteDescrip : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "Id",
                schema: "import",
                table: "SiteDescriptionDictionary",
                type: "int",
                nullable: false,
                defaultValue: 0)
                .Annotation("SqlServer:Identity", "1, 1");

            migrationBuilder.AddPrimaryKey(
                name: "PK_SiteDescriptionDictionary",
                schema: "import",
                table: "SiteDescriptionDictionary",
                column: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropPrimaryKey(
                name: "PK_SiteDescriptionDictionary",
                schema: "import",
                table: "SiteDescriptionDictionary");

            migrationBuilder.DropColumn(
                name: "Id",
                schema: "import",
                table: "SiteDescriptionDictionary");
        }
    }
}
