import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';


import { ConstantsService } from './constants.service'
import { SelectionsService } from './selections.service'

import { Router } from '@angular/router';
import { ResetPasswordModel } from "../model/ResetPasswordModel";
import { ForgotPasswordModel } from "../model/ForgotPasswordModel";
import { LoginModel } from "../model/LoginModel";
import { ApiAccessService } from './apiAccess.service';
import { ToastService } from './newToast.service';
import { PhotoMapReportType } from '../model/PhotoMapReportType';
import { GetPhotoMapItemGroupsParms } from '../model/GetPhotoMapItemGroupsParms';
import { ReconciliationState } from '../model/ReconciliationState';
import { WaterfallBarDetailParams } from '../model/WaterfallBarDetailParams';
import { UserPreference } from '../model/UserPreference';





@Injectable({
  providedIn: 'root'
})
export class GetDataService {

  downloadedScanImageIds: Array<string> = [];

  cameraIsRunning: boolean = false;


  saveCount: number = 0;




  imageRequests: Array<string> = [];
  imageRequestsHiRes: Array<string> = [];
  imageRequestsVin: string[] = [];

  enviro: string;



  constructor(
    public http: HttpClient,
    public constants: ConstantsService,
    public selections: SelectionsService,
    public router: Router,
    public apiAccessService: ApiAccessService,
    public toastService: ToastService
  ) {


  }




  // getOptions(){
  //   let headers = new HttpHeaders({
  //     'Accept': 'application/json', 'Content-Type': 'application/json'
  //   });
  //   let options = { headers: headers };

  //   return options
  // }

  provideUnknownVehicleImage(scanId: string): any {


    let myObservable: Observable<any> = new Observable(observer => {


      this.apiAccessService.get('BlobImage', 'Unknown/' + scanId).subscribe(

        result => {
          observer.next(result);
        },
        //errors
        error => {
          console.error('error when retrieving Image')
          this.toastService.errorToast(`Error when retrieving image`);
        }
        ,
        //finished
        () => {
          observer.complete();
        }
      )
    })

    return myObservable;
  }



  provideMissingVehicleImage(scanId: string): any {


    let myObservable: Observable<any> = new Observable(observer => {


      this.apiAccessService.get('BlobImage', 'Missing/' + scanId).subscribe(

        result => {
          observer.next(result);
        },
        //errors
        error => {
          console.error('error when retrieving Image')
          this.toastService.errorToast(`Error when retrieving image`);
        }
        ,
        //finished
        () => {
          observer.complete();
        }
      )
    })

    return myObservable;
  }


  getNewAccessToken() {

    let options = {
      headers: new HttpHeaders().set('refresh_token',  this.constants.refreshToken)
    };
    options.headers.set('Accept', 'application/json');
  

    return this.http.post<any>(this.constants.provideBaseUrl() + '/Account/RefreshToken', null, options);
  }


  getPhotoMapItemGroups(stockCheckId: number, reportType: PhotoMapReportType) {
    //const url = `${this.constants.baseURL}/StockChecks/GetPhotoMapItemGroups`
    const payload: GetPhotoMapItemGroupsParms = { StockCheckId: stockCheckId, ReportType: reportType }
    return this.apiAccessService.post('StockChecks', 'GetPhotoMapItemGroups', payload)
  }
  
  
  
  
  
  getWaterfallBarDetail(stockCheckId: number, state: ReconciliationState, isScan: boolean, reconcilingItemTypeId: number) {
    const payload: WaterfallBarDetailParams = {
      State: state,
      IsScan:isScan,
      StockcheckId: stockCheckId,
      ReconcilingItemTypeId: reconcilingItemTypeId || 0 
    }
    return this.apiAccessService.post('StockChecks', 'GetWaterfallBarDetail', payload)
  }

  getAllWaterfallBarDetail(stockCheckId: number) {
    
    let payload = [{ key: 'stockcheckId', value: stockCheckId }]
    return this.apiAccessService.get('StockChecks', 'GetAllWaterfallBarDetail', payload)
  }

  getResolvedMissings(stockcheckId: number) {
    let url: string = `${this.constants.provideBaseUrl()}/StockItems/GetStockItemsWithResolution?stockcheckId=${stockcheckId}`;
    return this.http.get(url);
  }

  getResolvedUnknowns(stockcheckId: number) {
    let url: string = `${this.constants.provideBaseUrl()}/Scans/GetScansWithResolution?stockcheckId=${stockcheckId}`;
    return this.http.get(url);
  }

  getStockCheckIdsForUserDealerGroup() {
    let url: string = `${this.constants.provideBaseUrl()}/StockChecks/GetStockCheckIdsForUserDealerGroup`;
    return this.http.get(url);
  }

  getMaintenanceTables() {
    return this.http.get(`${this.constants.provideBaseUrl()}/TableMaintenance/GetTables`)
  }

  getMaintenanceTableData(id: number) {
    return this.http.get(`${this.constants.provideBaseUrl()}/TableMaintenance/GetData?tableId=${id}`)
  }

  saveMaintenanceTableRowData(tableName: string, rowData: string) {
    return this.http.post(`${this.constants.provideBaseUrl()}/TableMaintenance/SaveData`, { tableName, rowData })
  }


  deleteMaintenanceTableRowData(tableName: string, rowId: number) {
    return this.http.post(`${this.constants.provideBaseUrl()}/TableMaintenance/DeleteData`, { tableName, rowId })
  }

  saveUserPreference(userPreference: UserPreference) {
    return this.http.post(`${this.constants.provideBaseUrl()}/User/SaveUserPreference`, userPreference);
  }
}
