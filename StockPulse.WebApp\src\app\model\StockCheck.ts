



export interface StockCheck {
  id: number;
  site: string;
  person: string;
  date: Date;
  lastUpdated: Date | string;
  status: string;
  statusId: number;
  scannedInStock: number;
  unknowns: number;
  missings: number;
  unknownOs: number;
  missingOs: number;
  stockItemsCount: number;
  isRegional: boolean;
  isTotal: boolean;
  approvedByAccountant: string;
  approvedByGM: string;
  approvedBy: string;
  hasSignoffImage: boolean;
  signoffImageURL: string;
  reconciliationCompletedDate: Date;
  reconciliationApprovedDate: Date;
  percentageComplete: number;
  scans: number;
  siteRegion: string;

  inStockValue: number;
  unresolvedMissingValue: number;

  //local props
  isChosen?: boolean;
  percentageCompleteExtra?: string;
  firstScan: Date | string;
  lastScan: Date | string;

  glValue: number;
  variance: number;
}
