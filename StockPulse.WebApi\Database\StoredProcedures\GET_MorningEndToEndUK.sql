﻿
  
CREATE OR ALTER PROCEDURE [admin].[GET_MorningEndToEndUK]  
AS   
BEGIN  
SET NOCOUNT ON  
 
    CREATE TABLE #ExpectedJobs(  
    OrderBy decimal(10,5),  
    WhenShouldRun varchar(10),   
    [Job] [nvarchar](50) NULL,
    DealerGroupId int
    )


    CREATE TABLE #TempJobsHolder(  
    OrderBy decimal(10,5),  
    WhenShouldRun varchar(10),   
    [Job] [nvarchar](50) NULL  
    )


    DECLARE @mmg_Jobs varchar(100);
    SET @mmg_Jobs = '11';  -- MMG 11

    DECLARE @lithiaUK_Jobs varchar(100);
    SET @lithiaUK_Jobs = '7';  -- <PERSON><PERSON>aUK 7

    -- MMG
    INSERT INTO #TempJobsHolder (OrderBy,WhenShouldRun,Job) 
    VALUES
    (1,DATEADD(HOUR, -24, GETDATE()),'MMGStockOnLoans'), 
    (2,DATEADD(HOUR, -24, GETDA<PERSON>()),'MMGWIP'),
    (3,DATEADD(HOUR, -24, GETDATE()),'MMGStockAtAuction'),  
    (4,DATEADD(HOUR, -24, GETDATE()),'MMGStock'),  
    (5,DATEADD(HOUR, -24, GETDATE()),'MMGTB');


    INSERT INTO #ExpectedJobs (OrderBy, WhenShouldRun, Job, DealerGroupId)
    SELECT 
        t.OrderBy + ROW_NUMBER() OVER (ORDER BY dg.Id) AS OrderBy,
        t.WhenShouldRun AS WhenShouldRun, 
        t.Job AS Job,
        dg.Id AS DealerGroupId
    FROM DealerGroup dg
    LEFT JOIN #TempJobsHolder t ON 1 = 1
    WHERE dg.Id IN (SELECT value FROM STRING_SPLIT(@mmg_Jobs, ','));

    --- Clear for Lithia
    DELETE FROM #TempJobsHolder WHERE 1=1;

    -- Lithia UK (Jardine)
    INSERT INTO #TempJobsHolder (OrderBy,WhenShouldRun,Job) 
    VALUES
    (6,DATEADD(HOUR, -24, GETDATE()),'JardineWIPs'),  
    (7,DATEADD(HOUR, -24, GETDATE()),'JardineStocks'),
    (8,DATEADD(HOUR, -24, GETDATE()),'JardineTB');

    INSERT INTO #ExpectedJobs (OrderBy,WhenShouldRun,Job,DealerGroupId) 
    (SELECT 
        t.OrderBy + ROW_NUMBER() Over(order by dg.Id) as OrderBy,
        t.WhenShouldRun as WhenShouldRun, 
        t.Job as JobName,
        dg.Id AS DealerGroupId
        FROM DealerGroup dg
        LEFT JOIN #TempJobsHolder t on 1=1
        WHERE dg.Id IN ( SELECT value from STRING_SPLIT(@lithiaUK_Jobs,','))
    );

    --- Get the latest log messages
    WITH RecentJobs AS  
    (  
        SELECT LM.*,  
                ROW_NUMBER() OVER (PARTITION BY LM.Job ORDER BY LM.SourceDate DESC) AS rn  
        FROM LogMessages LM LEFT JOIN DealerGroup DG ON LM.DealerGroup_Id = DG.Id
        WHERE finishDate > dateadd(hour,16,DATEDIFF(d,0,GETDATE()-1))   
    ),
    
	-- Rank them to prevent duplicate entries in email
	RankedResults AS (
		SELECT 
			CONCAT(dg.Description, ':', e.Job) as Job,  
			IIF(lm.Job IS NULL, 'Not Found', IIF(lm.ErrorCount > 0, 'Errors', 'Ok')) as Status,
			ROW_NUMBER() OVER (PARTITION BY CONCAT(dg.Description, ':', e.Job) ORDER BY e.OrderBy ASC) as RowNum
		FROM #ExpectedJobs e  
		LEFT JOIN RecentJobs lm ON lm.Job = e.Job
		LEFT JOIN DealerGroup dg ON e.DealerGroupId = dg.Id
	)

	SELECT 
		Job,
		Status
	INTO #Results
	FROM RankedResults
	WHERE RowNum = 1
	ORDER BY Job;

    SELECT STRING_AGG(Job,', ') as FailedList, 'Stockpulse UK - Morning end to end summary' as emailSubject FROM #Results where Status <> 'Ok' 

    DROP TABLE #ExpectedJobs  
    DROP TABLE #TempJobsHolder
	DROP TABLE #Results

END  
  
GO

--SELECT TOP 10 * FROM LogMessages
--SELECT * FROM DealerGroup