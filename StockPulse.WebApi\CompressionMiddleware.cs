﻿using Microsoft.AspNetCore.Http;
using System.IO;
using System.Threading.Tasks;
//using ZstdNet;
using System.IO.Compression;

namespace StockPulse.WebApi
{



    public class CompressionMiddleware
    {
        private readonly RequestDelegate _next;

        public CompressionMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            var acceptEncoding = context.Request.Headers["Accept-Encoding"].ToString();

            // Capture the original response body stream
            var originalBodyStream = context.Response.Body;
            using (var compressedBodyStream = new MemoryStream())
            {
                context.Response.Body = compressedBodyStream;

                await _next(context); // Call the next middleware in the pipeline

                //if (acceptEncoding.Contains("zstd"))
                //{
                //    context.Response.Headers["Content-Encoding"] = "zstd";
                //    compressedBodyStream.Seek(0, SeekOrigin.Begin);
                //    var compressedData = CompressZstd(compressedBodyStream.ToArray());
                //    context.Response.ContentLength = compressedData.Length;
                //    await originalBodyStream.WriteAsync(compressedData, 0, compressedData.Length);
                //}
                //else 
                if (acceptEncoding.Contains("br"))
                {
                    context.Response.Headers["Content-Encoding"] = "br";
                    compressedBodyStream.Seek(0, SeekOrigin.Begin);
                    var compressedData = CompressBrotli(compressedBodyStream.ToArray());
                    if (context.Response.StatusCode != 204)
                    {
                        context.Response.ContentLength = compressedData.Length;
                    }
                    await originalBodyStream.WriteAsync(compressedData, 0, compressedData.Length);
                }
                else if (acceptEncoding.Contains("gzip"))
                {
                    context.Response.Headers["Content-Encoding"] = "gzip";
                    compressedBodyStream.Seek(0, SeekOrigin.Begin);
                    var compressedData = CompressGzip(compressedBodyStream.ToArray());
                    if (context.Response.StatusCode != 204)
                    {
                        context.Response.ContentLength = compressedData.Length;
                    }
                    await originalBodyStream.WriteAsync(compressedData, 0, compressedData.Length);
                }
                else
                {
                    // No supported compression, send the original response
                    compressedBodyStream.Seek(0, SeekOrigin.Begin);
                    await compressedBodyStream.CopyToAsync(originalBodyStream);
                }
            }
        }

        //private byte[] CompressZstd(byte[] data)
        //{
        //    using (var compressor = new Compressor())
        //    {
        //        return compressor.Wrap(data);
        //    }
        //}

        private byte[] CompressBrotli(byte[] data)
        {
            using (var output = new MemoryStream())
            using (var brotliStream = new BrotliStream(output, CompressionLevel.Optimal))
            {
                brotliStream.Write(data, 0, data.Length);
                brotliStream.Close();
                return output.ToArray();
            }
        }

        private byte[] CompressGzip(byte[] data)
        {
            using (var output = new MemoryStream())
            using (var gzipStream = new GZipStream(output, CompressionLevel.Optimal))
            {
                gzipStream.Write(data, 0, data.Length);
                gzipStream.Close();
                return output.ToArray();
            }
        }
    }
}