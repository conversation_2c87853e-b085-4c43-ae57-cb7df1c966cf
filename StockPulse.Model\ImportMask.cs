﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace StockPulse.Model
{
    public class ImportMask
    {
        [Key]
        public int Id { get; set; }
        [StringLength(200)]
        public string Name { get; set; }
        public int TopRowsToSkip { get; set; }
        public string ColumnValueEqualsesJSON { get; set; }
        public string ColumnValueDifferentFromsJSON { get; set; }
        public string ColumnValueNotNullsJSON { get; set; }
        public string ColumnsWeWantJSON { get; set; }

        public int UserId { get; set; }
        [ForeignKey("UserId")]
        public virtual User User { get; set; }
        public bool IsStandard { get; set; }
        public bool IsMultiSite { get; set; }
        public bool IgnoreZeroValues { get; set; }
    }

    public class ImportMaskWithCreatedBy
    {
        public int Id { get; set; }
        [StringLength(200)]
        public string Name { get; set; }
        public int TopRowsToSkip { get; set; }
        public string ColumnValueEqualsesJSON { get; set; }
        public string ColumnValueDifferentFromsJSON { get; set; }
        public string ColumnValueNotNullsJSON { get; set; }
        public string ColumnsWeWantJSON { get; set; }
        public int UserId { get; set; }
        public string CreatedBy { get; set; }
        public bool IsStandard { get; set; }
        public bool IsMultiSite { get; set; }
        public bool IgnoreZeroValues { get; set; }
    }


    public class ImportMaskSave
    {
        [StringLength(200)]
        public string Name { get; set; }
        public int TopRowsToSkip { get; set; }
        public string ColumnValueEqualsesJSON { get; set; }
        public string ColumnValueDifferentFromsJSON { get; set; }
        public string ColumnValueNotNullsJSON { get; set; }
        public string ColumnsWeWantJSON { get; set; }
        public bool IsStandard { get; set; }
        public bool IsMultiSite { get; set; }
        public bool IgnoreZeroValues {  get; set; }
    }
    public class ImportMaskSaveWithId
    {
        public int Id { get; set; }
        [StringLength(200)]
        public string Name { get; set; }
        public int TopRowsToSkip { get; set; }
        public string ColumnValueEqualsesJSON { get; set; }
        public string ColumnValueDifferentFromsJSON { get; set; }
        public string ColumnValueNotNullsJSON { get; set; }
        public string ColumnsWeWantJSON { get; set; }
        public bool IsStandard { get; set; }
        public bool IsMultiSite { get; set; }
        public bool IgnoreZeroValues { get; set; }
    }

    public class ImportMaskUpdateParams
    {
        public List<ImportMaskUpdate> MasksToUpdate { get; set; }
    }

    public class ImportMaskUpdate
    {
        public int id { get; set; }
        public string newValue { get; set; }
        public bool isStandard {  get; set; }
        public bool IsMultiSite { get; set; }
        public bool IgnoreZeroValues { get; set; }
    }
}