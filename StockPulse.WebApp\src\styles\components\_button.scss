button.disabled,
button:disabled {
    color: var(--bodyColour);
    cursor: not-allowed !important;
    pointer-events: all !important;
}

.btn-primary {
    @include button-variant($primary, $primaryLight, $secondary, $primary, $primaryLight, $secondary);
    color: var(--bodyColour);
    border: 0px;
    cursor: pointer;
}

//for dropdown
.btn-primary:not(:disabled):not(.disabled):active,
.btn-primary:not(:disabled):not(.disabled).active,
.show>.btn-primary.dropdown-toggle {
    color: var(--bodyColour);
    background-color: var(--primaryLight);
    border-color: var(--primaryLight);
}

.dropdown-toggle.dropdownBottomButton::after {
    display: none !important;
}

.dropdown-toggle.manualToggleCloseItem::after {
    display: none !important;
}

.dropdown-item.disabled {
    background: grey
}

button:focus {
    outline: none !important;
}

button {
    box-shadow: none !important;
}

.btn {
    border-radius: 0.3em;
    border: none;
}

.btn:focus {
    outline: none;
}


.btn-danger {
    cursor: pointer;
}

.btn-primary:disabled {
    background-color: var(--primaryLighter);
}

.btn-primary:disabled:hover {
    background-color: var(--primaryLighter);
    color: inherit;
}

.btn-primary.small {
    height: 2em;
    padding: 0px 1em;
    padding: 0em 0.6em;
}

.buttonGroup button {
    margin: 0px;
    border-radius: 0px;
}

.buttonGroup button:first-of-type {
    margin: 0px;
    border-radius: 0.3em 0em 0em 0.3em;
}

.buttonGroup button:last-of-type {
    margin: 0px;
    border-radius: 0em 0.3em 0.3em 0em;
}

.buttonGroup button:first-of-type:last-of-type {
    margin: 0px;
    border-radius: 0.3em;
}

.buttonGroup {
    margin: 0em 1em;
}

.btn-primary {
    background-color: var(--primaryLight);
    color: var(--bodyColour);
    // border-radius: 0;
}

.btn-primary:hover,
.btn-primary.active,
.btn-primary:not(:disabled):not(.disabled).active {
    background-color: var(--secondary);
    color: #000000;
}

.btn.floatTopRightOfGrid {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 1;
    padding: 0;
}

button .ng-fa-icon {
    margin-right: 0.25em;
}

.buttonIconOnly .ng-fa-icon {
    margin-right: 0;
}