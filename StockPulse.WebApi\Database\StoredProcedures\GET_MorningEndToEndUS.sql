﻿
  
CREATE OR ALTER PROCEDURE [admin].[GET_MorningEndToEndUS]  
AS   
BEGIN  
SET NOCOUNT ON  
 
    CREATE TABLE #ExpectedJobs(  
    OrderBy decimal(10,5),  
    WhenShouldRun varchar(10),   
    [Job] [nvarchar](50) NULL,
    DealerGroupId int
    )


    CREATE TABLE #TempJobsHolder(  
    OrderBy decimal(10,5),  
    WhenShouldRun varchar(10),   
    [Job] [nvarchar](50) NULL  
    )


    DECLARE @lithia_Jobs varchar(100);
    SET @lithia_Jobs = '10';  -- LithiaUK/MMG 7/11

    --LocalBargain_Job
    INSERT INTO #TempJobsHolder (OrderBy,WhenShouldRun,Job) 
    VALUES
    (1,DATEADD(HOUR, -24, GETDATE()),'LithiaBookedAndPendingJob'), 
    (2,DATEADD(HOUR, -24, GETDATE()),'LithiaTB'),  
    (3,DATEADD(HOUR, -24, GET<PERSON><PERSON>()),'LithiaWIP'),
    (4,DATEADD(HOUR, -24, GETDATE()),'LithiaStockItems - New'),
    (5,DATEADD(HOUR, -24, GETDATE()),'LithiaStockItems - Used'),
    (6,DATEADD(HOUR, -24, GETDATE()),'LithiaStockItems - Program'),
    (7,DATEADD(HOUR, -24, GETDATE()),'LithiaStockItems - Rental'),
    (8,DATEADD(HOUR, -24, GETDATE()),'LithiaStockItems - Loaner');

    INSERT INTO #ExpectedJobs (OrderBy, WhenShouldRun, Job, DealerGroupId)
    SELECT 
        t.OrderBy + ROW_NUMBER() OVER (ORDER BY dg.Id) AS OrderBy,
        t.WhenShouldRun AS WhenShouldRun, 
        t.Job AS Job,
        dg.Id AS DealerGroupId
    FROM DealerGroup dg
    LEFT JOIN #TempJobsHolder t ON 1 = 1
    WHERE dg.Id IN (SELECT value FROM STRING_SPLIT(@lithia_Jobs, ','));

    WITH RecentJobs AS  
    (  
        SELECT LM.*,  
                ROW_NUMBER() OVER (PARTITION BY LM.Job ORDER BY LM.SourceDate DESC) AS rn  
        FROM LogMessages LM
        WHERE finishDate > dateadd(hour,9,DATEDIFF(d,0,getdate()-1))   
    ),
    
	-- Rank them to prevent duplicate entries in email
	RankedResults AS (
		SELECT 
			CONCAT(dg.Description, ':', e.Job) as Job,  
			IIF(lm.Job IS NULL, 'Not Found', IIF(lm.ErrorCount > 0, 'Errors', 'Ok')) as Status,
			ROW_NUMBER() OVER (PARTITION BY CONCAT(dg.Description, ':', e.Job) ORDER BY e.OrderBy ASC) as RowNum
		FROM #ExpectedJobs e  
		LEFT JOIN RecentJobs lm ON lm.Job = e.Job
		LEFT JOIN DealerGroup dg ON e.DealerGroupId = dg.Id
	)

    SELECT 
    Job,
    Status
	INTO #Results
	FROM RankedResults
	WHERE RowNum = 1
	ORDER BY Job;

    SELECT STRING_AGG(Job,', ') AS FailedList, 'Stockpulse US - Morning end to end summary' AS emailSubject FROM #Results where Status <> 'Ok' 

    DROP TABLE #ExpectedJobs  
    DROP TABLE #Results  
    DROP TABLE #TempJobsHolder

END  
  
GO

--SELECT TOP 10 * FROM LogMessages
--SELECT * FROM DealerGroup