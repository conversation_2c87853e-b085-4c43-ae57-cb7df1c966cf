﻿using System;

namespace StockPulse.WebApi.ViewModel
{
    public class ScanRegDifference
    {
        public int ScanId { get; set; }
        public string RegPerStockList { get; set; } //this can only ever be created for items that are both scanned and instock
        public string RegPerScan { get; set; }
        public string VinPerStockList { get; set; } //this can only ever be created for items that are both scanned and instock
        public string VinPerScan { get; set; }
        public bool RegMatches { get; set; }
        public string LocationDescription { get; set; }
        public string ScannerName { get; set; }
        public DateTime ScanDateTime { get; set; }
    }
}
