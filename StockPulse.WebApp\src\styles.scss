@use 'node_modules/@ngneat/hot-toast/src/styles/styles.scss';
@import "../node_modules/bootstrap/scss/bootstrap.scss";

@import 'styles/global/colours';
@import 'styles/global/fontSizes';
@import 'styles/global/helpers';
@import 'styles/global/variables';

@import 'styles/components/button';
@import 'styles/components/card';
@import 'styles/components/checkbox';
@import 'styles/components/chip';
@import 'styles/components/dropdown';
@import 'styles/components/fileUpload';
@import 'styles/components/grid';
@import 'styles/components/modal';
@import 'styles/components/popover';
@import 'styles/components/textarea';
@import 'styles/components/print';
@import 'styles/components/table';

@import "~ag-grid-community/styles/ag-grid.css";
@import "~ag-grid-community/styles/ag-theme-balham.css";

@import '~animate.css/animate.min.css';

//Fonts
@font-face {
  font-family: 'NumberPlate';
  font-style: normal;
  font-weight: normal;
  src: url('assets/fonts/CONSOLA.ttf');
}

@font-face {
  font-family: 'VIN';
  font-style: normal;
  font-weight: normal;
  src: url('assets/fonts/IBMPlexMono-Regular.ttf');
}

.content-new {
  position: absolute;
  display: flex;
  flex-direction: column;
  top: 35px;
  left: 10px;
  right: 0;
  bottom: 0;
  padding: 1em;
  overflow-x: hidden;
  overflow-y: auto;
  transition: all 0.5s;

  @media (max-width: 1680px) {
    top: 30px;
    left: 30px;
  }

  @media (max-width: 1280px) {
    top: 25px;
    left: 25px;
  }
}

.fixSideMenu .content-new {
  left: 220px;

  @media (max-width: 1680px) {
    left: 190px;
  }

  @media (max-width: 1280px) {
    left: 160px;
  }
}

nav.page-specific-navbar {
  position: fixed;
  display: flex;
  align-items: center;
  top: 0;
  left: 220px;
  right: 150px;
  height: 35px;
  z-index: 2;
  color: var(--bodyColour);

  @media (max-width: 1680px) {
    height: 30px;
  }

  @media (max-width: 1280px) {
      height: 25px;
  }

  .buttonGroup {
    display: flex;
  }

  button {
      height: 25px;
      // border-radius: 0 !important;
      line-height: 1;
      display: flex;
      align-items: center;

      @media (max-width: 1680px) {
        height: 22px;
      }
    
      @media (max-width: 1280px) {
          height: 20px;
      }
  }

  .page-title {
      margin-right: 3em;
  }
}

.fixSideMenu nav.page-specific-navbar {
  left: 400px;

  @media (max-width: 1680px) {
    left: 370px;
  }

  @media (max-width: 1280px) {
    left: 340px;
  }
}
