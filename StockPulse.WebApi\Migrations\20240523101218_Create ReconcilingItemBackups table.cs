﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace StockPulse.WebApi.Migrations
{
    /// <inheritdoc />
    public partial class CreateReconcilingItemBackupstable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "ReconcilingItemBackups",
                schema: "dbo",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    StockCheckId = table.Column<int>(type: "int", nullable: false),
                    ReconcilingItemTypeId = table.Column<int>(type: "int", nullable: false),
                    FileName = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ReconcilingItemBackups", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ReconcilingItemBackups_ReconcilingItemTypes_ReconcilingItemTypeId",
                        column: x => x.ReconcilingItemTypeId,
                        principalSchema: "dbo",
                        principalTable: "ReconcilingItemTypes",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_ReconcilingItemBackups_StockChecks_StockCheckId",
                        column: x => x.StockCheckId,
                        principalSchema: "dbo",
                        principalTable: "StockChecks",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_ReconcilingItemBackups_ReconcilingItemTypeId",
                schema: "dbo",
                table: "ReconcilingItemBackups",
                column: "ReconcilingItemTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_ReconcilingItemBackups_StockCheckId",
                schema: "dbo",
                table: "ReconcilingItemBackups",
                column: "StockCheckId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ReconcilingItemBackups",
                schema: "dbo");
        }
    }
}
