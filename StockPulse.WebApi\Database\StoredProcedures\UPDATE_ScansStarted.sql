﻿
/****** Object:  StoredProcedure [dbo].[UPDATE_ScansStarted]    Script Date: 30/04/2021 10:30:35 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


  
CREATE OR ALTER PROCEDURE [dbo].[UPDATE_ScansStarted]  
(  
    @StockCheckId INT = NULL,  
    @UserId INT = NULL  
)  
AS  
BEGIN  
  
SET NOCOUNT ON;  
  

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
	RETURN
END

  
UPDATE StockChecks  
SET StatusId = 2  
WHERE StockChecks.StatusId = 1  
AND Id = @StockCheckId  

-- If status was updated, log it
IF @@ROWCOUNT > 0
    BEGIN
        EXEC dbo.ADD_StatusChangeLogItem 
            @StockCheckId = @StockCheckId,
            @UserId = @UserId,
            @StatusId = 2;
    END
  
  
END  
  
GO


