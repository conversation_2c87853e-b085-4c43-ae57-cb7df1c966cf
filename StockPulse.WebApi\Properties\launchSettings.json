{"iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:62911", "sslPort": 44322}}, "$schema": "http://json.schemastore.org/launchsettings.json", "profiles": {"development": {"commandName": "IISExpress", "launchBrowser": false, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "development"}}, "test": {"commandName": "IISExpress", "launchBrowser": false, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "test"}}, "Production": {"commandName": "IISExpress", "launchBrowser": false, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Production"}}, "US-Production": {"commandName": "IISExpress", "launchBrowser": false, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "USProduction"}}, "US-Developement": {"commandName": "IISExpress", "launchBrowser": false, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "USDevelopment"}}}}