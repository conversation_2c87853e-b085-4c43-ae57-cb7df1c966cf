﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using PlateRecognizer;
using StockPulse.WebApi.Attribute;
using StockPulse.WebApi.Service;
using StockPulse.WebApi.ViewModel;
using System;
using System.Threading.Tasks;
using static StockPulse.WebApi.Attribute.UserLevelAccessAttribute;

namespace StockPulse.WebApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    [UserLevelAccess(new[] { UserRole.SysAdministrator, UserRole.Reconciler, UserRole.Resolver, UserRole.Scanner, UserRole.Approver, UserRole.GeneralManager, UserRole.ReadOnly, UserRole.ScanAndPrint, UserRole.ScanAndView })]
    public class RecogniserController : ControllerBase, IAttributeValueProvider
    {
        private readonly IRecogniseService recogniseService;
        private readonly IGlobalParamService globalParamService;
        private readonly IUserService userService;
        private readonly int userId;
        private readonly string userRole;

        public UserRole attributeUserRole => GetUserRole();
        private UserRole GetUserRole()
        {
            return (UserRole)Enum.Parse(typeof(UserRole), userRole);
        }

        public RecogniserController(IRecogniseService recogniseService, IUserService userService, IGlobalParamService globalParamService)
        {
            this.recogniseService = recogniseService;
            this.userService = userService;
            userId = userService.GetUserId();
            userRole = userService.GetUserRole();
            this.globalParamService = globalParamService;
        }


        ///this route is only used if we are interpreting from locally saved
        [HttpPost]
        [Route("InterpretReg")]
        public async Task<RegResult> InterpretReg(
           [FromForm(Name = "ImageFile")] IFormFile imageFile,
           [FromForm(Name = "Priority")] string priority
           )
        {
            int dealerGroupId = await userService.GetDealerGroupIdFromCache();
            string plateType = await globalParamService.GetPlateTypeFromCacheAsync(dealerGroupId);
            return await recogniseService.RecogniseImage(imageFile, priority, userId, plateType, true);
        }

        ///this route is used if we are interpreting from the scan page itself
        [HttpPost]
        [Route("InterpretRegAndCheckIfSeenBefore")]
        public async Task<RegResult> InterpretRegAndCheckIfSeenBefore(
            [FromForm(Name = "ImageFile")] IFormFile imageFile, 
            [FromForm(Name = "Priority")] string priority,
            [FromForm(Name = "StockCheckId")] int stockCheckId
            )
        {
            int dealerGroupId = await userService.GetDealerGroupIdFromCache();
            string plateType = await globalParamService.GetPlateTypeFromCacheAsync(dealerGroupId);
            return await recogniseService.RecogniseImageAndCheckIfDuplicate(imageFile, priority, stockCheckId,userId, plateType);
        }

        
        ///this route is only used if we are interpreting from locally saved
        [HttpPost]
        [Route("InterpretVin")]
        public async Task<VinResult> InterpretVin(VinToInterpret payload)
        {
            return await recogniseService.RecogniseVin(payload,true);
        }



        ///this route is used if we are interpreting from the scan page itself
        [HttpPost]
        [Route("InterpretVinAndCheckIfSeenBefore")]
        public async Task<VinResult> InterpretVinAndCheckIfSeenBefore(VinToInterpret payload, int stockCheckId)
        {
            return await recogniseService.RecogniseVinAndCheckIfSeenBefore(payload,stockCheckId,userId);
        }

    }


}
