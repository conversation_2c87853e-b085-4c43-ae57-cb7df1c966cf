﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace StockPulse.Model
{
    public class UserSite
    {
        [Key]
        public int Id { get; set; }
        
        

        //foreign keys "many in this table to one in the other table"
        public int UserId { get; set; }
        [ForeignKey("UserId")]
        public virtual User User { get; set; }

        public int SiteId { get; set; }
        [ForeignKey("SiteId")]
        public virtual Site Site { get; set; }

        public bool IsDefault { get; set; }



    }
}