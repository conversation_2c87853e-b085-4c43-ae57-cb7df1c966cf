import { ImageToUpdate } from "./ImageToUpdate";
import { Scan } from "./Scan";
import { StockItem } from "./StockItem";



export interface ItemFullDetail {
  stockItem: StockItem;
  scan: Scan;

  //Site that it relates to
  siteName: string;
  

  //Reconciling item things
  reconcilingItemId: number;
  reconcilingItemTypeId: number;
  reconcilingItemTypeDescription: string;
  reconcilingItemReg: string;
  reconcilingItemVin: string;
  reconcilingItemDesc: string;
  reconcilingItemComment: string;
  reconcilingItemRef: string;



  //Resolution things
  resolutionId: number;
  resolutionTypeId: number;
  resolutionTypeDescription: string;
  resolutionTypeBackup: string;
  isResolved: boolean;
  resolvedBy: string;
  resolutionDate: Date | string | null;
  resolutionNotes: string;
  resolutionImageIds: string;



  //Matching Site name stuff
  otherSiteName: string;

  //Extra props if it is a duplicate
  originalId: number;
  originalLocationDescription: string;
  originalScannedBy: string;
  originalScannedDate: Date | string;
  originalStockType: string;
  originalComment: string;
  originalReference: string;

  resolutionImages:ImageToUpdate[]


  //client side props
  id: number;

  isRegEditedOnDevice: boolean;
  isRegEditedOnWeb: boolean;
  interpretedReg: string;
  isVinEditedOnDevice: boolean;
  isVinEditedOnWeb: boolean;
  interpretedVin: string;
}
