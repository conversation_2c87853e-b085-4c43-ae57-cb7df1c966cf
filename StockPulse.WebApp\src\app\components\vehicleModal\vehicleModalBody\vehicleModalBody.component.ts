import { HttpClient } from '@angular/common/http';
import { Component, ElementRef, HostListener, Input, OnInit, SimpleChanges, ViewChild } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { CphPipe } from 'src/app/cph.pipe';
import { ImageToUpdate } from 'src/app/model/ImageToUpdate';
import { ItemFullDetail } from 'src/app/model/ItemFullDetail';
import { ItemToOpen } from 'src/app/model/ItemToOpen';
import { ReconciliationState } from 'src/app/model/ReconciliationState';
import { Resolution } from 'src/app/model/Resolution';
import { ResolutionDeleteParams } from 'src/app/model/ResolutionDeleteParams';
import { ResolutionType } from 'src/app/model/ResolutionType';
import { ApiAccessService } from 'src/app/services/apiAccess.service';
import { ConstantsService } from 'src/app/services/constants.service';
import { GetDataService } from 'src/app/services/getData.service';
import { IconService } from 'src/app/services/icon.service';
import { ToastService } from 'src/app/services/newToast.service';
import { SaveDataService } from 'src/app/services/saveData.service';
import { SelectionsService } from 'src/app/services/selections.service';
import { Location } from "../../../model/Location";
import { VehicleModalService } from '../vehicleModal.service';
import * as confetti from 'canvas-confetti';

interface FileWithBase64 extends File {
  base64: string;
}

@Component({
  selector: 'vehicleModalBody',
  templateUrl: './vehicleModalBody.component.html',
  styleUrls: ['./vehicleModalBody.component.scss']
})
export class VehicleModalBodyComponent implements OnInit {

  //@ViewChild('vehicleModalRef', { static: true }) vehicleModalRef: ElementRef;
  @ViewChild('zoomImageModal', { static: true }) zoomImageModal: ElementRef;
  @ViewChild('changeLocationModal', { static: true }) changeLocationModal: ElementRef;
  originalItem: ItemFullDetail;
  // @ViewChild('newRegBox', { static: false, read: ElementRef }) set content(content: ElementRef) {  //doesn't seem to work
  //   if (content) { // initially setter gets called with undefined
  //     this.newRegBox = content;
  //   }
  // }


  // newRegBox: ElementRef<any>;
  //modalRef: Promise<void>;
  //modalIsOpen: boolean;



  //@ViewChild('foo', { static: true }) foo: ElementRef;

  //@Input() isStockItem: boolean;
  @Input() item: ItemFullDetail;
  @Input() showCompact: boolean;


  states = ReconciliationState
  imagesURLs: ImageToUpdate[] = [];

  //changedScanEmitter: EventEmitter<boolean>;

  chosenLocation: string = null;

  modalImage: string;
  //blobImageURL: string;

  showRegImage: boolean = true;
  //amEditingReg: boolean = false;
  //amEditingVin: boolean = false;
  newReg: string;
  newVin: string;
  //public filesChosen: boolean;
  files: FileWithBase64[] 
  public fileName: any;
  //public fileChosen: any;//what is this?
  //originallyLoadedId: number;
  //idsToNavigateBetween: ItemToOpen[];
  //isStockOriginal: boolean;
  mapOptions: any;
  //changesMadeToResolution:boolean = false;

  //@HostListener('window:keydown', ['$event'])
  // onKeyDown(event: KeyboardEvent) {
  //   this.handleKeyboardEvent(event);
  // }


  //apiLoaded: Observable<boolean>;
  //location: string;
  amSavingReg: boolean;
  amSavingVin: boolean;
  fileSizeExceeded: boolean;

  constructor(
    // public vehicleModal: VehicleModalService,
    public constants: ConstantsService,
    public selections: SelectionsService,
    //public data: GetDataService,
    public modalService: NgbModal,
    //public cphPipe: CphPipe,
    public icon: IconService,
    //public save: SaveDataService,
    public apiAccessService: ApiAccessService,
    //public http: HttpClient,
    public toastService: ToastService,
    public service: VehicleModalService
  ) { }



  ngOnInit() {
    // this.changedScanEmitter = new EventEmitter(); //create it, but don't use it.  is designed to update the details within the scanWithPlatebox component
    this.initParams();

    this.originalItem = this.constants.clone(this.item)
    this.item.id = this.isStockItem() ? this.item.stockItem.stockItemId : this.item.scan.scanId;
    this.mapOptions = this.setMapOptions(this.item);

  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.item) {
      this.initParams();
    }
  }

  isStockItem(){
    return !!this.item.stockItem
  }


  initParams() {
    //this.item = null;
    this.showRegImage = true;
    this.service.amEditingReg = false;
    this.service.amEditingVin = false;
    this.imagesURLs = [];
    this.newReg = this.item.scan?.scanReg;
    this.newVin = this.item.scan?.scanVin;
    this.files = [];
    this.fileSizeExceeded=false;
    //this.changesMadeToResolution = false;
  }


 



  // private handleKeyboardEvent(event: KeyboardEvent) {
  //   if (!this.modalIsOpen) { return; }
  //   if (!this.idsToNavigateBetween || this.idsToNavigateBetween.length < 1) { return; }


  //   //let currentVehicleIndex: number = this.idsToNavigateBetween.map(x => x.id).indexOf(this.item.id);

  //   // if (event.key === 'ArrowDown') {
  //   //   if (currentVehicleIndex + 1 < this.idsToNavigateBetween.length) {
  //   //     //this.modalService.dismissAll();
  //   //     const newItem = this.idsToNavigateBetween[currentVehicleIndex + 1]
  //   //     this.getDataAndShowModal(newItem.id,newItem.isStockItem,  false);
  //   //   }
  //   // }

  //   // if (event.key === 'ArrowUp') {
  //   //   if (currentVehicleIndex >= 0) {
  //   //     const newItem = this.idsToNavigateBetween[currentVehicleIndex - 1]
  //   //     //this.modalService.dismissAll();
  //   //     this.getDataAndShowModal( newItem.id, newItem.isStockItem,false);
  //   //   }
  //   // }

  // }


  // public loadItemAndOpenModal(isStockItem: boolean, id: number, allIdsInList: ItemToOpen[]) {

  //   this.isStockItem() = isStockItem;
  //   this.idsToNavigateBetween = allIdsInList;
  //   this.getDataAndShowModal(id, isStockItem, true);

  // }


  private reloadContent(id: number, isStockItem: boolean) {

    const payload = [
      { key: 'stockCheckId', value: this.selections.stockCheck.id },
      { key: 'stockItemId', value: isStockItem ? id : 0 },
      { key: 'scanId', value: isStockItem ? 0 : id },
    ]
    this.apiAccessService.get('StockItems', 'GetItemFullDetail', payload).subscribe((res: ItemFullDetail) => {
      if (!!res.scan && !!res.scan.scanId) {
        //stockitem has a scan so generate the image url
        this.constants.addImageStringsToScan(res.scan)
      }
      this.item = res;
      this.item.id = this.isStockItem() ? this.item.stockItem.stockItemId : this.item.scan.scanId;
      this.originalItem = this.constants.clone(res)

      this.mapOptions = this.setMapOptions(this.item);
      

    })

  }


  checkForChangesMade(){
    if(this.files?.length>0){return true;}
    return this.item.resolutionImageIds != this.originalItem.resolutionImageIds ||
      this.item.resolutionNotes != this.originalItem.resolutionNotes ||
      this.item.resolutionTypeDescription != this.originalItem.resolutionTypeDescription ||
      this.item.isResolved != this.originalItem.isResolved ||
      JSON.stringify(this.item.resolutionImages) != JSON.stringify(this.originalItem.resolutionImages)
  }





  resolutionTypesForMissing() {
    return this.constants.ResolutionTypesActive.filter(e => e.explainsMissingVehicle).sort((a, b) => a.description.localeCompare(b.description));
  }

  resolutionTypesForUnknowns() {
    return this.constants.ResolutionTypesActive.filter(e => !e.explainsMissingVehicle).sort((a, b) => a.description.localeCompare(b.description));
  }


  // closeModal() {
  //   this.modalService.dismissAll();
  // }






  chooseResolution(problemResolution: ResolutionType) {
    this.item.resolutionTypeDescription = problemResolution.description;
    this.item.resolutionTypeBackup = problemResolution.backupRequired;
    //this.changesMadeToResolution =true;

  }


  public onFilePaste(event: ClipboardEvent) {
    event.preventDefault();
    let file = event.clipboardData.items[0].getAsFile();
    this.prepareFilesList([file]);
  }

  cancelEditProblemCarUpdate() {
    //TODO reload the thing    ?
    this.fileSizeExceeded = false;
    this.reloadContent(this.item.id, this.isStockItem());
  }



  deleteMissingResolutionImage(image: ImageToUpdate) {

    if (image.id) //Blob
    {
      image.status = "DELETE";
      const idx = this.item.resolutionImages.findIndex(f => f.id === image.id);
      this.item.resolutionImages[idx] = image;

      if (this.item.resolutionImages.filter(x => x.status !== 'DELETE').length === 0) this.item.isResolved = false;
    }
    else {
      const idx = this.item.resolutionImages.findIndex(f => f.fileBase64 === image.fileBase64);
      this.item.resolutionImages.splice(idx, 1);

      if (this.item.resolutionImages.length === 0) this.item.isResolved = false;
    }
  }

  deleteUnknownResolutionImage(image: ImageToUpdate) {

    if (image.id) //Blob
    {
      image.status = "DELETE";
      // const idx = this.editedUnknownResolution.images.findIndex(f => f.id === image.id);
      // this.editedUnknownResolution.images[idx] = image;
    }
    else {
      // const idx = this.editedUnknownResolution.images.findIndex(f => f.fileBase64 === image.fileBase64);
      // this.editedUnknownResolution.images.splice(idx, 1);
    }
  }



  zoomInBackup(image) {
    let imageFileTypes: string[] = ['png', 'jpg', 'jpeg', 'webp'];
    let imageFileExtension: string = image.fileName.slice(image.fileName.lastIndexOf('.') + 1);

    if (image.fileName === '' || imageFileTypes.includes(imageFileExtension) || image.fileName === 'FileName') {
      this.modalImage = image.url;
    } else {
      let link = document.createElement("a");
      link.href = image.url;
      link.download = image.fileName;
      link.click();
    }

  }


  saveResolution() {
    const savingToast = this.toastService.loadingToast('Saving resolution...');

    if(!this.item.resolutionTypeDescription){
      savingToast.close();
      this.toastService.errorToast('Please choose a resolution');
      return;
    }
    const controllerName = this.isStockItem() ? 'StockItems' : 'Scans';
    const methodName = this.isStockItem() ? 'SaveMissingResolution' : 'SaveUnknownResolution';

    //gather images to send
    const images:ImageToUpdate[] = [];
    
    //existing images from the item
    this.item.resolutionImages.forEach(image=>{
      images.push(image)
    })
    
    //newly uploaded files
    const newImages = this.convertFilesToImages();
    newImages.forEach(image=>{
      images.push(image);
    })


    //put any new images into array to save
    const payload: Resolution = {
      resolutionId: this.item.resolutionId,
      stockCheckId: this.selections.stockCheck.id,
      originalItemId: this.isStockItem() ? this.item.stockItem.stockItemId : this.item.scan.scanId,
      isResolved: this.item.isResolved,
      resolutionTypeId: this.constants.ResolutionTypes.find(x => x.description === this.item.resolutionTypeDescription && x.explainsMissingVehicle === this.isStockItem()).id,
      notes: this.item.resolutionNotes,
      images: images
    }

    this.apiAccessService.post(controllerName, methodName, payload).subscribe((data: any) => {
      this.toastService.successToast('Explanation Saved');
      if (this.item.isResolved) this.confettiExplosion();
      savingToast.close();
      setTimeout(() => {
        this.selections.stockCheckItemChanged.emit(true)
        let id = this.item.id;
        this.initParams();
        this.reloadContent(id,this.isStockItem());
        
      }, 200)

    }, error => {
      this.toastService.errorToast('Failed to save resolution');
      console.error('Failed to save resolution', error);
      savingToast.close();
    });
  }





  maybeDeleteResolution() {

    //do confirmation modal
    this.constants.alertModal.title = 'Really delete this problem resolution?'
    this.constants.alertModal.message = '';

    this.modalService.open(this.constants.alertModal.elementRef, { size: 'sm', keyboard: true, ariaLabelledBy: 'modal-basic-title' }).result.then((result) => {
      //have chosen to 'OK' selections

      this.goAheadAndDeleteResolution();

    }, (reason) => {
      //chose to cancel, do nothing
      return
    });



  }



  private goAheadAndDeleteResolution() {
    const controllerName = this.isStockItem() ? 'StockItems' : 'Scans';
    const payload:ResolutionDeleteParams = {ResolutionId:this.item.resolutionId, StockCheckId: this.selections.stockCheck.id}
    this.apiAccessService.post(controllerName, 'DeleteResolution', payload).subscribe((data: any) => {
      this.reloadContent(this.item.id,this.isStockItem());
      this.toastService.successToast('Explanation deleted');
      this.selections.stockCheckItemChanged.emit(true);
    });
  }

  cancelEditingReg() {
    this.service.amEditingReg = false;
    this.newReg = this.item.scan?.scanReg;
  }

  cancelEditingVin() {
    this.service.amEditingVin = false;
    this.newVin = this.item.scan?.scanVin;
  }

  editReg() {
    this.service.amEditingReg = true
  }

  editVin() {
    this.service.amEditingVin = true;
  }

  saveNewReg() {
    const savingToast = this.toastService.loadingToast('Saving...');

    let newReg: string = this.newReg.replace(/ /g, '').toUpperCase();
    let scanId: number = this.item.scan.scanId;// this.isStockItem() ? this.stockItem.scanId : this.scan.scanId;
    this.amSavingReg = true;

    this.apiAccessService.updateReg(scanId, newReg).subscribe(res => {
      this.toastService.successToast('Updated reg');

      this.item.scan.scanReg = newReg;

      this.selections.stockCheckItemChanged.next(true);
      this.newReg = newReg;
      this.service.amEditingReg = false;
      this.amSavingReg = false;
      savingToast.close();
      this.reloadContent(this.item.scan.scanId, false);
    }, e => {
      savingToast.close();
      this.toastService.errorToast('Failed to update reg');
    })
  }


  saveNewVin() {
    const savingToast = this.toastService.loadingToast('Saving...');

    let newVin: string = this.newVin.replace(/ /g, '').toUpperCase();
    let scanId: number = this.item.scan.scanId;// this.isStockItem() ? this.stockItem.scanId : this.scan.scanId;
    this.amSavingVin = true;

    this.apiAccessService.updateVin(scanId, newVin).subscribe(res => {
      this.toastService.successToast('Updated VIN');

      this.item.scan.scanVin = newVin;
      this.selections.stockCheckItemChanged.next(true);
      this.newVin = "";
      this.service.amEditingVin = false;
      this.amSavingVin = false;
      savingToast.close();
      this.reloadContent(this.item.scan.scanId, false); //reload data
    }, e => {
      savingToast.close();
      this.toastService.errorToast('Failed to update VIN');
    })
  }

  reviewLocation() {
    this.chosenLocation = this.item.scan.locationDescription;
    this.modalService.open(this.changeLocationModal, { keyboard: true, ariaLabelledBy: 'modal-basic-title' }).result.then((result) => {
      this.saveNewLocation();
    })
  }

  saveNewLocation() {
    this.apiAccessService.updateLocation(this.item.scan.scanId, this.constants.Locations.find(x => x.description === this.chosenLocation).id).subscribe(res => {
      //ok it's updated
      this.item.scan.locationDescription = this.chosenLocation;
      this.constants.refreshPage.emit(true)
      //this.scanIdToUpdate.emit({ scanId: this.item.scan.scanId, field: 'locationDescription', newValue: this.chosenLocation });
      //this.selections.changesMadeToScans = true;
    }, e => {
      this.toastService.errorToast('Failed to update scan location');
    })

  }

  onFileDropped(event: File[]) {
    this.prepareFilesList(event);
  }

  fileBrowseHandler(event: any) {
    const files: File[] = event.target.files;
    this.prepareFilesList(files);
  }



  prepareFilesList(files: File[]) {
    for (let item of files) {
      if (item.size > 10000000) { // 10mb limit
        this.fileSizeExceeded = true;
      } else {
        let reader = new FileReader();
        reader.onload = (e) => {
          let base64 = e.target.result as string;
          let object: FileWithBase64 = Object.assign(item, { base64: base64 });
          this.files.push(object);
        }
        reader.readAsDataURL(item);
      }
    }
  }

  formatBytes(bytes: number, decimals: number = 2) {
    if (bytes === 0) {
      return "0 Bytes";
    }
    const k = 1024;
    const dm = decimals <= 0 ? 0 : decimals;
    const sizes = ["Bytes", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + " " + sizes[i];
  }

  deleteFile(index: number) {
    this.files.splice(index, 1);
    if (this.files.length === 0) this.item.isResolved = false;
  }

  convertFilesToImages() :ImageToUpdate[]{
    const images:ImageToUpdate[] = [];//if (!this.item.resolutionImages) { this.item.resolutionImages = []; }
    this.files.forEach(file => {
      images.push({ status: "ADD", fileBase64: file.base64, id: null, url: '', fileName: file.name })
    })

    return images;
  }


  getResolutionTypes() {
    return this.isStockItem() ? this.resolutionTypesForMissing() : this.resolutionTypesForUnknowns();
  }


  chooseLocation(location: Location) {
    this.chosenLocation = location.description;
  }

  isDuplicate() {
    if (this.item.stockItem?.state == ReconciliationState.Duplicate) { return true; }
    if (this.item.scan?.scanState == ReconciliationState.Duplicate) { return true; }
    return false;
  }

  stockMatchesScan() {
    if (this.item.stockItem?.state === ReconciliationState.MatchedToStockOrScan) { return true; }
    return false;
  }

  itemMatchesIntercompany() {
    if (this.item.stockItem?.state === ReconciliationState.MatchedToOtherSite) { return true; }
    return false;
  }

  isMatchedToReconcilingItem() {
    if (this.item.stockItem?.state === ReconciliationState.MatchedToReport) { return true; }
    if (this.item.scan?.scanState === ReconciliationState.MatchedToReport) { return true; }
    return false;
  }

  public vehicleIsAnIssue() {
    if (this.item.stockItem?.state === ReconciliationState.OutstandingIssue) { return true; }
    if (this.item.stockItem?.state === ReconciliationState.Resolved) { return true; }
    if (this.item.scan?.scanState === ReconciliationState.OutstandingIssue) { return true; }
    if (this.item.scan?.scanState === ReconciliationState.Resolved) { return true; }
    return false;
  }

 

  showBackupRow() {
    const doShowBackupSetting = this.constants.GlobalParams.find(x => x.name === 'doShowBackupRequiredRow');
    return this.item.resolutionTypeDescription && doShowBackupSetting && doShowBackupSetting.boolValue;
  }


  setMapOptions(item: ItemFullDetail) {
    return {
      //mapId: "d8d9661ea2c51780",
      center: {
        lat: item.scan?.latitude,
        lng: item.scan?.longitude
      },
      zoom: 16,
      disableDefaultUI: true,
      streetViewControl: false,
      styles: [
        {
          featureType: "poi.business",
          stylers: [{ visibility: "off" }]
        },
        {
          featureType: "transit",
          elementType: "labels.icon",
          stylers: [{ visibility: "off" }]
        }
      ]
    }
  }

  getThumbnail(file: any) {
    if (file.base64.includes('data:image')) {
      return file.base64;
    } else {
      return this.imageForFileExtensions(file.name);
    }
  }

  updateUrl(imageIndex: number, fileName: string) {
    let imageElement: HTMLImageElement = document.getElementById(`backup-image-${imageIndex}`) as HTMLImageElement;
    imageElement.src = this.imageForFileExtensions(fileName);
  }

  imageForFileExtensions(nameOrSrc: string) {
    if (nameOrSrc.includes('.pdf')) {
      return './assets/imgs/backup-pdf.png';
    } else if (nameOrSrc.includes('.docx')) {
      return './assets/imgs/backup-word.png';
    } else if (nameOrSrc.includes('.xl') || nameOrSrc.includes('.csv')) {
      return './assets/imgs/backup-excel.png';
    } else {
      return './assets/imgs/backup-file.png';
    }
  }

  thereAreFiles(){
    if(this.files && this.files.length>0){return true;}
    if(this.item.resolutionImages && this.item.resolutionImages.length>0){return true;}
    return false;

  }

  instructionRowMessage(){
    return `Add files with the button below (10mb or less in size) or by dragging and dropping a file in this area.  You can also use the screen snipping tool by pressing windows key + shift + s, 
    dragging around an area on screen then pressing ctrl + v in this area to attach a screen snip.    The StockPulse mobile app allows you to add backup using your device's camera. 
    ** Please take care to obscure customer personal data from uploads ** `
  }

  canResolve() {
    if(this.selections.stockCheck.statusId > 3){return false;}

    if (this.isStockItem()){
      if (!this.constants.requireBackupForMissingResolution) {return true;}
    }
    else {
      if (!this.constants.requireBackupForUnknownResolution) {return true;}
    }

    if (this.item.resolutionImages && this.item.resolutionImages.length > 0 && this.item.resolutionImages.filter(x => x.status !== 'DELETE').length > 0) return true;
    if (this.files && this.files.length > 0) return true;
    return false;
  }
  confettiExplosion(): void {
    const canvas = document.createElement('canvas');
    canvas.classList.add('confetti-canvas');
    document.body.appendChild(canvas);

    const confettiCanvas = confetti.create(canvas, {
      resize: true
    });

    this.playAudio();

    confettiCanvas({
      particleCount: 100,
      spread: 160
    })

    setTimeout(() => {
      document.body.removeChild(canvas);
    }, 1000)
  }

  playAudio(){
    let audio: HTMLAudioElement = new Audio();
    audio.src = "../../../assets/audio/success.mp3";
    audio.load();
    audio.play();
  }

  shouldShowReg() {
    return !this.constants.GlobalParams.find(x => x.name === 'NeverScanReg').boolValue;
  }
}
