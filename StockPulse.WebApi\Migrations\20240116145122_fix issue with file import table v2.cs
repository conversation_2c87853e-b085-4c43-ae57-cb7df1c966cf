﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace StockPulse.WebApi.Migrations
{
    /// <inheritdoc />
    public partial class fixissuewithfileimporttablev2 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Id",
                schema: "import",
                table: "FileImports");

            migrationBuilder.AlterColumn<int>(
                name: "FileImportId",
                schema: "import",
                table: "StockItems",
                type: "int",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "int");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<int>(
                name: "FileImportId",
                schema: "import",
                table: "StockItems",
                type: "int",
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true);

            migrationBuilder.AddColumn<int>(
                name: "Id",
                schema: "import",
                table: "FileImports",
                type: "int",
                nullable: false,
                defaultValue: 0);
        }
    }
}
