﻿/****** Object:  StoredProcedure [dbo].[UPDATE_StockCheckStatus]    Script Date: 01/05/2021 20:06:53 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].[UPDATE_StockCheckStatus]
(
    @UserId INT = NULL,
	@StockCheckId INT = NULL,
    @NewStatusId INT = NULL	
)
AS
BEGIN

SET NOCOUNT ON;


IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END

--Check if allowed to update
DECLARE @userCanDoThis INT;
set @userCanDoThis = dbo.[UserCanMoveStatusTo](@NewStatusId,@UserId,@StockCheckId)

if @userCanDoThis = 1

BEGIN 

	--Get existing approvedByAccountant
	DECLARE @existingApprovedByAccountantId int;
	set @existingApprovedByAccountantId = (select ApprovedByAccountantId from dbo.[stockchecks] where id = @StockCheckId)

	--Get existing approvedBy
	DECLARE @existingApprovedById int;
	set @existingApprovedById  = (select ApprovedById from dbo.[stockchecks] where id = @StockCheckId)
										
	update dbo.[StockChecks] set [StatusId] = @NewStatusId WHERE [Id] = @StockCheckId 

	--set approved by accountant if required
	if @NewStatusId > 3 AND @existingApprovedByAccountantId is null
		BEGIN
			update dbo.[StockChecks]
			set [ApprovedByAccountantId] = @UserId, [ReconciliationCompletedDate] = GETUTCDATE()
			where id = @StockCheckId
		END

	--set approved if required
	if @NewStatusId > 4 AND @existingApprovedById is null
		BEGIN
			update dbo.[StockChecks]
			set [ApprovedById] = @UserId, [ReconciliationApprovedDate] = GETUTCDATE()
			where id = @StockCheckId
		END

	--unset approved if required
	if @NewStatusId <5 AND @existingApprovedById is not null
		BEGIN
			update dbo.[StockChecks]
			set [ApprovedById] = null, [ReconciliationApprovedDate] = null
			where id = @StockCheckId
		END

	--unset approvedByAccountant if required
	if @NewStatusId <4 AND @existingApprovedByAccountantId is not null
		BEGIN
			update dbo.[StockChecks]
			set [ApprovedByAccountantId] = null, [ReconciliationCompletedDate] = null
			where id = @StockCheckId
		END

					
	EXEC [dbo].[UPDATE_StockCheckLastUpdate] @StockCheckId, @UserId

END




END

