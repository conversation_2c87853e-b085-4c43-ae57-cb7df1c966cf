﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].[DELETE_AllReconcilingItems]
(
    @StockCheckId INT = NULL,
    @UserId INT = NULL,
    @ReconcilingItemTypeId INT = NULL
)
AS
BEGIN

SET NOCOUNT ON;

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END

	
--Firstly have to remove any references to these rec items in stockItems and scans
UPDATE StockItems set ReconcilingItemId = null 
where ReconcilingItemId in 
	(select Id from ReconcilingItems where StockCheckId =  @StockCheckId AND ReconcilingItemTypeId = @ReconcilingItemTypeId)

UPDATE Scans set ReconcilingItemId = null 
where ReconcilingItemId in 
	(select Id from ReconcilingItems where StockCheckId =  @StockCheckId AND ReconcilingItemTypeId = @ReconcilingItemTypeId)

--Now can remove
DELETE FROM ReconcilingItems 
WHERE StockCheckId =  @StockCheckId
AND ReconcilingItemTypeId = @ReconcilingItemTypeId

EXEC [dbo].[UPDATE_StockCheckLastUpdate] @StockCheckId, @UserId


END

GO


