import { Injectable } from '@angular/core';
import { HttpRequest, HttpHandler, HttpEvent, HttpInterceptor } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ConstantsService } from './services/constants.service';
import { environment } from '../environments/environment'
import version from './../../package.json'


@Injectable()
export class JwtInterceptor implements HttpInterceptor {
    constructor(
        private constants: ConstantsService
    ) { }

    intercept(request: HttpRequest<any>, next: <PERSON>ttp<PERSON>and<PERSON>): Observable<HttpEvent<any>> {
        // add authorization header with jwt token if available


        request = request.clone({
            setHeaders: {
                Authorization: `Bearer ${this.getAccessToken()}`,
                WebVersion: version.version,
                DealerGroupName: this.constants.getDealerGroupName()
            }
        });


        return next.handle(request);
    }

    



    public getAccessToken() {

        if (!this.constants.userLoggedInThroughAzureAD) { // local login
            return this.constants.accessToken;
        } else {
            if (document.referrer.endsWith('/auth')) {
                setTimeout(() => {
                    //this.router.navigateByUrl("/home");
                    window.location.href = window.location.origin;
                    //window.location.reload();
                }, 1500); // added this as it takes a bit time to get the token from azure
            }


            for (let index = 0; index < localStorage.length; index++) {
                const elementJson = localStorage.getItem(localStorage.key(index));
                try {
                    const element = JSON.parse(elementJson);
                    if (element.credentialType == "IdToken") {
                        return element.secret;
                    }
                } catch {}
            }
        }

    }



}