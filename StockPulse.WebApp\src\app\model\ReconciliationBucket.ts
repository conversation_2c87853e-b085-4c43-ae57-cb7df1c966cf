import { ReconciliationState } from "./ReconciliationState";





export interface ReconciliationBucket {
  description: string;
  vehicleCount: number;
  inStockValue: number;
  flooring: number;
  isFullHeight: boolean;
  order: number;
  isProblem: boolean;
  isStock: boolean;
  isScan: boolean;
  //IsReconciling: boolean;
  reconciliationTypeId: number | null;
  reconciliationState:ReconciliationState
}
