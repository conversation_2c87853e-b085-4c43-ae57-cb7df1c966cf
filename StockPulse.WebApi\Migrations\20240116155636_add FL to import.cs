﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace StockPulse.WebApi.Migrations
{
    /// <inheritdoc />
    public partial class addFLtoimport : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "FinancialLines",
                schema: "import",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false),
                    Code = table.Column<int>(type: "int", nullable: true),
                    AccountDescription = table.Column<string>(type: "nvarchar(250)", maxLength: 250, nullable: true),
                    Balance = table.Column<decimal>(type: "decimal(15,3)", nullable: false),
                    FileImportId = table.Column<int>(type: "int", nullable: true)
                },
                constraints: table =>
                {
                    table.ForeignKey(
                        name: "FK_FinancialLines_FileImports_FileImportId",
                        column: x => x.FileImportId,
                        principalSchema: "import",
                        principalTable: "FileImports",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateIndex(
                name: "IX_FinancialLines_FileImportId",
                schema: "import",
                table: "FinancialLines",
                column: "FileImportId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_FinancialLines_FileImports_FileImportId",
                schema: "dbo",
                table: "FinancialLines");

            migrationBuilder.DropTable(
                name: "FinancialLines",
                schema: "import");
        }
    }
}
