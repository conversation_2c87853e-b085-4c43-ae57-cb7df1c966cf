﻿using Microsoft.Extensions.Configuration;

namespace StockPulse.Loader.Services
{
    public static class ConfigService
    {
        //private static IConfigurationBuilder configurationBuilder;
        public static IConfigurationBuilder configurationBuilder { get; set; }


        public static void Init()
        {
            var configuration = configurationBuilder.Build();
            var connections = configuration.GetSection("ConnectionStrings");

            connectionNameUK = connections["DefaultConnection"];
            connectionNameUS = connections["USConnection"];

            var settings = configuration.GetSection("AppSettings");
            var emailSettings = configuration.GetSection("EmailSettings");

            mailAppTenantId = emailSettings["mailAppTenantId"];
            mailAppId = emailSettings["mailAppId"];
            mailSecretValue = emailSettings["mailSecretValue"];
            mailAccountInbound = emailSettings["mailAccountInbound"];
            mailAccountOutbound = emailSettings["mailAccountOutbound"];
            incomingRoot = settings["incomingRoot"];
            isDev = settings["isDev"] == "true";
            isUS = settings["isUS"] == "true";
            overrideRunJobNow = settings["overrideRunJobNow"];
            stockPulseLoaderJob = settings["stockPulseLoaderJob"];
            lihiaFTPFileFetchJob = settings["lihiaFTPFileFetchJob"];
        }


        //connection stuff

        public static string connectionNameUK { get; set; }
        public static string connectionNameUS { get; set; }

        //email settings
        public static string mailAppTenantId { get; set; }
        public static string mailAppId { get; set; }

        public static string mailSecretValue { get; set; }
        public static string mailAccountInbound { get; set; }
        public static string mailAccountOutbound { get; set; }
     
        public static string incomingRoot { get; set; }
        public static bool isDev { get; set; }
        public static bool isUS { get; set; }

        public static string overrideRunJobNow { get; set; }
        public static string stockPulseLoaderJob { get; set; }
        public static string lihiaFTPFileFetchJob { get; set; }

    }
}
