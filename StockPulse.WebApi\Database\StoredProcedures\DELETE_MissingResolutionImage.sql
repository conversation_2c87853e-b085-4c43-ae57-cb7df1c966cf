﻿
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].[DELETE_MissingResolutionImage]
(
    @StockCheckId INT,
	@UserId INT,
	@MissingResolutionId INT,
	@ImageId INT
)
AS
BEGIN

SET NOCOUNT ON;

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END

DELETE [dbo].[MissingResolutionImages]
WHERE Id = @ImageId AND MissingResolutionId = @MissingResolutionId


EXEC [dbo].[UPDATE_StockCheckLastUpdate] @StockCheckId, @UserId


END


GO


