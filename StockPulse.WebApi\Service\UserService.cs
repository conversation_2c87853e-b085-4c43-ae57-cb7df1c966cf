﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using StockPulse.WebApi.Auth.Model;
using StockPulse.WebApi.Auth.Service;
using StockPulse.WebApi.DataAccess;
using StockPulse.Model;
using StockPulse.WebApi.ViewModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Text.RegularExpressions;
using Microsoft.Extensions.Primitives;
using Microsoft.IdentityModel.Tokens;

namespace StockPulse.WebApi.Service
{
   public interface IUserService
   {
      Task<int> CreateUser(int userId, User user, int dealergroup);
      string GeneratePassword(bool useLowercase, bool useUppercase, bool useNumbers, int passwordSize);
      Task SetSites(int userId, int siteId, string sitesString, int newUserId);
      Task<string> GetUsersName();
      int GetUserId();
      Task<object> GetAllUsersAndLogins(int userId);
      Task UpdateUsersAndLogins(int userId, UserAndLogin userAndLogin);
      Task<object> AddUsersAndLogins(int userId, UserAndLogin userAndLogin);
      Task<bool> CheckLinkedPersonExists(int linkedPersonId, int dealerGroup);
      Task DeleteUsersAndLogins(int userId, string idToDelete);
      Task<IEnumerable<UserAndLogin>> GetAllUsersAndLoginsNew(int userId);
      Task<string> GetAllSiteIdsForUser(int userId);
      string GetUserEmailAddressFromToken();
      Task<IEnumerable<DealerGroupVM>> GetAllDealerGroups();

      Task<int> GetDealerGroupIdDirectFromDb(int userId);
      //Task UpdateDealerGroup(int userId, int dealerGroupId);
      string GetUserRole();
      Task<string> GetUserDefaultSiteName();
      Task<string> GetUserUsername();
      Task<List<IdentityRole>> GetAllRoles();
      Task<int> GetDealerGroupIdFromCache();
      Task<string> GetDealerGroupNameFromCache();
      Task<string> UploadUsersAndLogins(int userId, IFormFile file);
      Task<Dictionary<string, UserParamSet>> GetUserSiteRoleDictionary();
      Task<Dictionary<string, UserParamSet>> ReLoadUserSiteRoleCache();
      Task SaveUserPreference(UserPreference userPreference, int userId);
      Task<IEnumerable<UserPreference>> GetUserPreferences(int userId);
      Task<int> GetDealerGroupIdFromCache(string emailAddress);
      Task LogDeviceInfo(int userId, string deviceInfo);
      Task<IList<DealerGroupVM>> GetDealerGroupsForSpecificEmailAddress(string emailAddress);
      Task<string> GetAspNetUserId(string userName, string DealerGroupName);
      Task<int> UpdateUserPasswordAcrossAllDealerGroups(string passwordHash, string email);
      Task<string> GetAspnetUserIdFromCache(string emailAddress);
      Task RestoreUsersAndLogins(int userId, string idToRestore);
   }

   public class UserService : IUserService
   {
      //properties of the service
      private readonly IUserDataAccess userDataAccess;
      private readonly IHttpContextAccessor httpContextAccessor;
      private readonly UserManager<ApplicationUser> userManager;
      private readonly IEmailSenderCustom emailSender;
      private readonly IMemoryCache memoryCache;
      //private readonly IAzureADService azureADService;
      private readonly RoleManager<IdentityRole> roleManager;
      private readonly IExcelParseService excelParseService;
      private readonly ISiteService siteService;
      private readonly IConfiguration config;
      private readonly string configSectionName = "WebApp";
      private readonly string configURL = "URL";
      private readonly IGlobalParamDataAccess globalParamDataAccess;
      private readonly IRefreshTokenService refreshTokenService;

      //constructor
      public UserService(
          IUserDataAccess userDataAccess,
          IHttpContextAccessor httpContextAccessor,
          UserManager<ApplicationUser> userManager,
          IEmailSenderCustom emailSender,
          IMemoryCache memoryCache,
          //IAzureADService azureADService,
          RoleManager<IdentityRole> roleManager,
          IExcelParseService excelParseService,
          ISiteService siteService,
          IConfiguration config,
          IGlobalParamDataAccess globalParamDataAccess,
          IRefreshTokenService refreshTokenService)
      {
         this.userDataAccess = userDataAccess;
         this.httpContextAccessor = httpContextAccessor;
         this.userManager = userManager;
         this.emailSender = emailSender;
         this.memoryCache = memoryCache;
         //this.azureADService = azureADService;
         this.roleManager = roleManager;
         this.excelParseService = excelParseService;
         this.siteService = siteService;
         this.config = config;
         this.globalParamDataAccess = globalParamDataAccess;
         this.refreshTokenService = refreshTokenService;
      }

      public async Task<object> AddUsersAndLogins(int userId, UserAndLogin userAndLogin)
      {
         try
         {
            int dealerGroupId = await GetDealerGroupIdFromCache();
            string dealerGroupName = await GetDealerGroupNameFromCache();

            // User exists 
            userAndLogin.Email = Regex.Replace(userAndLogin.Email, @"\s+", "");
            string aspNetUserId = await this.GetAspNetUserId(userAndLogin.Email, dealerGroupName);

            if (!string.IsNullOrEmpty(aspNetUserId))
            {
               throw new Exception("User with email already exists");
            }
            else
            {
               var applicationUser = new ApplicationUser()
               {
                  UserName = $"{userAndLogin.UserName}-{System.Guid.NewGuid()}", //this is temp, later its updated back to email
                  Email = userAndLogin.Email,
                  LinkedPersonId = userAndLogin.Code,
                  EmailConfirmed = true,
                  DealerGroupId = dealerGroupId
               };

               try
               {
                  var result = await userManager.CreateAsync(applicationUser);

                  if (result.Succeeded)
                  {
                     aspNetUserId = await this.GetAspnetUserIdDirectFromDb(userAndLogin.Email, dealerGroupId);
                     var newAspNetUser = await userManager.FindByIdAsync(aspNetUserId);
                     await userManager.AddToRoleAsync(newAspNetUser, userAndLogin.RoleName);

                     var newUser = new User
                     {
                        Name = userAndLogin.Name,
                        EmployeeNumber = userAndLogin.EmployeeNumber
                     };

                     int newUserId = await CreateUser(userId, newUser, dealerGroupId);

                     applicationUser.LinkedPersonId = newUserId;
                     await userManager.UpdateAsync(applicationUser);

                     await SetSites(userId, userAndLogin.SiteCode.Value, userAndLogin.Sites, newUserId);
                     await userDataAccess.UpdateUsernameToEmail(aspNetUserId);
                     //string invitationURL = await azureADService.CreateUser(userAndLogin.Name, userAndLogin.Email);

                     await ReLoadUserSiteRoleCache();

                     IEnumerable<GlobalParam> globalParams = await globalParamDataAccess.GetAllParamsAsync(userId);
                     var isSSOEnabled = globalParams.First(param => param.Name == "isSSOEnabled").BoolValue;

                     if (isSSOEnabled)
                     {
                        await SendWelcomeEmailSSO(newAspNetUser.Id, applicationUser.Email);
                     }
                     else
                     {
                        await SendWelcomeEmail(newAspNetUser.Id, applicationUser.Email);
                     }


                     var o = new { code = newUserId, appUserId = newAspNetUser.Id };
                     return o;
                  }

               }
               catch (Exception ex)
               {
                  throw new Exception(ex.Message, ex);
               }
            }

         }
         catch (Exception ex)
         {
            throw new Exception(ex.Message, ex);
         }

         return new object();
      }

      public async Task<bool> CheckLinkedPersonExists(int linkedPersonId, int dealerGroup)
      {
         var result = await userDataAccess.CheckLinkedPersonExists(linkedPersonId, dealerGroup);
         return result > 0;
      }

      public Task<int> CreateUser(int userId, User user, int dealergroup)
      {
         return userDataAccess.CreateUser(userId, user, dealergroup);
      }

      public string GeneratePassword(bool useLowercase, bool useUppercase, bool useNumbers, int passwordSize)
      {

         const string LOWER_CASE = "abcdefghijklmnopqursuvwxyz";
         const string UPPER_CASE = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
         const string NUMBERS = "123456789";


         char[] _password = new char[passwordSize];
         string charSet = ""; // Initialise to blank
         System.Random _random = new Random();
         int counter;

         // Build up the character set to choose from
         if (useLowercase) charSet += LOWER_CASE;

         if (useUppercase) charSet += UPPER_CASE;

         if (useNumbers) charSet += NUMBERS;


         for (counter = 0; counter < passwordSize; counter++)
         {
            _password[counter] = charSet[_random.Next(charSet.Length - 1)];
         }

         return string.Join(null, _password);
      }

      public async Task<object> GetAllUsersAndLogins(int userId)
      {
         var users = await userDataAccess.GetAllUserWithSites(userId);

         var userIds = users.Select(u => u.UserId).Distinct().ToList();

         //get all the aspNetUser stuff
         var appUsers = userManager.Users.Where(u => userIds.Contains(u.LinkedPersonId.Value) && (u.LockoutEnd == null || u.LockoutEnd < DateTime.UtcNow)).ToList();

         List<UserAndLogin> usersAndLogins = new List<UserAndLogin>();

         foreach (var appUser in appUsers)
         {
            var userRole = await userManager.GetRolesAsync(appUser);

            UserAndLogin u = new UserAndLogin
            {
               AppUserId = appUser.Id,
               Code = appUser.LinkedPersonId.Value,
               Name = users.Where(u => u.UserId == appUser.LinkedPersonId).FirstOrDefault().Name,
               NameShort = users.Where(u => u.UserId == appUser.LinkedPersonId).FirstOrDefault().Name,
               UserName = appUser.UserName,
               RoleName = userRole.FirstOrDefault(),
               Email = appUser.Email,
               Sites = string.Join(", ", users.Where(u => u.UserId == appUser.LinkedPersonId).Select(s => s.SiteId).ToArray()),
               SiteCode = users.Where(u => u.UserId == appUser.LinkedPersonId && u.IsDefault == true)?.FirstOrDefault()?.SiteId
            };

            usersAndLogins.Add(u);
         }

         return usersAndLogins;
      }

      public async Task<IEnumerable<UserAndLogin>> GetAllUsersAndLoginsNew(int userId)
      {
         IEnumerable<UserAndLogin> usersAndLogins = await userDataAccess.GetAllUsersAndLogins(userId);
         return usersAndLogins.OrderBy(x => x.Name);
      }

      public int GetUserId()
      {
         var userParams = GetUserParamSet().Result;  //don't normally like .Result but 99.9% of the time this method should act synchronously
         return userParams.UserId;

      }

      public async Task<int> GetDealerGroupIdDirectFromDb(int userId)
      {
         return await userDataAccess.GetDealerGroupId(userId);  //we must get direct from db not from cache or token
      }

      public async Task<int> GetDealerGroupIdFromCache(string emailAddress)
      {
         UserParamSet userParams = await GetUserParamSet(emailAddress);
         if (userParams == null) return 0;
         return userParams.DealerGroupId;
      }

      public async Task<string> GetAspnetUserIdFromCache(string emailAddress)
      {
         UserParamSet userParams = await GetUserParamSet(emailAddress);
         if (userParams == null) return null;
         return userParams.AspNetUserId;
      }

      public async Task<int> GetDealerGroupIdFromCache()
      {
         UserParamSet userParams = await GetUserParamSet();
         return userParams.DealerGroupId;
      }

      public async Task<string> GetDealerGroupNameFromCache()
      {
         UserParamSet userParams = await GetUserParamSet();
         return userParams.DealerGroupName;
      }

      public async Task<string> GetUsersName()
      {
         UserParamSet userParams = await GetUserParamSet();
         return userParams.Name;
      }

      private async Task<string> GetUsersNameForSpecificEmailAddress(string emailAddress)
      {
         UserParamSet userParams = await GetUserParamSet(emailAddress);
         return userParams.Name;
      }

      public async Task<IList<DealerGroupVM>> GetDealerGroupsForSpecificEmailAddress(string emailAddress)
      {
         Dictionary<string, UserParamSet> userSiteRoleDictionary = await GetUserSiteRoleDictionary();
         var dealerGroupVMs = userSiteRoleDictionary
             .Where(u => u.Value.Email.ToUpper() == emailAddress.ToUpper())
             .Select(u => new DealerGroupVM
             {
                Id = u.Value.DealerGroupId,
                Description = u.Value.DealerGroupName
             })
             .Distinct()
             .ToList();

         return dealerGroupVMs;
      }

      public async Task<string> GetUserDefaultSiteName()
      {
         UserParamSet userParams = await GetUserParamSet();
         return userParams.DefaultSiteName;
      }

      public async Task<string> GetUserUsername()
      {
         UserParamSet userParams = await GetUserParamSet();
         return userParams.UserName;
      }

      public async Task SetSites(int userId, int siteId, string sitesString, int newUserId)
      {
         await userDataAccess.SaveUserSites(userId, siteId, sitesString, newUserId);
      }

      public async Task UpdateUsersAndLogins(int userId, UserAndLogin user)
      {
         try
         {

            int dealerGroupId = await GetDealerGroupIdFromCache();

            user.Email = Regex.Replace(user.Email, @"\s+", "");


            //We need to make this generic for update, add and bulk upload + this needs to check unqieness by dealergroup.
            var currentUserById = await userManager.FindByIdAsync(user.AppUserId);
            var currentUserRole = await userManager.GetRolesAsync(currentUserById);
            var allRoles = await roleManager.Roles.ToListAsync();

            if (currentUserById.DealerGroupId != dealerGroupId)
            {
               throw new Exception("User not part of the same dealergroup");
            }


            string roleIdToAdd = allRoles.Where(x => x.Name == user.RoleName).Select(x => x.Id).FirstOrDefault();
            if (currentUserRole.Count > 0)
            {
               if (currentUserRole[0] != user.RoleName)
               {

                  string roleIdToDelete = allRoles.Where(x => x.Name == currentUserRole[0]).Select(x => x.Id).FirstOrDefault();
                  await userDataAccess.RemoveFromRoleAsync(currentUserById.Id, roleIdToDelete);
                  await userDataAccess.AddToRoleAsync(currentUserById.Id, roleIdToAdd);
               }
            }
            else
            {
               await userDataAccess.AddToRoleAsync(currentUserById.Id, roleIdToAdd);
            }

            //Update user in the database
            await userDataAccess.UpdateUser(userId, user);
            await userDataAccess.SaveUserSites(userId, user.SiteCode.Value, user.Sites, user.Code.Value);
            await ReLoadUserSiteRoleCache();

         }
         catch (Exception ex)
         {
            throw new Exception(ex.Message, ex);
         }
      }

      public async Task DeleteUsersAndLogins(int userId, string idToDelete)
      {
         var currentUser = await userManager.FindByIdAsync(idToDelete);
         currentUser.LockoutEnd = DateTime.UtcNow.AddYears(100);
         await userManager.UpdateAsync(currentUser);
         //Remove users refresh token from UserRefreshToken table
         await refreshTokenService.DeleteTokens(currentUser);
         //await azureADService.DeleteUser(currentUser.Email);
         await ReLoadUserSiteRoleCache();
      }

      public async Task RestoreUsersAndLogins(int userId, string idToRestore)
      {
         var currentUser = await userManager.FindByIdAsync(idToRestore);
         currentUser.LockoutEnd = null;
         await userManager.UpdateAsync(currentUser);
         await ReLoadUserSiteRoleCache();
      }












      public string GetUserRole()
      {
         UserParamSet userParams = GetUserParamSet().Result;
         return userParams.RoleName;
      }

      public async Task<string> GetAllSiteIdsForUser(int userId)
      {
         var fromToken = httpContextAccessor.HttpContext.User.Claims.Where(c => c.Type == "Sites").FirstOrDefault();
         if (fromToken != null && fromToken.Value != null)
         {
            return fromToken.Value;
         }
         else
         {
            return await userDataAccess.GetAllSiteIdsForUser(userId);
         }
      }




      public string GetUserEmailAddressFromToken()
      {
         var emailClaim = httpContextAccessor.HttpContext.User.Claims.Where(c => c.Type == "preferred_username").FirstOrDefault();
         if (emailClaim != null && emailClaim.Value != null)
         {
            return emailClaim.Value;
         }
         else
         {
            return null;
         }
      }



      public async Task<Dictionary<string, UserParamSet>> ReLoadUserSiteRoleCache()
      {
         IEnumerable<UserParamSet> userSiteRoles = await userDataAccess.UserSiteRoleToCache();
         var userSiteRoleDictionary = userSiteRoles.ToDictionary(u => MakeUserParamSetKey(u.Email, u.DealerGroupId), u => u);
         memoryCache.Set("UserSiteRole", userSiteRoleDictionary, DateTime.UtcNow.AddDays(1));
         return userSiteRoleDictionary;
      }

      public async Task<IEnumerable<DealerGroupVM>> GetAllDealerGroups()
      {
         return await userDataAccess.GetAllDealerGroups();
      }


      /*
      public async Task UpdateDealerGroup(int userId, int dealerGroupId)
      {

          //valid UserIds
          int[] validUserIds = new int[] { 389, 933, 1096, 1097, 1101, 1243, 1275, 1283, 1597 };

          int newLinkedPersonId = 0;
          if (validUserIds.Contains(userId))
          {
              switch (dealerGroupId)
              {
                  case 1: //Vertu
                      newLinkedPersonId = 389;
                      break;
                  case 2://Renault
                      newLinkedPersonId = 933;
                      break;
                  case 3://Vindis
                      newLinkedPersonId = 1096;
                      break;
                  case 4: //ParkLane
                      newLinkedPersonId = 1097;
                      break;
                  case 5: //Perter vardy
                      newLinkedPersonId = 1101;
                      break;
                  case 6: //Dick Lovett
                      newLinkedPersonId = 1243;
                      break;
                  case 7: //Jardine Motors
                      newLinkedPersonId = 1275;
                      break;
                  case 8: //MercedesBenz South West
                      newLinkedPersonId = 1283;
                      break;
                  case 9: //HR Owen
                      newLinkedPersonId = 1597;
                      break;
                  default:
                      throw new Exception($"DealerGroup {dealerGroupId} not found.");

              }
          }

          string email = GetUserEmailAddressFromToken();
          await userDataAccess.UpdateLinkedPersonId(userId, email, newLinkedPersonId);
          await ReLoadUserSiteRoleCache();
      }
      */









      //----------------------------------------------
      // Private Methods
      //----------------------------------------------


      private async Task SendWelcomeEmail(string id, string newUserEmail)
      {
         ApplicationUser newUser = await userManager.FindByIdAsync(id);
         string dbuserName = await GetUsersNameForSpecificEmailAddress(newUserEmail);
         IList<string> userRoles = await userManager.GetRolesAsync(newUser);
         string roleName = userRoles.First();
         string sitesString = await GetUserSitesNameForSpecificEmailAddress(newUserEmail);

         string companyName = await GetUserDealerGroupName(newUser.LinkedPersonId.Value);
         string nominatedPersonEmail = await userDataAccess.GetNominatedUserEmail(newUser.LinkedPersonId.Value);
         string ccEmailList = nominatedPersonEmail != null ? nominatedPersonEmail : null;

         string webURL = config[$"{configSectionName}:{configURL}"];

         string guideIfAvailable = "";
         if (roleName != "Scanner")
         {
            guideIfAvailable = "Here you will also find a training guide which explains how to scan vehicles using StockPulse.";
         }


         //main body build
         string body = $"**Automated welcome message for: <strong>{dbuserName}</strong>**<br>";

         body += $"<h2>Welcome to StockPulse!</h2>";
         body += $"You have been set-up with access to StockPulse, the cloud-based stock check tool from CPH Insight.<br><br>";
         body += $"Your details are: <br><br>";
         body += $"<table > <tbody><tr><td>Email:</td><td><strong>{newUser.UserName}</strong>" +
             $"</td></tr><tr><td>User Role:</td><td><strong>{roleName}</strong></td></tr><tr><td>Sites:</td><td><strong>{sitesString}</strong></td></tr></tbody></table><br><br>";
         body += $"To get started, visit <a href=\"{webURL}\"> {webURL} </a> to initially set a password - simply click on the red 'Forgot Password' button. {guideIfAvailable}<br><br>";
         body += $"Once you have set your password, please download the StockPulse app to your iPhone, iPad or Android device. It is free to download from the App Store, simply search for StockPulse. When you start the app, login using your email and password. Note: not all user roles allow access to the mobile app.<br><br>";
         body += $"Should you have any queries, please first check the guidance notes attached with this email before picking up with CPHI support on:";
         body += $"<ul><li><EMAIL></li><li>+44 ************ (UK)</li><li>****** 674 1476 (US)</li></ul>";
         //body += $"Invitation URL: <a href=\"{invitationURL}\"> Accept Invitation </a>";

         string attachmentUrl = "https://stockpulseimages.blob.core.windows.net/stockpulse-training/StockPulse%20User%20Guide%20v3.2.pdf?sp=r&st=2023-08-23T12:51:09Z&se=2030-08-23T20:51:09Z&spr=https&sv=2022-11-02&sr=b&sig=bolRTgS%2F4PpAFyTCqp8v7WkBrm42VGmrgYA4fddqYOw%3D";

         await emailSender.SendEmailAsync(newUser.Email, "Welcome to StockPulse", body, ccEmailList, attachmentUrl);

      }

      private async Task SendWelcomeEmailSSO(string id, string newUserEmail)
      {
         ApplicationUser newUser = await userManager.FindByIdAsync(id);
         string dbuserName = await GetUsersNameForSpecificEmailAddress(newUserEmail);
         IList<string> userRoles = await userManager.GetRolesAsync(newUser);
         string roleName = userRoles.First();
         string sitesString = await GetUserSitesNameForSpecificEmailAddress(newUserEmail);

         string companyName = await GetUserDealerGroupName(newUser.LinkedPersonId.Value);
         string nominatedPersonEmail = await userDataAccess.GetNominatedUserEmail(newUser.LinkedPersonId.Value);
         string ccEmailList = nominatedPersonEmail != null ? nominatedPersonEmail : null;
         string webURL = config[$"{configSectionName}:{configURL}"];

         string guideIfAvailable = "";
         if (roleName != "Scanner")
         {
            guideIfAvailable = "Here you will also find a training guide which explains how to scan vehicles using StockPulse.";
         }


         //main body build
         string body = $"**Automated welcome message for: <strong>{dbuserName}</strong>**<br>";

         body += $"<h2>Welcome to StockPulse!</h2>";
         body += $"You have been set-up with access to StockPulse, the cloud-based stock check tool from CPH Insight.<br><br>";
         body += $"Your details are: <br><br>";
         body += $"<table > <tbody><tr><td>Email:</td><td><strong>{newUser.Email}</strong>" +
             $"</td></tr><tr><td>User Role:</td><td><strong>{roleName}</strong></td></tr><tr><td>Sites:</td><td><strong>{sitesString}</strong></td></tr></tbody></table><br><br>";
         body += $"To get started, please download the StockPulse app to your iPhone, iPad or Android device. It is free to download from the App Store, simply search for StockPulse. Note: not all user roles allow access to the mobile app.<br>";
         body += $"When you start the app, hit the ‘Sign in with Microsoft’ button to log in with your work email address and password.<br><br>";
         body += $"If your role allows, you’ll also be able to log into the web app here: <a>https://stockpulse.cphi.co.uk/</a><br><br>";
         body += $"Should you have any queries, please first check the guidance notes attached with this email before picking up with CPHI support on:";
         body += $"<ul><li><EMAIL></li><li>+44 ************ (UK)</li><li>****** 674 1476 (US)</li></ul>";
         //body += $"Invitation URL: <a href=\"{invitationURL}\"> Accept Invitation </a>";

         string attachmentUrl = "https://stockpulseimages.blob.core.windows.net/stockpulse-training/StockPulse%20User%20Guide%20v3.2.pdf?sp=r&st=2023-08-23T12:51:09Z&se=2030-08-23T20:51:09Z&spr=https&sv=2022-11-02&sr=b&sig=bolRTgS%2F4PpAFyTCqp8v7WkBrm42VGmrgYA4fddqYOw%3D";

         await emailSender.SendEmailAsync(newUser.Email, "Welcome to StockPulse", body, ccEmailList, attachmentUrl);

      }


      public async Task<Dictionary<string, UserParamSet>> GetUserSiteRoleDictionary()
      {
         Dictionary<string, UserParamSet> userSiteRoleDictionary = new Dictionary<string, UserParamSet>();

         if (!memoryCache.TryGetValue("UserSiteRole", out Dictionary<string, UserParamSet> cachedUserSiteRoleDictionary))
         {
            //Get fresh data from DB
            userSiteRoleDictionary = await ReLoadUserSiteRoleCache();
         }

         if (cachedUserSiteRoleDictionary != null)
         {
            userSiteRoleDictionary = cachedUserSiteRoleDictionary;
         }

         return userSiteRoleDictionary;
      }



      private async Task<string> GetUserSitesNameForSpecificEmailAddress(string emailAddress)
      {
         UserParamSet userParams = await GetUserParamSet(emailAddress);
         return userParams.SiteNames;
      }


      private async Task<UserParamSet> GetUserParamSet(string emailAddress = null)
      {
         if (emailAddress == null)
         {
            //this is normal operation.   we only pass in an emailAddress if we are specifically wanting to get
            //paramSet for some user other than the logged in user, for example if we're sending a welcome
            //email to a new person
            emailAddress = GetUserEmailAddressFromToken();
         }

         var dealerGroupId = await GetDealerGroupIdFromHeader(emailAddress);

         string key = MakeUserParamSetKey(emailAddress, dealerGroupId);

         Dictionary<string, UserParamSet> userSiteRoleDictionary = await GetUserSiteRoleDictionary();
         UserParamSet paramSet = userSiteRoleDictionary.FirstOrDefault(u => u.Key == key).Value;

         if (paramSet == null)
         {
            throw new Exception($"User {emailAddress} not found");
         }

         if (paramSet.RoleName == "Missing Role")
         {
            throw new Exception($"User {emailAddress} has not been assigned a role");
         }

         return paramSet;
      }

      private string MakeUserParamSetKey(string emailAddress, int dealerGroupId)
      {
         return $"{emailAddress.ToUpper()}|{dealerGroupId}";
      }

      private async Task<int> GetDealerGroupIdFromHeader(string emailAddress)
      {
         StringValues dealerGroupNameResult;
         bool canGetDealerGroupName = httpContextAccessor.HttpContext.Request.Headers.TryGetValue("DealerGroupName", out dealerGroupNameResult);
         var dealerGroupName = dealerGroupNameResult.ToString();
         if (canGetDealerGroupName && !string.IsNullOrEmpty(dealerGroupName))
         {
            var userSiteRoleDictionary = await GetUserSiteRoleDictionary();
            var dealerGroupId = userSiteRoleDictionary.FirstOrDefault(u => u.Value.DealerGroupName == dealerGroupName).Value.DealerGroupId;
            return dealerGroupId;
         }
         else
         {
            //To be backward compatible, where we are not passing DealerGroupId in the header, lets get the first avaiable DG for this user.
            var userSiteRoleDictionary = await GetUserSiteRoleDictionary();
            var dealerGroupId = userSiteRoleDictionary.FirstOrDefault(u => u.Value.Email.ToUpper() == emailAddress.ToUpper()).Value.DealerGroupId;
            return dealerGroupId;
            //throw new Exception($"No matching DealerGroup found");
         }
      }




      private async Task<string> GetUserDealerGroupName(int userId)
      {
         return await userDataAccess.GetUsersDealerGroupName(userId);
      }

      public async Task<List<IdentityRole>> GetAllRoles()
      {
         return await roleManager.Roles.ToListAsync();
      }

      public async Task<string> UploadUsersAndLogins(int userId, IFormFile file)
      {
         //Validate the file
         var parsedResult = await ParseFile(file, userId);


         int userCount = 0;


         if (parsedResult.ErrorMessage.Length > 0)
         {
            return "Error" + parsedResult.ErrorMessage.ToString();
         }



         try
         {
            List<UserAndLogin> userAndLogins = new List<UserAndLogin>();
            userAndLogins = BuildUsersFromImportedRows(parsedResult);

            //Replace Site Names with Ids
            var allsites = await siteService.GetAllSites(userId);

            foreach (var userAndLogin in userAndLogins)
            {
               try
               {
                  await AddUsersAndLogins(userId, userAndLogin);
               }
               catch (Exception ex)
               {
                  return $"Error while creating user {userAndLogin.Email}. {ex.Message}";
               }
            }

         }
         catch (Exception ex)
         {
            parsedResult.ErrorMessage.Append(ex.Message);
         }

         if (parsedResult.ErrorMessage.ToString() != string.Empty)
         {
            return "Error" + parsedResult.ErrorMessage.ToString();
         }
         else
         {
            return string.Concat(userCount, " Users created");
         }

      }

      public async Task LogDeviceInfo(int userId, string deviceInfo)
      {
         await userDataAccess.LogDeviceInfo(userId, deviceInfo);
      }

      private List<UserAndLogin> BuildUsersFromImportedRows(UserImportFile parsedResult)
      {
         List<UserAndLogin> userAndLogins = new List<UserAndLogin>();
         foreach (var row in parsedResult.userImportRows)
         {

            userAndLogins.Add(new UserAndLogin()
            {
               Name = row.Name,
               NameShort = row.Name,
               UserName = Regex.Replace(row.Email, @"\s+", ""),
               RoleName = row.Role,
               Email = Regex.Replace(row.Email, @"\s+", ""),
               Sites = row.SiteIds,
               SiteCode = int.Parse(row.DefaultSiteId),
               EmployeeNumber = row.EmployeeNumber
            });
         }

         return userAndLogins;
      }

      private async Task<UserImportFile> ParseFile(IFormFile file, int userId)
      {

         StringBuilder errorMessage = new StringBuilder();
         //Validate all the col values
         //Get all rows
         var allrows = GetAllRows(file);

         //rows to objects
         UserImportFile userImportFile = new UserImportFile();
         List<UserImportRow> userImportRows = new List<UserImportRow>();
         try
         {
            foreach (var row in allrows)
            {

               var userImportRow = new UserImportRow()
               {
                  Name = row[0].Trim(),
                  Role = row[1].Trim(),
                  UserName = row[2].Trim(),
                  Email = row[2].Trim(),
                  EmployeeNumber = row[3],
                  SiteIds = row[4],
                  DefaultSiteId = row[5]
               };

               userImportRows.Add(userImportRow);
            }

            var allUsers = await userDataAccess.UserSiteRoleToCache();

            ///---------------------------------Username ---------------------------////


            //Should not exists in the database
            //var allUsername = userImportRows.Select(p => p.UserName.ToLower()).Distinct().ToList();


            //No duplicates in the file
            //if (allUsername.Count != userImportRows.Count)
            //{
            //    errorMessage.AppendLine($"<br>Duplicate emails found.");
            //}


            //Check if all usernames
            //var allUsernameResult = allUsers.Where(a => allUsername.Contains(a.UserName.ToLower())).Select(a => a.UserName.ToLower()).ToList();

            //CheckForExistsError(allUsernameResult, "Username", errorMessage);


            ///---------------------------------Email---------------------------////



            //Should not exists in the database
            var allemails = userImportRows.Select(p => p.Email.ToLower()).Distinct().ToList();

            //No duplicates in the file
            if (allemails.Count != userImportRows.Count)
            {
               errorMessage.AppendLine($"<br>Duplicate emails found.");
            }

            //Check if all emails
            var allemailsResult = allUsers.Where(a => allemails.Contains(a.Email.ToLower())).Select(a => a.Email.ToLower()).ToList();

            CheckForExistsError(allemailsResult, "Email", errorMessage);

            ///---------------------------------Role---------------------------////

            //Should exists in the database
            var allroles = userImportRows.Select(p => p.Role).Distinct().ToList();
            var allExistingRoles = roleManager.Roles.Select(r => r.Name).ToList();
            //Check if all roles exists
            var allrolesResult = allroles.Where(a => !allExistingRoles.Contains(a)).ToList();

            CheckForNotFoundError(allrolesResult, "Role", errorMessage);



            ///---------------------------------Sites---------------------------////

            //Should exists in the database
            var allSiteIdsPerUser = userImportRows.Select(p => p.SiteIds).Distinct().ToList();
            var allSiteIds = new List<string>();
            foreach (var siteIds in allSiteIdsPerUser)
            {
               allSiteIds.AddRange(siteIds.Split(','));
            }
            allSiteIds = allSiteIds.Select(s => s.Trim()).Distinct().ToList();

            var allExistingSites = (await siteService.GetAllSites(userId)).Select(s => s.Id.ToString());
            //Check if all sites exists
            var allSiteIdsResult = allSiteIds.Where(a => !allExistingSites.Contains(a)).ToList();

            CheckForNotFoundError(allSiteIdsResult, "SiteId", errorMessage);


            ///---------------------------------Default Sites---------------------------////

            //Should exists in the database
            var allDefaultSiteIds = userImportRows.Select(p => p.DefaultSiteId).Distinct().ToList();
            var allExistingSiteIds = (await siteService.GetAllSites(userId)).Select(s => s.Id.ToString());
            //Check if all sites exists
            var allDefaultSiteResult = allDefaultSiteIds.Where(a => !allExistingSites.Contains(a)).ToList();

            CheckForNotFoundError(allDefaultSiteResult, "Default Site", errorMessage);

         }
         catch (Exception ex)
         {
            errorMessage.AppendLine(ex.Message);
         }
         finally
         {
            userImportFile.ErrorMessage = errorMessage;
            userImportFile.userImportRows = userImportRows;

         }

         return userImportFile;

      }


      private List<List<string>> GetAllRows(IFormFile file)
      {
         int headerSkip = 1;
         int headerRowCount = 1;
         int rowSkip = 1;
         int colIndexForFirstLevelName = 0;


         //string firstLevelName = string.Empty;
         List<string> headers = new List<string>();
         List<List<string>> rows = new List<List<string>>();
         using (var fs = file.OpenReadStream())
         {
            HeadersAndRows results = new HeadersAndRows();
            if (file.FileName.EndsWith(".xlsx") || file.FileName.EndsWith(".xls"))
            {
               results = excelParseService.GetExcelFileContents(fs, headerSkip, headerRowCount, rowSkip);
            }
            headers = results.headers.First();
            rows = results.rows;
            //List<string> row = rows[0];
            //firstLevelName = row[colIndexForFirstLevelName];
         }

         return rows;
         //return modelsService.GetActiveModelByName(firstLevelName);
      }

      private void CheckForExistsError(List<string> result, string name, StringBuilder errorMessage)
      {
         if (result != null && result.Count() > 0)
         {
            errorMessage.AppendLine($"<br>The following {name} already exists: {string.Join(",", result)}");
         }
      }


      private void CheckForNotFoundError(List<string> result, string name, StringBuilder errorMessage)
      {
         if (result != null && result.Count() > 0)
         {
            errorMessage.AppendLine($"<br>The following {name} are not found: {string.Join(",", result)}");
         }
      }


      public async Task SaveUserPreference(UserPreference userPreference, int userId)
      {
         await userDataAccess.SaveUserPreference(userPreference, userId);
      }

      public async Task<IEnumerable<UserPreference>> GetUserPreferences(int userId)
      {
         return await userDataAccess.GetUserPreferences(userId);
      }

      public async Task<string> GetAspNetUserId(string emailAddress, string DealerGroupName)
      {
         try
         {
            Dictionary<string, UserParamSet> userSiteRoleDictionary = await GetUserSiteRoleDictionary();
            var paramSetDictionary = userSiteRoleDictionary.FirstOrDefault(u => u.Value.Email.ToUpper() == emailAddress.ToUpper() && u.Value.DealerGroupName == DealerGroupName);
            if (paramSetDictionary.Key == null) return null;

            UserParamSet paramSet = paramSetDictionary.Value;
            return paramSet.AspNetUserId;
         }
         catch
         {
            return null;
         }

      }
      public async Task<string> GetAspnetUserIdDirectFromDb(string emailAddress, int dealerGroupId)
      {
         return await userDataAccess.GetAspnetUserIdDirectFromDb(emailAddress, dealerGroupId);  //we must get direct from db not from cache or token
      }

      public async Task<int> UpdateUserPasswordAcrossAllDealerGroups(string passwordHash, string email)
      {
         return await userDataAccess.UpdateUserPasswordAcrossAllDealerGroups(passwordHash, email);
      }
   }
   public class UserImportFile
   {
      public List<UserImportRow> userImportRows { get; set; }
      public StringBuilder ErrorMessage { get; set; }
   }
   public class UserImportRow
   {
      public string Name { get; set; }
      public string UserName { get; set; }
      public string Role { get; set; }
      public string Email { get; set; }
      public string SiteIds { get; set; }
      public string DefaultSiteId { get; set; }
      public string EmployeeNumber { get; set; }
   }
   public class HeadersAndRows
   {
      public List<List<string>> headers { get; set; }
      public List<List<string>> rows { get; set; }
   }

}
