.modal-content {
    background-color: transparent;

    .modal-header {
        background-color: var(--primaryLighter);
        color: var(--bodyColour);

        button.close {
            background-color: transparent;
            border: none;
            color: var(--bodyColour);
            line-height: 0;
        }
    }

    .modal-body,
    .modal-footer {
      background-color: var(--grey95) !important;
    }
}

.modal-open .modal {
    overflow-y: hidden !important;
  }
  
  .modal-dialog {
    width: 60% !important;
    max-width: 100% !important;
  }
  
  .modal-lg {
    width: 70% !important;
    max-width: 100% !important;
  }
  
  .modal-sm {
    width: 40% !important;
    max-width: 100% !important;
    margin-top: 32vh;
  }
  
  .highSmall .modal-sm {
    margin-top: 10vh;
  }
  
  .modal-xs {
    width: 30% !important;
    max-width: 100% !important;
  }
  
  .modal-content {
    .modal-body.lowHeight {
      min-height: unset;
    }
  
    .modal-footer .btn {
      min-width: 6em;
      margin: 0em 0.3em;
      border-top: 1px solid var(--grey70);
    }
  }
  
  .imageZoomModal .modal-content .modal-body {
    max-height: 90vh;
    width: unset;
    max-width: 90vw;
    margin: 0 auto;
  }
  
  .saveModal .modal-body {
    max-height: 50vh;
    overflow-y: auto;
  }
  
  .modal-sm .modal-body {
    min-height: 0;
  }
  
  .alertModalBody {
    background: hsla(210, 13%, 90%, 0.80) !important
  }
  
  .reallyWideModal .modal-lg {
    width: 85% !important;
  }
  
  .importModal {
    .modal-lg {
      width: 96vw !important;
      height: 95vh;
    }
  
    .modal-header {
      padding: 0.3em 1em;
    }
  
    .modal-body {
      max-height: 89vh !important;
    }
  
    .modal-footer {
      padding: 0.3em 1em;
    }
  }

  .backupImageModal .modal-body{
    display: flex;
    justify-content: center;
  }

  .confetti-canvas {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    margin: 0 auto;
    width: 100%;
    z-index: 9999;
    pointer-events: none;
}

@media screen and (max-width: 1679px) {
  .modal-lg {
    width: 85% !important;
    max-width: 100% !important;
  }
}