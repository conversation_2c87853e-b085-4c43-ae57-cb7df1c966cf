﻿using Newtonsoft.Json;
using System.Collections.Generic;
using System.Runtime.Serialization;

namespace PlateRecognizer
{
    [DataContract]
    public class PlateResult
    {

        [DataMember(Name = "box")]
        public Box Box { get; set; }

        [DataMember(Name = "plate")]
        public string Plate { get; set; }

        [DataMember(Name = "score")]
        public decimal Score { get; set; }

        [DataMember(Name = "dscore")]
        public decimal Dscore { get; set; }

        [DataMember(Name = "candidates")]
        [JsonProperty("scoresAndPlates")]
        public IList<ScoreAndPlateItem> ScoresAndPlates { get; set; }

    }




}