<nav class="page-specific-navbar">
  <ng-container *ngIf="selections.userRole === 'SysAdministrator'">
    <button class="btn btn-primary" (click)="addUser()">Add New User</button>
    <div class="fileInputWrapper">
      <input id="chooseFilesButton" width="unset"  type="file" (change)="onFileChange($event)" multiple="false" />
      <button (click)="uploadFile()" *ngIf="fileReadyToUpload">Upload</button>
      <span *ngIf="progress"> {{progress|cph:'number':0}}%</span>
    </div>
  </ng-container>
  <button class="btn btn-primary" *ngIf="selections.userRole == 'SysAdministrator' && showRestrictedButtons()" (click)="refreshUserCache()">Refresh User Cache</button>

  <div class="buttonGroup">
    <button class="btn btn-primary" *ngIf="selections.userRole == 'SysAdministrator'" [ngClass]="{ 'active': isAllUsers }" (click)="showAllUsers()">All users</button>
    <button class="btn btn-primary" *ngIf="selections.userRole == 'SysAdministrator'" [ngClass]="{ 'active': isActiveUsers }" (click)="showActiveUsers()">Active users</button>
    <button class="btn btn-primary" *ngIf="selections.userRole == 'SysAdministrator'" [ngClass]="{ 'active': isLockedUsers }" (click)="showLockedUsers()">Locked users</button>
    <button class="btn btn-primary" *ngIf="selections.userRole == 'SysAdministrator'" [ngClass]="{ 'active': isDeletedUsers }"  (click)="showDeletedUsers()">Deleted users</button>
</div>
</nav>


<!-- Main Page -->
<div class="content-new">
    <!-- the main grid -->
    <div id="gridHolder">
      <ng-container>
        <button class="btn floatTopRightOfGrid" (click)="export()">
          <img style="width:2em;" [src]="logo.provideExcelLogo()">
        </button>

        <ag-grid-angular *ngIf="showGrid" id="byStatus" class="ag-theme-balham" [gridOptions]="mainTableGridOptions">
        </ag-grid-angular>
      </ng-container>
    </div>
</div>