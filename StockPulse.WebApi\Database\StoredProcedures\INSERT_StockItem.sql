﻿
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].[INSERT_StockItem]
(
    @Reg VARCHAR(50) = NULL,
    @Vin VARCHAR(50) = NULL,
    @Description VARCHAR(500) = NULL,
    @DIS INT = NULL,
    @GroupDIS INT = NULL,
    @Branch VARCHAR(50) = NULL,
    @StockType VARCHAR(50) = NULL,
    @Comment VARCHAR(500) = NULL,
    @Reference VARCHAR(50) = NULL,
    @StockCheckId INT = NULL,
    @UserId INT = NULL,
    @StockValue INT = NULL
)
AS
BEGIN

SET NOCOUNT ON;

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END



-- Note source report is currently hardcoded, needs to change.
INSERT INTO StockItems (Reg, Vin, [Description], DIS, GroupDIS, Branch, StockType, Comment, Reference, 
StockValue, Stock<PERSON>heckId, SourceReportId)
VALUES (@Reg, @Vin, @Description, @DIS, @GroupDIS, @Branch, @StockType, @Comment, @Reference, @StockValue, @StockCheckId, 1);


EXEC [dbo].[UPDATE_StockCheckLastUpdate] @StockCheckId, @UserId


END

GO


