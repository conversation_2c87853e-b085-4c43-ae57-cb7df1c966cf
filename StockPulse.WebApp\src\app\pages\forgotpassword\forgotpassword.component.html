<div class="accountPage" id="forgotPassword" style="opacity:0" [ngClass]="{ 'mobile': showMobileView }">
    <div class="inner">
        <div class="imgHolder">
            <img src="assets/imgs/stockpulseLogoBlack.svg">
        </div>
        <div *ngIf=forgotSuccess>Please check your email to reset your password.</div>

        <section id="loginForm">

            <form [formGroup]="forgotPasswordFormGroup" (ngSubmit)="Submit()">
                <table>
                    <tbody>
                        <tr>
                            <td>
                                <ul>
                                    <li style="display:none"></li>
                                </ul>
                            </td>
                        </tr>
                        <tr *ngIf="!forgotSuccess">
                            <td>
                                <div class="inputAndIcon">
                                    <fa-icon [icon]="icon.faEnvelope" [fixedWidth]="true"></fa-icon>
                                    <input class="form-control" formControlName="email" id="Email" name="Email"
                                        placeholder="Email" type="text" value="" (keyup)="validateEmail($event)">
                                </div>
                            </td>
                        </tr>

                        <tr>
                            <td>
                                <button *ngIf="!forgotSuccess" class="btn btn-primary"
                                    [disabled]="forgotPasswordFormGroup.pristine || forgotPasswordFormGroup.invalid || disableSubmit"
                                    (click)="Submit()">
                                    Email Password Reset Link
                                </button>

                                <button *ngIf="forgotSuccess" class="btn btn-primary"
                                    (click)="RedirectToLogin()">
                                    Return to Login
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>

                <!-- <div class="validation-summary-valid text-danger" data-valmsg-summary="true">
                    
                </div>
                <div class="form-group" *ngIf="!forgotSuccess">
                    
                </div>
                <div class="form-group">
                   

                </div> -->


            </form>
        </section>

    </div>














    <script src="/bundles/jqueryval"></script>











</div>