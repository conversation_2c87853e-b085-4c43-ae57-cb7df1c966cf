﻿
  
CREATE OR ALTER PROCEDURE [dbo].[GET_ReconciliationBuckets]    
(    
    @StockCheckId INT = NULL,    
    @UserId INT = NULL    
)    
AS    
BEGIN    
    
SET NOCOUNT ON;    
    
IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0  
BEGIN   
    RETURN  
END  

DECLARE @DealerGroupId INT;  
    
DECLARE @isRegional INT;    
SET @isRegional = (SELECT IsRegional FROM StockChecks    
                    WHERE StockChecks.Id = @StockCheckId)    
    
DECLARE @isTotal INT;    
SET @isTotal = (SELECT IsTotal FROM StockChecks    
                    WHERE StockChecks.Id = @StockCheckId)    
    
DECLARE @StockCheckDate DateTime;    
SET @StockCheckDate = (SELECT Date FROM StockChecks    
                    WHERE StockChecks.Id = @StockCheckId)    
   
   
---------------------  
 --IF SINGLE SITE  
 ---------------------  
IF @isRegional = 0 AND @isTotal = 0    
     
    BEGIN     
    
       SELECT 'Stock' as 'Description',     
       (  
            SELECT COUNT(Id)   
            FROM StockItems   
            WHERE StockCheckId = @StockCheckId  
       )  as 'VehicleCount',   
       (  
            SELECT SUM(StockValue)   
            FROM StockItems   
            WHERE StockCheckId = @StockCheckId  
       )  as 'InStockValue',   
	  (  
            SELECT SUM(Flooring)   
            FROM StockItems   
            WHERE StockCheckId = @StockCheckId  
       )  as 'Flooring', 
       1 as 'IsFullHeight',   
       0 as 'Order',  
       0 as 'IsProblem',  
       1 as 'IsStock',  
       0 as 'IsScan',  
       null as 'ReconciliationTypeId'    ,
       'AllItems' as ReconciliationState
    
 UNION ALL    
  
    SELECT 'Duplicate stock record' as 'Description',     
    (  
        SELECT COUNT(si.Id)   
        FROM StockItems AS si    
        WHERE si.StockCheckId = @StockCheckId   
        AND IsDuplicate = 1  
    )  as 'VehicleCount',   
     (  
        SELECT SUM(si.StockValue)   
        FROM StockItems AS si    
        WHERE si.StockCheckId = @StockCheckId   
        AND IsDuplicate = 1  
    )  as 'InStockValue',  
	(  
        SELECT SUM(Flooring)   
        FROM StockItems   
        WHERE StockCheckId = @StockCheckId  
    )  as 'Flooring', 
    0 as 'IsFullHeight',   
    0 as 'Order',  
    0 as 'IsProblem',  
    1 as 'IsStock',  
    0 as 'IsScan',  
    null as 'ReconciliationTypeId'  ,
    'Duplicate' as ReconciliationState
    
    
 UNION ALL  
   
    SELECT * from   
    (  
      SELECT RecTypes.Description as 'Description',     
      COUNT(si.Id) as 'VehicleCount',   
      SUM(si.StockValue) as 'InStockValue',  
	  SUM(si.Flooring) as 'Flooring',  
      0 as 'IsFullHeight',   
      1 as 'Order',  
      0 as 'IsProblem',  
      1 as 'IsStock',  
      0 as 'IsScan',  
      Min(RecTypes.Id) as 'ReconciliationTypeId',  --don't know why we have to use the MIN    
      'MatchedToReport' as ReconciliationState
      FROM ReconcilingItemTypes RecTypes    
      FULL JOIN ReconcilingItems ri on ri.ReconcilingItemTypeId = RecTypes.Id  AND ri.StockCheckId = @StockCheckId
      FULL JOIN StockItems si on si.ReconcilingItemId = ri.Id    
      INNER JOIN Users AS U ON U.DealerGroupId = RecTypes.DealerGroupId AND U.Id = @UserId    
      WHERE (si.StockCheckId = @StockCheckId OR si.StockCheckId IS NULL)    
      AND RecTypes.ExplainsMissingVehicle = 1   
      AND si.ScanId IS NULL --Added si.ScanId IS NULL to fix (vindis - skoda vw cambridge) issue    
      GROUP BY RecTypes.Description, RecTypes.SortOrder, RecTypes.IsActive
	  HAVING RecTypes.IsActive = 1 OR COUNT(si.Id) > 0  
      ORDER BY RecTypes.SortOrder OFFSET 0 ROWS  
    ) x  
     
 UNION ALL   
   
    SELECT     
    'Scanned at another site'  as 'Description' ,    
    (  
        SELECT COUNT(st.Id)   
        FROM StockItems st     
        INNER JOIN Scans sc on sc.Id = st.ScanId    
        WHERE sc.StockCheckId <> @StockCheckId     
        AND ScanId is not null    
        AND sc.IsDuplicate = 0    
        AND st.IsDuplicate = 0    
        AND st.StockCheckId = @StockCheckId  
    ) as 'VehicleCount',   
    (  
        SELECT SUM(st.StockValue)   
        FROM StockItems st     
        INNER JOIN Scans sc on sc.Id = st.ScanId    
        WHERE sc.StockCheckId <> @StockCheckId     
        AND ScanId is not null    
        AND sc.IsDuplicate = 0    
        AND st.IsDuplicate = 0    
        AND st.StockCheckId = @StockCheckId  
    ) as 'InStockValue',  
	(  
        SELECT SUM(st.Flooring)   
        FROM StockItems st     
        INNER JOIN Scans sc on sc.Id = st.ScanId    
        WHERE sc.StockCheckId <> @StockCheckId     
        AND ScanId is not null    
        AND sc.IsDuplicate = 0    
        AND st.IsDuplicate = 0    
        AND st.StockCheckId = @StockCheckId  
    ) as 'Flooring', 
    0 as 'IsFullHeight',   
    2 as 'Order',  
    0 as 'IsProblem',  
    1 as 'IsStock',  
    0 as 'IsScan',  
    null as 'ReconciliationTypeId'   ,
    'MatchedToOtherSite' as ReconciliationState
    
 UNION ALL   
   
    SELECT     
    'Missing resolved'  as 'Description',      
    (  
        SELECT COUNT(st.Id)   
        FROM StockItems st     
        INNER JOIN MissingResolutions mr on mr.Id = st.MissingResolutionId     
        WHERE st.ScanId is null AND mr.IsResolved = 1 AND st.ReconcilingItemId is null      
        AND st.StockCheckId = @StockCheckId  
    )   AS 'VehicleCount',  
    (  
        SELECT SUM(st.StockValue)   
        FROM StockItems st     
        INNER JOIN MissingResolutions mr on mr.Id = st.MissingResolutionId     
        WHERE st.ScanId is null AND mr.IsResolved = 1 AND st.ReconcilingItemId is null      
        AND st.StockCheckId = @StockCheckId  
    )   AS 'InStockValue',  

	(  
        SELECT SUM(st.Flooring)   
        FROM StockItems st     
        INNER JOIN MissingResolutions mr on mr.Id = st.MissingResolutionId     
        WHERE st.ScanId is null AND mr.IsResolved = 1 AND st.ReconcilingItemId is null      
        AND st.StockCheckId = @StockCheckId  
    )   AS 'Flooring', 

    0 as 'IsFullHeight',  
    3 as 'Order',  
    0 as 'IsProblem',  
    1 as 'IsStock',  
    0 as 'IsScan',  
    null as 'ReconciliationTypeId'    ,
    'Resolved' as ReconciliationState
    
 UNION ALL   
   
    SELECT     
    'Missing unresolved' as 'Description',     
    (  
        SELECT COUNT(Id)   
        FROM stockitems     
        WHERE ScanId is null   
        AND ReconcilingItemId is null     
        AND MissingResolutionId is null   
        AND IsDuplicate = 0   
        AND StockItems.StockCheckId = @StockCheckId  
    )    
        +     
    (  
        SELECT COUNT(st.Id)   
        FROM StockItems st     
        INNER JOIN MissingResolutions mr on mr.Id = st.MissingResolutionId     
        WHERE mr.IsResolved = 0   
        AND st.ScanId is null   
        AND st.ReconcilingItemId is null   
        AND st.IsDuplicate = 0     
        AND st.StockCheckId = @StockCheckId  
    )  as 'VehicleCount',  
    (  
        SELECT COALESCE(SUM(st.StockValue), 0)   
        FROM stockitems   st  
        WHERE ScanId is null   
        AND ReconcilingItemId is null     
        AND MissingResolutionId is null   
        AND IsDuplicate = 0   
        AND st.StockCheckId = @StockCheckId  
    )    
        +     
    (  
        SELECT COALESCE(SUM(st.StockValue), 0)   
        FROM StockItems st     
        INNER JOIN MissingResolutions mr on mr.Id = st.MissingResolutionId     
        WHERE mr.IsResolved = 0   
        AND st.ScanId is null   
        AND st.ReconcilingItemId is null   
        AND st.IsDuplicate = 0     
        AND st.StockCheckId = @StockCheckId  
    )  as 'InStockValue', 

	(  
        SELECT COALESCE(SUM(st.Flooring), 0)   
        FROM stockitems   st  
        WHERE ScanId is null   
        AND ReconcilingItemId is null     
        AND MissingResolutionId is null   
        AND IsDuplicate = 0   
        AND st.StockCheckId = @StockCheckId  
    )    
        +     
    (  
        SELECT COALESCE(SUM(st.Flooring), 0)   
        FROM StockItems st     
        INNER JOIN MissingResolutions mr on mr.Id = st.MissingResolutionId     
        WHERE mr.IsResolved = 0   
        AND st.ScanId is null   
        AND st.ReconcilingItemId is null   
        AND st.IsDuplicate = 0     
        AND st.StockCheckId = @StockCheckId  
    )  as 'Flooring', 

    0 as 'IsFullHeight',  
    4 as 'Order',  
    1 as 'IsProblem',  
    1 as 'IsStock',  
    0 as 'IsScan',null as 'ReconciliationTypeId'    ,
    'OutstandingIssue' as ReconciliationState
    
 UNION ALL   
   
    SELECT     
    'In Stock and Scanned' as 'Description',     
    (  
        SELECT COUNT(st.Id)   
        FROM StockItems st     
        INNER JOIN Scans sc on sc.Id = st.ScanId     
        WHERE sc.StockCheckId = @StockCheckId   
        AND  ScanId is not null     
        AND st.StockCheckId = @StockCheckId  
    ) as 'VehicleCount',  
    (  
        SELECT SUM(st.StockValue)  
        FROM StockItems st     
        INNER JOIN Scans sc on sc.Id = st.ScanId     
        WHERE sc.StockCheckId = @StockCheckId   
        AND  ScanId is not null     
        AND st.StockCheckId = @StockCheckId  
    ) as 'InStockValue',  
	(  
        SELECT SUM(st.Flooring)  
        FROM StockItems st     
        INNER JOIN Scans sc on sc.Id = st.ScanId     
        WHERE sc.StockCheckId = @StockCheckId   
        AND  ScanId is not null     
        AND st.StockCheckId = @StockCheckId  
    ) as 'Flooring',
    1 as 'IsFullHeight',  
    5 as 'Order',  
    0 as 'IsProblem',  
    1 as 'IsStock',  
    1 as 'IsScan',  
    null as 'ReconciliationTypeId'    ,
    'MatchedToStockOrScan' as ReconciliationState
    
UNION ALL  
  
    SELECT * FROM   
        (   
            SELECT RecTypes.Description as 'Description',     
            COUNT(scns.Id)  as 'VehicleCount',  
            0 as 'InStockValue',  
			0 as 'Flooring', 
            0 as 'IsFullHeight',  
            6 as 'Order',  
            0 as 'IsProblem',  
            0 as 'IsStock',  
            1 as 'IsScan',  
            MIN(RecTypes.Id) as 'ReconciliationTypeId', --don't know why we have to use the MIN    
            'MatchedToReport' as ReconciliationState
            FROM ReconcilingItemTypes RecTypes    
            FULL JOIN ReconcilingItems ri on ri.ReconcilingItemTypeId = RecTypes.Id AND ri.StockCheckId = @StockCheckId   
            FULL JOIN Scans scns on scns.ReconcilingItemId = ri.Id    
            INNER JOIN Users AS U ON U.DealerGroupId = RecTypes.DealerGroupId AND U.Id = @UserId    
            WHERE   
            (  
                scns.StockCheckId = @StockCheckId OR   
                scns.StockCheckId IS NULL  
            )    
            AND scns.StockItemId IS NULL    
            AND RecTypes.ExplainsMissingVehicle = 0    
            GROUP BY RecTypes.Description, RecTypes.SortOrder, RecTypes.IsActive
			HAVING RecTypes.IsActive = 1 OR COUNT(scns.Id) > 0
            ORDER BY RecTypes.SortOrder OFFSET 0 ROWS  
        ) x  
     
UNION ALL   
  
        SELECT 'In stock at another site' as 'Description',     
        (  
            SELECT Count(scns.Id)   
            FROM scans scns     
            INNER JOIN StockItems stockitem on stockitem.Id = scns.StockItemId    
            WHERE scns.StockCheckId = @StockCheckId     
            AND stockItem.StockCheckId <> @StockCheckId  
        ) as 'VehicleCount',  
        0 as 'InStockValue', 
		0 as 'Flooring',
        0 as 'IsFullHeight',  
        7 as 'Order',  
        0 as 'IsProblem',  
        0 as 'IsStock',  
        1 as 'IsScan',  
        null as 'ReconciliationTypeId'   ,
        'MatchedToOtherSite' as ReconciliationState
    
UNION ALL   
  
        SELECT 'Duplicate scan' as 'Description',     
        (  
            SELECT COUNT(Id)   
            FROM Scans   
            WHERE IsDuplicate = 1     
            AND Scans.StockCheckId = @StockCheckId  
        ) as 'VehicleCount',  
        0 as 'InStockValue',  
		0 as 'Flooring',  
        0 as 'IsFullHeight',  
        8 as 'Order',  
        0 as 'IsProblem',  
        0 as 'IsStock',  
        1 as 'IsScan',  
        null as 'ReconciliationTypeId'  ,
        'Duplicate' as ReconciliationState
    
UNION ALL   
  
        SELECT 'Unknown resolved' as 'Description',      
        (  
            SELECT COUNT(sc.Id) from Scans sc     
            INNER JOIN UnknownResolutions mr on mr.Id = sc.UnknownResolutionId     
            WHERE mr.IsResolved = 1   
            AND sc.IsDuplicate = 0   
            AND sc.StockItemId IS NULL   
            AND sc.ReconcilingItemId is null   
            AND sc.StockCheckId = @StockCheckId  
        ) as 'VehicleCount',  
        0 as 'InStockValue',  
		0 as 'Flooring',
        0 as 'IsFullHeight',  
        9 as 'Order',  
        0 as 'IsProblem',  
        0 as 'IsStock',  
        1 as 'IsScan',  
        null as 'ReconciliationTypeId'    ,
        'Resolved' as ReconciliationState
    
UNION ALL   
  
        SELECT 'Unknown unresolved' as 'Description',     
        (  
            SELECT COUNT(Id)   
            FROM Scans   
            WHERE StockItemId is null   
            AND ReconcilingItemId is null   
            AND UnknownResolutionId is null   
            AND IsDuplicate = 0   
            AND Scans.StockCheckId = @StockCheckId  
        )     
    +     
        (  
            SELECT COUNT(sc.Id) from Scans sc     
            INNER JOIN UnknownResolutions mr on mr.Id = sc.UnknownResolutionId     
            WHERE mr.IsResolved = 0   
            AND StockItemId IS NULL   
            AND ReconcilingItemId IS NULL   
            AND sc.IsDuplicate = 0   
            AND sc.StockCheckId = @StockCheckId  
        ) as 'VehicleCount',  
        0 as 'InStockValue',  
		0 as 'Flooring',
        0 as 'IsFullHeight',  
        10 as 'Order',  
        1 as 'IsProblem',  
        0 as 'IsStock',  
        1 as 'IsScan',  
        null as 'ReconciliationTypeId'    ,
        'OutstandingIssue' as ReconciliationState
    
UNION ALL   
  
        SELECT 'Scanned' as 'Description',   
        (  
            SELECT COUNT(Id)   
            FROM Scans   
            WHERE StockCheckId = @StockCheckId  
        ) as 'VehicleCount' ,  
        0 as 'InStockValue',  
		0 as 'Flooring', 
        1 as 'IsFullHeight',  
        11 as 'Order',  
        0 as 'IsProblem',  
        0 as 'IsStock',  
        1 as 'IsScan',  
        null as 'ReconciliationTypeId' ,
        'AllItems' as ReconciliationState
    
 END    
    
  
  
  
  
  
 ---------------------  
 --IF REGIONAL  
 ---------------------  
IF @isRegional = 1 AND @isTotal = 0    
    
    BEGIN     
    
    DECLARE @DivisionId INT;    
    
    SET @DivisionId = (SELECT DivisionId FROM StockChecks    
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId    
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id    
                        WHERE StockChecks.Id = @StockCheckId AND StockChecks.IsRegional = 1)


	    
        
    SET @DealerGroupId = (SELECT DealerGroupId FROM StockChecks    
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId    
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id    
                        WHERE StockChecks.Id = @StockCheckId)    
						
    
	SELECT  'Stock' as 'Description',
	COUNT(StockItems.Id) as 'VehicleCount',
	SUM(StockItems.StockValue) as 'InStockValue',
	SUM(StockItems.Flooring) AS 'Flooring',
	1 as 'IsFullHeight',
	0 as 'Order',
	0 as 'IsProblem',
	1 as 'IsStock',
	0 as 'IsScan',
	null as 'ReconciliationTypeId',
    'AllItems' as ReconciliationState
	FROM StockItems   
	INNER JOIN Stockchecks ON Stockchecks.Id=StockItems.StockCheckId AND Stockchecks.Date = @StockCheckDate  
	INNER JOIN Sites ON Sites.Id=StockChecks.SiteId  
	INNER JOIN Divisions ON Divisions.Id=Sites.DivisionId  
	WHERE Divisions.Id = @DivisionId
  
 UNION ALL  

	SELECT 'Duplicate stock record' as 'Description',   
	COUNT(StockItems.Id) as 'VehicleCount',
	SUM(StockItems.StockValue)  as 'InStockValue',
	SUM(StockItems.Flooring) AS 'Flooring',
		0 as 'IsFullHeight',
	0 as 'Order',
	0 as 'IsProblem',
	1 as 'IsStock',
	0 as 'IsScan',
	null as 'ReconciliationTypeId'  ,
    'Duplicate' as ReconciliationState
	FROM StockItems   
	INNER JOIN Stockchecks ON Stockchecks.Id=StockItems.StockCheckId AND Stockchecks.Date = @StockCheckDate  
	INNER JOIN Sites ON Sites.Id=StockChecks.SiteId  
	INNER JOIN Divisions ON Divisions.Id=Sites.DivisionId  
	WHERE Divisions.Id = @DivisionId AND IsDuplicate = 1
      
    
    
 UNION ALL   
   
    SELECT   RecTypes.Description as 'Description',     
    COUNT(si.Id) as 'VehicleCount',  
    SUM(si.StockValue) as 'InStockValue',  
	SUM(si.Flooring) AS 'Flooring',
    0 as 'IsFullHeight',  
    1 as 'Order',  
    0 as 'IsProblem',  
    1 as 'IsStock',  
    0 as 'IsScan',  
    Min(RecTypes.Id) as 'ReconciliationTypeId' , --don't know why we have to use the MIN    
    'MatchedToReport' as ReconciliationState
    FROM ReconcilingItemTypes RecTypes    
    FULL JOIN ReconcilingItems ri on ri.ReconcilingItemTypeId = RecTypes.Id    
    FULL JOIN StockItems si on si.ReconcilingItemId = ri.Id    
    FULL JOIN Stockchecks sc ON sc.Id=si.StockCheckId     
    FULL JOIN Sites st ON st.Id=sc.SiteId    
    FULL JOIN Divisions di ON di.Id=st.DivisionId    
    INNER JOIN Users AS U ON U.DealerGroupId = RecTypes.DealerGroupId AND U.Id = @UserId    
    WHERE (di.Id = @DivisionId OR di.Id IS NULL)    
    AND RecTypes.ExplainsMissingVehicle = 1 AND si.ScanId IS NULL AND sc.Date = @StockCheckDate    
    GROUP BY RecTypes.Description    
     
        
 UNION ALL   
   
	SELECT   'Scanned at another site'  as 'Description' ,  
	COUNT(st.Id) as 'VehicleCount',
	SUM(st.StockValue) AS 'InStockValue',
	SUM(st.Flooring) AS 'Flooring',
	0 as 'IsFullHeight',
	2 as 'Order',
	0 as 'IsProblem',
	1 as 'IsStock',
	0 as 'IsScan',
	null as 'ReconciliationTypeId'  ,
    'MatchedToOtherSite' as ReconciliationState
	FROM StockItems st   
	INNER JOIN Scans sc on sc.Id = st.ScanId  
	INNER JOIN Stockchecks stkck ON stkck.Id=st.StockCheckId AND stkck.Date = @StockCheckDate  
	INNER JOIN Sites si ON si.Id = stkck.SiteId  
	INNER JOIN Divisions di ON di.Id=si.DivisionId  
	WHERE sc.StockCheckId <> st.StockCheckId  
	AND di.Id = @DivisionId  
	AND sc.IsDuplicate = 0  
	AND st.IsDuplicate = 0  
	AND  ScanId is not null
    
    
 UNION ALL   
   
    SELECT  'Missing resolved' AS 'Description',    
    COUNT(st.Id) AS 'VehicleCount',
	SUM(st.StockValue)  AS 'InStockValue',
	SUM(st.Flooring) AS 'Flooring',
	0 as 'IsFullHeight',
    3 as 'Order',
    0 as 'IsProblem',
    1 as 'IsStock',
    0 as 'IsScan',
    null as 'ReconciliationTypeId'  ,
    'Resolved' as ReconciliationState
    FROM StockItems st   
    INNER JOIN MissingResolutions mr on mr.Id = st.MissingResolutionId   
    INNER JOIN Stockchecks stkck ON stkck.Id=st.StockCheckId AND stkck.Date = @StockCheckDate  
    INNER JOIN Sites si ON si.Id = stkck.SiteId  
    INNER JOIN Divisions di ON di.Id=si.DivisionId  
    WHERE st.ScanId is null 
    AND mr.IsResolved = 1 
    AND st.ReconcilingItemId is null    
    AND di.Id = @DivisionId
     
    
        
 UNION ALL   
   
    SELECT   'Missing unresolved' as 'Description',     
    (  
        SELECT COUNT(stockitems.Id)   
        FROM stockitems    
        INNER JOIN Stockchecks stkck ON stkck.Id=stockitems.StockCheckId AND stkck.Date = @StockCheckDate    
        INNER JOIN Sites si ON si.Id = stkck.SiteId    
        INNER JOIN Divisions di ON di.Id=si.DivisionId    
        WHERE ScanId is null   
        AND ReconcilingItemId is null     
        AND MissingResolutionId is null   
        AND IsDuplicate = 0   
        AND di.Id = @DivisionId  
    )    
      
    +     
      
    (  
        SELECT COUNT(st.Id)   
        FROM StockItems st     
        INNER JOIN MissingResolutions mr on mr.Id = st.MissingResolutionId    
        INNER JOIN Stockchecks stkck ON stkck.Id=st.StockCheckId AND stkck.Date = @StockCheckDate    
        INNER JOIN Sites si ON si.Id = stkck.SiteId    
        INNER JOIN Divisions di ON di.Id=si.DivisionId    
        WHERE mr.IsResolved = 0   
        AND st.ScanId is null   
        AND st.ReconcilingItemId is null   
        AND st.IsDuplicate = 0 AND di.Id = @DivisionId  
    )  as 'VehicleCount',  
     (  
        SELECT SUM(stockitems.StockValue)   
        FROM stockitems    
        INNER JOIN Stockchecks stkck ON stkck.Id=stockitems.StockCheckId AND stkck.Date = @StockCheckDate    
        INNER JOIN Sites si ON si.Id = stkck.SiteId    
        INNER JOIN Divisions di ON di.Id=si.DivisionId    
        WHERE ScanId is null   
        AND ReconcilingItemId is null     
        AND MissingResolutionId is null   
        AND IsDuplicate = 0   
        AND di.Id = @DivisionId  
    )    
      
    +     
      
    (  
        SELECT SUM(st.StockValue)   
        FROM StockItems st     
        INNER JOIN MissingResolutions mr on mr.Id = st.MissingResolutionId    
        INNER JOIN Stockchecks stkck ON stkck.Id=st.StockCheckId AND stkck.Date = @StockCheckDate    
        INNER JOIN Sites si ON si.Id = stkck.SiteId    
        INNER JOIN Divisions di ON di.Id=si.DivisionId    
        WHERE mr.IsResolved = 0   
        AND st.ScanId is null   
        AND st.ReconcilingItemId is null   
        AND st.IsDuplicate = 0 AND di.Id = @DivisionId  
    )  as 'InStockValue',  

	(  
        SELECT SUM(stockitems.Flooring)   
        FROM stockitems    
        INNER JOIN Stockchecks stkck ON stkck.Id=stockitems.StockCheckId AND stkck.Date = @StockCheckDate    
        INNER JOIN Sites si ON si.Id = stkck.SiteId    
        INNER JOIN Divisions di ON di.Id=si.DivisionId    
        WHERE ScanId is null   
        AND ReconcilingItemId is null     
        AND MissingResolutionId is null   
        AND IsDuplicate = 0   
        AND di.Id = @DivisionId  
    )    
      
    +     
      
    (  
        SELECT SUM(st.Flooring)   
        FROM StockItems st     
        INNER JOIN MissingResolutions mr on mr.Id = st.MissingResolutionId    
        INNER JOIN Stockchecks stkck ON stkck.Id=st.StockCheckId AND stkck.Date = @StockCheckDate    
        INNER JOIN Sites si ON si.Id = stkck.SiteId    
        INNER JOIN Divisions di ON di.Id=si.DivisionId    
        WHERE mr.IsResolved = 0   
        AND st.ScanId is null   
        AND st.ReconcilingItemId is null   
        AND st.IsDuplicate = 0 AND di.Id = @DivisionId  
    )  as 'Flooring',  

    0 as 'IsFullHeight',  
    4 as 'Order',  
    1 as 'IsProblem',  
    1 as 'IsStock',  
    0 as 'IsScan',  
    null as 'ReconciliationTypeId'    ,
    'OutstandingIssue' as ReconciliationState
    
    
 UNION ALL   
   
   
	SELECT   'In Stock and Scanned' as 'Description',   
	COUNT(st.Id) as 'VehicleCount',
	SUM(st.StockValue)  as 'InStockValue',
	SUM(st.Flooring)  as 'Flooring',
	1 as 'IsFullHeight',
	5 as 'Order',
	0 as 'IsProblem',
	1 as 'IsStock',
	1 as 'IsScan',
	null as 'ReconciliationTypeId'  ,
    'MatchedToStockOrScan' as ReconciliationState
	FROM StockItems st   
	INNER JOIN Scans sc on sc.Id = st.ScanId  
	INNER JOIN Stockchecks stkck ON stkck.Id=st.StockCheckId AND stkck.Date = @StockCheckDate  
	INNER JOIN Sites si ON si.Id = stkck.SiteId  
	INNER JOIN Divisions di ON di.Id=si.DivisionId  
	WHERE sc.StockCheckId = st.StockCheckId AND  ScanId is not null   
	AND di.Id = @DivisionId
            
    
UNION ALL 

			SELECT 
			RecTypes.Description as 'Description', 
			COALESCE(x.VehicleCount,0) as 'VehicleCount',
			0 as 'InStockValue',
			0 as 'Flooring', 
			0 as 'IsFullHeight',    
			6 as 'Order',    
			0 as 'IsProblem',    
			0 as 'IsStock',    
			1 as 'IsScan',    
			RecTypes.Id as 'ReconciliationTypeId', 
			'MatchedToReport' as 'ReconciliationState'  

			FROM  
			ReconcilingItemTypes RecTypes
			LEFT JOIN 
				(     
				  SELECT 
				  COUNT(scns.Id)  as 'VehicleCount',    
				  MIN(RecTypes.Id) as 'ReconciliationTypeId' --don't know why we have to use the MIN      
				  FROM ReconcilingItemTypes RecTypes      
				  FULL JOIN ReconcilingItems ri on ri.ReconcilingItemTypeId = RecTypes.Id      
				  FULL JOIN Scans scns on scns.ReconcilingItemId = ri.Id      
				  INNER JOIN Stockchecks stkck ON stkck.Id=ri.StockCheckId      
				  INNER JOIN Sites si ON si.Id = stkck.SiteId      
				  INNER JOIN Divisions di ON di.Id=si.DivisionId      
				  WHERE (di.Id = @DivisionId OR scns.StockCheckId IS NULL)     
				  AND scns.StockItemId IS NULL      
				  AND RecTypes.ExplainsMissingVehicle = 0      
				  AND stkck.Date = @StockCheckDate
				  AND RecTypes.DealerGroupId = @DealerGroupId
				  GROUP BY RecTypes.Description, RecTypes.SortOrder    
				  ORDER BY RecTypes.SortOrder OFFSET 0 ROWS    
				) x  
			ON RecTypes.Id = x.ReconciliationTypeId
			WHERE RecTypes.ExplainsMissingVehicle = 0
			AND RecTypes.DealerGroupId = @DealerGroupId 
    
    
	UNION ALL   
   
    SELECT 'In stock at another site' as 'Description',     
    (  
        SELECT COUNT(scns.Id) from scans scns     
        INNER JOIN StockItems stockitem on stockitem.Id = scns.StockItemId    
        INNER JOIN Stockchecks stkck ON stkck.Id=scns.StockCheckId AND stkck.Date = @StockCheckDate    
        INNER JOIN Sites si ON si.Id = stkck.SiteId    
        INNER JOIN Divisions di ON di.Id=si.DivisionId    
        WHERE di.Id = @DivisionId    
        AND stockItem.StockCheckId <> scns.StockCheckId  
    ) as 'VehicleCount',  
    0 as 'InStockValue',  
	0 as 'Flooring',
    0 as 'IsFullHeight',  
    7 as 'Order',  
    0 as 'IsProblem',  
    0 as 'IsStock',  
    1 as 'IsScan',  
    null as 'ReconciliationTypeId'    ,
    'MatchedToOtherSite' as ReconciliationState
    
    
 UNION ALL   
   
    SELECT 'Duplicate scan' as 'Description',     
    (  
        SELECT count(Scans.Id) from Scans     
        INNER JOIN Stockchecks stkck ON stkck.Id=Scans.StockCheckId AND stkck.Date = @StockCheckDate    
        INNER JOIN Sites si ON si.Id = stkck.SiteId    
        INNER JOIN Divisions di ON di.Id=si.DivisionId      
        WHERE Scans.IsDuplicate = 1     
        AND di.Id = @DivisionId  
    ) as 'VehicleCount',  
    0 as 'InStockValue',  
	0 as 'Flooring',
    0 as 'IsFullHeight',  
    8 as 'Order',  
    0 as 'IsProblem',  
    0 as 'IsStock',  
    1 as 'IsScan',  
    null as 'ReconciliationTypeId'    ,
    'Duplicate' as ReconciliationState
    
    
 UNION ALL   
   
    SELECT 'Unknown resolved' as 'Description',      
    (  
        SELECT count(sc.Id) from Scans sc     
        INNER JOIN UnknownResolutions mr on mr.Id = sc.UnknownResolutionId     
        INNER JOIN Stockchecks stkck ON stkck.Id=sc.StockCheckId AND stkck.Date = @StockCheckDate    
        INNER JOIN Sites si ON si.Id = stkck.SiteId    
        INNER JOIN Divisions di ON di.Id=si.DivisionId      
        WHERE mr.IsResolved = 1   
        AND sc.IsDuplicate = 0   
        AND sc.StockItemId IS NULL   
        AND sc.ReconcilingItemId is null   
        AND di.Id = @DivisionId  
    ) as 'VehicleCount',  
    0 as 'InStockValue',  
	0 as 'Flooring', 
    0 as 'IsFullHeight',  
    9 as 'Order',  
    0 as 'IsProblem',  
    0 as 'IsStock',  
    1 as 'IsScan',  
    null as 'ReconciliationTypeId'    ,
    'Resolved' as ReconciliationState
    
 UNION ALL   
   
    SELECT 'Unknown unresolved' as 'Description',     
    (  
        SELECT count(Scans.Id) from Scans     
        INNER JOIN Stockchecks ON Stockchecks.Id=Scans.StockCheckId AND StockChecks.Date = @StockCheckDate    
        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId    
        INNER JOIN Divisions ON Divisions.Id=Sites.DivisionId    
        WHERE StockItemId is null   
        AND ReconcilingItemId is null   
        AND UnknownResolutionId is null   
        AND Scans.IsDuplicate = 0   
        AND Divisions.Id = @DivisionId  
    )     
    
    +     
      
    (  
        SELECT count(sc.Id)   
        FROM Scans sc     
        INNER JOIN UnknownResolutions mr on mr.Id = sc.UnknownResolutionId    
        INNER JOIN Stockchecks stkck ON stkck.Id=sc.StockCheckId AND stkck.Date = @StockCheckDate    
        INNER JOIN Sites si ON si.Id = stkck.SiteId    
        INNER JOIN Divisions di ON di.Id=si.DivisionId      
        WHERE mr.IsResolved = 0   
        AND sc.StockItemId IS NULL   
        AND sc.ReconcilingItemId IS NULL   
        AND sc.IsDuplicate = 0    
        AND di.Id = @DivisionId  
    ) as 'VehicleCount',  
    0 as 'InStockValue',  
	0 as 'Flooring',  
    0 as 'IsFullHeight',  
    10 as 'Order',  
    1 as 'IsProblem',  
    0 as 'IsStock',  
    1 as 'IsScan',  
    null as 'ReconciliationTypeId'    ,
    'OutstandingIssue' as ReconciliationState
    
 UNION ALL   
   
    SELECT 'Scanned' as 'Description',   
    (  
        SELECT count(Scans.Id) from Scans     
        INNER JOIN Stockchecks stkck ON stkck.Id=Scans.StockCheckId  AND stkck.Date = @StockCheckDate    
        INNER JOIN Sites si ON si.Id = stkck.SiteId    
        INNER JOIN Divisions di ON di.Id=si.DivisionId     
        WHERE di.Id = @DivisionId  
    ) as 'VehicleCount',  
    0 as 'InStockValue',  
	0 as 'Flooring',
    1 as 'IsFullHeight',  
    11 as 'Order',  
    0 as 'IsProblem',  
    0 as 'IsStock',  
    1 as 'IsScan',  
    null as 'ReconciliationTypeId'   ,
    'AllItems' as ReconciliationState
  
 END    
  
  
  
 ---------------------  
 --IF TOTAL  
 ---------------------  
IF @isRegional = 0 AND @isTotal = 1    
    
    BEGIN     
    
    SET @DealerGroupId = (SELECT DealerGroupId FROM StockChecks    
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId    
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id    
                        WHERE StockChecks.Id = @StockCheckId)    
    
	SELECT 'Stock' as 'Description',     
	count(StockItems.Id)  as 'VehicleCount',
	SUM(StockItems.StockValue) as 'InStockValue',  
	SUM(StockItems.Flooring) as 'Flooring', 
	1 as 'IsFullHeight',  
	0 as 'Order',  
	0 as 'IsProblem',  
	1 as 'IsStock',  
	0 as 'IsScan',  
	null as 'ReconciliationTypeId'   ,
    'AllItems' as ReconciliationState
	from StockItems
	INNER JOIN Stockchecks ON Stockchecks.Id=StockItems.StockCheckId  AND StockChecks.Date = @StockCheckDate    
	INNER JOIN Sites ON Sites.Id=StockChecks.SiteId    
	INNER JOIN Divisions ON Divisions.Id=Sites.DivisionId    
	INNER JOIN DealerGroup ON Divisions.DealerGroupId=DealerGroup.Id    
	WHERE DealerGroup.Id = @DealerGroupId    
   
    
 UNION ALL    
  
	SELECT 'Duplicate stock record' as 'Description',     
	count(StockItems.Id) as 'VehicleCount',
	SUM(StockItems.StockValue) as 'InStockValue',
	SUM(StockItems.Flooring) as 'Flooring',
	0 as 'IsFullHeight',  
	0 as 'Order',  
	0 as 'IsProblem',  
	1 as 'IsStock',  
	0 as 'IsScan',  
	null as 'ReconciliationTypeId'    ,
    'Duplicate' as ReconciliationState
	from StockItems     
	INNER JOIN Stockchecks ON Stockchecks.Id=StockItems.StockCheckId  AND StockChecks.Date = @StockCheckDate    
	INNER JOIN Sites ON Sites.Id=StockChecks.SiteId    
	INNER JOIN Divisions ON Divisions.Id=Sites.DivisionId    
	INNER JOIN DealerGroup ON Divisions.DealerGroupId=DealerGroup.Id    
	WHERE DealerGroup.Id = @DealerGroupId AND IsDuplicate = 1    
    
    
 UNION ALL   
   
    SELECT   RecTypes.Description as 'Description',     
    COUNT(si.Id) as 'VehicleCount',  
    SUM(si.StockValue) as 'InStockValue',  
	SUM(si.Flooring) as 'Flooring',  
    0 as 'IsFullHeight',   
    1 as 'Order',  
    0 as 'IsProblem',  
    1 as 'IsStock',  
    0 as 'IsScan',  
    Min(RecTypes.Id) as 'ReconciliationTypeId'   ,
    'MatchedToReport' as ReconciliationState
    FROM ReconcilingItemTypes RecTypes  --don't know why we have to use the MIN    
    FULL JOIN ReconcilingItems ri on ri.ReconcilingItemTypeId = RecTypes.Id    
    FULL JOIN StockItems si on si.ReconcilingItemId = ri.Id    
    FULL JOIN Stockchecks sc ON sc.Id=si.StockCheckId      
    FULL JOIN Sites st ON st.Id=sc.SiteId    
    FULL JOIN Divisions di ON di.Id=st.DivisionId    
    FULL JOIN DealerGroup ON di.DealerGroupId=DealerGroup.Id    
    WHERE (DealerGroup.Id = @DealerGroupId OR si.StockCheckId IS NULL)    
    AND RecTypes.ExplainsMissingVehicle = 1 AND si.ScanId IS NULL AND sc.Date = @StockCheckDate    
    GROUP BY RecTypes.Description    
     
        
 UNION ALL   
   
	SELECT   'Scanned at another site'  as 'Description' ,    
	count(st.Id)  as 'VehicleCount' ,
	SUM(st.StockValue)  as 'InStockValue', 
	SUM(st.Flooring)  as 'Flooring',
	0 as 'IsFullHeight',  
	2 as 'Order',  
	0 as 'IsProblem',  
	1 as 'IsStock',  
	0 as 'IsScan',  
	null as 'ReconciliationTypeId'    ,
    'MatchedToOtherSite' as ReconciliationState
	FROM StockItems st     
	INNER JOIN Scans sc on sc.Id = st.ScanId    
	INNER JOIN Stockchecks stkck ON stkck.Id=st.StockCheckId  AND stkck.Date = @StockCheckDate    
	INNER JOIN Sites si ON si.Id = stkck.SiteId    
	INNER JOIN Divisions di ON di.Id=si.DivisionId    
	INNER JOIN DealerGroup ON di.DealerGroupId=DealerGroup.Id    
	WHERE sc.StockCheckId <> st.StockCheckId    
	AND DealerGroup.Id = @DealerGroupId    
	AND sc.IsDuplicate = 0    
	AND st.IsDuplicate = 0    
	AND  ScanId is not null  
    
    
 UNION ALL   
   
	SELECT   'Missing resolved'  as 'Description',      
	count(st.Id)   as 'VehicleCount', 
	SUM(st.StockValue) as 'InStockValue',
	SUM(st.Flooring) as 'Flooring',
	0 as 'IsFullHeight',  
	3 as 'Order',  
	0 as 'IsProblem',  
	1 as 'IsStock',  
	0 as 'IsScan',  
	null as 'ReconciliationTypeId'    ,
    'Resolved' as ReconciliationState
	FROM StockItems st     
	INNER JOIN MissingResolutions mr on mr.Id = st.MissingResolutionId     
	INNER JOIN Stockchecks stkck ON stkck.Id=st.StockCheckId  AND stkck.Date = @StockCheckDate    
	INNER JOIN Sites si ON si.Id = stkck.SiteId    
	INNER JOIN Divisions di ON di.Id=si.DivisionId    
	INNER JOIN DealerGroup ON di.DealerGroupId=DealerGroup.Id    
	WHERE st.ScanId is null   
	AND mr.IsResolved = 1   
	AND st.ReconcilingItemId is null      
	AND DealerGroup.Id = @DealerGroupId    
        
    
        
 UNION ALL   
   
        SELECT     
		'Missing unresolved' as 'Description',     
        (  
            SELECT count(stockitems.Id)   
            FROM stockitems    
            INNER JOIN Stockchecks stkck ON stkck.Id=stockitems.StockCheckId    
            INNER JOIN Sites si ON si.Id = stkck.SiteId  AND stkck.Date = @StockCheckDate    
            INNER JOIN Divisions di ON di.Id=si.DivisionId    
            INNER JOIN DealerGroup ON di.DealerGroupId=DealerGroup.Id    
            WHERE ScanId is null AND ReconcilingItemId is null     
            AND MissingResolutionId is null AND IsDuplicate = 0 AND DealerGroup.Id = @DealerGroupId  
        )    
          
        +     
    
        (  
            SELECT count(st.Id)   
            FROM StockItems st     
            INNER JOIN MissingResolutions mr on mr.Id = st.MissingResolutionId    
            INNER JOIN Stockchecks stkck ON stkck.Id=st.StockCheckId  AND stkck.Date = @StockCheckDate    
            INNER JOIN Sites si ON si.Id = stkck.SiteId    
            INNER JOIN Divisions di ON di.Id=si.DivisionId    
            INNER JOIN DealerGroup ON di.DealerGroupId=DealerGroup.Id    
            WHERE mr.IsResolved = 0   
            AND st.ScanId is null   
            AND st.ReconcilingItemId is null   
            AND st.IsDuplicate = 0    
            AND DealerGroup.Id = @DealerGroupId  
        )  as 'VehicleCount',  

         (  
            SELECT SUM(stockitems.StockValue)   
            FROM stockitems    
            INNER JOIN Stockchecks stkck ON stkck.Id=stockitems.StockCheckId    
            INNER JOIN Sites si ON si.Id = stkck.SiteId  AND stkck.Date = @StockCheckDate    
            INNER JOIN Divisions di ON di.Id=si.DivisionId    
            INNER JOIN DealerGroup ON di.DealerGroupId=DealerGroup.Id    
            WHERE ScanId is null AND ReconcilingItemId is null     
            AND MissingResolutionId is null AND IsDuplicate = 0 AND DealerGroup.Id = @DealerGroupId  
        )    
          
        +     
    
        (  
            SELECT SUM(st.StockValue)   
            FROM StockItems st     
            INNER JOIN MissingResolutions mr on mr.Id = st.MissingResolutionId    
            INNER JOIN Stockchecks stkck ON stkck.Id=st.StockCheckId  AND stkck.Date = @StockCheckDate    
            INNER JOIN Sites si ON si.Id = stkck.SiteId    
            INNER JOIN Divisions di ON di.Id=si.DivisionId    
            INNER JOIN DealerGroup ON di.DealerGroupId=DealerGroup.Id    
            WHERE mr.IsResolved = 0   
            AND st.ScanId is null   
            AND st.ReconcilingItemId is null   
            AND st.IsDuplicate = 0    
            AND DealerGroup.Id = @DealerGroupId  
        )  as 'InStockValue',  

         (  
            SELECT SUM(stockitems.Flooring)   
            FROM stockitems    
            INNER JOIN Stockchecks stkck ON stkck.Id=stockitems.StockCheckId    
            INNER JOIN Sites si ON si.Id = stkck.SiteId  AND stkck.Date = @StockCheckDate    
            INNER JOIN Divisions di ON di.Id=si.DivisionId    
            INNER JOIN DealerGroup ON di.DealerGroupId=DealerGroup.Id    
            WHERE ScanId is null AND ReconcilingItemId is null     
            AND MissingResolutionId is null AND IsDuplicate = 0 AND DealerGroup.Id = @DealerGroupId  
        )    
          
        +     
    
        (  
            SELECT SUM(st.Flooring)   
            FROM StockItems st     
            INNER JOIN MissingResolutions mr on mr.Id = st.MissingResolutionId    
            INNER JOIN Stockchecks stkck ON stkck.Id=st.StockCheckId  AND stkck.Date = @StockCheckDate    
            INNER JOIN Sites si ON si.Id = stkck.SiteId    
            INNER JOIN Divisions di ON di.Id=si.DivisionId    
            INNER JOIN DealerGroup ON di.DealerGroupId=DealerGroup.Id    
            WHERE mr.IsResolved = 0   
            AND st.ScanId is null   
            AND st.ReconcilingItemId is null   
            AND st.IsDuplicate = 0    
            AND DealerGroup.Id = @DealerGroupId  
        )  as 'Flooring', 

        0 as 'IsFullHeight',  
        4 as 'Order',  
        1 as 'IsProblem',  
        1 as 'IsStock',  
        0 as 'IsScan',  
        null as 'ReconciliationTypeId'    ,
        'OutstandingIssue' as ReconciliationState
    
    
 UNION ALL   
   
	SELECT  'In Stock and Scanned' as 'Description',     
	count(st.Id)   as 'VehicleCount', 
	SUM(st.StockValue) as 'InStockValue', 
	SUM(st.Flooring) as 'Flooring', 
	1 as 'IsFullHeight',  
	5 as 'Order',  
	0 as 'IsProblem',  
	1 as 'IsStock',  
	1 as 'IsScan',  
	null as 'ReconciliationTypeId'   ,
    'MatchedToStockOrScan' as ReconciliationState
	FROM StockItems st     
	INNER JOIN Scans sc on sc.Id = st.ScanId    
	INNER JOIN Stockchecks stkck ON stkck.Id=st.StockCheckId  AND stkck.Date = @StockCheckDate    
	INNER JOIN Sites si ON si.Id = stkck.SiteId    
	INNER JOIN Divisions di ON di.Id=si.DivisionId    
	INNER JOIN DealerGroup ON di.DealerGroupId=DealerGroup.Id    
	WHERE sc.StockCheckId = st.StockCheckId   
	AND  ScanId is not null     
	AND DealerGroup.Id = @DealerGroupId  
            
    
 UNION ALL     
  

	SELECT 
	RecTypes.Description as 'Description', 
	COALESCE(x.VehicleCount,0) as 'VehicleCount',
	0 as 'InStockValue',
	0 as 'Flooring',
	0 as 'IsFullHeight',    
	6 as 'Order',    
	0 as 'IsProblem',    
	0 as 'IsStock',    
	1 as 'IsScan',    
	RecTypes.Id as 'ReconciliationTypeId', 
	'MatchedToReport' as 'ReconciliationState'  

	FROM  
	ReconcilingItemTypes RecTypes
	LEFT JOIN 
		(     
			SELECT RecTypes.Description as 'Description',       
			COUNT(scns.Id)  as 'VehicleCount',    
			MIN(RecTypes.Id) as 'ReconciliationTypeId' --don't know why we have to use the MIN      
			FROM ReconcilingItemTypes RecTypes      
			FULL JOIN ReconcilingItems ri on ri.ReconcilingItemTypeId = RecTypes.Id      
			FULL JOIN Scans scns on scns.ReconcilingItemId = ri.Id      
			INNER JOIN Stockchecks stkck ON stkck.Id=ri.StockCheckId  AND stkck.Date = @StockCheckDate      
			INNER JOIN Sites si ON si.Id = stkck.SiteId      
			INNER JOIN Divisions di ON di.Id=si.DivisionId      
			INNER JOIN DealerGroup ON di.DealerGroupId=DealerGroup.Id      
			WHERE (DealerGroup.Id = @DealerGroupId) AND scns.StockItemId IS NULL      
			AND RecTypes.ExplainsMissingVehicle = 0      
			GROUP BY RecTypes.Description, RecTypes.SortOrder    
			ORDER BY RecTypes.SortOrder OFFSET 0 ROWS     
		) x  
		ON RecTypes.Id = x.ReconciliationTypeId
		WHERE RecTypes.ExplainsMissingVehicle = 0
		AND RecTypes.DealerGroupId = @DealerGroupId    
    
    
 UNION ALL   
   
        SELECT 'In stock at another site' as 'Description',     
        (  
            SELECT Count(scns.Id)   
            FROM scans scns     
            INNER JOIN StockItems stockitem on stockitem.Id = scns.StockItemId    
            INNER JOIN Stockchecks stkck ON stkck.Id=scns.StockCheckId  AND stkck.Date = @StockCheckDate    
            INNER JOIN Sites si ON si.Id = stkck.SiteId    
            INNER JOIN Divisions di ON di.Id=si.DivisionId    
            INNER JOIN DealerGroup ON di.DealerGroupId=DealerGroup.Id    
            WHERE DealerGroup.Id = @DealerGroupId    
            AND stockItem.StockCheckId <> scns.StockCheckId   
        ) as 'VehicleCount',  
        0 as 'InStockValue',
		0 as 'Flooring',
        0 as 'IsFullHeight',  
        7 as 'Order',  
        0 as 'IsProblem',  
        0 as 'IsStock',  
        1 as 'IsScan',  
        null as 'ReconciliationTypeId'    ,
        'MatchedToOtherSite' as ReconciliationState
    
    
 UNION ALL   
   
        SELECT 'Duplicate scan' as 'Description',     
        (  
            SELECT count(Scans.Id)   
            FROM Scans     
            INNER JOIN Stockchecks stkck ON stkck.Id=Scans.StockCheckId  AND stkck.Date = @StockCheckDate    
            INNER JOIN Sites si ON si.Id = stkck.SiteId    
            INNER JOIN Divisions di ON di.Id=si.DivisionId      
            INNER JOIN DealerGroup ON di.DealerGroupId=DealerGroup.Id    
            WHERE Scans.IsDuplicate = 1     
            AND DealerGroup.Id = @DealerGroupId  
        ) as 'VehicleCount',  
        0 as 'InStockValue',
		0 as 'Flooring',
        0 as 'IsFullHeight',  
        8 as 'Order',  
        0 as 'IsProblem',  
        0 as 'IsStock',  
        1 as 'IsScan',  
        null as 'ReconciliationTypeId'    ,
        'Duplicate' as ReconciliationState
    
    
 UNION ALL   
   
        SELECT 'Unknown resolved' as 'Description',      
        (  
            SELECT count(sc.Id)   
            FROM Scans sc     
            INNER JOIN UnknownResolutions mr on mr.Id = sc.UnknownResolutionId     
            INNER JOIN Stockchecks stkck ON stkck.Id=sc.StockCheckId  AND stkck.Date = @StockCheckDate    
            INNER JOIN Sites si ON si.Id = stkck.SiteId    
            INNER JOIN Divisions di ON di.Id=si.DivisionId     
            INNER JOIN DealerGroup ON di.DealerGroupId=DealerGroup.Id    
            WHERE mr.IsResolved = 1   
            AND sc.IsDuplicate = 0   
            AND sc.StockItemId IS NULL   
            AND sc.ReconcilingItemId is null   
            AND DealerGroup.Id = @DealerGroupId  
        ) as 'VehicleCount',  
        0 as 'InStockValue',
		0 as 'Flooring',
        0 as 'IsFullHeight',  
        9 as 'Order',  
        0 as 'IsProblem',  
        0 as 'IsStock',  
        1 as 'IsScan',  
        null as 'ReconciliationTypeId'    ,
        'Resolved' as ReconciliationState
    
 UNION ALL   
   
        SELECT 'Unknown unresolved' as 'Description',     
        (  
            SELECT count(Scans.Id)   
            FROM Scans     
            INNER JOIN Stockchecks stkck ON stkck.Id=Scans.StockCheckId  AND stkck.Date = @StockCheckDate    
            INNER JOIN Sites si ON si.Id = stkck.SiteId    
            INNER JOIN Divisions di ON di.Id=si.DivisionId      
            INNER JOIN DealerGroup ON di.DealerGroupId=DealerGroup.Id    
            WHERE StockItemId is null   
            AND Scans.ReconcilingItemId is null   
            AND UnknownResolutionId is null   
            AND Scans.IsDuplicate = 0   
            AND DealerGroup.Id = @DealerGroupId  
        )     
          
        +     
      
        (   
            SELECT COUNT(sc.Id)   
            FROM Scans sc     
            INNER JOIN UnknownResolutions mr on mr.Id = sc.UnknownResolutionId    
            INNER JOIN Stockchecks stkck ON stkck.Id=sc.StockCheckId  AND stkck.Date = @StockCheckDate    
            INNER JOIN Sites si ON si.Id = stkck.SiteId    
            INNER JOIN Divisions di ON di.Id=si.DivisionId      
            INNER JOIN DealerGroup ON di.DealerGroupId=DealerGroup.Id    
            WHERE mr.IsResolved = 0   
            AND StockItemId IS NULL   
            AND ReconcilingItemId IS NULL   
            AND sc.IsDuplicate = 0   
            AND DealerGroup.Id = @DealerGroupId  
        ) as 'VehicleCount',  
        0 as 'InStockValue',
		0 as 'Flooring',
        0 as 'IsFullHeight',  
        10 as 'Order',  
        1 as 'IsProblem',  
        0 as 'IsStock',  
        1 as 'IsScan',  
        null as 'ReconciliationTypeId'    ,
        'OutstandingIssue' as ReconciliationState
    
 UNION ALL   
   
        SELECT 'Scanned' as 'Description',   
        (  
            SELECT count(Scans.Id)   
            FROM Scans     
            INNER JOIN Stockchecks stkck ON stkck.Id=Scans.StockCheckId  AND stkck.Date = @StockCheckDate    
            INNER JOIN Sites si ON si.Id = stkck.SiteId    
            INNER JOIN Divisions di ON di.Id=si.DivisionId     
            INNER JOIN DealerGroup ON di.DealerGroupId=DealerGroup.Id    
            WHERE DealerGroup.Id = @DealerGroupId  
        ) as 'VehicleCount' ,  
        0 as 'InStockValue',  
		0 as 'Flooring', 
        1 as 'IsFullHeight',  
        11 as 'Order',  
        0 as 'IsProblem',  
        0 as 'IsStock',  
        1 as 'IsScan',  
        null as 'ReconciliationTypeId'    ,
        'AllItems' as ReconciliationState
    
 END    
    
END    
    
    
GO
  