﻿using System;
using System.Collections.Generic;

namespace StockPulse.WebApi.ViewModel
{
    public class ScanWithResolution : Scan
    {
        //public int ResolutionTypeId { get; set; }
        public string ResolutionTypeDescription { get; set; } 
        public bool IsResolved { get; set; }
        public string ResolvedBy { get; set; }
        public string ResolutionNotes { get; set; }
        public int ResolutionId { get; set; }
        public string ResolutionImageIds { get; set; }
        public DateTime ResolutionDate { get; set; }
        public DateTime StockCheckDate { get; set; }
        public List<ImageToUpdate> ResolutionImages { get; set; }
    }
}
