﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace StockPulse.Repository.Migrations
{
    /// <inheritdoc />
    public partial class newtableStatusChangeLogItem : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "StatusChangeLogItems",
                schema: "dbo",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    StockCheckId = table.Column<int>(type: "int", nullable: false),
                    UserId = table.Column<int>(type: "int", nullable: false),
                    StatusId = table.Column<int>(type: "int", nullable: false),
                    Date = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_StatusChangeLogItems", x => x.Id);
                    table.ForeignKey(
                        name: "FK_StatusChangeLogItems_Statuses_StatusId",
                        column: x => x.StatusId,
                        principalSchema: "dbo",
                        principalTable: "Statuses",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_StatusChangeLogItems_StockChecks_StockCheckId",
                        column: x => x.StockCheckId,
                        principalSchema: "dbo",
                        principalTable: "StockChecks",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_StatusChangeLogItems_Users_UserId",
                        column: x => x.UserId,
                        principalSchema: "dbo",
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_StatusChangeLogItems_StatusId",
                schema: "dbo",
                table: "StatusChangeLogItems",
                column: "StatusId");

            migrationBuilder.CreateIndex(
                name: "IX_StatusChangeLogItems_StockCheckId",
                schema: "dbo",
                table: "StatusChangeLogItems",
                column: "StockCheckId");

            migrationBuilder.CreateIndex(
                name: "IX_StatusChangeLogItems_UserId",
                schema: "dbo",
                table: "StatusChangeLogItems",
                column: "UserId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "StatusChangeLogItems",
                schema: "dbo");
        }
    }
}
