﻿using Dapper;
using StockPulse.WebApi.Dapper;
using System;
using System.Threading.Tasks;

namespace StockPulse.WebApi.DataAccess
{
    public interface IErrorReportDataAccess
    {
        Task SaveErrorReport(string errors, int userId);
    }

    public class ErrorReportDataAccess : IErrorReportDataAccess
    {
        private readonly IDapper dapper;

        public ErrorReportDataAccess(IDapper dapper)
        {
            this.dapper = dapper;
        }

        // Get all sites a user has permissions to view
        public async Task SaveErrorReport(string errors, int userId)
        {
            var paramList = new DynamicParameters();
            paramList.Add("UserId", userId);
            paramList.Add("Report", errors);

            try
            {

            await dapper.ExecuteAsync("dbo.INSERT_ErrorReport",paramList, System.Data.CommandType.StoredProcedure);
            
            }
            catch
            {
                throw new Exception("Failed to save errorReport");
            }
        }

    }
}
