﻿using Microsoft.Exchange.WebServices.Data;
using Microsoft.Extensions.Configuration;
using Microsoft.Identity.Client;
using System;
using System.IO;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace StockPulse.WebApi.Auth.Service
{
    public interface IEmailSenderCustom
    {
        System.Threading.Tasks.Task SendEmailAsync(string email, string subject, string htmlMessage, string cc = null, string attachmentUrl = null);
    }



    public class EmailSenderService : IEmailSenderCustom 
    {

        private readonly IConfiguration config;

        //private readonly string sectionName = "EmailSettings";
        //private readonly string userName = "mailUser";
        //private readonly string password = "mailPwd";

        public EmailSenderService(IConfiguration config)
        {
            this.config = config;
        }


        public async System.Threading.Tasks.Task SendEmailAsync(string email, string subject, string htmlMessage, string cc = null, string attachmentUrl = null)
        {
            var env = config[$"WebApp:Env"];
            if (env.Length > 0 && env != null && env != "PROD")
            {
                subject += $" {env} ENVIRONMENT";
            }


            var service = await ProvideExchangeService();

            EmailMessage messageNew = new EmailMessage(service);
            // Specify the email recipient and subject. 
            //messageNew.ToRecipients.Remove(messageNew.ToRecipients[0]);
            messageNew.ToRecipients.Add(email);
            if (!string.IsNullOrEmpty(cc))
            {
                foreach (var item in cc.Split(','))
                {
                    messageNew.CcRecipients.Add(item);
                }
            }

            messageNew.Subject = subject;

            // Specify when to send the email by setting the value of the extended property. 

            // Specify the email body. 
            StringBuilder str = new StringBuilder();
            str.AppendLine(htmlMessage);
            //str.AppendLine("This is a line of text 1");
            messageNew.Body = str.ToString();

            if (!string.IsNullOrEmpty(attachmentUrl))
            {
                using (var httpClient = new HttpClient())
                {
                    var response = await httpClient.GetAsync(attachmentUrl);
                    if (response.IsSuccessStatusCode)
                    {
                        var stream = await response.Content.ReadAsStreamAsync();
                        var fileName = Path.GetFileName(new Uri(attachmentUrl).LocalPath);
                        messageNew.Attachments.AddFileAttachment(fileName, stream);
                    }
                }
            }

            // Submit the request to send the email message. 
            try
            {

                // Save the message to the Drafts folder
                await messageNew.Save(WellKnownFolderName.Drafts);

                // Define the properties to load (including InternetMessageId)
                PropertySet props = new PropertySet(BasePropertySet.IdOnly, EmailMessageSchema.InternetMessageId);

                // Reload the message with the specified properties
                await messageNew.Load(props);

                // Wait for 5 seconds
                try
                {
                    await SendSavedMessage(service, messageNew, 1000);

                }
                catch (Exception ex)
                {
                    try
                    {
                        //logger.Error($"Failed sending message on 1st attempt, trying again: {ex.Message}");
                        await SendSavedMessage(service, messageNew, 5000);
                        //logger.Info("Messaged succeeded on first retry");
                    }
                    catch (Exception ex1)
                    {
                        //logger.Error($"Failed sending message on 2nd attempt, trying again: {ex.Message}");
                        try
                        {
                            await SendSavedMessage(service, messageNew, 5000);
                        }
                        catch (Exception ex2)
                        {
                            //No more retries, it will be sent via loader job
                            
                        }
                        //logger.Info("Messaged succeeded on second retry");
                    }
                }

            }
            catch (Exception ex)
            {
                //logger.Error($"Failed sending message to {messageNew.ToRecipients.First()}");
                throw ex;
                { }
            }


            return;
        }


        private static async System.Threading.Tasks.Task SendSavedMessage(ExchangeService service, EmailMessage message, int delay)
        {
            await System.Threading.Tasks.Task.Delay(delay);

            // Bind to the Drafts folder
            Folder draftsFolder = await Folder.Bind(service, WellKnownFolderName.Drafts);
            ItemView view = new ItemView(1); // Look for the most recent item, adjust as necessary

            // Create the search filter using the loaded InternetMessageId
            SearchFilter searchFilter = new SearchFilter.IsEqualTo(EmailMessageSchema.InternetMessageId, message.InternetMessageId);

            // Find the item in the Drafts folder
            FindItemsResults<Item> findResults = await service.FindItems(draftsFolder.Id, searchFilter, view);

            if (findResults.TotalCount > 0)
            {
                // Assuming the first found item is the email to be sent
                EmailMessage emailToBeSent = findResults.Items[0] as EmailMessage;
                await emailToBeSent.Load(new PropertySet(EmailMessageSchema.InternetMessageId));
                await emailToBeSent.SendAndSaveCopy();
            }
            else
            {
                Console.WriteLine("No email found with the specified InternetMessageId.");
            }
        }


        private  async Task<ExchangeService> ProvideExchangeService()
        {
            ///SPK-2789 migrated to new approach
            var cca = ConfidentialClientApplicationBuilder
                .Create(config[$"EmailSettings:mailAppId"])
                .WithClientSecret(config[$"EmailSettings:mailSecretValue"])
                .WithTenantId(config[$"EmailSettings:mailAppTenantId"])
                .Build();
            var ewsScopes = new string[] { "https://outlook.office365.com/.default" };
            AuthenticationResult authResult = await cca.AcquireTokenForClient(ewsScopes).ExecuteAsync();
            var ewsClient = new ExchangeService();
            ewsClient.Url = new Uri("https://outlook.office365.com/EWS/Exchange.asmx");
            ewsClient.Credentials = new OAuthCredentials(authResult.AccessToken);
            ewsClient.ImpersonatedUserId = new ImpersonatedUserId(ConnectingIdType.SmtpAddress, config[$"EmailSettings:mailUser"]);

            return ewsClient;
        }

    }
}
