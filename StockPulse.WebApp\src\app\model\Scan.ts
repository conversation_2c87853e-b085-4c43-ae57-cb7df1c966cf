import { ReconciliationState } from "./ReconciliationState";

export interface Scan {
  scanId: number;
  locationDescription: string;
  scannerName: string;
  regConfidence: number;
  vinConfidence: number;
  longitude: number;
  latitude: number;
  scanDateTime: Date | string;
  scanComment: string;
  scanReg: string;
  scanVin: string;
  scanDescription: string;
  hasVinImage: boolean;
  scanState: ReconciliationState;
  distanceFromDealershipInMiles: number;


  //client side props
  regImageLargeUrl: string;
  regImageThumbnailUrl: string;
  vinImageUrl: string;

  regEditStatus: string;
  vinEditStatus: string;
}




