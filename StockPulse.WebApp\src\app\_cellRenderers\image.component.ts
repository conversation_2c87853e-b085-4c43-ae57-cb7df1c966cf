import { Component } from "@angular/core";
import { ICellRendererAngularComp } from "ag-grid-angular";
import { GetDataService } from '../services/getData.service'

// both this and the parent component could be folded into one component as they're both simple, but it illustrates how
// a fuller example could work
@Component({
    selector: 'image-cell',
    template: `
        <img
            *ngIf="params.data.regImageThumbnailUrl"
            [src]="params.data.regImageThumbnailUrl"
            alt="Scan image"
            [ngbPopover]="imagePopover"
            [openDelay]="300"
              [closeDelay]="500" 
            container="body"
            placement="auto"
            triggers="mouseenter:mouseleave"
            popoverClass="scanImagePopover">

        <ng-template #imagePopover>
            <img id="popoverImage" [src]="params.data.regImageLargeUrl" alt="Scan image">
        </ng-template>
    `

    ,
    styles: [
        `img, .imagePlaceholder{
            height: 4em;
            border-radius: 4px;
            margin: 4px 0px;
        }
        #popoverImage{margin:0px;border:4px solid var(--primaryLight); box-shadow: rgb(75,75,75) 5px 5px 10px 5px !important}
        .imagePlaceholder{display:flex;align-items:center;}
        
        `
    ]
})
export class ImageComponent implements ICellRendererAngularComp {

    constructor(
        private data: GetDataService,

    ) { }

    params: any;

    agInit(params: any): void {
        this.params = params;


    }

    refresh(): boolean {
        return false;
    }
}


