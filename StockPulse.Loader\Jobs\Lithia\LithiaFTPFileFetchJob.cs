﻿using Quartz;
using System;
using System.IO;
using log4net;
using System.Threading.Tasks;
using Microsoft.Extensions.Options;
using System.Diagnostics;
using Microsoft.Extensions.Configuration;
using System.Linq;
using StockPulse.Loader.Services;
using Aspose.Cells.Drawing;

namespace StockPulse.Loader.Jobs
{
    [DisallowConcurrentExecution]
    public class LithiaFTPFileFetchJob : IJob
    {
      //This is all for Lithia US
        private static readonly ILog Logger = LogManager.GetLogger(typeof(LithiaFTPFileFetchJob));


        public LithiaFTPFileFetchJob()
        {
        }


        public async Task Execute(IJobExecutionContext context)
        {
            try
            {

                // Folder names to loop through
                string[] folderNames =  {
                    "GLSchedule_41_NEW",
                    "GLSchedule_42_USED",
                    "GLSchedule_43_PROGRAM",
                    "GLSchedule_44_RENTAL",
                    "GLSchedule_48_LOANER",
                    "GLTrailBalance",
                    "Sales_BookedFinalized",
                    "Service_OpenROs",
                    "GLSchedule_244_FLEET_NEW"
                    };

                // sftp directory
                string sftpDirectory = "C:\\SFTP\\Clients\\lithia\\"; 
                
                // Target directory for copying files
                string targetDirectory = ConfigService.incomingRoot.Replace("{customer}", "lithia");

                // Ensure the target directory exists
                if (!Directory.Exists(targetDirectory))
                {
                    Directory.CreateDirectory(targetDirectory);
                }

                foreach (string folderName in folderNames)
                {
                    // Full path to the current folder
                    string currentFolderPath = Path.Combine(sftpDirectory, folderName);

                    // Ensure the folder exists
                    if (Directory.Exists(currentFolderPath))
                    {
                        // Get all CSV files in the current folder
                        string[] csvFiles = Directory.GetFiles(currentFolderPath, "*.csv");

                        // Archive folder path for the current folder
                        string archiveFolderPath = Path.Combine(currentFolderPath, "archive");

                        // Ensure the archive folder exists
                        if (!Directory.Exists(archiveFolderPath))
                        {
                            Directory.CreateDirectory(archiveFolderPath);
                        }

                        // Loop through each CSV file
                        foreach (string csvFile in csvFiles)
                        {
                            try
                            {
                                // File name
                                Logger.Info($"csvFile: {csvFile} ");
                                string originalFileName = Path.GetFileName(csvFile);
                                string filePath = Path.GetFullPath(csvFile);

                                // Generate the new file name with timestamp
                                string timePrefix = DateTime.UtcNow.ToString("yyyyMMdd_HHmmss") + "-";
                                string newFileNameOnServer =  $"{timePrefix}{originalFileName}";
                                string newFileNamePathOnServer = Path.Combine(currentFolderPath, newFileNameOnServer);

                                Logger.Info($"Moving: {csvFile} to {newFileNamePathOnServer}");
                                File.Move(csvFile, newFileNamePathOnServer);

                                // Copy the CSV file to the target directory
                                string targetFilePath = Path.Combine(targetDirectory, newFileNameOnServer);
                                Logger.Info($"Copying: {newFileNamePathOnServer} to {targetFilePath}");
                                File.Copy(newFileNamePathOnServer, targetFilePath, true); // Overwrite if exists

                                // Move the original file to the archive folder
                                string archiveFilePath = Path.Combine(archiveFolderPath, newFileNameOnServer);
                                Logger.Info($"Archiving: {newFileNamePathOnServer} to {archiveFilePath}");
                                File.Move(newFileNamePathOnServer, archiveFilePath);

                                Logger.Info($"Processed: {newFileNamePathOnServer}");
                                Console.WriteLine($"Processed: {newFileNamePathOnServer}");

                            }
                            catch (Exception ex)
                            {
                                Logger.Error($"Error processing {csvFile}: {ex.Message}");
                                Console.WriteLine($"Error processing {csvFile}: {ex.Message}");
                            }
                        }
                    }
                    else
                    {
                        Logger.Error($"Folder not found: {currentFolderPath}");
                        Console.WriteLine($"Folder not found: {currentFolderPath}");
                    }
                }

                Console.WriteLine("Processing completed.");


            }
            catch (Exception e)
            {
                Logger.Error(e);
            }
        }



    }
}
