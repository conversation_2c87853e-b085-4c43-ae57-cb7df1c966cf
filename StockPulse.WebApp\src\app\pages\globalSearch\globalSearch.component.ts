import { Component, OnInit } from '@angular/core';
import { GlobalSearchResultItem } from 'src/app/model/GlobalSearchResultItem';
import { ResultType } from 'src/app/model/ResultType';
import { StockCheck } from 'src/app/model/StockCheck';
import { ApiAccessService } from 'src/app/services/apiAccess.service';
import { ConstantsService } from 'src/app/services/constants.service';
import { ToastService } from 'src/app/services/newToast.service';
import { SelectionsService } from 'src/app/services/selections.service';
import { GlobalSearchService } from './globalSearch.service';

@Component({
  selector: 'app-globalSearch',
  templateUrl: './globalSearch.component.html',
  styleUrls: ['./globalSearch.component.scss']
})
export class GlobalSearchComponent implements OnInit {
  
  resultType = ResultType;

  constructor(
   public service: GlobalSearchService,
   private constants: ConstantsService,
   public toastService: ToastService,
   private apiAccessService: ApiAccessService,
   private selectionsService: SelectionsService
  ) { }

  ngOnInit() {
    this.initParams();
    this.chooseRegAndChassis();
  }

  initParams(){
    this.service.resultItems = [];
    this.toastService.destroyToast();
  }

  chooseRegAndChassis() {
    const toastRef = this.service.toastService.loadingToast();

    if (!this.service.reg) this.service.reg = "";
    if (!this.service.chassis) this.service.chassis = "";

    const methodName = `GetGlobalSearchResults?reg=${this.service.reg.replace(/\s/g, "")}&vin=${this.service.chassis.replace(/\s/g, "")}&requireAndMatch=${this.service.requireAndMatch}`
    
    this.service.apiService.get('StockItems', methodName).subscribe((res) => {
      res.map(x=>{
        if (!!x.item.scan && !!x.item.scan.scanId) {
          //item has a scan so generate the image url
          this.constants.addImageStringsToScan(x.item.scan)
        }
      })
      this.service.resultItems = res.sort((a,b) => new Date(a.stockCheckDate).getTime() - new Date(b.stockCheckDate).getTime());
      toastRef.close();
    })
  }

  showVehicleModal(id: number, isStock: boolean) {
    this.service.constants.vehicleModal.loadItemAndOpenModal(isStock, id,[]);
  }


  loadItem(resultItem: GlobalSearchResultItem) {
    if (!!resultItem.item.stockItem) {
      this.constants.vehicleModal.loadItemAndOpenModal(
        true,
        resultItem.item.stockItem.stockItemId,
        [],
        null
      );
    } else {
      this.constants.vehicleModal.loadItemAndOpenModal(
        false,
        resultItem.item.scan.scanId,
        [],
        null
      );
    }
  }


  toggleRequireAndMatch(){
    this.service.requireAndMatch = !this.service.requireAndMatch
  }

  showReg() {
    return !this.constants.GlobalParams.find(x => x.name === 'NeverScanReg').boolValue;
  }
}
