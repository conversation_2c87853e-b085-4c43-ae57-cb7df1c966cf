﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using StockPulse.Model;
using StockPulse.WebApi.Attribute;
using StockPulse.WebApi.Service;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using static StockPulse.WebApi.Attribute.UserLevelAccessAttribute;

namespace StockPulse.WebApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    [UserLevelAccess(new[] { UserRole.SysAdministrator, UserRole.Reconciler, UserRole.Resolver, UserRole.Approver, UserRole.GeneralManager, UserRole.ReadOnly, UserRole.ScanAndView })]
    public class ImportMasksController : ControllerBase, IAttributeValueProvider
    {

        private readonly IImportMaskService importMasksService;
        private readonly int userId;
        private readonly string userRole;

        public UserRole attributeUserRole => GetUserRole();
        private UserRole GetUserRole()
        {
            return (UserRole)Enum.Parse(typeof(UserRole), userRole);
        }

        //constructor
        public ImportMasksController(IImportMaskService importMasksService, IUserService userService)
        {
            this.importMasksService = importMasksService;
            userId = userService.GetUserId();
            userRole = userService.GetUserRole();
        }

        [HttpGet]
        [Route("GetImportMasks")]
        public async Task<IEnumerable<ImportMaskWithCreatedBy>> GetImportMasks()
        {
            return await importMasksService.GetImportMasks(userId);
        }


        [HttpPut]
        [Route("SaveExistingImportMask")]
        [UserLevelAccess(new[] { UserRole.SysAdministrator, UserRole.Reconciler, UserRole.Resolver, UserRole.Approver })]
        public async Task SaveExistingImportMask(ImportMaskSaveWithId importMask)
        {
            await importMasksService.SaveExistingImportMask(importMask, userId);
        }


        [HttpPost]
        [Route("SaveNewImportMask")]
        [UserLevelAccess(new[] { UserRole.SysAdministrator, UserRole.Reconciler, UserRole.Resolver, UserRole.Approver })]
        public async Task SaveNewImportMask(ImportMaskSave importMaskWithId)
        {
            await importMasksService.SaveNewImportMask(importMaskWithId, userId);
        }

        [HttpPut]
        [Route("Update")]
        public async Task Rename(ImportMaskUpdateParams parms)
        {
            await importMasksService.UpdateImportMasks(parms);
        }

        [HttpDelete]
        [Route("Delete")]
        public async Task Delete(int importMaskId)
        {
            await importMasksService.DeleteImportMask(importMaskId);
        }
    }
}
