﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace StockPulse.WebApi.Migrations
{
    /// <inheritdoc />
    public partial class renamedisRegEditedfieldinscans : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "IsVinEdited",
                schema: "dbo",
                table: "Scans",
                newName: "IsVinEditedOnWeb");

            migrationBuilder.RenameColumn(
                name: "IsEdited",
                schema: "dbo",
                table: "Scans",
                newName: "IsRegEditedOnWeb");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "IsVinEditedOnWeb",
                schema: "dbo",
                table: "Scans",
                newName: "IsVinEdited");

            migrationBuilder.RenameColumn(
                name: "IsRegEditedOnWeb",
                schema: "dbo",
                table: "Scans",
                newName: "IsEdited");
        }
    }
}
