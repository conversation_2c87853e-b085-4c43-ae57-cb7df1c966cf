﻿using StockPulse.WebApi.DataAccess;
using StockPulse.WebApi.ViewModel;
using System.Threading.Tasks;

namespace StockPulse.WebApi.Service
{
    public interface IFileImportService
    {
        Task<int> AddFileImport(FileImport fileImpor, int userId);
    }

    public class FileImportService : IFileImportService
    {
        private readonly IFileImportDataAccess fileImportDataAccess;

        public FileImportService(IFileImportDataAccess fileImportDataAccess)
        {
            this.fileImportDataAccess = fileImportDataAccess;
        }

        public async Task<int> AddFileImport(FileImport fileImport, int userId)
        {
            return await fileImportDataAccess.AddFileImport(fileImport, userId);
        }
    }
}
