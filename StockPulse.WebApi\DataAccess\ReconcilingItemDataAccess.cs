﻿using Dapper;
using StockPulse.WebApi.Dapper;
using StockPulse.Model;
using StockPulse.WebApi.ViewModel;
using StockPulse.WebApi.Service;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using StockPulse.Model.Import;

namespace StockPulse.WebApi.DataAccess
{
    public interface IReconcilingItemDataAccess
    {
        Task<IEnumerable<ReconcilingItem>> GetReconcilingItems(int stockcheckId, int userId);
        Task DeleteAllItems(int stockcheckId, int userId, int reconcilingItemType);
        Task DeleteRecItem(int stockcheckId, int userId, int itemId);
        //Task<ReconcilingItem> GetReconcilingItem(int stockcheckId, int userId);
        Task<IEnumerable<ReconcilingItemTypeStat>> GetReconcilingItemTypeStats(int stockcheckId, int userId);
        Task<IEnumerable<ReconcilingItemWithType>> GetReconcilingItemsWithType(int stockcheckId, int userId);
        Task<IEnumerable<ReconcilingItemVM>> GetReconcilingItemArray(int stockcheckId, int userId, int reconcilingItemTypeId);
        Task SaveNewReconcilingItems(List<ReconcilingItemSave> items, int userId);
        Task<IEnumerable<MatchItem>> GetMissingsMatchItems(int stockCheckId, int userId);
        Task<IEnumerable<MatchItem>> GetUnknownsMatchItems(int stockCheckId, int userId);
        Task<int> SaveBackup(string fileName, int stockCheckId, int reconcilingItemTypeId, int userId, IDbConnection db, IDbTransaction tran);
        Task DeleteBackup(int stockCheckId, int reconcilingItemTypeId, int fileId, int userId, IDbConnection db, IDbTransaction tran);
        Task<IEnumerable<ReconcilingItemBackup>> GetBackups(int stockCheckId, int reconcilingItemTypeId, int userId);
    }

    public class ReconcilingItemDataAccess : IReconcilingItemDataAccess
    {
        private readonly IDapper dapper;
        private readonly ISiteService siteService;
        private readonly IStockCheckDataAccess stockCheckDataAccess;

        public ReconcilingItemDataAccess(IDapper dapper, IStockCheckDataAccess stockCheckDataAccess, ISiteService siteService)
        {
            this.dapper = dapper;
            this.stockCheckDataAccess = stockCheckDataAccess;
            this.siteService = siteService;
        }

        public async Task<IEnumerable<ReconcilingItem>> GetReconcilingItems(int stockcheckId, int userId)
        {
            var paramList = new DynamicParameters();
            paramList.Add("StockCheckId", stockcheckId);
            paramList.Add("UserId", userId);
            return await dapper.GetAllAsync<ReconcilingItem>("dbo.GET_ReconcilingItems", paramList, System.Data.CommandType.StoredProcedure);
        }

        public async Task<IEnumerable<ReconcilingItemTypeStat>> GetReconcilingItemTypeStats(int stockcheckId, int userId)
        {
            var paramList = new DynamicParameters();
            paramList.Add("StockCheckId", stockcheckId);
            paramList.Add("UserId", userId);
            return await dapper.GetAllAsync<ReconcilingItemTypeStat>("dbo.GET_ReconcilingItemTypeStats", paramList, System.Data.CommandType.StoredProcedure);
        }

        //public async Task<ReconcilingItem> GetReconcilingItem(int stockcheckId, int userId)
        //{
        //    var paramList = new DynamicParameters();
        //    paramList.Add("ReconcilingItemId", stockcheckId);
        //    paramList.Add("UserId", userId);
        //    return await dapper.GetAsync<ReconcilingItem>("dbo.GET_ReconcilingItem", paramList, System.Data.CommandType.StoredProcedure);
        //}

        public async Task<IEnumerable<ReconcilingItemWithType>> GetReconcilingItemsWithType(int stockcheckId, int userId)
        {
            var paramList = new DynamicParameters();
            paramList.Add("StockCheckId", stockcheckId);
            paramList.Add("UserId", userId);

            return await dapper.GetAllAsync<ReconcilingItemWithType>("dbo.GET_ReconcilingItemsWithType", paramList, System.Data.CommandType.StoredProcedure);
        }

        public async Task<IEnumerable<ReconcilingItemVM>> GetReconcilingItemArray(int stockcheckId, int userId, int reconcilingItemTypeId)
        {
            var paramList = new DynamicParameters();
            paramList.Add("StockCheckId", stockcheckId);
            paramList.Add("UserId", userId);
            paramList.Add("ReconcilingItemTypeId", reconcilingItemTypeId);

            return await dapper.GetAllAsync<ReconcilingItemWithType>("dbo.GET_ReconcilingItemArray", paramList, System.Data.CommandType.StoredProcedure);
        }


        public async Task DeleteAllItems(int stockcheckId, int userId, int reconcilingItemType)
        {
            var paramList = new DynamicParameters();
            paramList.Add("StockCheckId", stockcheckId);
            paramList.Add("UserId", userId);
            paramList.Add("ReconcilingItemTypeId", reconcilingItemType);

            await dapper.ExecuteAsync("DELETE_AllReconcilingItems", paramList, System.Data.CommandType.StoredProcedure);
        }

        public async Task DeleteRecItem(int stockcheckId, int userId, int itemId)
        {
            var paramList = new DynamicParameters();
            paramList.Add("StockCheckId", stockcheckId);
            paramList.Add("UserId", userId);
            paramList.Add("ItemId", itemId);

            await dapper.ExecuteAsync("dbo.DELETE_RecItems", paramList, System.Data.CommandType.StoredProcedure);
        }



        public async Task SaveNewReconcilingItems(List<ReconcilingItemSave> items, int userId)
        {
            var paramList = new DynamicParameters(0);
            DataTable dt = new DataTable();

            dt.Columns.Add("Vin");
            dt.Columns.Add("Reg");
            dt.Columns.Add("Description");
            dt.Columns.Add("Comment");
            dt.Columns.Add("Reference");
            dt.Columns.Add("StockCheckId");
            dt.Columns.Add("SourceReportId");
            dt.Columns.Add("ReconcilingItemTypeId");
            dt.Columns.Add("FileImportId");

            List<string> unknownSiteNames = new List<string>();
            List<string> sitesWithoutActiveStockChecks = new List<string>();

            try
            {

                // Items need to be assigned to the stockcheck for their respective sites
                if(items.First().MultiSite)
                {
                    // Get a list of site names that user has access to & a list of all sites for dealergroup
                    IEnumerable<ViewModel.SiteVM> sitesForUser = await siteService.GetSites(userId);
                    IEnumerable<ViewModel.SiteVM> sitesForDealerGroup = await siteService.GetAllSites(userId);
                    IEnumerable<SiteDescriptionDictionary> sitesForDealerGroupFromDictionary = await siteService.GetAllSitesFromDictionary(userId);

                    List<string> siteNamesForUser = sitesForUser.Select(x => x.Description.ToUpper()).ToList();
                    List<string> siteNamesForDealerGroup = sitesForDealerGroup.Select(x => x.Description.ToUpper()).ToList();

                    IEnumerable<StockCheckVM> stockchecks = await stockCheckDataAccess.GetStockChecksOverview(userId, true);

                    foreach (var item in items)
                    {

                        int stockCheckIdForSite;
                        string uniqueVehicleIdentifier = item.Reg != "" ? $"reg {item.Reg}" : $"VIN {item.Vin}";

                        if (item.Site != null) { item.Site = item.Site.TrimEnd(' ', '\r'); };

                        if (item.Site == null)
                        {
                            throw new Exception($"Item with {uniqueVehicleIdentifier} does not contain a site name.");
                        }
                        else if (!siteNamesForDealerGroup.Contains(item.Site.ToUpper()) && !sitesForDealerGroupFromDictionary.Select(x => x.Description).Contains(item.Site.ToUpper()))
                        {
                            stockCheckIdForSite = 0;
                            unknownSiteNames.Add(item.Site);
                        }
                        else if (!siteNamesForUser.Contains(item.Site.ToUpper()) && (siteNamesForDealerGroup.Contains(item.Site.ToUpper()) && sitesForDealerGroupFromDictionary.Select(x => x.Description).Contains(item.Site.ToUpper())))
                        {
                            throw new Exception($"Could not upload the report as you do not have access to the site {item.Site}.");
                        }
                        else
                        {
                            int siteIdToUse;

                            var siteToUse = sitesForDealerGroup.Where(x => x.IsActive == true).FirstOrDefault(x => x.Description.ToUpper() == item.Site.ToUpper());
                            if (siteToUse != null)
                            {
                                siteIdToUse = siteToUse.Id;
                            }
                            else
                            {
                                siteIdToUse = sitesForDealerGroupFromDictionary.FirstOrDefault(x => x.Description == item.Site.ToUpper())?.SiteId ?? 0;
                            }

                            var stockCheckForSite = stockchecks.Where(x => x.SiteId == siteIdToUse).FirstOrDefault();

                            if (stockCheckForSite == null)
                            {
                                // Instead of throwing exception immediately, add to list of sites without stock checks
                                sitesWithoutActiveStockChecks.Add(item.Site);
                                // Use 0 as a placeholder, we'll check this list before executing the stored procedure
                                stockCheckIdForSite = 0;
                            }
                            else
                            {
                                stockCheckIdForSite = stockCheckForSite.Id;
                            }
                        }

                        dt.Rows.Add(
                            item.Vin,
                            item.Reg,
                            item.Description,
                            item.Comment,
                            item.Reference,
                            stockCheckIdForSite,
                            item.SourceReportId,
                            item.ReconcilingItemTypeId,
                            item.FileImportId
                            );
                    }
                }
                else
                {
                    foreach (var item in items)
                    {

                        dt.Rows.Add(
                            item.Vin,
                            item.Reg,
                            item.Description,
                            item.Comment,
                            item.Reference,
                            item.StockCheckId,
                            item.SourceReportId,
                            item.ReconcilingItemTypeId,
                            item.FileImportId
                            );
                    }
                }

            }
            catch(Exception e)
            {
                throw new Exception(e.Message);
            }

            if (unknownSiteNames.Count() > 0)
            {
                throw new Exception($"Could not find the following site names: '{string.Join(", ", unknownSiteNames.Distinct())}'. Please double check that the spelling matches a valid site in StockPulse or a mapping has been created.");
            }

            if (sitesWithoutActiveStockChecks.Count > 0)
            {
                throw new Exception($"No active stock check for the following sites: {string.Join(", ", sitesWithoutActiveStockChecks.Distinct())}.");
            }

            paramList.Add("Items", dt, DbType.Object);
            paramList.Add("UserId", userId);

            await dapper.ExecuteAsync("dbo.CREATE_ReconcilingItems", paramList, System.Data.CommandType.StoredProcedure);
        }


        public async Task<IEnumerable<MatchItem>> GetMissingsMatchItems(int stockCheckId, int userId)
        {
            return await dapper.GetAllAsync<MatchItem>("dbo.GET_ReconcilingItemMissingsMatchItems", new DynamicParameters(new { stockCheckId, userId }), CommandType.StoredProcedure);
        }
        public async Task<IEnumerable<MatchItem>> GetUnknownsMatchItems(int stockCheckId, int userId)
        {
            return await dapper.GetAllAsync<MatchItem>("dbo.GET_ReconcilingItemUnknownsMatchItems", new DynamicParameters(new { stockCheckId, userId }), CommandType.StoredProcedure);
        }

        public async Task<int> SaveBackup(string fileName, int stockCheckId, int reconcilingItemTypeId, int userId, IDbConnection db, IDbTransaction tran)
        {
            var paramList = new DynamicParameters();
            paramList.Add("FileName", fileName);
            paramList.Add("StockCheckId", stockCheckId);
            paramList.Add("ReconcilingItemTypeId", reconcilingItemTypeId);
            paramList.Add("UserId", userId);

            return await dapper.InsertAsync<int>("dbo.ADD_ReconcilingItemBackup", paramList, tran, db, CommandType.StoredProcedure);
        }

        public async Task DeleteBackup(int stockCheckId, int reconcilingItemTypeId, int fileId, int userId, IDbConnection db, IDbTransaction tran)
        {
            var paramList = new DynamicParameters();
            paramList.Add("StockCheckId", stockCheckId);
            paramList.Add("UserId", userId);
            paramList.Add("ReconcilingItemTypeId", reconcilingItemTypeId);
            paramList.Add("FileId", fileId);

            await dapper.InsertAsync<int>("dbo.DELETE_ReconcilingItemBackup", paramList, tran, db, CommandType.StoredProcedure);
        }

        public async Task<IEnumerable<ReconcilingItemBackup>> GetBackups(int stockCheckId, int reconcilingItemTypeId, int userId)
        {
            return await dapper.GetAllAsync<ReconcilingItemBackup>("dbo.GET_ReconcilingItemBackups", new DynamicParameters(new { stockCheckId, reconcilingItemTypeId, userId }), CommandType.StoredProcedure);

        }
    }
}
