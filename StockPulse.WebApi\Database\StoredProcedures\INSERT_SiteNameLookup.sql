

CREATE OR ALTER PROCEDURE [dbo].[INSERT_SiteNameLookup]
(
	@Name varchar(max),
	@IsPrimary bit,
	@UserId int,
	@SiteId int
)
  
AS  
BEGIN  

SET NOCOUNT ON;

DECLARE @userDealerGroupId INT;

SET @userDealerGroupId = (SELECT DealerGroupId FROM Users WHERE Id = @UserId)
  
INSERT INTO import.SiteDescriptionDictionary (Description, DealerGroupId, SiteId, IsPrimarySiteId)
VALUES
(@Name, @userDealerGroupId, @SiteId, @IsPrimary)

END
GO
