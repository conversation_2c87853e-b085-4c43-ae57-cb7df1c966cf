

export interface ImportMask {
  id: number;
  name: string;
  topRowsToSkip: number;
  columnValueEqualsesJSON?: string;
  columnValueEqualses?: Array<{ colIndex: number; colLetter?: string; matchingValue: string; }>;
  columnValueDifferentFromsJSON?: string;
  columnValueDifferentFroms?: Array<{ colIndex: number; colLetter?: string; matchingValue: string; }>;
  columnValueNotNullsJSON?: string;
  columnValueNotNulls?: { colIndex: number; colLetter?: string; }[];
  columnsWeWantJSON?: string;
  columnsWeWant?: {
    reg: Array<number>;
    vin: Array<number>;
    description: Array<number>;
    dis: Array<number>;
    groupDIS: Array<number>;
    reference: Array<number>;
    branch: Array<number>;
    comment: Array<number>;
    regPrevious: Array<number>;
    stockType: Array<number>;
    stockValue: Array<number>;
    site: Array<number>;
    accountCode: Array<number>;
    balance: Array<number>;
    flooring: Array<number>;
  };
  columnsWeWantAsLetters?: {
    reg: Array<string>;
    vin: Array<string>;
    description: Array<string>;
    dis: Array<string>;
    groupDIS: Array<string>;
    reference: Array<string>;
    branch: Array<string>;
    comment: Array<string>;
    regPrevious: Array<string>;
    stockType: Array<string>;
    stockValue: Array<string>;
    site: Array<string>;
    accountCode: Array<number>;
    balance: Array<number>;
    flooring: Array<string>;
  };
  createdBy?: string;
  isStandard?: boolean;
  isMultiSite?: boolean;
  ignoreZeroValues?: boolean;
}
