﻿using StockPulse.Model;
using System;
using System.Collections.Generic;

namespace StockPulse.WebApi.ViewModel
{
    public class StockCheckMobileApp
    {

        public StockCheckMobileApp(StockCheckVM stockCheck) 
        {
            Id = stockCheck.Id;
            Site = stockCheck.Site;
            Person = stockCheck.Person;
            Date = stockCheck.Date;
            LastUpdated = stockCheck.LastUpdated;
            Status = stockCheck.Status;
            StatusId = stockCheck.StatusId;
            IsRegional = stockCheck.IsRegional;
            IsTotal = stockCheck.IsTotal;
            HasSignoffImage = stockCheck.HasSignoffImage;
            SiteLongitude = stockCheck.SiteLongitude;
            SiteLatitude = stockCheck.SiteLatitude;
            ScannedInStock = stockCheck.ScannedInStock;
            Unknowns = stockCheck.Unknowns;
            Missings = stockCheck.Missings;
            UnknownOs = stockCheck.UnknownOs;
            MissingOs = stockCheck.MissingOs;
            ApprovedByAccountant = stockCheck.ApprovedByAccountant;
            ApprovedByGM = stockCheck.ApprovedByGM;
            ApprovedBy = stockCheck.ApprovedBy;
            ReconciliationCompletedDate = stockCheck.ReconciliationCompletedDate;
            ReconciliationApprovedDate = stockCheck.ReconciliationApprovedDate;
            StockItemsCount = stockCheck.StockItemsCount;
            OverrideLongLat = stockCheck.OverrideLongLat;
        }

        public int Id { get; set; }
        public string Site { get; set; }
        public string Person { get; set; }
        public DateTime Date { get; set; }
        public DateTime LastUpdated { get; set; }
        public string Status { get; set; }
        public int StatusId { get; set; }
        public bool IsRegional { get; set; }
        public bool IsTotal { get; set; }
        public bool HasSignoffImage { get; set; }

        
        public decimal SiteLongitude { get; set; }
        public decimal SiteLatitude { get; set; }


        //extra props added in

        public int ScannedInStock { get; set; }
        public int Unknowns { get; set; }
        public int Missings { get; set; }
        public int UnknownOs { get; set; }
        public int MissingOs { get; set; }
        public string ApprovedByAccountant { get; set; }
        public string ApprovedByGM { get; set; }
        public string ApprovedBy { get; set; }
        public DateTime? ReconciliationCompletedDate { get; set; }
        public DateTime? ReconciliationApprovedDate { get; set; }
        public decimal StockItemsCount { get; set; }

        //extra properties needed for mobile app
        public IEnumerable<StockItem> StockItems { get; set; }
        public IEnumerable<Location> Locations { get; set; }
        public IEnumerable<ReconcilingItemWithType> ReconcilingItems { get; set; }

        public IEnumerable<Scan> Scans { get; set; }

        public bool OverrideLongLat { get; set; }

    }
}
