﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].[UPDATE_ScanWithUnknownResolution]
(
    @StockCheckId INT,
	@UserId INT,
	@UnknownResolutionId INT,
	@ScanId INT
)
AS
BEGIN

SET NOCOUNT ON;

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END

UPDATE dbo.Scans
SET UnknownResolutionId = @UnknownResolutionId 
WHERE Id = @ScanId

EXEC [dbo].[UPDATE_StockCheckLastUpdate] @StockCheckId, @UserId


END
	


GO


