﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace StockPulse.Model
{
    public class DealerGroup
    {
        [Key]
        public int Id { get; set; }
        public string Description { get; set; }
        public int? NominatedUserId { get; set; }

        [ForeignKey("NominatedUserId")]
        public virtual User User { get; set; }

    }
}