import { ElementRef, EventEmitter, Injectable } from '@angular/core';
import { ConfirmModalComponent } from '../components/confirmModal/confirmModal.component';
import { VehicleModalComponent } from '../components/vehicleModal/vehicleModal.component';
import { CphPipe } from '../cph.pipe';
import { ImageStrings } from '../model/ImageStrings';
import { ResolutionType } from '../model/ResolutionType';
import { ScanRegDifference } from '../model/ScanRegDifference';
import { Scan } from '../model/Scan';
import { ScanWithLocation } from '../model/ScanWithLocation';
import { StockItemWithScan } from '../model/StockItemWithScan';
import { NewUserModalComponent } from '../pages/userSetup/addUserModal.component';
import { SiteVMWithSelected } from "../model/SiteVMWithSelected";
import { AppUserRole } from "../model/AppUserRole";
import { GlobalParam } from "./../model/GlobalParam";
import { ImportMask } from "./../model/ImportMask";
import { Location } from "./../model/Location";
import { Status } from "./../model/Status";
import { User } from "./../model/User";
import { DealerGroupSelectionModalComponent } from '../components/dealerGroupSelectionModal/dealerGroupSelectionModal.component';
import { ScanLocationsModalComponent } from '../components/scanLocationsModal/scanLocationsModal.component';
import { SelectionsService } from './selections.service';
import { Totals } from '../pages/signoff/signoff.component';

@Injectable({
  providedIn: 'root'
})
export class ConstantsService {


  //declarations
  //isInitialLoadComplete: boolean = false;
  Sites: SiteVMWithSelected[];
  SitesActive: SiteVMWithSelected[];
  Locations: Location[];
  Statuses: Array<Status>;
  Users: Array<User>;
  ResolutionTypes: ResolutionType[];
  ResolutionTypesActive: ResolutionType[];
  ImportMasks: Array<ImportMask>;
  userLoggedInThroughAzureAD: boolean = true;

  baseURL: string;
  baseURLUS: string;
  baseURLUK: string;
  //copiedTable: string;
  isMultiDealerGroupAccount: boolean;


  public alert: {
    show: boolean;
    message: string;
    type: string;
  } = {
      show: false,
      message: null,
      type: null
    }

  //vehicleModalRef: ElementRef;

  //showSpinner: boolean;
  alertModal: {
    elementRef: ElementRef;
    title: string;
    message: string;
    showOkInSuccessColour?:boolean;
  }


  Roles: AppUserRole[]
  addUserModal: NewUserModalComponent;
  confirmModal: ConfirmModalComponent;
  dealerGroupSelectionModal: DealerGroupSelectionModalComponent;
  vehicleModal: VehicleModalComponent;
  GlobalParams: GlobalParam[];
  ImageKey: string;
  ConfigFilePath: any;
  IsUploadDMSStockEnabled: boolean;
  IsSignoffConsignedEnabled: boolean;
  shouldUploadSignoffImage: boolean;
  allowAgencyStockUpload: boolean;
  AutomatedDataImport: boolean;
  BackgroundImageURL: string = '';

  refreshPage: EventEmitter<boolean> = new EventEmitter();
  //refreshPageDone: EventEmitter<boolean> = new EventEmitter();

  loadItemsExcelDownload: EventEmitter<boolean> = new EventEmitter();
  //alterReportBlobSize: EventEmitter<number> = new EventEmitter<number>();
  isDevelopmentEnvironment: boolean;
  accessToken: string;
  refreshToken: string;

  spinnerBackdrop: boolean = false;

  lightTheme: boolean = false;
  requireBackupForMissingResolution: boolean;
  requireBackupForUnknownResolution: boolean;
  strictStockCheckStatus: boolean;
  strictTBStockCheckStatus: boolean;

  currency: string;
  currencySymbol: string;
  scanLocationsModal: ScanLocationsModalComponent;
  importMasksUpdatedEmitter: EventEmitter<void> = new EventEmitter();

  showLabelPrintFeature: boolean;
  lockedReconcilingItemTypeIds: number[];

  smallScreenSize: boolean;

  sidenavToggledEmitter: EventEmitter<void> = new EventEmitter();
  
  whoAmILoaded: boolean = false;
  neverScanReg: boolean;

  constructor(
    //private selections: SelectionsService,
    private cph: CphPipe,
    private selections: SelectionsService
  ) {


    this.Statuses = [
      { id: 1, description: "Not Started" },
      { id: 2, description: "Scans In Progress" },
      { id: 3, description: "Scans Completed" },
      { id: 4, description: "Reconciliation Completed" },
      { id: 5, description: "Reconciliation Approved" }
    ]

  }



  splitOn(source: string, splitChar: string): string[] {

    var parts: string[] = [];
    var currentPart = "";
    var isInQuotes = false;

    for (let i = 0; i < source.length; i++) {
      var char = source.charAt(i);
      if (char === splitChar && !isInQuotes) {
        parts.push(currentPart);
        currentPart = "";
      } else {
        currentPart += char;
      }
      if (char === '"') {
        isInQuotes = !isInQuotes;
      }
    }

    if (currentPart) parts.push(currentPart);

    return parts
  }



  pluralise(number: Number, singular: string, plural: string) {
    if (number == 0) {
      return 'No ' + plural;
    } else if (number == 1) {
      return number + ' ' + singular
    } else {
      return this.cph.transform(number, 'number', 0) + ' ' + plural
    }

  }

  sum(values: Array<any>): number {
    if (values.length == 0) { return 0 }
    let total = 0;
    values.forEach(v => {
      total += parseFloat(v)
    })
    return total

  }

  columnNumberToColumnLetterString(num: number): string {
    function toLetters(num) {
      "use strict";
      var mod = num % 26;
      var pow = num / 26 | 0;
      var out = mod ? String.fromCharCode(64 + mod) : (pow--, 'Z');

      let result = pow ? toLetters(pow) + out : out;
      if (result == '?Z') result = ''
      return result
    }

    return toLetters(num)
  }

  columnLetterStringToNumber(letters: string): number {
    function fromLetters(str) {
      "use strict";
      var out = 0, len = str.length, pos = len;
      while (--pos > -1) {
        out += (str.charCodeAt(pos) - 64) * Math.pow(26, len - 1 - pos);
      }
      return out;
    }

    return fromLetters(letters)
  }

  columnNumbersToLetters(numbers: number[]): string[] {
    let letters: string[] = []

    if (numbers) {
      numbers.forEach(num => {
        letters.push(this.columnNumberToColumnLetterString(num))
      })
    }

    return letters
  }

  columnLettersToNumbers(letters: string[]): number[] {
    let numbers: number[] = []

    if (letters) {

      letters.forEach(letter => {
        numbers.push(this.columnLetterStringToNumber(letter))
      })
    }

    return numbers
  }

  clone(object: any): any {
    let clone = JSON.parse(JSON.stringify(object))
    return clone

  }

  // public addImageStringsToStockItemFullDetail(stockItem: StockItemFullDetail) {
  //   let results:ImageStrings=this.buildImageStrings(stockItem.scanId,false)
  //   stockItem.regImageLargeUrl = results.regImageLargeUrl;
  //   stockItem.regImageThumbnailUrl = results.regImageThumbnailUrl;

  // }


  // public addImageStringsToScanFullDetail(scan: ScanFullDetail) {
  //   let results:ImageStrings=this.buildImageStrings(scan.scanId,scan.hasVinImage)
  //   scan.regImageLargeUrl = results.regImageLargeUrl;
  //   scan.regImageThumbnailUrl = results.regImageThumbnailUrl;
  //   scan.vinImageUrl = results.vinImageUrl;
  // }
  public addImageStringsToScan(scan: Scan) {
    let results: ImageStrings = this.buildImageStrings(scan.scanId, scan.hasVinImage)
    scan.regImageLargeUrl = results.regImageLargeUrl;
    scan.regImageThumbnailUrl = results.regImageThumbnailUrl;
    scan.vinImageUrl = results.vinImageUrl;
  }


  public addImageStringsToScanWithLocation(scan: ScanWithLocation) {
    let results: ImageStrings = this.buildImageStrings(scan.id, false)
    scan.regImageLargeUrl = results.regImageLargeUrl;
    scan.regImageThumbnailUrl = results.regImageThumbnailUrl;
    scan.vinImageUrl = results.vinImageUrl;
  }
  public addImageStringsToStockItemWithScan(stockItem: StockItemWithScan) {
    let results: ImageStrings = this.buildImageStrings(stockItem.scanId, false)
    stockItem.regImageLargeUrl = results.regImageLargeUrl;
    stockItem.regImageThumbnailUrl = results.regImageThumbnailUrl;
    stockItem.vinImageUrl = results.vinImageUrl;
  }



  public addImageThumbnailStringsToScanRegDifference(scanRegDifference: ScanRegDifference) {
    let results: ImageStrings = this.buildImageStrings(scanRegDifference.scanId, false)
    scanRegDifference.regImageThumbnailUrl = results.regImageThumbnailUrl;
  }

  public buildSignOffImageURL(id: number): string {
    let fileName = id + "S";
    return this.makeFullImageUrl(fileName);
  }



  public buildImageStrings(id: number, hasVinImage: boolean): ImageStrings {

    let result: ImageStrings = {
      regImageLargeUrl: this.makeLargeImageUrl(id),
      regImageThumbnailUrl: this.makeThumbnailImageUrl(id),
      vinImageUrl: hasVinImage ? this.makeVinImageUrl(id) : null,
    }

    return result;
  }


  public makeLargeImageUrl(id: number) {
    return this.makeFullImageUrl(id + "L.jpg")
  }

  public makeThumbnailImageUrl(id: number) {
    return this.makeFullImageUrl(id + "T.jpg")
  }

  public makeVinImageUrl(id: number) {
    return this.makeFullImageUrl(id + "V.jpg")
  }

  private makeFullImageUrl(filename: string): string {
    return `${this.ConfigFilePath}${filename}?${this.ImageKey}`
  }

  getDealerGroupName(): string {
    const DealerGroupName = localStorage.getItem('selectedDealerGroup');
    if (DealerGroupName) {
        return DealerGroupName;
    }
    else return '';
  }

  provideBaseUrl(): string {
    const url = localStorage.getItem('baseURL');
    if (url){
      return `${url}/api`;
    }
    
    return `${this.baseURL}/api`;
    //return 'https://stockpulseapi.cphi.co.uk/api' //to update based on user dealerGroup
  }

  disableStatus(status: Status, totals?: Totals): boolean {
    
    if (this.selections.userIsReadOnly){
      return true;
    }
    
    else if (this.selections.stockCheck.statusId > 3 && !this.selections.userIsApprover) {
      return true;
    }

    else if (status.id === 4 && (this.selections.userIsResolver || this.selections.userIsGeneralManager)) {
      return true;
    }

    else if (this.strictStockCheckStatus && 
      (this.selections.stockCheck.missingOs > 0 || this.selections.stockCheck.unknownOs > 0) && 
     (status.description === 'Reconciliation Completed' || status.description === 'Reconciliation Approved')
    ) {
      return true;
    }

    else if (
      this.strictTBStockCheckStatus && totals && totals?.difference != 0 &&
      (status.description === 'Reconciliation Completed' || status.description === 'Reconciliation Approved')
    ) {
      return true;
    }

    else if (this.selections.stockCheck.statusId < 4 && status.id === 5) {
      return true; // can't move to approved if not completed
    }

    return false;
  }

 


}
