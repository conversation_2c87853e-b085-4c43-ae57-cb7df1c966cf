﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace StockPulse.WebApi.Migrations
{
    /// <inheritdoc />
    public partial class removeimporttablesmmg : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "MMGAtAuctions",
                schema: "import");

            migrationBuilder.DropTable(
                name: "MMGFinancialLines",
                schema: "import");

            migrationBuilder.DropTable(
                name: "MMGStockItems",
                schema: "import");

            migrationBuilder.DropTable(
                name: "MMGStockOnLoans",
                schema: "import");

            migrationBuilder.DropTable(
                name: "MMGWIPs",
                schema: "import");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "MMGAtAuctions",
                schema: "import",
                columns: table => new
                {
                    FileImportId = table.Column<int>(type: "int", nullable: true),
                    ReconcilingItemTypeId = table.Column<int>(type: "int", nullable: false),
                    SiteId = table.Column<int>(type: "int", nullable: false),
                    SourceReportId = table.Column<int>(type: "int", nullable: false),
                    Comment = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    Description = table.Column<string>(type: "nvarchar(250)", maxLength: 250, nullable: true),
                    Id = table.Column<int>(type: "int", nullable: false),
                    Reference = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Reg = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Vin = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.ForeignKey(
                        name: "FK_MMGAtAuctions_FileImports_FileImportId",
                        column: x => x.FileImportId,
                        principalSchema: "import",
                        principalTable: "FileImports",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_MMGAtAuctions_ReconcilingItemTypes_ReconcilingItemTypeId",
                        column: x => x.ReconcilingItemTypeId,
                        principalSchema: "dbo",
                        principalTable: "ReconcilingItemTypes",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_MMGAtAuctions_Sites_SiteId",
                        column: x => x.SiteId,
                        principalSchema: "dbo",
                        principalTable: "Sites",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_MMGAtAuctions_SourceReports_SourceReportId",
                        column: x => x.SourceReportId,
                        principalSchema: "dbo",
                        principalTable: "SourceReports",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "MMGFinancialLines",
                schema: "import",
                columns: table => new
                {
                    FileImportId = table.Column<int>(type: "int", nullable: true),
                    SiteId = table.Column<int>(type: "int", nullable: false),
                    AccountDescription = table.Column<string>(type: "nvarchar(250)", maxLength: 250, nullable: true),
                    Balance = table.Column<decimal>(type: "decimal(15,3)", nullable: false),
                    Code = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Id = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.ForeignKey(
                        name: "FK_MMGFinancialLines_FileImports_FileImportId",
                        column: x => x.FileImportId,
                        principalSchema: "import",
                        principalTable: "FileImports",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_MMGFinancialLines_Sites_SiteId",
                        column: x => x.SiteId,
                        principalSchema: "dbo",
                        principalTable: "Sites",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "MMGStockItems",
                schema: "import",
                columns: table => new
                {
                    FileImportId = table.Column<int>(type: "int", nullable: true),
                    SiteId = table.Column<int>(type: "int", nullable: false),
                    SourceReportId = table.Column<int>(type: "int", nullable: false),
                    Branch = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Comment = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    DIS = table.Column<int>(type: "int", nullable: true),
                    Description = table.Column<string>(type: "nvarchar(250)", maxLength: 250, nullable: true),
                    GroupDIS = table.Column<int>(type: "int", nullable: true),
                    Id = table.Column<int>(type: "int", nullable: false),
                    Reference = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Reg = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    StockType = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    StockValue = table.Column<decimal>(type: "decimal(15,3)", nullable: false),
                    Vin = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.ForeignKey(
                        name: "FK_MMGStockItems_FileImports_FileImportId",
                        column: x => x.FileImportId,
                        principalSchema: "import",
                        principalTable: "FileImports",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_MMGStockItems_Sites_SiteId",
                        column: x => x.SiteId,
                        principalSchema: "dbo",
                        principalTable: "Sites",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_MMGStockItems_SourceReports_SourceReportId",
                        column: x => x.SourceReportId,
                        principalSchema: "dbo",
                        principalTable: "SourceReports",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "MMGStockOnLoans",
                schema: "import",
                columns: table => new
                {
                    FileImportId = table.Column<int>(type: "int", nullable: true),
                    ReconcilingItemTypeId = table.Column<int>(type: "int", nullable: false),
                    SiteId = table.Column<int>(type: "int", nullable: false),
                    SourceReportId = table.Column<int>(type: "int", nullable: false),
                    Comment = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    Description = table.Column<string>(type: "nvarchar(250)", maxLength: 250, nullable: true),
                    Id = table.Column<int>(type: "int", nullable: false),
                    Reference = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Reg = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Vin = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.ForeignKey(
                        name: "FK_MMGStockOnLoans_FileImports_FileImportId",
                        column: x => x.FileImportId,
                        principalSchema: "import",
                        principalTable: "FileImports",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_MMGStockOnLoans_ReconcilingItemTypes_ReconcilingItemTypeId",
                        column: x => x.ReconcilingItemTypeId,
                        principalSchema: "dbo",
                        principalTable: "ReconcilingItemTypes",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_MMGStockOnLoans_Sites_SiteId",
                        column: x => x.SiteId,
                        principalSchema: "dbo",
                        principalTable: "Sites",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_MMGStockOnLoans_SourceReports_SourceReportId",
                        column: x => x.SourceReportId,
                        principalSchema: "dbo",
                        principalTable: "SourceReports",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "MMGWIPs",
                schema: "import",
                columns: table => new
                {
                    FileImportId = table.Column<int>(type: "int", nullable: true),
                    ReconcilingItemTypeId = table.Column<int>(type: "int", nullable: false),
                    SiteId = table.Column<int>(type: "int", nullable: false),
                    SourceReportId = table.Column<int>(type: "int", nullable: false),
                    Comment = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Description = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Id = table.Column<int>(type: "int", nullable: false),
                    Reference = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Reg = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Vin = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.ForeignKey(
                        name: "FK_MMGWIPs_FileImports_FileImportId",
                        column: x => x.FileImportId,
                        principalSchema: "import",
                        principalTable: "FileImports",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_MMGWIPs_ReconcilingItemTypes_ReconcilingItemTypeId",
                        column: x => x.ReconcilingItemTypeId,
                        principalSchema: "dbo",
                        principalTable: "ReconcilingItemTypes",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_MMGWIPs_Sites_SiteId",
                        column: x => x.SiteId,
                        principalSchema: "dbo",
                        principalTable: "Sites",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_MMGWIPs_SourceReports_SourceReportId",
                        column: x => x.SourceReportId,
                        principalSchema: "dbo",
                        principalTable: "SourceReports",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_MMGAtAuctions_FileImportId",
                schema: "import",
                table: "MMGAtAuctions",
                column: "FileImportId");

            migrationBuilder.CreateIndex(
                name: "IX_MMGAtAuctions_ReconcilingItemTypeId",
                schema: "import",
                table: "MMGAtAuctions",
                column: "ReconcilingItemTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_MMGAtAuctions_SiteId",
                schema: "import",
                table: "MMGAtAuctions",
                column: "SiteId");

            migrationBuilder.CreateIndex(
                name: "IX_MMGAtAuctions_SourceReportId",
                schema: "import",
                table: "MMGAtAuctions",
                column: "SourceReportId");

            migrationBuilder.CreateIndex(
                name: "IX_MMGFinancialLines_FileImportId",
                schema: "import",
                table: "MMGFinancialLines",
                column: "FileImportId");

            migrationBuilder.CreateIndex(
                name: "IX_MMGFinancialLines_SiteId",
                schema: "import",
                table: "MMGFinancialLines",
                column: "SiteId");

            migrationBuilder.CreateIndex(
                name: "IX_MMGStockItems_FileImportId",
                schema: "import",
                table: "MMGStockItems",
                column: "FileImportId");

            migrationBuilder.CreateIndex(
                name: "IX_MMGStockItems_SiteId",
                schema: "import",
                table: "MMGStockItems",
                column: "SiteId");

            migrationBuilder.CreateIndex(
                name: "IX_MMGStockItems_SourceReportId",
                schema: "import",
                table: "MMGStockItems",
                column: "SourceReportId");

            migrationBuilder.CreateIndex(
                name: "IX_MMGStockOnLoans_FileImportId",
                schema: "import",
                table: "MMGStockOnLoans",
                column: "FileImportId");

            migrationBuilder.CreateIndex(
                name: "IX_MMGStockOnLoans_ReconcilingItemTypeId",
                schema: "import",
                table: "MMGStockOnLoans",
                column: "ReconcilingItemTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_MMGStockOnLoans_SiteId",
                schema: "import",
                table: "MMGStockOnLoans",
                column: "SiteId");

            migrationBuilder.CreateIndex(
                name: "IX_MMGStockOnLoans_SourceReportId",
                schema: "import",
                table: "MMGStockOnLoans",
                column: "SourceReportId");

            migrationBuilder.CreateIndex(
                name: "IX_MMGWIPs_FileImportId",
                schema: "import",
                table: "MMGWIPs",
                column: "FileImportId");

            migrationBuilder.CreateIndex(
                name: "IX_MMGWIPs_ReconcilingItemTypeId",
                schema: "import",
                table: "MMGWIPs",
                column: "ReconcilingItemTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_MMGWIPs_SiteId",
                schema: "import",
                table: "MMGWIPs",
                column: "SiteId");

            migrationBuilder.CreateIndex(
                name: "IX_MMGWIPs_SourceReportId",
                schema: "import",
                table: "MMGWIPs",
                column: "SourceReportId");
        }
    }
}
