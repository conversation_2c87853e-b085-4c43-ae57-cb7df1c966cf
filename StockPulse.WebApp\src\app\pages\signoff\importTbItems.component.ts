import { Component, OnInit,  ElementRef, ViewChild } from "@angular/core";
import { SelectionsService } from '../../services/selections.service';
import { ConstantsService } from '../../services/constants.service';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Observable } from 'rxjs';
import { GetDataService } from '../../services/getData.service';
import * as XLSX from 'xlsx';
import { CphPipe } from '../../cph.pipe';
import {IconService} from '../../services/icon.service'
import { SaveDataService } from 'src/app/services/saveData.service';
import { FinancialLineVM } from "src/app/model/FinancialLineVM";
import { ApiAccessService } from "src/app/services/apiAccess.service";
import { ToastService } from "src/app/services/newToast.service";
import { Router } from '@angular/router';

@Component({
  selector: 'importTbItems',
  template:    `

    <button id="importButton" class="btn btn-primary" [disabled]="selections.stockCheck.statusId>3 || selections.userIsGeneralManager || selections.userIsReadOnly" (click)="redirectToImportDataPage()">
    <fa-icon [icon]="icon.faFileUpload"  ></fa-icon>
        Import</button>


    `
  ,
  styles: [
    `
    #importButton{margin:3em auto;}
    #tableHolder{height: 71vh}
  `
  ]
})


export class ImportTbItemsComponent implements OnInit {
  @ViewChild('importVehiclesModal', { static: true }) importVehiclesModal: ElementRef;

  //for excel import
  excelData: any[][] = [[1, 2], [3, 4]];
  wopts: XLSX.WritingOptions = { bookType: 'xlsx', type: 'array' };
  fileName: string = 'SheetJS.xlsx';
  
  newlyImportedCount: number;
  amLoadingOrDeleting: boolean;

  financialLinesToImport: FinancialLineVM[]

  constructor(
    public selections: SelectionsService,
    public constants: ConstantsService,
    public data: GetDataService,
    public save: SaveDataService,
    public modalService: NgbModal,
    public cphPipe: CphPipe,
    public icon: IconService,
    public api: ApiAccessService,
    public toastService: ToastService,
    public router: Router,
  ) {

  }


  ngOnInit(): void {

   

  }



  initParams() {

  }






  //-----------------------------------------
  // Import process 

  redirectToImportDataPage(){
    this.router.navigateByUrl('/loadItems');
  }
  openImportModal() {
    //this.importModalIsOpen = true;



    this.modalService.open(this.importVehiclesModal, {windowClass: 'highSmall', size: 'sm', keyboard: true, ariaLabelledBy: 'modal-basic-title' }).result.then((result) => {
      //have chosen to 'OK' selections
      //give vehicles to parent
      ///this.saveImportedLines(this.financialLinesToImport)


    }, (reason) => {
      //dismissed
      this.financialLinesToImport = [];
    });
  }

  onFileChange(evt: any) {
    /* wire up file reader */
    const target: DataTransfer = <DataTransfer>(evt.target);
    if (target.files.length !== 1) throw new Error('Cannot use multiple files');
    let filename: string = target.files[0].name + ' uploaded ' + this.cphPipe.transform(new Date(), 'time', 0) + ' ' + this.cphPipe.transform(new Date(), 'shortDate', 0) + ' by ' + this.selections.usersName;
    const reader: FileReader = new FileReader();
    reader.onload = (e: any) => {
      /* read workbook */
      const bstr: string = e.target.result;
      const wb: XLSX.WorkBook = XLSX.read(bstr, { type: 'binary' });

      /* grab first sheet */
      const wsname: string = wb.SheetNames[0];
      const ws: XLSX.WorkSheet = wb.Sheets[wsname];

      /* save data */
      this.excelData = <any[][]>(XLSX.utils.sheet_to_json(ws, { header: 1, blankrows: false }));

      this.parseImportedReport(filename);
    };
    reader.readAsBinaryString(target.files[0]);
  }


  //standard method to find the required data based on provided column indexes
  provideData(line: any, columnIndexes: Array<number>) {
    let result = '';
    columnIndexes.forEach((index, iteration) => {
      //if -1, indicates just want empty result
      if (index == -1) {
        result = ''
      } else {
        //otherwise do properly
        if (iteration > 0) {
          result = result + ' | ';
        }
        result = result + line[index]
      }

    })

    if (result == 'undefined') {
      result = '';
    }
    return result;
  }


  //standard method to sum the required data based on provided column indexes
  sumData(line: any, columnIndexes: Array<number>) {
    let result = 0;
    try{

      columnIndexes.forEach((index, iteration) => {
        //if -1, indicates just want empty result
        if (index == -1) {
          result = 0
        } else {
          //aggregate
          result += line[index]
        }
        
      })
      
    }
    catch{
      result = 0
    }

    return result;
  }





  parseImportedReport(filename: string) {

    //do the processing
    this.excelData.splice(0, 3); //remove the header row
   
    //let accountCodesExclude = [9030,9031,9032,9033,9034,9038,9045,9046,9047,9059,9062,9063,9064,9065,9066,9067,9075,9076,9077,9085,9086,9087];
   
    /*
    let filteredImportData = this.excelData.filter(item =>  
      item &&
      accountCodesExclude.indexOf(item[0]) == -1 &&
      item[0] >= 9020 &&
      item[0] <= 9087)
      */
      let filteredImportData = this.excelData;
   
    //just grab the columns we want
    try {
      let financialLinesToImport: FinancialLineVM[] = []
      filteredImportData.forEach(line => {
        
        let financialLine: FinancialLineVM = {
         accountCode: JSON.stringify(line[1]),
          description: line[2],
          notes: '',
          balance: line[3],
          isReconcilingAdj:false,

        };

        financialLinesToImport.push(financialLine);
      })

      this.financialLinesToImport = financialLinesToImport;

    }
    catch (e) {
     
    }


  }

  removeImport(i: number) {
    this.financialLinesToImport.splice(i, 1);
  }





}


