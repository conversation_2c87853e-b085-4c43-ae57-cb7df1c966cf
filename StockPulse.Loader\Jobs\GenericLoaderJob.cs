﻿using Quartz;
using System;
using System.Collections.Generic;
using log4net;
using System.Threading.Tasks;
using StockPulse.Loader.Services;
using StockPulse.Loader.Stockpulse.ViewModel;
using System.IO;
using System.Data;
using System.Linq;
using StockPulse.Model;
using StockPulse.Repository.Database;
using System.Globalization;

namespace StockPulse.Loader.Jobs
{
   [DisallowConcurrentExecution]
   public class GenericLoaderJob : IJob
   {
      private ILog logger;
      private LogMessage logMessage;
      private const int maxDelayMinutes = 10; // Minutes to wait before displaying NoFilesFoundMsg

      public async Task Execute(IJobExecutionContext context)
      {
         // If job already running, go no further.
         if (LocksService.GenericLoaderJob.value == true) { return; }

         logger = LogManager.GetLogger(typeof(GenericLoaderJob));

         List<JobParams> incomingLoadCollection = GenericJobCustomerLookup.BuildIncomingLoads();

         bool haveFilesToProcess = CheckIfHaveFilesToProcess(incomingLoadCollection);

         if (!haveFilesToProcess)
         {
            CreateNoFilesFoundMessage();
            return;
         }
         else
         {
            LocksService.GenericLoaderJob.value = true;
         }

         try
         {
            using (var db = new StockpulseContext())
            {
               foreach (JobParams parms in incomingLoadCollection)
               {
                  // Get the specific service for the job based on the params
                  GenericLoaderJobServiceParams jobParams = GenericJobCustomerLookup.GetStockJobParams(parms);

                  foreach (var fileToProcess in parms.allMatchingFiles)
                  {
                     try
                     {
                        logMessage = new LogMessage();

                        string processingFilePath = "";

                        // If file already processing, do nothing and continue
                        parms.filename = Path.GetFileName(fileToProcess);

                        parms.errorCount = 0;

                        if (fileToProcess.Contains("-p"))
                        {
                           logger.Info($@"[{DateTime.UtcNow}] File {fileToProcess} found but already processing...");
                           continue;
                        }
                        else
                        {

                           if (parms.fileType == FileType.csv)
                           {
                              string extension = Path.GetExtension(fileToProcess).ToLower();

                              if (extension == ".csv")
                              {
                                 string newFilePath = Path.ChangeExtension(fileToProcess, null) + "-p.csv";
                                 File.Move(fileToProcess, newFilePath);
                                 processingFilePath = newFilePath;
                              }
                           }
                           else if (parms.fileType == FileType.xlsx)
                           {
                              string extension = Path.GetExtension(fileToProcess).ToLower();

                              if (extension == ".xlsx")
                              {
                                 string newFilePath = Path.ChangeExtension(fileToProcess, null) + "-p.xlsx";
                                 File.Move(fileToProcess, newFilePath);
                                 processingFilePath = newFilePath;
                              }
                           }

                           if (parms.convertToXlsx)
                           {
                              if (System.IO.Path.GetExtension(processingFilePath).Equals(".csv", StringComparison.OrdinalIgnoreCase))
                              {
                                 string newFile = System.IO.Path.ChangeExtension(processingFilePath, ".xlsx");

                                 System.IO.File.Move(processingFilePath, newFile); // Copying and renaming the file
                                 System.IO.File.Delete(processingFilePath); // Deleting old file

                                 processingFilePath = newFile; // Updating the array with the new file path
                                 parms.fileType = FileType.xlsx;
                              }
                           }
                        }

                        CreateNewFileFoundMessage(logMessage, processingFilePath);

                        var sharedService = new SharedLoaderService(logger);

                        DateTime start = DateTime.UtcNow;

                        parms.fileImportId = await sharedService.AddFileToImports(processingFilePath, parms.isUS);

                        RowsAndHeadersResult rowsAndCells = await sharedService.GetRowsAndHeaders(parms, processingFilePath, logMessage);

                        DataTable datatable = jobParams.InterpretFile(parms, rowsAndCells, logMessage);

                        await sharedService.FinishImport(parms, datatable, start, processingFilePath, logMessage);
                     }
                     catch (Exception err)
                     {
                        logMessage.FailNotes = logMessage.FailNotes + $"General failure " + err.ToString();
                        parms.errorCount++;
                        logMessage.DealerGroup_Id = parms.dealerGroupId;
                        logMessage.FailNotes = $"{parms.errorCount} errors " + "\r\n ------------------------ Errors ----------------------------- \r\n" + logMessage.FailNotes;
                        logMessage.Job = SharedLoaderService.GetJobName(parms);
                        CentralLoggingService.ReportError(logMessage, parms.isUS);
                     }
                     finally
                     {
                        db.ChangeTracker.Clear();
                     }
                  }
               }
            }
         }
         finally
         {
            logger.Info($@"[{DateTime.UtcNow}] | GenericLoaderJob | Job completed. Removing lock.");
            LocksService.GenericLoaderJob.value = false;
         }
      }


      private static bool CanParseDateFromFilename(string filename)
      {
         if (string.IsNullOrEmpty(filename) || filename.Length < 15)
         {
            return false;
         }

         try
         {
            DateTime.ParseExact(filename.Substring(0, 15), "yyyyMMdd_HHmmss", CultureInfo.InvariantCulture);
            return true;
         }
         catch (FormatException)
         {
            return false;
         }
      }

      private void CreateNoFilesFoundMessage()
      {
         TimeSpan age = DateTime.UtcNow - PulsesService.GenericLoaderJob.Date;

         if (age.Minutes > maxDelayMinutes)
         {
            PulsesService.GenericLoaderJob.Date = DateTime.UtcNow;
            logger.Info($@"[{DateTime.UtcNow}] | GenericLoaderJob | No files found");
         }
      }

      private static bool CheckIfHaveFilesToProcess(List<JobParams> incoming)
      {
         bool haveFilesToProcess = false;
         int i = 0;

         while (!haveFilesToProcess && i < incoming.Count())
         {
            JobParams thisLoad = incoming[i];
            haveFilesToProcess = thisLoad.allMatchingFiles.Count() > 0;
            i++;
         }

         return haveFilesToProcess;
      }

      private void CreateNewFileFoundMessage(LogMessage logMessage, string fileToProcess)
      {
         logMessage.SourceDate = DateTime.UtcNow;
         logMessage.Job = GetType().Name;

         logger.Info($@"[{DateTime.UtcNow}] New file found at {ConfigService.incomingRoot}.  Starting {fileToProcess}");   //update logger 
      }


   }


}
