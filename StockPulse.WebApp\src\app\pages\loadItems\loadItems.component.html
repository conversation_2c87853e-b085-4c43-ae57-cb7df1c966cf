<nav class="page-specific-navbar">
  <singleSitePickerWithSearch></singleSitePickerWithSearch>

  <!-- Dropdown to choose report -->
  <div *ngIf="reconcilingItems?.length" ngbDropdown id="chooseReport" class="d-inline-block">
   
    <!-- The button which is the dropdown showing what is chosen -->
    <button class="btn btn-primary dropdownButtonToPickReportType" ngbDropdownToggle [ngClass]="{ 'restricted': constants.smallScreenSize }">
      <div class="buttonInner">
        <div class="spaceBetween">
          <!-- Report header -->
          <div class="reportDescription">
            <span>{{ selectedReport ? selectedReport.description : 'Select Data Type' }}</span>
          </div>
          <div *ngIf="selectedReport" class="count">
            <span>
              {{ selectedReport.vehicleCount | cph:'number':0 }}
              <!-- <fa-icon [icon]="icon.faArrowAltDown"></fa-icon> -->
            </span>
          </div>
        </div>
      </div>
    </button>

    <!-- The buttons to pick from -->
    <div ngbDropdownMenu aria-labelledby="dropdownBasic1">

      <!-- Agency Stock button (if enabled for DG)-->
      <ng-container *ngFor="let item of getSpecificReconcilingItems('agencyStock')" >
        <button *ngIf="constants.allowAgencyStockUpload" 
                class=" reconcilingItemTypeButton " 
                ngbDropdownItem  (click)="chooseReconcilingItemType(item)">
          <div class="spaceBetween">
            <div>{{ item.description }}</div>
            <div class="flex">
              <div class="count">{{ item.vehicleCount | cph:'number':0 }}</div>
            </div>
          </div>
        </button>
      </ng-container>

      <div *ngIf="constants.allowAgencyStockUpload"  class="dropdown-divider"></div>

      <!-- DMS Stock button -->
      <ng-container *ngFor="let item of getSpecificReconcilingItems('dmsStock')" >
        <button class=" reconcilingItemTypeButton " ngbDropdownItem  (click)="chooseReconcilingItemType(item)">
          <div class="spaceBetween">
            <div>{{ item.description }}</div>
            <div class="flex">
              <div class="count">{{ item.vehicleCount | cph:'number':0 }}</div>
            </div>
          </div>
        </button>
      </ng-container>

      
      <div class="dropdown-divider"></div>


      <!-- Trial Balance -->
      <ng-container *ngIf="allowTrialBalanceUpload()">
        <ng-container *ngFor="let item of getSpecificReconcilingItems('trialBalance')" >
          <button class=" reconcilingItemTypeButton " ngbDropdownItem (click)="chooseReconcilingItemType(item)">
            <div class="spaceBetween">
              <div>{{ item.description }}</div>
              <div class="flex">
                <div class="count">{{ item.vehicleCount | cph:'number':0 }}</div>
              </div>
            </div>
          </button>
        </ng-container>

        <div class="dropdown-divider"></div>
      </ng-container>

      <!-- Missing -->
      <ng-container *ngFor="let item of getSpecificReconcilingItems('missing'); index as i" >
        <div *ngIf="i === 0" class="dropdownSubTitle"><b>Missing Reports</b></div>
        <button class=" reconcilingItemTypeButton " ngbDropdownItem  (click)="chooseReconcilingItemType(item)">
          <div class="spaceBetween">
            <div>{{ item.description }}</div>
            <div class="flex">
              <div class="count">{{ item.vehicleCount | cph:'number':0 }}</div>
            </div>
          </div>
        </button>
      </ng-container>

      <!-- Unknown -->
      <ng-container *ngFor="let item of getSpecificReconcilingItems('unknown'); index as i" >
        <ng-container *ngIf="i === 0">
          <div class="dropdown-divider"></div>
          <div class="dropdownSubTitle"><b>Unknown Reports</b></div>
        </ng-container>
        <button class=" reconcilingItemTypeButton " ngbDropdownItem  (click)="chooseReconcilingItemType(item)">
          <div class="spaceBetween">
            <div>{{ item.description }}</div>
            <div class="flex">
              <div class="count">{{ item.vehicleCount | cph:'number':0 }}</div>
            </div>
          </div>
        </button>
      </ng-container>
    </div>
  </div>

  <ng-container *ngIf="selectedReport && !loadItemsService.showImportProcess && !selections.userIsGeneralManager && !selections.userIsReadOnly">
    <div class="buttonGroup">
      
      <!-- <button
        id="addSingleItem"
        class="btn btn-primary"
        [disabled]="!enableImportAndDeleteButtons || selections.stockCheck.statusId > 3"
        (click)="addNewVehicleModal()"
      >
        <fa-icon [icon]="icon.faFilePlus"></fa-icon>
        Add Item Manually
      </button> -->

      <button
        id="leadImportButton"
        class="btn btn-success me-2"
        [ngClass]="{'active':loadItemsService.showImportProcess}"
        [disabled]="!enableImportAndDeleteButtons"
        (click)="toggleShowImportProcess()"
      >
        <fa-icon [icon]="icon.faFileUpload"></fa-icon>
        Import from File
      </button>

      <button
        id="deleteAllButton"
        class="btn btn-danger leadButton"
        [disabled]="!enableImportAndDeleteButtons"
        (click)="deleteAllItems()"
      >
        <fa-icon [icon]="icon.faTrash"></fa-icon>
        Delete all
      </button>

      <button
        *ngIf="loadItemsService.selectedRowsParams"
        class="btn btn-danger"
        [disabled]="!enableImportAndDeleteButtons"
        (click)="deleteSelectedItems()"
      >
        <fa-icon [icon]="icon.faTrash"></fa-icon>
        Delete selected
      </button>
    </div>

    <!-- <form class="search-bar">
      <fa-icon [icon]="icon.faSearch"></fa-icon>
      <input placeholder="Search" class="form-control ms-2" type="text" [formControl]="loadItemsService.filter" />
    </form> -->
    </ng-container>

  <!-- <button *ngIf="loadItemsService.showImportProcess" class="btn btn-primary" (click)="openReviewImportMapsModal()">
    Review import maps
  </button> -->

  <button *ngIf="selectedReport && !loadItemsService.showImportProcess && loadItemsService.chosenReconcilingItemType.id"
    class="btn btn-primary" (click)="openReconcilingReportBackupModal()">
    Attach / Review Backup Files
  </button>

  <statusAndBarChart></statusAndBarChart>
</nav>



<!-- Main Page -->
<div class="content-new">  
  

  <instructionRow *ngIf="!selectedReport" [message]="'Use the dropdown menu above to choose which type of data you would like to import into StockPulse'"></instructionRow>

  <instructionRow *ngIf="selectedReport && !loadItemsService.showImportProcess" [message]="selectedReportMessage()"></instructionRow>
  
  

  
  <!-- Import items component -->
  <ng-container *ngIf="loadItemsService.showImportProcess">
    <import id="importComponent"></import>
  </ng-container> 

  <!-- Normal tables -->
  
  <ng-container *ngIf="!loadItemsService.showImportProcess">
    <dmsStockTable
      *ngIf="selectedReport && loadItemsService.chosenReconcilingItemType.description === 'DMS Stock' "
      (deleteItem)="deleteItem($event)"
      [rowData]="dmsRowData"
      [persistentRowData]="dmsRowData"
      (filteredRowData)="setFilteredDmsRowData($event)"
    ></dmsStockTable>

    <dmsStockTable
    *ngIf="selectedReport && loadItemsService.chosenReconcilingItemType.description === 'Agency Stock' "
    (deleteItem)="deleteItem($event)"
    [rowData]="agencyRowData"
    [persistentRowData]="agencyRowData"
    (filteredRowData)="setFilteredDmsRowData($event)"
  ></dmsStockTable>

    <TrialBalanceTable
    *ngIf="selectedReport && loadItemsService.chosenReconcilingItemType.description === 'Trial Balance' "
    (deleteItem)="deleteItemTB($event)"
    [rowData]="trialBalanceRowData"
    [persistentRowData]="trialBalanceRowData"
    (filteredRowData)="setFilteredTrialBalanceRowData($event)"
  ></TrialBalanceTable>
    
    <reconcilingItemsTable
      *ngIf="selectedReport && loadItemsService.chosenReconcilingItemType.description !== 'Trial Balance' && loadItemsService.chosenReconcilingItemType.description !== 'DMS Stock' && loadItemsService.chosenReconcilingItemType.description !== 'Agency Stock'"
      (deleteItem)="deleteItem($event)"
      [rowData]="reconcilingRowData"
      [persistentRowData]="reconcilingRowData"
      [selectedReport]="selectedReport"
      (filteredRowData)="setFilteredReconcilingRowData($event)"
    ></reconcilingItemsTable>
  </ng-container>
</div>






  <!-- Add new vehicle Modal -->

  <ng-template #newVehicle let-modal>
    <!-- If adding new stock item -->
    <ng-container *ngIf="showStock">
      <form [formGroup]="newStockItemForm" (ngSubmit)="saveNewStockItem()">
        <div class="modal-header">
          <h4 class="modal-title" id="modal-basic-title">
            Add new item
          </h4>
          <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body lowHeight">
          <table class="cph newItem ">
            <tbody>
              <tr>
                <td>Reg</td>
                <td><input formControlName='Reg' ngbAutofocus [ngClass]="{'is-invalid': s.Reg.errors}" type="text" maxlength = "8" />
                </td>
              </tr>
              <tr>
                <td>VIN</td>
                <td><input formControlName='Vin' [ngClass]="{'is-invalid': s.Vin.errors}" type="text" maxlength = "8" /></td>
              </tr>
              <tr>
                <td>Description</td>
                <td><input formControlName='Description' [ngClass]="{'is-invalid': s.Description.errors}" type="text" />
                </td>
              </tr>

              <tr>
                <td>DIS</td>
                <td><input formControlName='DIS' [ngClass]="{'is-invalid': s.DIS.errors}" type="number" /></td>
              </tr>
              <tr>
                <td>Group DIS</td>
                <td><input formControlName='Group DIS' [ngClass]="{'is-invalid': s.GroupDIS.errors}" type="number" />
                </td>
              </tr>
              <tr>
                <td>Branch</td>
                <td><input formControlName='Branch' [ngClass]="{'is-invalid': s.Branch.errors}" type="text" /></td>
              </tr>
              <tr>
                <td>Stock Type</td>
                <td><input formControlName='StockType' [ngClass]="{'is-invalid': s.StockType.errors}" type="text" />
                </td>
              </tr>
              <tr>
                <td>Notes</td>
                <td><input formControlName='Comment' [ngClass]="{'is-invalid': s.Comment.errors}" type="text" /></td>
              </tr>
              <tr>
                <td>Reference</td>
                <td><input formControlName='Reference' [ngClass]="{'is-invalid': s.Reference.errors}" type="text" />
                </td>
              </tr>
              <tr>
                <td>Stock Value</td>
                <td><input formControlName='StockValue' [ngClass]="{'is-invalid': s.StockValue.errors}" type="number" />
                </td>
              </tr>

            </tbody>

          </table>






        </div>
        <div class="modal-footer">
          <button class="btn btn-success" type="submit">Save New Item</button>
          <button type="button" class="btn btn-primary" (click)="modal.dismiss('Cancelled')">Close</button>
        </div>
      </form>
    </ng-container>

    <!-- If adding new reconciling item -->
    <ng-container *ngIf="!showStock">
      <form [formGroup]="newReconcilingItemForm" (ngSubmit)="saveNewReconcilingItem()">
        <div class="modal-header">
          <h4 class="modal-title" id="modal-basic-title">
            Add new item
          </h4>
          <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body lowHeight">


          <table class="cph newItem ">
            <tbody>
              <tr>
                <td>Reg</td>
                <td><input formControlName='Reg' ngbAutofocus [ngClass]="{'is-invalid': r.Reg.errors}" type="text" maxlength = "7" />
                </td>
              </tr>
              <tr>
                <td>VIN</td>
                <td><input formControlName='Vin' [ngClass]="{'is-invalid': r.Vin.errors}" type="text" maxlength = "8"/></td>
              </tr>
              <tr>
                <td>Description</td>
                <td><input formControlName='Description' [ngClass]="{'is-invalid': r.Description.errors}" type="text" />
                </td>
              </tr>



              <tr>
                <td>Notes</td>
                <td><input formControlName='Comment' [ngClass]="{'is-invalid': r.Comment.errors}" type="text" /></td>
              </tr>
              <tr>
                <td>Reference</td>
                <td><input formControlName='Reference' [ngClass]="{'is-invalid': r.Reference.errors}" type="text" />
                </td>
              </tr>


            </tbody>

          </table>
        </div>
        <div class="modal-footer">
          <button class="btn btn-success" type="submit">Save New Item</button>
          <button type="button" class="btn btn-primary" (click)="modal.dismiss('Cancelled')">Close</button>
        </div>
      </form>
    </ng-container>

  </ng-template>