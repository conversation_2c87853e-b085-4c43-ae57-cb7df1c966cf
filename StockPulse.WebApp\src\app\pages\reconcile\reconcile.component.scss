#waterfallContainer {
    width: 100%;
    background: #FFFFFF;
    border: 1px solid #BDC3C7;
    padding: 2.5em 1em 4em 1em;
    flex: 1;
    opacity: 1;

    #waterfall {
        width: 100%;
        height: 100%;
        padding: 1em;
        position: relative;

        #xAxis {
            width: calc(100% - 1.6em);
            position: absolute;
            bottom: calc(20% - 1em);
            height: 1px;
            background-color: var(--grey60);
            left: 0.6em;
        }

        #yAxis {
            height: calc(100% - 4em);
            position: absolute;
            bottom: calc(20% - 1em);
            left: 1em;
            width: 1px;
            background-color: var(--grey60)
        }

        #barArea {
            width: calc(100% - 2em);
            height: 80%;
            position: absolute;
            left: 0.6em;
            top: 1em;
        }

        #barAreaInsideArea {
            height: 100%;
        }

        .label {
            position: absolute;
            margin-top: 0.5em;
            color: black;
            text-align: center;
            line-height: 1;
            cursor: pointer;
            padding: 0em 0.5em;
            height: 5em;
        }

        .label:hover {
            color: var(--secondary);
        }

        .label.active {
            border-bottom: 5px solid var(--secondary);
            padding-bottom: 0.5em !important;
        }

        .bar {
            cursor: pointer;
            display: none;
            position: absolute;
            transition: 0.5s ease all;
            background-color: var(--success);
            transition: ease all 0.5s;
            opacity: 0.75;
            min-height: 1px;

            &.active,
            &:hover {
                opacity: 1;
            }

            .caption {
                position: absolute;
                width: 100%;
                text-align: center;
                transform: translateY(-1.4em);
            }

            .caption.bold {
                font-weight: 700;
            }
        }

        .bar.problem {
            background-color: var(--dangerLight);
        }

        .bar.isFullHeight {
            background-color: var(--primaryLight);
        }

        .bar:hover.isFullHeight {
            background-color: var(--primaryLighter);
        }

        .showInterimBars .bar {
            display: inline-block !important;
        }

        .bar.isFullHeight {
            display: inline-block !important;
        }

        .bar.reveal {
            display: inline-block !important;
        }

        .bar.blackLine {
            background-color: rgba(0, 0, 0, 0.4) !important;
        }
    }

}

.content-new.hidden {
    opacity: 0;
}


#gridHolder {
    flex: 3;
    position: relative;
    width: 100%;
    margin-top: 1em;
    overflow: auto;

    ag-grid-angular {
        height: 100%;
        margin: 0em auto;
        width: 100%;

        img {
            min-height: 6em;
            max-height: 6em;
        }
    }
}

.barTypeLabel {
    position: absolute;
    top: -2em;
    text-align: center;
    margin-left: 1.2em;
}

.barsLoadingLabel {
    position: absolute;
    top: 5em;
    width: 100%;
    text-align: center;
    margin-left: 0em;
}

:host ::ng-deep {
    .ag-cell.ag-cell-wrap-text {
        text-align: left;
        line-height: 16px;
        display: flex !important;
        align-items: center;
    }

   
}

:host ::ng-deep .ag-row-focus .ag-cell {
    background: var(--secondaryLight) !important;
}

#downloadAllData {
    border-radius: 0.3em 0 0 0.3em;

    img {
        width: 1.75em;
    }
}