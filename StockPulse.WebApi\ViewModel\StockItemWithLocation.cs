﻿namespace StockPulse.WebApi.ViewModel
{
    public class StockItemWithLocation
    {
        public int Id { get; set; }
        public string Description { get; set; } //stock desc
        public string LocationName { get; set; } //either scan location if matching scan or Reconciling Item RecType Description or if Missing, just 'Missing'
        public string Status { get; set; } // Scanned / Reconciled / MissingResolved / Missing
        public int Dis { get; set; }
        public int? ScanId { get; set; }
    }
}
