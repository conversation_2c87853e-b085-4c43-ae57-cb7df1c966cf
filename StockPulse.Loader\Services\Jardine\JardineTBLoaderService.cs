﻿using Microsoft.EntityFrameworkCore;
using StockPulse.Loader.Stockpulse.ViewModel;
using StockPulse.Model;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using StockPulse.Repository.Database;
using StockPulse.Loader.ViewModel;

namespace StockPulse.Loader.Services.Jardine
{


    public class JardineTBLoaderService : GenericLoaderJobServiceParams
    {

        public JardineTBLoaderService()
        {

        }

        public JobParams GetMatchingFilesAndImportParams(string incomingRoot)
        {
            string customer = "jardine";

            JobParams parms = new JobParams()
            {
                jobType = LoaderJob.JardineTB,
                customerFolder = "jardine",
                filename = "*STK-Jardine-TB.csv",
                importSPName = null,
                loadingTableName = "FinancialLines",
                jobName = "JardineTB",
                pulse = PulsesService.STK_JardineTB,
                fileType = FileType.csv,
                regexPattern = "(?<=^|\\|)(\\\"[^\\\"]*\\\"|[^|]*)",
                headerFailColumn = "StockCheckSite",
                //parsedItemType = typeof(JardineStock),
                dealerGroupId = 7,
                headerDefinitions = BuildHeaderDictionary(),
                errorCount = 0,
                allMatchingFiles = Directory.GetFiles(incomingRoot.Replace("{customer}", customer), $"*STK-Jardine-TB.csv"),
                delimiter = '|'
            };

            return parms;
        }

        public DataTable InterpretFile(JobParams parms, RowsAndHeadersResult rowsAndHeaders, LogMessage logMessage)
        {
            List<Model.Input.FinancialLine> incomingTB = new List<Model.Input.FinancialLine>();// ExtractIncoming(parms, ref parms.errorCount, logMessage, allRows, headerLookup);
            int incomingProcessCount = 0;
            incomingTB = new List<Model.Input.FinancialLine>(10000);  //preset the list size (slightly quicker than growing it each time)

            Dictionary<string, int> missingSitesDictionary = new Dictionary<string, int>();

            using (var db = new StockpulseContext())
            {
                // Main sites
                List<Site> sites = db.Sites
                    .AsNoTracking()
                    .Where(s => s.Divisions.DealerGroupId == parms.dealerGroupId)
                    .Include(s => s.Divisions) 
                    .ToList();

                // Description dictionary backup
                var siteDescriptionDictionary = db.SiteDescriptionDictionary.Where(x => x.DealerGroupId == parms.dealerGroupId)
                                                                            .AsEnumerable().ToDictionary(x => x.Description.ToUpper(), x => x);

                HashSet<string> closedSites = new HashSet<string>
                {
                    "DAF BIRTLEY",
                    "DAF LEEDS",
                    "DAF STOCKTON",
                    "LAND ROVER AYLESBURY"
                };

                foreach (var rowCols in rowsAndHeaders.rowsAndCells)
                {
                    incomingProcessCount++;

                    try
                    {
                        if (rowCols.Length != rowsAndHeaders.headerLookup.Count())
                        {
                            //something weird happened, not got enough rowCols for the number of headerCols, skip this record
                            logMessage.FailNotes = logMessage.FailNotes + $"InterpretFile {rowCols[rowsAndHeaders.headerLookup[parms.headerFailColumn]]}: Skipped rowCol as had {rowCols.Length} rowCols and needed {rowsAndHeaders.headerLookup.Count()} <br>";
                            parms.errorCount++;
                            continue;
                        }

                        string code = rowCols[rowsAndHeaders.headerLookup["Nominal_Code"]];

                        // Skip these codes
                        var codesToIgnore = new HashSet<string> { "XAA999", "YAA1CL", "YAA1CM", "YAA1CS", "ZZZZZZ" };

                        if (codesToIgnore.Contains(code))
                        {
                           continue;
                        }

                        string siteName = rowCols[rowsAndHeaders.headerLookup["Branch"]].ToUpper().Trim();

                        bool isClosedSite = closedSites.Contains(siteName);

                        if (isClosedSite)
                        {
                            continue;
                        }

                        int siteId = 0;

                        // First try Site table
                        Site site = sites.Where(x => x.Description.ToUpper() == siteName.ToUpper()).FirstOrDefault();

                        if (site == null)
                        {
                            // If no match, then try the dictionary
                            if (!siteDescriptionDictionary.TryGetValue(siteName.ToUpper(), out var siteDictionary) || !siteDictionary.IsPrimarySiteId)
                            {
                                 parms.errorCount++;

                                 if (siteName == "" || siteName == null)
                                 {
                                    siteName = "[BLANK NAME]";
                                 }

                                 if (!missingSitesDictionary.ContainsKey(siteName))
                                 {
                                    missingSitesDictionary[siteName] = 1;
                                 }
                                 else
                                 {
                                    missingSitesDictionary[siteName] += 1;
                                 }

                                 continue;
                            }
                            else
                            {
                                siteId = siteDictionary.SiteId;
                            }
                        }
                        else
                        {
                            siteId = site.Id;
                        }

                        Model.Input.FinancialLine jardineTB = new Model.Input.FinancialLine()
                        {
                            SiteId = siteId,
                            Code = code,
                            AccountDescription = rowCols[rowsAndHeaders.headerLookup["Nominal_Code_Description"]],
                            Balance = RowInterpretationService.GetDecimal(rowCols[rowsAndHeaders.headerLookup["Nominal_Balance"]]),
                            DealerGroupId = parms.dealerGroupId,
                            FileImportId = parms.fileImportId
                        };

                        incomingTB.Add(jardineTB);
                    }

                    catch (Exception err)
                    {
                        logMessage.FailNotes = logMessage.FailNotes + $" failed on adding item, Item: {incomingProcessCount} {err.ToString()} <br>";
                        parms.errorCount++;
                        continue;
                    }
                }

            }

            missingSitesDictionary = missingSitesDictionary
             .OrderBy(kvp => kvp.Key) // Sort by siteName
             .ToDictionary(kvp => kvp.Key, kvp => kvp.Value);

            foreach (var item in missingSitesDictionary)
            {
               logMessage.FailNotes += $"Unable to find site: {item.Key} ({item.Value}) <br>";
            }

            // Summarise rows into a new list grouped by Site, Code, CodeDescription
            List<Model.Input.FinancialLine> summarisedList = incomingTB
                   .GroupBy(x => new { x.SiteId, x.Code, x.AccountDescription })
                   .Select(g => new Model.Input.FinancialLine
                   {
                       SiteId = g.Key.SiteId,
                       Code = g.Key.Code,
                       AccountDescription = g.Key.AccountDescription,
                       DealerGroupId = parms.dealerGroupId,
                       FileImportId = parms.fileImportId,
                       Balance = g.Sum(x => x.Balance)
                   })
                   .ToList();

            DataTable result = summarisedList.ToDataTable();

            result.Columns.Remove("Sites");
            result.Columns.Remove("DealerGroup");
            result.Columns.Remove("FileImport");

            return result;
        }

        private Dictionary<string, string> BuildHeaderDictionary()
        {
            Dictionary<string, string> headerDefinitions = new Dictionary<string, string>()
                {
                        { "Enterprise",  "ENTERPRISE"},
                        { "Division", "DIVISION" },
                        { "Branch", "BRANCH" },
                        { "Stock_Type", "STOCK_TYPE" },
                        { "Current_Status", "CURRENT_STATUS" },
                        { "Stock_Number", "STOCK_NUMBER" },
                        { "VIN", "VIN" },
                        { "Nominal_Code", "NOMINAL_CODE" },
                        { "Nominal_Code_Description", "NOMINAL_CODE_DESCRIPTION" },
                        { "Nominal_Balance", "NOMINAL_BALANCE" },
            };

            return headerDefinitions;
        }











    }
}
