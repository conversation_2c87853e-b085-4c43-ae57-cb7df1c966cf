<div id="tableContainer">

  <div id="counter" *ngIf="rowData">
  <h4>
    {{ constants.pluralise(rowData.length, 'record', 'records') }}
    <div *ngIf="loadItemsService.filteredOutDueToBlanks && rowData.length > 0 && constants.neverScanReg">
      {{ constants.pluralise(loadItemsService.filteredOutDueToBlanks, 'record', 'records') }}
      with blank VIN have been filtered out
    </div>
    <div *ngIf="loadItemsService.filteredOutDueToBlanks && rowData.length > 0 && !constants.neverScanReg">
      {{ constants.pluralise(loadItemsService.filteredOutDueToBlanks, 'record', 'records') }}
      with blank reg and VIN have been filtered out
    </div>
  </h4>
</div> 
  
<ag-grid-angular  id="importTable" class="ag-theme-balham" 
[gridOptions]="mainTableGridOptions" 
[rowData]="rowData"
(gridReady)="onGridReady($event)"
      >
</ag-grid-angular>
</div>
