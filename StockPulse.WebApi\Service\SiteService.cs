﻿using StockPulse.WebApi.DataAccess;
using StockPulse.Model;
using StockPulse.Model.Import;
using StockPulse.WebApi.ViewModel;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace StockPulse.WebApi.Service
{
    public interface ISiteService
    {
        Task<IEnumerable<SiteVM>> GetSites(int userId);
        Task<IEnumerable<Location>> GetLocationsForStockCheck(int stockCheckId, int userId);
        //Task<IEnumerable<Site>> GetSitesForDivision(int divisionId, int userId);
        Task<IEnumerable<SiteVM>> GetAllSites(int userId);
        //Task<SiteLongLat> GetLongAndLat(int stockcheckId, int userId);
        Task<IEnumerable<SiteDescriptionDictionary>> GetAllSitesFromDictionary(int userId);
        Task<IEnumerable<SiteNameLookup>> GetSiteNameLookups(int userId);
        Task UpdateSiteNameLookups(UpdateSiteNameLookupsParams parms, int userId);
        Task DeleteSiteNameLookup(int id, int userId);
    }

    public class SiteService: ISiteService
    {
        //properties of the service
        private readonly ISiteDataAccess siteDataAccess;

        //constructor
        public SiteService(ISiteDataAccess siteDataAccess)
        {
            this.siteDataAccess = siteDataAccess;
        }


        //methods of the service
        // Gets sites that a user has access to
        public async Task<IEnumerable<SiteVM>> GetSites(int userId)
        {
            return await siteDataAccess.GetSites(userId);
        }

        public async Task<IEnumerable<Location>> GetLocationsForStockCheck(int stockCheckId, int userId)
        {
            return await siteDataAccess.GetLocationsForStockCheck(stockCheckId, userId);
        }

        //public async Task<IEnumerable<Site>> GetSitesForDivision(int divisionId, int userId)
        //{
        //    return await siteDataAccess.GetSitesForDivision(divisionId, userId);
        //}

        //public async Task<SiteLongLat> GetLongAndLat(int stockcheckId, int userId)
        //{
        //    return await siteDataAccess.GetLongAndLat(stockcheckId, userId);
        //}

        public async Task<IEnumerable<SiteVM>> GetAllSites(int userId)
        {
            return await siteDataAccess.GetAllSites(userId);
        }

        public async Task<IEnumerable<SiteDescriptionDictionary>> GetAllSitesFromDictionary(int userId)
        {
            return await siteDataAccess.GetAllSitesFromDictionary(userId);
        }

        public async Task<IEnumerable<SiteNameLookup>> GetSiteNameLookups(int userId)
        {
            return await siteDataAccess.GetSiteNameLookups(userId);
        }

        public async Task UpdateSiteNameLookups(UpdateSiteNameLookupsParams parms, int userId)
        {
            await siteDataAccess.UpdateSiteNameLookups(parms, userId);
        }

        public async Task DeleteSiteNameLookup(int id, int userId)
        {
            await siteDataAccess.DeleteSiteNameLookup(id, userId);
        }
    }
}
