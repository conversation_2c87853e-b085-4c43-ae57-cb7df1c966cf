﻿using Aspose.Cells.Drawing;
using Microsoft.EntityFrameworkCore;
using StockPulse.Loader.Services.Jardine;
using StockPulse.Loader.Stockpulse.ViewModel;
using StockPulse.Repository.Database;
using StockPulse.Model;
using StockPulse.Model.Import;
using StockPulse.Loader.ViewModel;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;

namespace StockPulse.Loader.Services.MMG
{


    public class MMGTBLoaderService : GenericLoaderJobServiceParams
    {

        //constructor
        public MMGTBLoaderService()
        {

        }


        public JobParams GetMatchingFilesAndImportParams(string incomingRoot)
        {
            string customer = "mmg";
            string filePattern = "*StockPulseTrialBalance*.csv";

            JobParams parms = new JobParams()
            {
                jobType = LoaderJob.MMGTB,
                customerFolder = "mmg",
                filename = filePattern,
                importSPName = null,
                loadingTableName = "FinancialLines",
                jobName = "MMGTB",
                pulse = PulsesService.STK_MMGTB,
                fileType = FileType.csv,
                regexPattern = "((?<=\")[^\"]*(?=\"(,|$)+)|(?<=,|^)[^,\"]*(?=,|$))",
                headerFailColumn = null,
                headerDefinitions = null,
                errorCount = 0,
                dealerGroupId = 11,
                allMatchingFiles = Directory.GetFiles(incomingRoot.Replace("{customer}", customer), filePattern),
            };

            return parms;
        }


        public DataTable InterpretFile(JobParams parms, RowsAndHeadersResult rowsAndHeaders, LogMessage logMessage)
        {
            MMGSharedService mmgSharedService = new MMGSharedService(logMessage);

            List<Model.Input.FinancialLine> incomingLines = new List<Model.Input.FinancialLine>();// ExtractIncoming(parms, ref parms.errorCount, logMessage, allRows, headerLookup);

            int incomingProcessCount = 0;

            incomingLines = new List<Model.Input.FinancialLine>(10000);  //preset the list size (slightly quicker than growing it each time)

            int total = rowsAndHeaders.rowsAndCells.Count;

            logMessage.FailNotes = "";

            using (var db = new StockpulseContext())
            {
                IEnumerable<SiteDescriptionDictionary> siteDescriptionDictionary = db.SiteDescriptionDictionary.Where(x => x.DealerGroupId == parms.dealerGroupId).AsNoTracking().AsEnumerable();

                foreach (var rowCols in rowsAndHeaders.rowsAndCells)
                {
                    incomingProcessCount++;

                    System.Console.WriteLine($"Count {incomingProcessCount} / {total}");

                    try
                    {

                        string siteName = rowCols[0].ToString().Trim();

                        if (mmgSharedService.IsSiteSkippable(siteName))
                        {
                            continue;
                        }

                        SiteDescriptionDictionary siteDictionary = siteDescriptionDictionary.Where(x => x.Description == siteName && x.IsPrimarySiteId).FirstOrDefault();

                        if (siteDictionary == null)
                        {
                            logMessage.FailNotes = logMessage.FailNotes + $" failed on adding item, Item: {incomingProcessCount}. Site: {siteName} not found. \r\n";
                            parms.errorCount++;
                            continue;
                        }

                        // Found site in dictionary - get SiteId (will go to catch if not found)
                        int siteId = siteDictionary.SiteId;

                        Model.Input.FinancialLine incomingLine = new Model.Input.FinancialLine()
                        {
                            SiteId = siteId,
                            Code = rowCols[1].ToString().Trim(),
                            AccountDescription = rowCols[2].ToString(),
                            Balance = decimal.Parse(rowCols[4].ToString()),
                            FileImportId = parms.fileImportId,
                            DealerGroupId = parms.dealerGroupId
                        };

                        incomingLines.Add(incomingLine);
                    }

                    catch (Exception err)
                    {
                        logMessage.FailNotes = logMessage.FailNotes + $" failed on adding item, Item: {incomingProcessCount} {err.ToString()}";
                        parms.errorCount++;
                        continue;
                    }
                }
            }

            DataTable result = incomingLines.ToDataTable();
            result.Columns.Remove("Sites");
            result.Columns.Remove("FileImport");
            result.Columns.Remove("DealerGroup");
            return result;
        }




    }
}
