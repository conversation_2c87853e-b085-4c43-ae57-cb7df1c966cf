import { ImageToUpdate } from "./ImageToUpdate";



export class WaterfallBarDetailItem {

  stockItemId:number;
  reg: string;
  vin: string;
  regConfidence :number;
  vinConfidence :number;
  siteName: string;
  branchName: string;
  description: string;
  comment: string;
  reference: string;
  scanId: number;
  scannedBy: string;
  scannedDate: Date | string;
  scanLocation: string;
  distance: number;
  dis: number;
  groupDIS: number;
  stockType: string;
  sIV: number;
  matchingItemTypeDesc:string;
  matchingItemDesc: string;
  matchingItemComment: string;
  matchingItemRef: string;
  otherSiteName: string;
  resolutionDate: Date | string;
  resolvedBy:string;
  resolutionTypeDesc: string;
  resolutionNotes: string;
  resolutionImages: ImageToUpdate[];

  originalId: number; //the matching scan which this is a  duplicate of.   Should only ever be 1 for the same stockcheck, based on matching on either reg or vin
  originalLocationDescription: string;
  originalScannedBy: string;
  originalScannedDate: Date | string;

  originalStockType: string;
  originalComment: string;
  originalReference: string;

  latitude: number;
  longitude: number;

  //client side props
  regImageLargeUrl: string;
  regImageThumbnailUrl:string;
  index?: number;
  stockValue: number;
  flooring: number;
  regEditStatus?: string;
  vinEditStatus?: string;
}
