﻿

CREATE OR ALTER PROCEDURE [dbo].[ADD_Scan]
(
    @StockCheckId INT,
	@UserId INT,
	@LocationId INT,
	@RegConfidence INT,
	@VinConfidence INT,
	@Longitude decimal(9,4),
	@Latitude decimal(9,4),
	@ScanDateTime datetime,
	@HasVimImage bit,
    @Reg nvarchar(20),
    @Vin nvarchar(20),
    @InterpretedReg nvarchar(20),
    @InterpretedVin nvarchar(20),
    @IsRegEditedOnDevice INT,
    @IsVinEditedOnDevice INT,
    @Comment nvarchar(500),
    @Description nvarchar(250)
)
AS
BEGIN

SET NOCOUNT ON;


IF (dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0 OR dbo.[CanSaveScans](@StockCheckId) = 0)
BEGIN 
	RETURN
END

DECLARE @Id INT

BEGIN TRY
INSERT INTO [dbo].[Scans]
    (
    [StockCheckId]
    ,[UserId]
    ,[LastEditedById]
    ,[LocationId]
    ,[RegConfidence]
    ,[VinConfidence]
    ,[Longitude]
    ,[Latitude]
    ,[ScanDateTime]
    ,[HasVinImage]
    ,[IsDuplicate]
    ,[Reg]
    ,[Vin]
    ,[InterpretedReg]
    ,[InterpretedVin]
    ,[IsRegEditedOnWeb]
    ,[IsVinEditedOnWeb]
    ,[IsRegEditedOnDevice]
    ,[IsVinEditedOnDevice]
    ,[Comment]
    ,[Description]
    ,[SaveDate]
    )

VALUES
    (
    @StockCheckId
    ,@UserId
    ,@UserId
    ,@LocationId
    ,@RegConfidence
    ,@VinConfidence
    ,@Longitude
    ,@Latitude
    ,@ScanDateTime
    ,@HasVimImage
    ,0 --IsDuplicate
    ,@Reg 
    ,@Vin 
    ,@InterpretedReg
    ,@InterpretedVin
    ,0 --IsRegEditedOnWeb
    ,0 --IsVinEditedOnWeb
    ,@IsRegEditedOnDevice
    ,@IsVinEditedOnDevice
    ,@Comment 
    ,@Description
    ,GETUTCDATE()
    
    )

		SET @Id = SCOPE_IDENTITY();

		EXEC [dbo].[UPDATE_StockCheckLastUpdate] @StockCheckId, @UserId

		SELECT @Id AS ScanId, 0 AS IsExistingScan

END TRY
BEGIN CATCH
IF ERROR_NUMBER() = 2601 OR ERROR_NUMBER() = 2627 -- error number for UNIQUE KEY violation
        BEGIN
            DECLARE @ExistingId INT;

            -- Fetch the existing record's primary key
            SELECT @ExistingId = Id -- Replace 'Id' with the actual PK column name
            FROM dbo.Scans
            WHERE UserId = @UserId AND Reg = @Reg AND Vin = @Vin AND ScanDateTime = @ScanDateTime AND StockCheckId = @StockCheckId;

            -- Raise a custom error including the existing record's PK
            --RAISERROR ('UNIQUE KEY violation. The existing record''s primary key is %d.', 16, 1, @ExistingId);
			SELECT @ExistingId AS ScanId, 1 AS IsExistingScan
        END
        ELSE
        BEGIN
            -- Re-throw the original error if it's not a UNIQUE KEY violation
            THROW
        END
END CATCH;

    
    


END
	
  
	


GO


