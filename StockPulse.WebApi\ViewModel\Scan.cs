﻿using System;

namespace StockPulse.WebApi.ViewModel
{
    public class Scan
    {
        public Scan() { }
        public Scan(ScanFullDetail scan)
        {
            ScanId = scan.ScanId;
            LocationDescription = scan.LocationDescription;
            ScannerName = scan.ScannerName;
            RegConfidence = scan.RegConfidence;
            VinConfidence = scan.VinConfidence;
            Longitude = scan.Longitude;
            Latitude = scan.Latitude;
            ScanDateTime = scan.ScanDateTime;
            ScanComment = scan.ScanComment;
            ScanReg = scan.ScanReg;
            ScanVin = scan.ScanVin;
            ScanDescription = scan.ScanDescription;
            HasVinImage = scan.HasVinImage;
            ScanState = scan.ScanState;
            SiteName = scan.SiteName;
        }

        public Scan(StockItemFullDetail stockItem)
        {
            ScanId = (int)stockItem.ScanId;
            LocationDescription = stockItem.LocationDescription;
            ScannerName = stockItem.ScannerName;
            RegConfidence =(decimal)stockItem.RegConfidence;
            VinConfidence = (decimal)stockItem.VinConfidence;
            Longitude =(decimal)stockItem.Longitude;    
            Latitude =(decimal)stockItem.Latitude;
            ScanDateTime = stockItem.ScanDateTime;
            ScanComment = stockItem.ScanComment;
            ScanReg = stockItem.ScanReg;
            ScanVin = stockItem.ScanVin;    
            ScanDescription = stockItem.ScanDescription;
            HasVinImage = (bool)stockItem.HasVinImage;
            ScanState = (ReconciliationState)stockItem.ScanState;
        }


        public int ScanId { get; set; }
        public decimal RegConfidence { get; set; }
        public decimal VinConfidence { get; set; }
        public decimal Longitude { get; set; }
        public decimal Latitude { get; set; }
        public string LocationDescription { get; set; }
        public string ScannerName { get; set; }
        public DateTime ScanDateTime { get; set; }
        public string ScanComment { get; set; }
        public string ScanReg { get; set; }
        public string ScanVin { get; set; }
        public string ScanDescription { get; set; }
        public bool HasVinImage { get; set; }
        public ReconciliationState ScanState { get;set; }


        public decimal DistanceFromDealershipInMiles { get; set; }
        public decimal StockCheckLongitude { get; set; }
        public decimal StockCheckLatitude {  get; set; }

        public string RegEditStatus { get; set; }
        public string VinEditStatus { get; set; }
        public string SiteName {  get; set; }
    }


   
}
