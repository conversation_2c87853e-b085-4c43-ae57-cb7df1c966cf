// Regular theme
$primary: #323130;
$secondary: #FFBF00;
$success: #1F8637;
$danger: #DC3545;
$warning: #FFC107;
$grey: #D2D2D2;
$info: #B9D6F1;
$numberPlate: #F5E94E;

$primaryLight: lighten($primary, 5%);
$primaryLighter: lighten($primary, 15%);
$primaryLightest: lighten($primary, 25%);
$primaryDark: darken($primary, 5%);
$primaryDarker: darken($primary, 15%);

$secondaryLight: lighten($secondary, 5%);
$secondaryLighter: lighten($secondary, 15%);
$secondaryLightest: lighten($secondary, 25%);
$secondaryDark: darken($secondary, 5%);
$secondaryDarker: darken($secondary, 15%);

$successLight: lighten($success, 5%);
$successLighter: lighten($success, 25%);
$successLightest: lighten($success, 25%);
$successDark: darken($success, 5%);
$successDarker: darken($success, 15%);

$dangerLight: lighten($danger, 5%);
$dangerLighter: lighten($danger, 15%);
$dangerLightest: lighten($danger, 25%);
$dangerDark: darken($danger, 5%);
$dangerDarker: darken($danger, 15%);

$warningLight: lighten($warning, 5%);
$warningLighter: lighten($warning, 15%);
$warningLightest: lighten($warning, 25%);
$warningDark: darken($warning, 5%);
$warningDarker: darken($warning, 15%);

$greyLight: lighten($grey, 5%);
$greyLighter: lighten($grey, 15%);
$greyLightest: lighten($grey, 25%);
$greyDark: darken($grey, 5%);
$greyDarker: darken($grey, 15%);

$infoLight: lighten($info, 5%);
$infoLighter: lighten($info, 15%);
$infoLightest: lighten($info, 25%);
$infoDark: darken($info, 5%);
$infoDarker: darken($info, 15%);

$numberPlateFrom: lighten($numberPlate, 20%);
$numberPlateTo: darken($numberPlate, 1%);
$infoBlue:#3c73a8;
$postItNote:rgba(253, 255, 156,0.5);

// Light theme
$primaryLightTheme: #f0f0f0;
$secondaryLightTheme: #0f6cbd;

$primaryLightThemeLight: lighten($primaryLightTheme, 5%);
$primaryLightThemeLighter: lighten($primaryLightTheme, 15%);
$primaryLightThemeLightest: lighten($primaryLightTheme, 25%);
$primaryLightThemeDark: darken($primaryLightTheme, 5%);
$primaryLightThemeDarker: darken($primaryLightTheme, 15%);

$secondaryLightThemeLight: lighten($secondaryLightTheme, 5%);
$secondaryLightThemeLighter: lighten($secondaryLightTheme, 15%);
$secondaryLightThemeLightest: lighten($secondaryLightTheme, 25%);
$secondaryLightThemeDark: darken($secondaryLightTheme, 5%);
$secondaryLightThemeDarker: darken($secondaryLightTheme, 15%);