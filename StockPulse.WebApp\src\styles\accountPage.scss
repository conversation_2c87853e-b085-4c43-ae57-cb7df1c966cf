#backgroundImage{
  transition: 0.3s ease-in all;
  width: 100vw;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0px;
  background: linear-gradient(hsla(210, 13%, 0%, 0.5), hsla(210, 13%, 0%, 0.7)), url("/assets/imgs/dealership.jpg");
  background-size: cover;
  filter: blur(8px);

}
.accountPage {
  transition: 0.3s ease-in all;
  width: 100vw;
  height: 100vh;
  position: fixed;
  opacity: 1 !important;

  .imgHolder {
    width: 100%;
    display: flex;
    justify-content: center;
    margin-bottom: 3em;

    img {
      width: 13.6em;
      height: 2.6em;
    }
  }

  .inner {
    width: 40em;
    margin: 4em auto;
    background: rgba(255, 255, 255, 0.9);
    padding: 2em 5em 7em 5em;
    border-radius: 0.5em;
  }

  #loginBox {
    width: 90%;
    border-radius: 0.5em;
    background: rgba(255, 255, 255, 0.7);
    padding: 1em;
    margin: 5em auto;
  }

  table {
    width: 100%;
    margin-left: 0.5em;
    margin-top: 5em;

    td:nth-of-type(2) {
      width: 1em;
      max-width: 1em;
      min-width: 1em;
    }

    input {
      padding-left: 3em;
    }

    fa-icon {
      transform: translateX(3em);
    }

    fa-icon.error {
      color: red;
    }
  }

  .inputAndIcon {
    position: relative;

    input {
      padding-left: 4em;
    }

    fa-icon {

      position: absolute;
      top: 0.3em;
      left: -2.2em;
      color: var(--grey40);
      z-index: 1000;
    }
  }

  #spinner {
    position: fixed;
    top: calc(50% - 32px);
    left: calc(50% - 32px);
    z-index: 99999;
  }
}