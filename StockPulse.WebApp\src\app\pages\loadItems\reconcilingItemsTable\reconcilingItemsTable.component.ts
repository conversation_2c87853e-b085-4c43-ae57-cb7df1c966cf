import { Component, EventEmitter, HostListener, Input, OnInit, Output } from "@angular/core";
import { Subscription } from 'rxjs';
import { ReconcilingItemVM } from "src/app/model/ReconcilingItemVM";
import { LoadItemsService } from "src/app/pages/loadItems/loadItems.service";
import { ConstantsService } from "src/app/services/constants.service";
import { LogoService } from "src/app/services/logo.service";
import { ToastService } from "src/app/services/newToast.service";
import { ChassisComponent } from "../../../_cellRenderers/chassis.component";
import { CustomHeaderComponent } from '../../../_cellRenderers/customHeader.component';
import { DeleteButtonComponent } from '../../../_cellRenderers/deleteButton.component';
import { IndexComponent } from "../../../_cellRenderers/index.component";
import { RegPlateComponent } from "../../../_cellRenderers/regPlate.component";
import { SelectionsService } from '../../../services/selections.service';
import { SelectionChangedEvent } from "ag-grid-community";
import { CheckboxIconComponent } from "src/app/_cellRenderers/checkboxIcon";
import { ImageComponent } from "src/app/_cellRenderers/image.component";
import { ReconcilingItemTypeStat } from "src/app/model/ReconcilingItemTypeStat";
import { CphPipe } from "src/app/cph.pipe";



@Component({
  selector: 'reconcilingItemsTable',
  templateUrl: './reconcilingItemsTable.component.html',
  styleUrls: ['./reconcilingItemsTable.component.scss']
})

export class ReconcilingItemsTableComponent implements OnInit {

  @Output() deleteItem = new EventEmitter<{ stockItem: ReconcilingItemVM, controller: string, route: string }>();
  @Input() rowData: ReconcilingItemVM[];
  @Input() persistentRowData: ReconcilingItemVM[];
  @Input() selectedReport: ReconcilingItemTypeStat;

  @Output() filteredRowData = new EventEmitter<ReconcilingItemVM[]>();

  @HostListener('window:resize', [])
  private onresize(event) {
    if (this.gridApi) this.gridApi.sizeColumnsToFit();
  }

  mainTableGridOptions: any;
  frameworkComponents: { agColumnHeader: any; };
  public gridApi;
  public columnApi;
  subscription: Subscription;
  sidenavToggledSubscription: Subscription;

  constructor(
    public selections: SelectionsService,
    public loadItemsService: LoadItemsService,
    public toastService: ToastService,
    public logo: LogoService,
    public constants: ConstantsService,
    public cphPipe: CphPipe
  ) {

  }

  ngOnDestroy() {
    if (this.subscription) this.subscription.unsubscribe()
    if (this.sidenavToggledSubscription) { this.sidenavToggledSubscription.unsubscribe(); }
  }

  ngOnInit(): void {
    this.initParams()
  }

  initParams() {
    this.loadItemsService.filter.valueChanges.subscribe(result => this.search(result))
    this.subscription = this.loadItemsService.updateMainTable.subscribe(res => {
      this.search(this.loadItemsService.filter.value)
      this.updateGrid()
    })
    this.setGridOptions();    

    this.sidenavToggledSubscription = this.constants.sidenavToggledEmitter.subscribe(() => {
      if (this.gridApi) {
        this.gridApi.sizeColumnsToFit();
      }
    })
  }

  setGridOptions() {
    this.frameworkComponents = { agColumnHeader: CustomHeaderComponent };
    this.mainTableGridOptions = {
      columnTypes: {
        "numberColumn": { filter: 'agNumberColumnFilter' },
        "labelColumn": { cellClass: 'agAlignCentre', filter: 'agTextColumnFilter' },
        "date": { cellClass: 'agAlignCentre', cellRenderer:(params)=>this.cphPipe.transform(params.value,'date',0), filter: 'agTextColumnFilter' }
      },
      rowSelection: 'multiple',
      onSelectionChanged: (event) => this.onSelectionChanged(event),
      onFirstDataRendered: (event) => this.gridApi?.sizeColumnsToFit(),
      onRowDoubleClicked: (event) => this.openVehicleModal(event),
      defaultColDef: {
        resizable: true,
        sortable: true,
        floatingFilter: true
      },
      enableCellTextSelection: true,
      columnDefs: [
        { headerName: "", field: "index", cellClass: 'indexCell', cellRenderer: IndexComponent, autoHeight: true, width: 100 },
        { headerName: "Reg", field: "reg", width: 100, cellRenderer: RegPlateComponent, autoHeight: true, type: 'labelColumn', hide: this.constants.currencySymbol === "$" },
        { headerName: "VIN", field: "vin", width: 100, cellRenderer: ChassisComponent, type: 'labelColumn' },
        { headerName: "Description", field: "description", width: 400, type: "labelColumn" },
        { headerName: "Notes", field: "comment", width: 300, type: 'labelColumn' },
        { headerName: "Reference", field: "reference", width: 200, type: 'labelColumn' },
        { headerName: "Matched to Scan", colId: 'matchedToScan', cellRenderer: ImageComponent, autoHeight: true, filter: null, hide: this.selectedReport.explainsMissing, width: 60, type: 'labelColumn' },
        { headerName: "File Name", field: "fileName", width: 200, type: 'labelColumn' },
        { headerName: "File Date", field: "fileDate", width: 200, type: 'date' },
        { headerName: "Load Date", field: "loadDate", width: 200, type: 'date' },
        { headerName: "User Name", field: "userName", width: 200, type: 'labelColumn' },
        { headerName: "", field: "", width: 60, type: 'labelColumn', filter: null, cellRenderer: DeleteButtonComponent, cellRendererParams: { onClick: this.processDeleteClick.bind(this), } },
        { headerName: '', field: '', width: 40, headerCheckboxSelection: true, checkboxSelection: true }
      ]
    }
  }

  onGridReady(params) {
    this.gridApi = params.api;
    this.columnApi = params.columnApi;
    this.gridApi.sizeColumnsToFit();
    this.toastService.destroyToast();
  }

  processDeleteClick(stockCheckItem: ReconcilingItemVM) {
    this.deleteItem.next({ stockItem: stockCheckItem, controller: 'ReconcilingItems', route: 'RecItem' });
  }

  search(text: string): ReconcilingItemVM[] {
    if (!this.rowData) { return }
    if (text == '') { this.rowData = this.persistentRowData; return }

    this.rowData = this.rowData.filter(stockCheckItem => {
      const term = text.toLowerCase();
      return stockCheckItem.description.toLowerCase().includes(term)
        || (stockCheckItem.reg && stockCheckItem.reg.toLowerCase().includes(term))
        || (stockCheckItem.vin && stockCheckItem.vin.toLowerCase().includes(term));
    });

    this.filteredRowData.next(this.rowData);
  }

  updateGrid() {
    this.search('');
    if (this.gridApi) {
      this.columnApi.setColumnVisible('matchedToScan', !this.selectedReport.explainsMissing);
    }
  }

  downloadSpreadsheet() {
    this.constants.loadItemsExcelDownload.emit(true);
  }

  onSelectionChanged(event: SelectionChangedEvent) {
    let selectedRows: any[] = event.api.getSelectedRows();
    let selectedReconcilingItemIds: number[] = [];

    selectedRows.forEach(selectedRow => {
      selectedReconcilingItemIds.push(selectedRow.id);
    });

    this.loadItemsService.selectedRowsParams = {
      controller: 'ReconcilingItems',
      route: 'RecItem',
      ids: selectedReconcilingItemIds
    }
  }

  openVehicleModal(event) {
    if (event.data.hasBeenScanned === 0) { return; }
    this.constants.vehicleModal.loadItemAndOpenModal(false, event.data.scanId, [event.data.scanId]);
  }
}
