﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace StockPulse.WebApi.Migrations
{
    /// <inheritdoc />
    public partial class addsiteidtoimportfl : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "SiteId",
                schema: "import",
                table: "FinancialLines",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.CreateIndex(
                name: "IX_FinancialLines_SiteId",
                schema: "import",
                table: "FinancialLines",
                column: "SiteId");

            migrationBuilder.AddForeignKey(
                name: "FK_FinancialLines_Sites_SiteId",
                schema: "import",
                table: "FinancialLines",
                column: "SiteId",
                principalSchema: "dbo",
                principalTable: "Sites",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_FinancialLines_Sites_SiteId",
                schema: "import",
                table: "FinancialLines");

            migrationBuilder.DropIndex(
                name: "IX_FinancialLines_SiteId",
                schema: "import",
                table: "FinancialLines");

            migrationBuilder.DropColumn(
                name: "SiteId",
                schema: "import",
                table: "FinancialLines");
        }
    }
}
