<nav class="page-specific-navbar">
  <div class="page-title">
    <singleSitePickerWithSearch></singleSitePickerWithSearch>
  </div>

  <div class="buttonGroup">
    <button *ngFor="let m of service.mismatchOptions" 
    class="btn btn-primary" [ngClass]="{'active':service.mismatchOption == m}"
     (click)="showMismatchOption(m)">{{m}}</button>
   </div>
</nav>

<div *ngIf="service.scanRegDifferences" class="content-new">
  <div *ngIf="service.scanRegDifferences.length > 0" id="cardsContainer">
    <div *ngFor="let item of service.scanRegDifferences; index as i" class="cph-card" (dblclick)="goToVehicleScan(item)">
      <div class="cph-card-header">
        <span class="cph-card-header-title">
          Vehicle {{ i+1 }} of {{ service.scanRegDifferences.length | cph:'number':0 }}
        </span>
      </div>
      <div class="cph-card-body clickable">
        <wrongRegItem [item]="item"></wrongRegItem>
      </div>
    </div>
  </div>
  <div *ngIf="service.scanRegDifferences.length === 0" id="noDifferencesMessage">
    No differences to display
  </div>
</div>
