﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [import].[MERGE_Stocks]
(  
	@DealerGroupId INT,
	@StockTypes VARCHAR(300) = NULL
)  
AS
BEGIN

    -- SET NOCOUNT ON added to prevent extra result sets from
    -- interfering with SELECT statements.
    SET NOCOUNT ON

	BEGIN TRAN

	DROP TABLE IF EXISTS #CurrentStockItems 
	DROP TABLE IF EXISTS #NewStockItems
	DROP TABLE IF EXISTS #SameItems

	-- Get the current stock items in the db
	SELECT 
	CONCAT(ISNULL(StkItms.Reg,''), ISNULL(StkItms.Vin,''), Sit.Id) AS [UniqueId],
	StkItms.Id
	INTO #CurrentStockItems 
	FROM StockItems AS StkItms
	INNER JOIN StockChecks AS StkCheck ON StkItms.StockCheckId = StkCheck.Id 
	INNER JOIN Sites as Sit on StkCheck.SiteId = Sit.Id
	INNER JOIN Divisions as Div on Sit.DivisionId = Div.Id
	WHERE Div.DealerGroupId = @DealerGroupId 
	AND StkCheck.IsActive = 1

	-- Get the new stock items
	SELECT
	CONCAT(ISNULL(Inputs.Reg,''), ISNULL(Inputs.Vin,''), Inputs.SiteId) AS [UniqueId]
	INTO #NewStockItems
	FROM [input].[StockItems] As Inputs
	INNER JOIN Sites AS Sit ON Sit.Id = Inputs.SiteId 
	LEFT JOIN StockChecks AS StkCheck ON Inputs.SiteId = StkCheck.SiteId AND StkCheck.IsActive = 1
	LEFT JOIN StockItems AS StkItms ON Inputs.Reg = StkItms.Reg AND Inputs.Vin = StkItms.Vin AND StkCheck.SiteId = Inputs.SiteId
	WHERE StkItms.Id IS NULL
	AND StkCheck.IsActive = 1
	AND Inputs.DealerGroupId = @DealerGroupId

	-- // 1. DELETING OLD STOCK //
	-- Remove any references from Scans to the StockItems we are about to remove
	UPDATE S
	SET S.StockItemId = NULL 
	FROM Scans AS S
	INNER JOIN StockChecks AS StkCheck ON S.StockCheckId = StkCheck.Id
	INNER JOIN Sites as Sit on StkCheck.SiteId = Sit.Id
	INNER JOIN Divisions as Div on Sit.DivisionId = Div.Id
	WHERE Div.DealerGroupId = @DealerGroupId
	AND StkCheck.IsActive = 1
	AND S.StockItemId IN (SELECT Id FROM #CurrentStockItems WHERE UniqueId NOT IN (SELECT * FROM #NewStockItems))

	-- Delete the old stock
	DELETE SI
	FROM StockItems AS SI
	INNER JOIN StockChecks AS StkCheck ON SI.StockCheckId = StkCheck.Id
	INNER JOIN Sites as Sit on StkCheck.SiteId = Sit.Id
	INNER JOIN Divisions as Div on Sit.DivisionId = Div.Id
	WHERE Div.DealerGroupId = @DealerGroupId
	AND StkCheck.IsActive = 1
	AND SI.IsAgencyStock = 0
	AND CONCAT(ISNULL(SI.Reg,''), ISNULL(SI.Vin,''), StkCheck.SiteId) IN (SELECT UniqueId FROM #CurrentStockItems WHERE UniqueId NOT IN (SELECT * FROM #NewStockItems))

	--- // 2. ADDING NEW STOCK 
	INSERT INTO Stockitems 
			   ([ScanId]
			   ,[ReconcilingItemId]
			   ,[MissingResolutionId]
			   ,[StockCheckId]
			   ,[SourceReportId]
			   ,[Reg]
			   ,[Vin]
			   ,[Description]
			   ,[DIS]
			   ,[GroupDIS]
			   ,[Branch]
			   ,[Comment]
			   ,[StockType]
			   ,[Reference]
			   ,[StockValue])
           
	SELECT
	NULL, -- ScanId
	NULL, -- ReconcilingItemId
	NULL, -- MissingResolutionId
	StkCheck.Id, -- StockCheckId
	1, -- SourceReportId
	STK.Reg, 
	STK.Vin,
	STK.Description,
	STK.DIS,
	STK.GroupDIS,
	STK.Branch,
	STK.Comment, 
	STK.StockType, 
	STK.Reference, 
	ISNULL(STK.StockValue,0)
	FROM [input].[StockItems] As STK
	INNER JOIN Sites AS Sit ON Sit.Id = STK.SiteId 
	INNER JOIN Divisions as Div on Sit.DivisionId = Div.Id
	INNER JOIN StockChecks AS StkCheck ON STK.SiteId = StkCheck.SiteId AND StkCheck.IsActive = 1
	WHERE
	Div.DealerGroupId = @DealerGroupId 
	AND
	CONCAT(ISNULL(STK.Reg,''), ISNULL(STK.Vin,''), STK.SiteId) IN 
	(SELECT UniqueId FROM #NewStockItems WHERE UniqueId NOT IN (SELECT UniqueId FROM #CurrentStockItems))

	---- // 3. UPDATE SAME ITEMS //
	SELECT
	STK.Branch,
	STK.Comment,
	STK.Description,
	STK.DealerGroupId,
	STK.DIS,
	STK.FileImportId,
	STK.GroupDIS,
	STK.Reference,
	STK.Reg,
	STK.SiteId,
	STK.SourceReportId,
	STK.StockType,
	STK.StockValue,
	STK.Vin,
	StkCheck.Id AS StockCheckId
	INTO #SameItems
	FROM [input].[StockItems] As STK
	INNER JOIN Sites AS Sit ON Sit.Id = STK.SiteId 
	INNER JOIN Divisions as Div on Sit.DivisionId = Div.Id
	INNER JOIN StockChecks AS StkCheck ON STK.SiteId = StkCheck.SiteId AND StkCheck.IsActive = 1
	WHERE Div.DealerGroupId = 10
 

	UPDATE SI
	SET SI.Reg = STK.Reg,
	SI.Vin = STK.Vin,
	SI.Description = STK.Description,
	SI.DIS = STK.DIS,
	SI.GroupDIS = STK.GroupDIS,
	SI.Branch = STK.Branch,
	SI.Comment = STK.Comment,
	SI.StockType = STK.StockType,
	SI.Reference = STK.Reference,
	SI.StockValue = STK.StockValue
	FROM StockItems As SI
	INNER JOIN StockChecks AS SC ON SI.StockCheckId = SC.Id AND SC.IsActive = 1
	INNER JOIN #SameItems As STK ON ISNULL(SI.Reg,'') = ISNULL(STK.Reg,'') AND ISNULL(SI.Vin,'') = ISNULL(STK.Vin,'') AND SC.SiteId = STK.SiteId
	INNER JOIN Sites as Sit on SC.SiteId = Sit.Id
	INNER JOIN Divisions as Div on Sit.DivisionId = Div.Id
	WHERE Div.DealerGroupId = @DealerGroupId


COMMIT TRAN



END

GO