import { EventEmitter, Injectable } from '@angular/core';
import { DealerGroupVM } from '../model/DealerGroupVM';
import { StockCheck } from '../model/StockCheck';
import { UserAndLogin } from "./../model/UserAndLogin";


@Injectable({
  providedIn: 'root'
})
export class SelectionsService {

  stockCheck: StockCheck;
  public stockCheckLongName: string;
  userRole: string;
  userId:number;
  userIsApprover:boolean;
  userIsGeneralManager: boolean;
  userIsReadOnly: boolean;
  userIsScanAndPrintOnly: boolean;
  userIsResolver: boolean;
  usersName:string;
  userSiteIds: number[];
  userHomeSiteName:string;
  userUsername: string;

  dealerGroups: DealerGroupVM[] = [];
  userDealerGroup:string;
  showVehicleModal: boolean = false;

  //spinner: { show: boolean, message?: string, icon?: string } = { show: false, message: 'none', icon: 'none' };
  //showSpinnerBackdrop: boolean;

  newUserAndLoginEmitter: EventEmitter<UserAndLogin> = new EventEmitter();
  confirmModalEmitter: EventEmitter<boolean> = new EventEmitter()
  dealerGroupSelectionModalEmitter: EventEmitter<DealerGroupVM> = new EventEmitter()
  financialLinesImportedEmitter: EventEmitter<boolean> = new EventEmitter()
  
  //newStockCheckSelectedEmitter: EventEmitter<void> = new EventEmitter()




  

  stockCheckItemChanged: EventEmitter<boolean> = new EventEmitter<boolean>();
  //excelDownload: EventEmitter<boolean> = new EventEmitter<boolean>();

 


  // overview: {
  //   reports: { name: string, value: string }[]
  //   report: { name: string, value: string }
  // }

  userSetup: {
    rowData: UserAndLogin[]
  }

  
  // globalSearch:{
    
  // }
  


 
  //changesMadeToScans: boolean;
  userIsNotAuthorisedForApp: boolean;
  //refreshToken: string;

  


  constructor(
    //public constants: ConstantsService
  ) {

    //this.initAllVehicleDetails();

    

  }


  // initAllVehicleDetails(){

  //   //this.globalSearch = {
  //   //   reg: '',
  //   //   chassis: '',
  //   //   stageValue: '',
  //   //   data: [],
  //   //   stockChecks: []
  //   //  }
  // }





  
}
