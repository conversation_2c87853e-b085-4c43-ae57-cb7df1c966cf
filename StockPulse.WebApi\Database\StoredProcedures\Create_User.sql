﻿/****** Object:  StoredProcedure [dbo].[CREATE_User]    Script Date: 9/3/21 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].CREATE_User
(
	@UserId INT,
    @Name varchar(30) = NULL,
	@DealerGroup int,
	@EmployeeNumber varchar(50) = NULL
)
AS
BEGIN

--declare @NewIdOutput table (Id int);

SET NOCOUNT ON

IF ((SELECT DealerGroupId FROM dbo.Users WHERE Id = @UserId) != @DealerGroup)
BEGIN 
    RETURN
END

INSERT 
INTO Users (Name,DealerGroupId,EmployeeNumber)
	--OUTPUT inserted.Id into @NewIdOutput
 values (@Name,@DealerGroup,@EmployeeNumber);

 --select Id from @NewIdOutput

 SELECT SCOPE_IDENTITY();
END

GO




--To use this run 
--exec [CREATE_User] @Name = 'Bob', @DealerGroup = 1