﻿/****** Object:  StoredProcedure [dbo].[CREATE_User]    Script Date: 29/03/2021 18:46:10 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].[UPDATE_User]
(
	@UserId int,
    @UserToUpdateId int,
	@Name varchar(50),
	@DefaultSiteId int,
	@NewEmail varchar(50),
	@NewEmployeeNumber varchar(50)
)
AS
BEGIN



SET NOCOUNT ON



DECLARE @UserDealerGroupId INT;
DECLARE @NewUserDealerGroupId INT;
SELECT @UserDealerGroupId = DealerGroupId FROM Users WHERE Id = @UserId
SELECT @NewUserDealerGroupId = DealerGroupId FROM Users WHERE Id = @UserToUpdateId
IF (@NewUserDealerGroupId != @UserDealerGroupId)
BEGIN 
    RETURN
END


UPDATE U
SET U.Name = @Name
FROM [Users] As U
WHERE U.Id = @UserToUpdateId

UPDATE U
SET U.EmployeeNumber = @NewEmployeeNumber
FROM [Users] As U
WHERE U.Id = @UserToUpdateId

UPDATE US 
SET US.IsDefault = 0
FROM UserSites AS US
WHERE US.UserId = @UserToUpdateId


UPDATE US 
SET US.IsDefault = 1
FROM UserSites AS US
WHERE US.UserId = @UserToUpdateId AND US.SiteId = @DefaultSiteId

UPDATE ANU
SET ANU.Email = CASE WHEN @NewEmail IS NOT NULL THEN @NewEmail ELSE ANU.Email END
FROM [AspNetUsers] As ANU
WHERE ANU.LinkedPersonId = @UserToUpdateId

UPDATE ANU
SET ANU.NormalizedEmail = CASE WHEN @NewEmail IS NOT NULL THEN UPPER(@NewEmail) ELSE ANU.NormalizedEmail END
FROM [AspNetUsers] As ANU
WHERE ANU.LinkedPersonId = @UserToUpdateId

UPDATE ANU
SET ANU.UserName = CASE WHEN @NewEmail IS NOT NULL THEN @NewEmail ELSE ANU.Email END
FROM [AspNetUsers] As ANU
WHERE ANU.LinkedPersonId = @UserToUpdateId

UPDATE ANU
SET ANU.NormalizedUserName = CASE WHEN @NewEmail IS NOT NULL THEN UPPER(@NewEmail) ELSE ANU.NormalizedEmail END
FROM [AspNetUsers] As ANU
WHERE ANU.LinkedPersonId = @UserToUpdateId

END

Go

