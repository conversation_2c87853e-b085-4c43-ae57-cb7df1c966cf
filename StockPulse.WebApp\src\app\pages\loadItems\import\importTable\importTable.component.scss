#tableContainer{overflow: auto; height: 100%; width: 100%;}
    
h4{font-weight:700;}
#importTable{height: 100%}
.numberChip{position:fixed;top:60px;right:20px;}
// #counter{position:absolute;top:-2em;height:2em;right:0em;    background: var(--secondaryLighter);      padding: 0.2em 1em;      border-radius: 0.3em 0.3em 0 0;}

:host ::ng-deep .ag-cell.highlight{background:var(--secondaryLightest)}
:host ::ng-deep .ag-cell.agAlignRight{justify-content: flex-end;padding-right:20px;}

:host ::ng-deep .asterisk .customHeaderLabel::after {
  content: " *";
  color: var(--danger);
}

#counter {
  position: absolute;
  right: 1em;
  background: var(--secondaryLighter);
  padding: 0.5em 1em;
  border-radius: var(--border-radius);
  transform: translateY(calc(-1em - 30px));
  // height: 30px;
}