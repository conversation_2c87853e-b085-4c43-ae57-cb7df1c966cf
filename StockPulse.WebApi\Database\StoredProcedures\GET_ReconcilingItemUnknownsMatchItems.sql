SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].GET_ReconcilingItemUnknownsMatchItems
(
    @StockCheckId INT,
	@UserId INT
)
AS
BEGIN

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END


SELECT
r.Id as ItemId,
r.<PERSON>,
r.Vin
FROM ReconcilingItems r
INNER JOIN StockChecks sc on sc.Id = r.StockCheckId AND sc.Id = @StockCheckId
INNER JOIN ReconcilingItemTypes rt on rt.Id = r.ReconcilingItemTypeId
WHERE rt.ExplainsMissingVehicle=0
ORDER BY r.Id

END


GO

