﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace StockPulse.WebApi.Migrations
{
    /// <inheritdoc />
    public partial class changetablestobeDGspecificx2 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "MMGReconcilingItems",
                schema: "import");

            migrationBuilder.CreateTable(
                name: "MMGAtAuctions",
                schema: "import",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false),
                    ReconcilingItemTypeId = table.Column<int>(type: "int", nullable: false),
                    Reg = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Vin = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Description = table.Column<string>(type: "nvarchar(250)", maxLength: 250, nullable: true),
                    Comment = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    Reference = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    SourceReportId = table.Column<int>(type: "int", nullable: false),
                    SiteId = table.Column<int>(type: "int", nullable: false),
                    FileImportId = table.Column<int>(type: "int", nullable: true)
                },
                constraints: table =>
                {
                    table.ForeignKey(
                        name: "FK_MMGAtAuctions_FileImports_FileImportId",
                        column: x => x.FileImportId,
                        principalSchema: "import",
                        principalTable: "FileImports",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_MMGAtAuctions_ReconcilingItemTypes_ReconcilingItemTypeId",
                        column: x => x.ReconcilingItemTypeId,
                        principalSchema: "dbo",
                        principalTable: "ReconcilingItemTypes",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_MMGAtAuctions_Sites_SiteId",
                        column: x => x.SiteId,
                        principalSchema: "dbo",
                        principalTable: "Sites",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_MMGAtAuctions_SourceReports_SourceReportId",
                        column: x => x.SourceReportId,
                        principalSchema: "dbo",
                        principalTable: "SourceReports",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "MMGStockOnLoans",
                schema: "import",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false),
                    ReconcilingItemTypeId = table.Column<int>(type: "int", nullable: false),
                    Reg = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Vin = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Description = table.Column<string>(type: "nvarchar(250)", maxLength: 250, nullable: true),
                    Comment = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    Reference = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    SourceReportId = table.Column<int>(type: "int", nullable: false),
                    SiteId = table.Column<int>(type: "int", nullable: false),
                    FileImportId = table.Column<int>(type: "int", nullable: true)
                },
                constraints: table =>
                {
                    table.ForeignKey(
                        name: "FK_MMGStockOnLoans_FileImports_FileImportId",
                        column: x => x.FileImportId,
                        principalSchema: "import",
                        principalTable: "FileImports",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_MMGStockOnLoans_ReconcilingItemTypes_ReconcilingItemTypeId",
                        column: x => x.ReconcilingItemTypeId,
                        principalSchema: "dbo",
                        principalTable: "ReconcilingItemTypes",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_MMGStockOnLoans_Sites_SiteId",
                        column: x => x.SiteId,
                        principalSchema: "dbo",
                        principalTable: "Sites",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_MMGStockOnLoans_SourceReports_SourceReportId",
                        column: x => x.SourceReportId,
                        principalSchema: "dbo",
                        principalTable: "SourceReports",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "MMGWIPs",
                schema: "import",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false),
                    ReconcilingItemTypeId = table.Column<int>(type: "int", nullable: false),
                    Reg = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Vin = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Description = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Comment = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Reference = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    SourceReportId = table.Column<int>(type: "int", nullable: false),
                    SiteId = table.Column<int>(type: "int", nullable: false),
                    FileImportId = table.Column<int>(type: "int", nullable: true)
                },
                constraints: table =>
                {
                    table.ForeignKey(
                        name: "FK_MMGWIPs_FileImports_FileImportId",
                        column: x => x.FileImportId,
                        principalSchema: "import",
                        principalTable: "FileImports",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_MMGWIPs_ReconcilingItemTypes_ReconcilingItemTypeId",
                        column: x => x.ReconcilingItemTypeId,
                        principalSchema: "dbo",
                        principalTable: "ReconcilingItemTypes",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_MMGWIPs_Sites_SiteId",
                        column: x => x.SiteId,
                        principalSchema: "dbo",
                        principalTable: "Sites",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_MMGWIPs_SourceReports_SourceReportId",
                        column: x => x.SourceReportId,
                        principalSchema: "dbo",
                        principalTable: "SourceReports",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_MMGAtAuctions_FileImportId",
                schema: "import",
                table: "MMGAtAuctions",
                column: "FileImportId");

            migrationBuilder.CreateIndex(
                name: "IX_MMGAtAuctions_ReconcilingItemTypeId",
                schema: "import",
                table: "MMGAtAuctions",
                column: "ReconcilingItemTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_MMGAtAuctions_SiteId",
                schema: "import",
                table: "MMGAtAuctions",
                column: "SiteId");

            migrationBuilder.CreateIndex(
                name: "IX_MMGAtAuctions_SourceReportId",
                schema: "import",
                table: "MMGAtAuctions",
                column: "SourceReportId");

            migrationBuilder.CreateIndex(
                name: "IX_MMGStockOnLoans_FileImportId",
                schema: "import",
                table: "MMGStockOnLoans",
                column: "FileImportId");

            migrationBuilder.CreateIndex(
                name: "IX_MMGStockOnLoans_ReconcilingItemTypeId",
                schema: "import",
                table: "MMGStockOnLoans",
                column: "ReconcilingItemTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_MMGStockOnLoans_SiteId",
                schema: "import",
                table: "MMGStockOnLoans",
                column: "SiteId");

            migrationBuilder.CreateIndex(
                name: "IX_MMGStockOnLoans_SourceReportId",
                schema: "import",
                table: "MMGStockOnLoans",
                column: "SourceReportId");

            migrationBuilder.CreateIndex(
                name: "IX_MMGWIPs_FileImportId",
                schema: "import",
                table: "MMGWIPs",
                column: "FileImportId");

            migrationBuilder.CreateIndex(
                name: "IX_MMGWIPs_ReconcilingItemTypeId",
                schema: "import",
                table: "MMGWIPs",
                column: "ReconcilingItemTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_MMGWIPs_SiteId",
                schema: "import",
                table: "MMGWIPs",
                column: "SiteId");

            migrationBuilder.CreateIndex(
                name: "IX_MMGWIPs_SourceReportId",
                schema: "import",
                table: "MMGWIPs",
                column: "SourceReportId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "MMGAtAuctions",
                schema: "import");

            migrationBuilder.DropTable(
                name: "MMGStockOnLoans",
                schema: "import");

            migrationBuilder.DropTable(
                name: "MMGWIPs",
                schema: "import");

            migrationBuilder.CreateTable(
                name: "MMGReconcilingItems",
                schema: "import",
                columns: table => new
                {
                    FileImportId = table.Column<int>(type: "int", nullable: true),
                    ReconcilingItemTypeId = table.Column<int>(type: "int", nullable: false),
                    SiteId = table.Column<int>(type: "int", nullable: false),
                    SourceReportId = table.Column<int>(type: "int", nullable: false),
                    Comment = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    Description = table.Column<string>(type: "nvarchar(250)", maxLength: 250, nullable: true),
                    Id = table.Column<int>(type: "int", nullable: false),
                    Reference = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Reg = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Vin = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.ForeignKey(
                        name: "FK_MMGReconcilingItems_FileImports_FileImportId",
                        column: x => x.FileImportId,
                        principalSchema: "import",
                        principalTable: "FileImports",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_MMGReconcilingItems_ReconcilingItemTypes_ReconcilingItemTypeId",
                        column: x => x.ReconcilingItemTypeId,
                        principalSchema: "dbo",
                        principalTable: "ReconcilingItemTypes",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_MMGReconcilingItems_Sites_SiteId",
                        column: x => x.SiteId,
                        principalSchema: "dbo",
                        principalTable: "Sites",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_MMGReconcilingItems_SourceReports_SourceReportId",
                        column: x => x.SourceReportId,
                        principalSchema: "dbo",
                        principalTable: "SourceReports",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_MMGReconcilingItems_FileImportId",
                schema: "import",
                table: "MMGReconcilingItems",
                column: "FileImportId");

            migrationBuilder.CreateIndex(
                name: "IX_MMGReconcilingItems_ReconcilingItemTypeId",
                schema: "import",
                table: "MMGReconcilingItems",
                column: "ReconcilingItemTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_MMGReconcilingItems_SiteId",
                schema: "import",
                table: "MMGReconcilingItems",
                column: "SiteId");

            migrationBuilder.CreateIndex(
                name: "IX_MMGReconcilingItems_SourceReportId",
                schema: "import",
                table: "MMGReconcilingItems",
                column: "SourceReportId");
        }
    }
}
