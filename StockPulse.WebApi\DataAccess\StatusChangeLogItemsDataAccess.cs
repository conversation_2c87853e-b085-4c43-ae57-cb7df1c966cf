﻿using Dapper;
using StockPulse.Model;
using StockPulse.WebApi.Dapper;
using StockPulse.WebApi.ViewModel;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace StockPulse.WebApi.DataAccess
{
    public interface IStatusChangeLogItemsDataAccess
    {
        Task<IEnumerable<StatusChangeLogItemVM>> GetStatusChangeLogItems(int stockCheckId);
        Task AddStatusChangeLogItem(StatusChangeLogItemParams parms);
    }

    public class StatusChangeLogItemsDataAccess : IStatusChangeLogItemsDataAccess
    {
        private readonly IDapper dapper;

        public StatusChangeLogItemsDataAccess(IDapper dapper)
        {
            this.dapper = dapper;
        }

        public async Task<IEnumerable<StatusChangeLogItemVM>> GetStatusChangeLogItems(int stockCheckId)
        {
            var paramList = new DynamicParameters();
            paramList.Add("StockCheckId", stockCheckId);
            return await dapper.GetAllAsync<StatusChangeLogItemVM>("dbo.GET_StatusChangeLogItems", paramList, System.Data.CommandType.StoredProcedure);
        }

        public async Task AddStatusChangeLogItem(StatusChangeLogItemParams parms)
        {
            var paramList = new DynamicParameters();
            paramList.Add("StockCheckId", parms.StockCheckId);
            paramList.Add("UserId", parms.UserId);
            paramList.Add("StatusId", parms.StatusId);
            await dapper.ExecuteAsync("dbo.ADD_StatusChangeLogItem", paramList, System.Data.CommandType.StoredProcedure);
        }
    }
}
