﻿
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].[ADD_UnknownResolutionImage]
(
	@fileName nvarchar(255),
    @StockCheckId INT,
	@UserId INT,
	@UnknownResolutionId INT
)
AS
BEGIN

SET NOCOUNT ON;

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END

INSERT INTO [dbo].[UnknownResolutionImages]
([UnknownResolutionId], [FileName])
VALUES
(@UnknownResolutionId, @fileName)


DECLARE @Id INT
SET @Id = SCOPE_IDENTITY();

EXEC [dbo].[UPDATE_StockCheckLastUpdate] @StockCheckId, @UserId

SELECT @Id



END
	
  
	

GO


