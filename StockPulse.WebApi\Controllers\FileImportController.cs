﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using StockPulse.WebApi.Attribute;
using StockPulse.WebApi.Service;
using StockPulse.WebApi.ViewModel;
using System;
using System.Threading.Tasks;
using static StockPulse.WebApi.Attribute.UserLevelAccessAttribute;

namespace StockPulse.WebApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    [UserLevelAccess(new[] { UserRole.SysAdministrator, UserRole.Reconciler, UserRole.Approver, UserRole.GeneralManager, UserRole.ReadOnly, UserRole.ScanAndView })]
    public class FileImportController : ControllerBase, IAttributeValueProvider
    {

        private readonly IFileImportService fileImportService;
        private readonly int userId;
        private readonly string userRole;

        public UserRole attributeUserRole => GetUserRole();
        private UserRole GetUserRole()
        {
            return (UserRole)Enum.Parse(typeof(UserRole), userRole);
        }

        //constructor
        public FileImportController(IFileImportService fileImportService, IUserService userService)
        {
            this.fileImportService = fileImportService;
            userId = userService.GetUserId();
            userRole = userService.GetUserRole();
        }

        [HttpPost]
        [Route("AddFileImport")]
        [UserLevelAccess(new[] { UserRole.SysAdministrator, UserRole.Reconciler, UserRole.Approver })]
        public async Task<int> AddFileImport(FileImport fileImport)
        {
            return await fileImportService.AddFileImport(fileImport, userId);
        }
    }
}
