

CREATE OR ALTER PROCEDURE [dbo].[GET_StockChecksOverview]
(
    @UserId INT = NULL,
	@IsActive BIT,   --nullable
	@StockCheckId INT,  --nullable
	@FromDate NVARCHAR(50),
	@ToDate NVARCHAR(50)
)
AS
BEGIN

SET NOCOUNT ON;

DECLARE @userDealerGroup int = (SELECT DealerGroupId FROM Users WHERE Id = @userId)


DECLARE @IsRegional BIT = NULL
DECLARE @IsTotal BIT = NULL

SELECT @IsRegional = IsRegional, @IsTotal = IsTotal FROM dbo.StockChecks WHERE Id = @StockCheckId AND @StockCheckId IS NOT NULL





SELECT
SiteId
INTO #userSites
FROM UserSites
WHERE UserId = @UserId

-----------------------------------------------
-- 1. Work out regional stats
-----------------------------------------------
;WITH RegionalStats AS 
(SELECT  
	mainSC.Date as StockCheckDate,
	si.DivisionId as DivisionId,

	(SELECT Count(sti.Id) 
	FROM StockItems sti 
	INNER JOIN StockChecks sc on sc.id = sti.StockCheckId
	INNER JOIN Sites site on site.id = sc.SiteId 
	INNER JOIN #userSites us on sc.SiteId = us.SiteId
	WHERE  
	(@IsActive IS NULL OR sc.IsActive = @IsActive)
	AND site.DivisionId = si.DivisionId
	AND sc.Date = mainSC.Date
	) 
	as StockItemsCount,

	(SELECT SUM(sti.StockValue)
	FROM StockItems sti 
	INNER JOIN StockChecks sc on sc.id = sti.StockCheckId
	INNER JOIN Sites site on site.id = sc.SiteId 
	INNER JOIN #userSites us on sc.SiteId = us.SiteId
	WHERE  
	(@IsActive IS NULL OR sc.IsActive = @IsActive)
	AND site.DivisionId = si.DivisionId
	AND sc.Date = mainSC.Date
	) 
	as StockValue,
	
	(SELECT COUNT(sca.Id) 
	FROM Scans sca 
	INNER JOIN StockChecks sc on sc.id = sca.StockCheckId 
	INNER JOIN Sites site on site.id = sc.SiteId 
	INNER JOIN #userSites us on sc.SiteId = us.SiteId
	WHERE 
	(@IsActive IS NULL OR sc.IsActive = @IsActive)
	AND site.DivisionId = si.DivisionId
	AND sc.Date = mainSC.Date
	) as Scans,
	
	(SELECT COUNT(sti.Id) 
	FROM StockItems sti 
	INNER JOIN StockChecks sc on sc.id = sti.StockCheckId
	INNER JOIN Sites site on site.id = sc.SiteId
	INNER JOIN #userSites us on sc.SiteId = us.SiteId
	INNER JOIN Scans scans on scans.Id = sti.ScanId AND scans.StockCheckId = sti.StockCheckId
	WHERE
	(@IsActive IS NULL OR sc.IsActive = @IsActive)
	AND site.DivisionId = si.DivisionId
	AND sc.Date = mainSC.Date
	AND sti.IsDuplicate = 0
	) as ScannedInStock,
	
	(SELECT COUNT(sca.Id) 
	FROM Scans sca
	INNER JOIN StockChecks sc on sc.id = sca.StockCheckId
	INNER JOIN Sites site on site.id = sc.SiteId 
	INNER JOIN #userSites us on sc.SiteId = us.SiteId
	WHERE
	(@IsActive IS NULL OR sc.IsActive = @IsActive)
	AND site.DivisionId = si.DivisionId
	AND sc.Date = mainSC.Date
	AND sca.IsDuplicate = 0 
	AND sca.StockItemId IS NULL 
	AND sca.ReconcilingItemId IS NULL
	) as UnknownsTot,

	(SELECT COUNT(sca.Id) 
	FROM Scans sca
	INNER JOIN StockChecks sc on sc.id = sca.StockCheckId
	INNER JOIN Sites site on site.id = sc.SiteId 
	INNER JOIN #userSites us on sc.SiteId = us.SiteId
	LEFT JOIN UnknownResolutions ur on ur.Id = sca.UnknownResolutionId
	WHERE 
	(@IsActive IS NULL OR sc.IsActive = @IsActive)
	AND site.DivisionId = si.DivisionId 
	AND sc.Date = mainSC.Date
	AND sca.IsDuplicate = 0 
	AND sca.StockItemId IS NULL 
	AND sca.ReconcilingItemId IS NULL
	AND 
		(ur.Id IS  NULL
			OR
			ur.IsResolved = 0
		)
	) as UnknownOs,

	(SELECT COUNT(sti.Id) 
	FROM StockItems sti
	INNER JOIN StockChecks sc on sc.id = sti.StockCheckId
	INNER JOIN Sites site on site.id = sc.SiteId 
	INNER JOIN #userSites us on sc.SiteId = us.SiteId
	WHERE 
	(@IsActive IS NULL OR sc.IsActive = @IsActive)
	AND site.DivisionId = si.DivisionId
	AND sc.Date = mainSC.Date
	AND IsDuplicate = 0 
	AND ScanId IS NULL 
	AND ReconcilingItemId IS NULL
	) 	as MissingsTot,

	(SELECT SUM(sti.StockValue) * 0.001
	FROM StockItems sti
	INNER JOIN StockChecks sc on sc.id = sti.StockCheckId 
	INNER JOIN Sites site on site.id = sc.SiteId 
	INNER JOIN #userSites us on sc.SiteId = us.SiteId
	LEFT JOIN MissingResolutions mr on mr.Id = sti.MissingResolutionId 
	WHERE 
	(@IsActive IS NULL OR sc.IsActive = @IsActive)
	AND site.DivisionId = si.DivisionId
	AND sc.Date = mainSC.Date
	AND IsDuplicate = 0 
	AND ScanId IS NULL 
	AND ReconcilingItemId IS NULL
	AND 
		(mr.Id IS  NULL
		OR
		mr.IsResolved = 0
		)
	) 
	as MissingValue,

	(SELECT COUNT(sti.Id) 
	FROM StockItems sti
	INNER JOIN StockChecks sc on sc.id = sti.StockCheckId 
	INNER JOIN Sites site on site.id = sc.SiteId 
	INNER JOIN #userSites us on sc.SiteId = us.SiteId
	LEFT JOIN MissingResolutions mr on mr.Id = sti.MissingResolutionId 
	WHERE 
	(@IsActive IS NULL OR sc.IsActive = @IsActive)
	AND site.DivisionId = si.DivisionId
	AND sc.Date = mainSC.Date
	AND IsDuplicate = 0 
	AND ScanId IS NULL 
	AND ReconcilingItemId IS NULL
	AND 
		(mr.Id IS  NULL
		OR
		mr.IsResolved = 0
		)
	) 
	as MissingOs,--

	(SELECT TOP 1 ScanDateTime
	FROM Scans s
	INNER JOIN StockChecks sc on sc.id = s.StockCheckId 
	INNER JOIN Sites site on site.id = sc.SiteId 
	WHERE
	(@IsActive IS NULL OR sc.IsActive = @IsActive)
	AND site.DivisionId = si.DivisionId
	AND sc.Date = mainSC.Date
	ORDER BY s.ScanDateTime
	) as FirstScan,

	(SELECT TOP 1 ScanDateTime
	FROM Scans s
	INNER JOIN StockChecks sc on sc.id = s.StockCheckId 
	INNER JOIN Sites site on site.id = sc.SiteId 
	WHERE
	(@IsActive IS NULL OR sc.IsActive = @IsActive)
	AND site.DivisionId = si.DivisionId
	AND sc.Date = mainSC.Date
	ORDER BY s.ScanDateTime DESC
	) as LastScan,

	(SELECT SUM(Balance)
	FROM FinancialLines fl
	INNER JOIN StockChecks sc ON sc.Id = fl.StockCheckId
	INNER JOIN Sites s on s.Id = sc.SiteId
	INNER JOIN #userSites us on sc.SiteId = us.SiteId
	WHERE
	sc.Date = mainSC.Date
	AND s.DivisionId = si.DivisionId
	) AS GLValue
	

 
FROM [dbo].[StockChecks] AS mainSC
INNER JOIN  Sites AS si ON si.Id = mainSC.SiteId
INNER JOIN #userSites us on mainSC.SiteId = us.SiteId
WHERE  
	(@IsActive IS NULL OR mainSC.IsActive = @IsActive)
	AND si.Id IN (SELECT SiteId FROM #userSites)
	AND (@FromDate IS NULL OR mainSC.Date >= @FromDate)
	AND (@ToDate IS NULL OR mainSC.Date <= @ToDate)
	AND mainSC.IsRegional = 1
	AND @IsRegional IS NULL OR @IsRegional = 1

GROUP BY 
	mainSC.Date,
	si.DivisionId
	),

	
-----------------------------------------------
-- 2. Do total stats
-----------------------------------------------
TotalStats AS (
SELECT  
	mainSC.Date as StockCheckDate,

	(SELECT Count(sti.Id) 
	FROM StockItems sti 
	INNER JOIN StockChecks sc on sc.id = sti.StockCheckId
	INNER JOIN #userSites us on sc.SiteId = us.SiteId
	WHERE  
	sc.Date = mainSC.Date
	) as StockItemsCount,
	
	(SELECT SUM(sti.StockValue) 
	FROM StockItems sti 
	INNER JOIN StockChecks sc on sc.id = sti.StockCheckId
	INNER JOIN #userSites us on sc.SiteId = us.SiteId
	WHERE  
	sc.Date = mainSC.Date
	) as StockValue,

	(SELECT COUNT(sca.Id) 
	FROM Scans sca 
	INNER JOIN StockChecks sc on sc.id = sca.StockCheckId
	INNER JOIN #userSites us on sc.SiteId = us.SiteId
	WHERE  
	sc.Date = mainSC.Date
	) as Scans,
	
	(SELECT COUNT(sti.Id) 
	FROM StockItems sti 
	INNER JOIN StockChecks sc on sc.id = sti.StockCheckId 
	INNER JOIN Scans scans on scans.Id = sti.ScanId AND scans.StockCheckId = sti.StockCheckId
	INNER JOIN #userSites us on sc.SiteId = us.SiteId
	WHERE  
	sc.Date = mainSC.Date
	AND sti.IsDuplicate = 0
	) as ScannedInStock,
	
	(SELECT COUNT(sca.Id) 
	FROM Scans sca
	INNER JOIN StockChecks sc on sc.id = sca.StockCheckId
	INNER JOIN #userSites us on sc.SiteId = us.SiteId
	WHERE  
	(@IsActive IS NULL OR sc.IsActive = @IsActive)
	AND sc.Date = mainSC.Date
	AND sca.IsDuplicate = 0 
	AND sca.StockItemId IS NULL 
	AND sca.ReconcilingItemId IS NULL
	) as UnknownsTot,

	(SELECT COUNT(sca.Id) 
	FROM Scans sca
	INNER JOIN StockChecks sc on sc.id = sca.StockCheckId
	INNER JOIN #userSites us on sc.SiteId = us.SiteId
	LEFT JOIN UnknownResolutions ur on ur.Id = sca.UnknownResolutionId
	WHERE  
	(@IsActive IS NULL OR sc.IsActive = @IsActive)
	AND sc.Date = mainSC.Date
	AND sca.IsDuplicate = 0 
	AND sca.StockItemId IS NULL 
	AND sca.ReconcilingItemId IS NULL
	AND		
		(ur.Id IS  NULL
			OR
			ur.IsResolved = 0
		)
	) as UnknownOs,

	(SELECT COUNT(sti.Id) 
	FROM StockItems sti
	INNER JOIN StockChecks sc on sc.id = sti.StockCheckId
	INNER JOIN #userSites us on sc.SiteId = us.SiteId
	WHERE
	(@IsActive IS NULL OR sc.IsActive = @IsActive)
	AND sc.Date = mainSC.Date
	AND IsDuplicate = 0 
	AND ScanId IS NULL 
	AND ReconcilingItemId IS NULL
	) 	as MissingsTot,

	(SELECT SUM(sti.StockValue) * 0.001 
	FROM StockItems sti
	INNER JOIN StockChecks sc on sc.id = sti.StockCheckId
	INNER JOIN #userSites us on sc.SiteId = us.SiteId
	LEFT JOIN MissingResolutions mr on mr.Id = sti.MissingResolutionId 
	WHERE  
	(@IsActive IS NULL OR sc.IsActive = @IsActive)
	AND sc.Date = mainSC.Date
	AND IsDuplicate = 0 
	AND ScanId IS NULL 
	AND ReconcilingItemId IS NULL
	AND 
		(mr.Id IS  NULL
		OR
		mr.IsResolved = 0
		)
	) 
	as MissingValue,

	(SELECT COUNT(sti.Id) 
	FROM StockItems sti
	INNER JOIN StockChecks sc on sc.id = sti.StockCheckId
	INNER JOIN #userSites us on sc.SiteId = us.SiteId
	LEFT JOIN MissingResolutions mr on mr.Id = sti.MissingResolutionId 
	WHERE  
	(@IsActive IS NULL OR sc.IsActive = @IsActive)
	AND sc.Date = mainSC.Date
	AND IsDuplicate = 0 
	AND ScanId IS NULL 
	AND ReconcilingItemId IS NULL
	AND 
		(mr.Id IS  NULL
		OR
		mr.IsResolved = 0
		)
	) 
	as MissingOs,

	(SELECT TOP 1 ScanDateTime
	FROM Scans s
	INNER JOIN StockChecks sc on sc.id = s.StockCheckId
	WHERE
	(@IsActive IS NULL OR sc.IsActive = @IsActive)
	AND sc.Date = mainSC.Date
	ORDER BY s.ScanDateTime
	) as FirstScan,

	(SELECT TOP 1 ScanDateTime
	FROM Scans s
	INNER JOIN StockChecks sc on sc.id = s.StockCheckId
	WHERE
	(@IsActive IS NULL OR sc.IsActive = @IsActive)
	AND sc.Date = mainSC.Date
	ORDER BY s.ScanDateTime DESC
	) as LastScan,

	(
	SELECT SUM(Balance)
	FROM FinancialLines fl
	INNER JOIN StockChecks sc ON sc.Id = fl.StockCheckId
	INNER JOIN #userSites us on sc.SiteId = us.SiteId
	WHERE
	sc.Date = mainSC.Date
	) AS GLValue
	
	--select top 1 * from FinancialLines
 
FROM [dbo].[StockChecks] AS mainSC
INNER JOIN  Sites AS si ON si.Id = mainSC.SiteId
INNER JOIN #userSites us on mainSC.SiteId = us.SiteId
WHERE  
	(@IsActive IS NULL OR mainSC.IsActive = @IsActive)
	AND si.Id IN (SELECT SiteId FROM #userSites)
	AND (@FromDate IS NULL OR mainSC.Date >= @FromDate)
	AND (@ToDate IS NULL OR mainSC.Date <= @ToDate)
	AND mainSC.IsTotal = 1
	AND @IsTotal IS NULL OR @IsTotal = 1

GROUP BY 
	mainSC.Date)


---------------------------------
-- 3. Do main query
---------------------------------

	SELECT  
	sc.Id,
	si.[Description] AS Site,
	si.[Id] AS SiteId,
	u.[Name] AS Person,
	[Date],
	[LastUpdated],
	Ua.Name AS ApprovedByAccountant,
	Uaa.Name AS ApprovedBy,
	[IsRegional],
	[IsTotal],
	st.[Description] AS Status,
	st.[Id] AS StatusId,
	sc.HasSignoffImage,
	sc.[IsActive],
	
	CASE
		WHEN sc.IsRegional = 1 AND sc.IsTotal = 0 THEN SUM(regFigs.StockItemsCount)
		WHEN sc.IsRegional = 0 AND sc.IsTotal = 1 THEN SUM(totFigs.StockItemsCount)
		ELSE(SELECT Count(Id) FROM StockItems WHERE StockCheckId = sc.Id)
	END as StockItemsCount,

	CASE
		WHEN sc.IsRegional = 1 AND sc.IsTotal = 0 THEN SUM(regFigs.StockValue) * 0.001
		WHEN sc.IsRegional = 0 AND sc.IsTotal = 1 THEN SUM(totFigs.StockValue)* 0.001
		ELSE(SELECT SUM(StockValue) * 0.001 FROM StockItems WHERE StockCheckId = sc.Id)
	END as InStockValue,

	CASE
		WHEN sc.IsRegional = 1 AND sc.IsTotal = 0 THEN SUM(regFigs.MissingValue)
		WHEN sc.IsRegional = 0 AND sc.IsTotal = 1 THEN SUM(totFigs.MissingValue)
		ELSE(
			SELECT SUM(StockValue) * 0.001 FROM StockItems sti
			LEFT JOIN MissingResolutions mr on mr.Id = sti.MissingResolutionId 
			WHERE sti.StockCheckId = sc.Id
			AND sti.IsDuplicate = 0 
			AND sti.ScanId IS NULL 
			AND sti.ReconcilingItemId IS NULL
			AND 
			(mr.Id IS  NULL
			OR
			mr.IsResolved = 0
			)
		)
	END as UnresolvedMissingValue,

	CASE
		WHEN sc.IsRegional = 1 AND sc.IsTotal = 0 THEN SUM(regFigs.Scans)
		WHEN sc.IsRegional = 0 AND sc.IsTotal = 1 THEN SUM(totFigs.Scans)
		ELSE (SELECT COUNT(Id) FROM Scans WHERE StockCheckId = sc.Id) 
	END as Scans,
	CASE
		WHEN sc.IsRegional = 1 AND sc.IsTotal = 0 THEN SUM(regFigs.ScannedInStock)
		WHEN sc.IsRegional = 0 AND sc.IsTotal = 1 THEN SUM(totFigs.ScannedInStock)
		ELSE 
		(
			SELECT COUNT(si.Id) 
			FROM StockItems si 
			INNER JOIN Scans scans on scans.Id = si.ScanId AND scans.StockCheckId = sc.Id
			WHERE si.StockCheckId = sc.Id
			AND si.IsDuplicate = 0
		)
	END as ScannedInStock,
	
	CASE
		WHEN sc.IsRegional = 1 AND sc.IsTotal = 0 THEN SUM(regFigs.UnknownsTot)
		WHEN sc.IsRegional = 0 AND sc.IsTotal = 1 THEN SUM(totFigs.UnknownsTot)
		ELSE (SELECT COUNT(Id) FROM Scans WHERE StockCheckId = sc.Id AND IsDuplicate = 0 AND StockItemId IS NULL AND ReconcilingItemId IS NULL) 
	END as UnknownsTot,
	CASE
		WHEN sc.IsRegional = 1 AND sc.IsTotal = 0 THEN SUM(regFigs.UnknownOs)
		WHEN sc.IsRegional = 0 AND sc.IsTotal = 1 THEN SUM(totFigs.UnknownOs)
		ELSE (
			SELECT COUNT(sca.Id) 
			FROM Scans sca
			LEFT JOIN UnknownResolutions ur on ur.Id = sca.UnknownResolutionId
			WHERE sca.StockCheckId = sc.Id 
			AND sca.IsDuplicate = 0 
			AND sca.StockItemId IS NULL 
			AND sca.ReconcilingItemId IS NULL 
			AND 
				(ur.Id IS  NULL
				OR
				ur.IsResolved = 0
				)
			)
	END as UnknownOs,

	CASE
		WHEN sc.IsRegional = 1 AND sc.IsTotal = 0 THEN SUM(regFigs.MissingsTot)
		WHEN sc.IsRegional = 0 AND sc.IsTotal = 1 THEN SUM(totFigs.MissingsTot)
		ELSE (SELECT COUNT(Id) FROM StockItems WHERE StockCheckId = sc.Id AND IsDuplicate = 0 AND ScanId IS NULL AND ReconcilingItemId IS NULL) 
	END as MissingsTot,
	CASE
		WHEN sc.IsRegional = 1 AND sc.IsTotal = 0 THEN SUM(regFigs.MissingOs)
		WHEN sc.IsRegional = 0 AND sc.IsTotal = 1 THEN SUM(totFigs.MissingOs)
		ELSE (
			SELECT COUNT(st.Id) 
			FROM StockItems st 
			LEFT JOIN MissingResolutions mr on mr.Id = st.MissingResolutionId 
			WHERE st.StockCheckId = sc.Id 
			AND st.IsDuplicate = 0 
			AND st.ScanId IS NULL 
			AND st.ReconcilingItemId IS NULL 
			AND 
				(mr.Id IS  NULL
				OR
				mr.IsResolved = 0
				)
			)
	END as MissingOs,
	sc.ReconciliationCompletedDate,
	sc.ReconciliationApprovedDate,
	si.Longitude AS SiteLongitude,
	si.Latitude AS SiteLatitude,
	si.OverrideLongLat,
	D.Description AS SiteRegion,

	CASE
		WHEN sc.IsRegional = 1 AND sc.IsTotal = 0 THEN regFigs.FirstScan
		WHEN sc.IsRegional = 0 AND sc.IsTotal = 1 THEN totFigs.FirstScan
		ELSE (SELECT TOP 1 ScanDateTime FROM Scans WHERE StockCheckId = sc.Id ORDER BY ScanDateTime)
	END AS FirstScan,
	
	CASE
		WHEN sc.IsRegional = 1 AND sc.IsTotal = 0 THEN regFigs.LastScan
		WHEN sc.IsRegional = 0 AND sc.IsTotal = 1 THEN totFigs.LastScan
		ELSE (SELECT TOP 1 ScanDateTime FROM Scans WHERE StockCheckId = sc.Id ORDER BY ScanDateTime DESC)
	END AS LastScan,

	CASE
		WHEN sc.IsRegional = 1 AND sc.IsTotal = 0 THEN (regFigs.GLValue / 1000)
		WHEN sc.IsRegional = 0 AND sc.IsTotal = 1 THEN (totFigs.GLValue / 1000)
		ELSE (SUM(fl.Balance) / 1000)
	END AS GLValue
  
FROM [dbo].[StockChecks] AS sc
  INNER JOIN  Sites AS si ON si.Id = sc.SiteId
  INNER JOIN  Users AS U ON U.Id = sc.UserId
  INNER JOIN  Statuses AS st ON st.Id = sc.StatusId
  INNER JOIN Divisions as D ON D.Id = si.DivisionId 
  LEFT JOIN FinancialLines fl ON fl.StockCheckId = sc.Id
  LEFT JOIN Users Ua ON Ua.Id = sc.ApprovedByAccountantId
  LEFT JOIN Users Uaa ON Uaa.Id = sc.ApprovedById
  LEFT JOIN RegionalStats regFigs on regFigs.DivisionId = si.DivisionId and regFigs.StockCheckDate = sc.Date and sc.IsRegional = 1
  LEFT JOIN TotalStats totFigs on totFigs.StockCheckDate = sc.Date and sc.IsTotal = 1
  

WHERE  
	(@IsActive IS NULL OR SC.IsActive = @IsActive)
	AND si.Id IN (SELECT SiteId FROM #userSites)
	AND (@stockCheckId IS NULL OR sc.Id = @stockCheckId)
	AND (@FromDate IS NULL OR sc.Date >= @FromDate)
	AND (@ToDate IS NULL OR sc.Date <= @ToDate)

GROUP BY 
	sc.Id,
	si.[Description],
	si.[Id],
	u.[Name] ,
	[Date],
	[LastUpdated],
	Ua.Name ,
	Uaa.Name,
	[IsRegional],
	[IsTotal],
	ST.[Description] ,
	ST.[Id] ,
	SC.HasSignoffImage,
	SC.[IsActive],
	SC.ReconciliationCompletedDate,
	SC.ReconciliationApprovedDate,
	si.Longitude,
	si.Latitude,
	si.OverrideLongLat,
	D.Description,
	regFigs.FirstScan,
	regFigs.LastScan,
	totFigs.FirstScan,
	totFigs.LastScan,
	regFigs.GLValue,
	totFigs.GLValue

ORDER BY
	sc.[Date] desc, si.[Description]



DROP TABLE #userSites

END



