﻿using System.Runtime.Serialization;
using System.Linq;
using System;

namespace PlateRecognizer
{
    [DataContract]
    public class ScoreAndPlateItem
    {
        [DataMember(Name = "score")]
        public decimal Score { get; set; }
       

        [DataMember(Name = "plate")]
        public string Plate { get; set; }

        [DataMember(Name = "scoreDebug")]
        public string ScoreDebug { get; set; }

        public string parsePlate(string plate)
        {
            plate = plate.ToUpper().Replace(" ", "");
            if (plate.Count() != 7) { return plate; }  //private plate maybe.  don't adjust it.

            return plate;

           
        }

      

        public decimal provideAdjustedScore(decimal score, string plate)
        {
            if (plate.Count() != 7) { return score * 100; }
            if (
            !char.<PERSON><PERSON>ber(plate[0]) &&
            !char.<PERSON>umber(plate[1]) &&
            char.<PERSON>N<PERSON>ber(plate[2]) &&
            char.<PERSON>N<PERSON>ber(plate[3]) &&
            !char.<PERSON>Number(plate[4]) &&
            !char.<PERSON><PERSON><PERSON>ber(plate[5]) &&
            !char.<PERSON><PERSON><PERSON><PERSON>(plate[6])
            )
            {
                return Math.Min(99.9M, score * 100 + 5);
            }
            else
            {
                return Math.Max(0, score * 100 - 5);
            }
        }
    }




}