﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE OR ALTER PROCEDURE [admin].[GET_StockCheckScanRecognitionReport] 
(
	@StockCheckId INT
)
AS  
BEGIN  
  
SET NOCOUNT ON;  

--This is the normal query bit

DECLARE @TotalScans int = (SELECT COUNT(Id) FROM Scans WHERE StockCheckId = @StockCheckId)

SELECT
CASE
	WHEN sca.IsRegEditedOnDevice = 1 THEN 'Changed on device'
	WHEN sca.IsRegEditedOnWeb =1 THEN 'Changed on web'
	ELSE 'OK'
END as Reg,

CASE
	WHEN sca.IsVinEditedOnDevice = 1 THEN 'Changed on device'
	WHEN sca.IsVinEditedOnWeb =1 THEN 'Changed on web'
	ELSE 'OK'
END as Vin,

COUNT(Id) as Count,
CAST(ROUND((CAST(COUNT(Id) AS FLOAT) / @TotalScans) * 100, 0) AS VARCHAR) + '%' as Percentage

FROM Scans sca
WHERE sca.StockCheckId = @StockCheckId
GROUP BY 
CASE
	WHEN sca.IsRegEditedOnDevice = 1 THEN 'Changed on device'
	WHEN sca.IsRegEditedOnWeb =1 THEN 'Changed on web'
	ELSE 'OK'
END ,

CASE
	WHEN sca.IsVinEditedOnDevice = 1 THEN 'Changed on device'
	WHEN sca.IsVinEditedOnWeb =1 THEN 'Changed on web'
	ELSE 'OK'
END 

UNION ALL

SELECT 'Total','',@TotalScans,''




--This bit outputs a load of messages giving us detail on the ones that it could not find


DECLARE @Id int, @Type nvarchar(20), @InterpretedString nvarchar(255), @FinalString nvarchar(255)
DECLARE @Message nvarchar(max)

-- Cursor to loop through the rows
DECLARE diagnostic_cursor CURSOR FOR 

SELECT Id, 'RegEditedOnDev', InterpretedReg, Reg FROM Scans WHERE StockCheckId = @StockCheckId AND IsRegEditedOnDevice = 1
UNION ALL
SELECT Id, 'RegEditedOnWeb', InterpretedReg, Reg FROM Scans WHERE StockCheckId = @StockCheckId AND IsRegEditedOnWeb = 1
UNION ALL
SELECT Id, 'VinEditedOnDev', InterpretedVin, Vin FROM Scans WHERE StockCheckId = @StockCheckId AND IsVinEditedOnDevice = 1
UNION ALL
SELECT Id, 'VinEditedOnWeb', InterpretedVin, Vin FROM Scans WHERE StockCheckId = @StockCheckId AND IsVinEditedOnWeb = 1

-- Open the cursor
OPEN diagnostic_cursor

-- Fetch the first row from the cursor
FETCH NEXT FROM diagnostic_cursor INTO @Id, @Type, @InterpretedString, @FinalString

-- Loop through the rows
WHILE @@FETCH_STATUS = 0
BEGIN
    -- Construct and print the message for each row
    SET @Message = @Type + ': ' + 'Id #' + CAST(@Id AS nvarchar) 
            + ', Interpreted: ' + RIGHT('          ' + CONVERT(nvarchar(8), @InterpretedString), 10)
            + ', Final: ' + RIGHT('          ' + CONVERT(varchar(8), @FinalString), 10)

    PRINT @Message

    -- Fetch the next row from the cursor
    FETCH NEXT FROM diagnostic_cursor INTO @Id, @Type,@InterpretedString, @FinalString
END

-- Close and deallocate the cursor
CLOSE diagnostic_cursor
DEALLOCATE diagnostic_cursor


END  
  
GO



