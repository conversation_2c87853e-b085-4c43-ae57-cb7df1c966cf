import { Injectable } from '@angular/core';
import { CphPipe } from '../cph.pipe';
import { PreferenceKey, PreferenceTypeMap, UserPreference } from '../model/UserPreference';
import { GetDataService } from './getData.service';

interface DeferredPreference {
  prefToPersist: UserPreference
  whenAdded: Date,
}

@Injectable({ providedIn: 'root' })



export class UserPreferenceService {
  preferenceStore: { [key in PreferenceKey]?: UserPreference } = {};

  deferredPreferences: DeferredPreference[] = [];

  constructor(
    public cphPipe: CphPipe,
    private getDataService: GetDataService
  ) { }

  getPreference<prefKey extends PreferenceKey>(key: prefKey): PreferenceTypeMap[prefKey] | undefined {
    if (!this.preferenceStore) { return undefined; }
    const pref = this.preferenceStore[key];
    if (!pref) { return undefined; }
    return pref.getPreference(key) as PreferenceTypeMap[prefKey];

  }

  setPreference<prefKey extends PreferenceKey>(key: prefKey, value: PreferenceTypeMap[prefKey]) {

    let prefToPersist = this.preferenceStore[key];
    if (prefToPersist) {
      prefToPersist.setPreference(key, value);
    } else {
      prefToPersist = new UserPreference();
      this.preferenceStore[key] = prefToPersist;
      this.preferenceStore[key].setPreference(key, value);
    }

    this.deferredPreferences = this.deferredPreferences.filter(x => x.prefToPersist.preferenceName != prefToPersist.preferenceName); //remove any pending
    const whenAdded = new Date();
    this.deferredPreferences.push({ prefToPersist, whenAdded: whenAdded });

    setTimeout(() => {
      const deferredPref = this.deferredPreferences.find(x => x.whenAdded.getTime() == whenAdded.getTime())
      if (deferredPref) {
        this.persistPreference(deferredPref.prefToPersist)
      }
    }, 2000)
  }

  private persistPreference<prefKey extends PreferenceKey>(prefToPersist: UserPreference) {
    this.getDataService.saveUserPreference(prefToPersist).subscribe((res: UserPreference) => {

    });
  }
}