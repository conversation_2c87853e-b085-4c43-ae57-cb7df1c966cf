using NUnit.Framework;
using PlateRecognizer;
using StockPulse.WebApi.Service;
using System.Collections.Generic;
using System.Linq;

[TestFixture]
public class TidyUpResponsesTests
{
    [Test]
    public void TestTidyUpResponses_NoSpacesNoErrors()
    {
        // Arrange
        var processResult = new PlateReaderResult
        {
            Results = new List<PlateResult>
            {
                new PlateResult { Plate = "GU65UEN", ScoresAndPlates = new List<ScoreAndPlateItem> { new ScoreAndPlateItem { Plate = "AM55ABC" } } }
            }
        };

        // Act
        ConstantsService.TidyUpResponses(processResult);

        // Assert
        Assert.That(processResult.Results.First().Plate, Is.EqualTo("GU65UEN"));
        Assert.That(processResult.Results.First().ScoresAndPlates.First().Plate, Is.EqualTo("AM55ABC"));
    }

    [Test]
    public void TestTidyUpResponses_SpacesNoErrors()
    {
        // Arrange
        var processResult = new PlateReaderResult
        {
            Results = new List<PlateResult>
            {
                new PlateResult { Plate = "GU65 UEN", ScoresAndPlates = new List<ScoreAndPlateItem> { new ScoreAndPlateItem { Plate = "AM55 ABC" } } }
            }
        };

        // Act
        ConstantsService.TidyUpResponses(processResult);

        // Assert
        Assert.That(processResult.Results.First().Plate, Is.EqualTo("GU65UEN"));
        Assert.That(processResult.Results.First().ScoresAndPlates.First().Plate, Is.EqualTo("AM55ABC"));
    }

    [Test]
    public void TestTidyUpResponses_7CharWronglyShowsNumberInsteadOfLetter()
    {
        // Arrange
        var processResult = new PlateReaderResult
        {
            Results = new List<PlateResult>
            {
                new PlateResult { Plate = "5U65UEN", ScoresAndPlates = new List<ScoreAndPlateItem> { new ScoreAndPlateItem { Plate = "8U65UEN" } } }
            }
        };

        // Act
        ConstantsService.TidyUpResponses(processResult);

        // Assert
        Assert.That(processResult.Results.First().Plate, Is.EqualTo("SU65UEN"));
        Assert.That(processResult.Results.First().ScoresAndPlates.First().Plate, Is.EqualTo("BU65UEN"));
    }

    [Test]
    public void TestTidyUpResponses_7CharWronglyShowsLetterInsteadOfNumber()
    {
        // Arrange
        var processResult = new PlateReaderResult
        {
            Results = new List<PlateResult>
            {
                new PlateResult { Plate = "GU6BUEN", ScoresAndPlates = new List<ScoreAndPlateItem> { new ScoreAndPlateItem { Plate = "GU6SUEN" } } }
            }
        };

        // Act
        ConstantsService.TidyUpResponses(processResult);

        // Assert
        Assert.That(processResult.Results.First().Plate, Is.EqualTo("GU68UEN"));
        Assert.That(processResult.Results.First().ScoresAndPlates.First().Plate, Is.EqualTo("GU65UEN"));
    }


    // More tests to cover different scenarios...
}
