﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].GET_StockItem
(
    @StockItemId INT = NULL,
	@UserId INT = NULL
)
AS
BEGIN

SET NOCOUNT ON;

IF dbo.[AuthenticateUser](@UserId, (select StockCheckId from dbo.StockItems where Id = @StockItemId)) = 0
BEGIN 
    RETURN
END


	
SELECT 
Id,Reg,Vin,Description,DIS,GroupDIS,Branch,StockType,Comment,Reference,StockValue
FROM [dbo].[StockItems] 
WHERE Id = @StockItemId

	
END

GO



--To use this run 
 --exec [GET_StockItem] @StockItemId = 1, @UserId = 1