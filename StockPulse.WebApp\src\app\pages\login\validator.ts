import {AbstractControl, ValidationErrors, ValidatorFn} from '@angular/forms';

export function formItemHasAtSymbolValidator(): ValidatorFn {
    return (control:AbstractControl) : ValidationErrors | null => {

        const value = control.value;

        if (!value) {
            return null;
        }

        const hasAtSymbol = /[@]+/.test(value);
        const passwordValid = hasAtSymbol;

        return !passwordValid ? {hasAtSymbol:true}: null;
    }
}