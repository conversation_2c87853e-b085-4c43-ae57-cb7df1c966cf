# Node.js with Angular
# Build a Node.js project that uses Angular.
# Add steps that analyze code, save build artifacts, deploy, and more:
# https://docs.microsoft.com/azure/devops/pipelines/languages/javascript




pool:
  Default

jobs:
- job: BuildAndAduitAngularStockpulseClient
  displayName: 'Build And Audit Angular StockPulse Client'

  steps:
  - checkout: self
    fetchDepth: 1
    fetchTags: false  # Don't fetch unnecessary tags
    clean: true  # Ensures a fresh checkout
    persistCredentials: true

  - task: NodeTool@0
    inputs:
      versionSpec: '18.x'
    displayName: 'Install Node.js'

    # Install dependencies
  - script: npm ci
    workingDirectory: '$(Build.SourcesDirectory)/StockPulse.WebApp'
    displayName: 'Install Dependencies'

  # Microsoft scanner
  - task: MicrosoftSecurityDevOps@1
    inputs:
      scanFolder: '$(Build.SourcesDirectory)/StockPulse.WebApp'

  # 🧪 Download and run OWASP Dependency-Check
  - powershell: |
      $version = "9.0.9"
      $url = "https://github.com/jeremylong/DependencyCheck/releases/download/v$version/dependency-check-$version-release.zip"
      $output = "$(Agent.TempDirectory)\dependency-check.zip"
      $extractPath = "$(Agent.TempDirectory)\dependency-check"
      Invoke-WebRequest -Uri $url -OutFile $output
      Expand-Archive -Path $output -DestinationPath $extractPath
      $odcPath = Join-Path $extractPath "dependency-check"
      New-Item -ItemType Directory -Path "$(Build.SourcesDirectory)\StockPulse.WebApp\odc-report" -Force | Out-Null
      & "$odcPath\bin\dependency-check.bat" `
          --project "AngularApp" `
          --scan "$(Build.SourcesDirectory)\StockPulse.WebApp" `
          --out "$(Build.SourcesDirectory)\StockPulse.WebApp\odc-report" `
          --format HTML
    displayName: 'Run OWASP Dependency-Check (No Docker)'

  # 📤 Publish OWASP report
  - task: PublishBuildArtifacts@1
    inputs:
      PathtoPublish: '$(Build.SourcesDirectory)/StockPulse.WebApp/odc-report'
      ArtifactName: 'OWASP-Report'
      publishLocation: 'Container'


  



  
