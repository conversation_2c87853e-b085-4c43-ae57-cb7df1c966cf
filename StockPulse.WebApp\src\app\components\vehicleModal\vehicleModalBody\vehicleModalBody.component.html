<div [ngClass]="{'compact':showCompact}" class="d-flex justify-content-between">


  <!-- ############################################### -->
  <!-- Left card - DMS details -->
  <!-- ############################################### -->
  <div class="cph-card vehicle-modal-card">
    <div class="cph-card-header">
      <span class="cph-card-header-title">DMS Details</span>
    </div>
    <div class="cph-card-body">
      <table [ngClass]="{'compact':showCompact}" id="dms-details-table">
        <tbody>
          <tr *ngIf="!item.stockItem">
            <td colspan="2">
              <div class="holdingNote holdingNoteBad">
                <fa-icon [icon]="icon.faMinusCircle"></fa-icon>
                <div>No Matching Stock Item</div>
              </div>

            </td>
          </tr>

          <ng-container *ngIf="item.stockItem">
            <tr>
              <td>Id</td>
              <td>{{ item.stockItem?.stockItemId }}</td>
            </tr>
            <tr class="topBorder">
              <td>Description</td>
              <td>{{ item.stockItem?.description }}</td>
            </tr>
            <tr *ngIf="shouldShowReg()" class="topBorder">
              <td>Registration </td>
              <td>
                <div class="regPlate">{{ item.stockItem?.reg | cph:'numberPlate':0 }}</div>
              </td>
            </tr>
            <tr class="topBorder">
              <td>VIN</td>
              <td>
                <div class="chassis">{{ item.stockItem?.vin | cph:'chassis':0 }}</div>
              </td>
            </tr>
            <tr class="topBorder">
              <td>StockCheck Site</td>
              <td>{{ item.stockItem?.siteName }}</td>
            </tr>
            <tr class="topBorder">
              <td>Branch</td>
              <td>{{ item.stockItem?.branch }}</td>
            </tr>
            <tr class="topBorder">
              <td>Stock Type</td>
              <td>{{ item.stockItem?.stockType }}</td>
            </tr>
            <tr class="topBorder">
              <td>Notes</td>
              <td>{{ item.stockItem?.comment }}</td>
            </tr>
            <tr class="topBorder">
              <td>Reference</td>
              <td>{{ item.stockItem?.reference }}</td>
            </tr>
            <tr class="topBorder">
              <td>Days In Stock</td>
              <td>{{ item.stockItem?.dis }} ({{ item.stockItem?.groupDIS }})</td>
            </tr>
            <tr class="topBorder">
              <td>Stock Value</td>
              <td>{{ item.stockItem?.stockValue | cph:'currency':2 }}</td>
            </tr>
          </ng-container>
        </tbody>
      </table>

    </div>
  </div>



  <!-- ############################################### -->
  <!-- Middle card - Scan details -->
  <!-- ############################################### -->
  <div class="cph-card vehicle-modal-card">
    <div class="cph-card-header">
      <span class="cph-card-header-title">Scan Details</span>
    </div>
    <div class="cph-card-body">



      <div *ngIf="!item.scan" class="holdingNote holdingNoteBad">
        <fa-icon [icon]="icon.faMinusCircle"></fa-icon>
        <div>No Matching Scan</div>
      </div>

      <!-- ############################################### -->
      <!-- The scan and details area-->
      <!-- ############################################### -->
      <div *ngIf="item.scan" class="card-row">




        <!-- ############################################### -->
        <!-- The scan -->
        <!-- ############################################### -->
        <div id="regScanAndInput">
          <!-- REG STUFF -->
          <div id="regImage" class="subtleBoxShadow">
            <img [src]="item.scan?.regImageLargeUrl" class="popoverImage" (click)="modalImage = item.scan?.regImageLargeUrl">
          </div>

          <!-- Reg box -->
          <div *ngIf="shouldShowReg()" class="plateContainer">


            <!-- If not editing reg -->
            <div *ngIf="!service.amEditingReg" class="plateBox regBox"
              [ngClass]="{ 'wide': selections.stockCheck.statusId <= 2 }">
              <span *ngIf="item.scan">{{ item.scan?.scanReg | cph:'numberPlate':0 }}</span>
            </div>

            <button *ngIf="selections.stockCheck.statusId <= 2 && !service.amEditingReg && !showCompact" class="btn btn-primary btnEditPlate"
              id="editRegButton" (click)="editReg()">
              <fa-icon [icon]="icon.faPencil"></fa-icon>
            </button>

            <!-- If editing reg -->
            <ng-container *ngIf="service.amEditingReg">
              
              <div class="position-relative d-flex align-items-center">
                <input id="regEditBox" #newRegBox spellcheck="false" style="text-transform:uppercase"   (keydown.enter)="saveNewReg()" [(ngModel)]="newReg" 
                  maxlength="7"/>

                  <!-- Saving spinner -->
                  <fa-icon class="spinnerIcon fa-spin" id="savingSpinner" *ngIf="amSavingReg" [icon]="icon.faCircleNotch">
                  </fa-icon>
              </div>
              
              <!-- Save -->
              <button id="saveRegButton" class="btn btn-success btnEditPlate"
                (click)="saveNewReg()">
                <fa-icon class="saveInput" [icon]="icon.faSave"></fa-icon>
              </button>
              <!-- Cancel -->
              <button class="btn btn-danger btnEditPlate" id="cancelRegButton" (click)="cancelEditingReg()">
                <fa-icon class="cancelInput" [icon]="icon.faTimesCircle"></fa-icon>
              </button>


            </ng-container>
          </div>
        </div>


        <!-- ############################################### -->
        <!-- The scan Details table and vin image-->
        <!-- ############################################### -->
        <div id="scanDetails">
          <table [ngClass]="{'compact':showCompact}" id="scan-table">
            <tbody>
              <tr>
                <td>Scan Id</td>
                <td>
                  <span>{{ item.scan?.scanId }}</span>
                  <!-- <span *ngIf="!!stockItem?.scanId">{{ stockItem?.scanId }}</span> -->
                </td>
              </tr>
              <tr class="topBorder" *ngIf="item.stockItem?.state===states.MatchedToOtherSite">
                <td>Scanned at site</td>
                <td>{{item.otherSiteName}}</td>
              </tr>
              <tr class="topBorder">
                <td>Scan Location</td>
                <td>
                  <button class="btn btn-primary" [disabled]="selections.stockCheck.statusId > 3 || showCompact"
                    (click)="reviewLocation()">
                    {{ item.scan?.locationDescription }}
                  </button>
                  <!-- <div *ngIf="stockItem" class="locationChip">
                        {{ item.stockItem?.locationDescription }}
                      </div> -->
                </td>
              </tr>
              <tr class="topBorder">
                <td>Scanned by</td>
                <td>
                  <span>{{ item.scan?.scannerName }}</span>
                  <!-- <span *ngIf="!!stockItem?.scanId">{{ item.stockItem?.scannedBy }}</span> -->
                </td>
              </tr>
              <tr class="topBorder">
                <td>Scanned day</td>
                <td>
                  <span>{{ item.scan?.scanDateTime | cph:'day':0 }}</span>
                  <!-- <span *ngIf="!!stockItem?.scanId">{{ item.stockItem?.scanDateTime |cph:'day':0 }}</span> -->
                </td>
              </tr>
              <tr class="topBorder">
                <td>Scanned time</td>
                <td>
                  <span>{{ item.scan?.scanDateTime | cph:'time':0 }}</span>
                  <!-- <span *ngIf="!!stockItem?.scanId">{{ item.stockItem?.scanDateTime | cph:'time':0 }}</span> -->
                </td>
              </tr>
              <tr class="topBorder">
                <td>Plate Confidence</td>
                <td>
                  <div class="confidence">
                    {{ item.scan?.regConfidence | cph:'regConfidence':0 }}
                  </div>
                </td>
              </tr>
              <tr class="topBorder">
                <td>VIN Confidence</td>
                <td>
                  <div class="confidence">
                    {{ item.scan?.vinConfidence | cph:'vinConfidence':0 }}
                  </div>
                </td>
              </tr>
              <tr class="topBorder">
                <td>Notes</td>
                <td>
                  <div class="comment">
                    <span>{{ item.scan?.scanComment }}</span>
                    <!-- <span *ngIf="!!stockItem?.scanId">{{ item.stockItem?.scanComment }}</span> -->
                  </div>
                </td>
              </tr>
            </tbody>
          </table>

          <div id="vinScanAndInput">
            <!-- VIN image -->
            <div id="vinImage" class="subtleBoxShadow">
              <span *ngIf="!item.scan?.vinImageUrl">No VIN scan image available</span>
              <img *ngIf="item.scan?.vinImageUrl" [src]="item.scan?.vinImageUrl" class="popoverImage"
                (click)="modalImage = item.scan?.vinImageUrl">
              <!-- <img *ngIf="stockItem?.vinImageUrl" [src]="item.stockItem?.vinImageUrl" class="popoverImage"
                    (click)="zoomIn(item.stockItem?.vinImageUrl)"> -->
            </div>


            <!-- Vin box -->
            <div class="plateContainer">

              <!-- If not editing vin -->
              <div *ngIf="!service.amEditingVin" class="plateBox vinBox"
                [ngClass]="{ 'wide': selections.stockCheck.statusId <= 2 }">
                <span *ngIf="item.scan">{{ item.scan?.scanVin | cph:'numberPlate':0 }}</span>
              </div>

              <button *ngIf="selections.stockCheck.statusId <= 2 && !service.amEditingVin && !showCompact" class="btn btn-primary btnEditPlate"
                id="editVinButton" (click)="editVin()">
                <fa-icon [icon]="icon.faPencil"></fa-icon>
              </button>

              <!-- If editing vin -->
              <div *ngIf="service.amEditingVin">
                <span *ngIf="newVin && newVin.length < 8" class="text-danger">* VIN must be 8 characters</span>

                <div class="d-flex">
                  <div class="position-relative d-flex align-items-center">
                    <input id="vinEditBox" #newVinBox spellcheck="false" (keydown.enter)="saveNewVin()" [(ngModel)]="newVin" maxlength="8" />
                    
                    <!-- Saving spinner -->
                    <fa-icon class="spinnerIcon fa-spin" id="savingSpinnerVin" *ngIf="amSavingVin"
                      [icon]="icon.faCircleNotch">
                    </fa-icon>
                  </div>
                  
                  <!-- Save -->
                  <button id="saveVinButton" class="btn btn-success btnEditPlate"
                    (click)="saveNewVin()">
                    <fa-icon class="saveInput" [icon]="icon.faSave"></fa-icon>
                  </button>
                  <!-- Cancel -->
                  <button class="btn btn-danger btnEditPlate" id="cancelVinButton" (click)="cancelEditingVin()">
                    <fa-icon class="cancelInput" [icon]="icon.faTimesCircle"></fa-icon>
                  </button>
                </div>

              </div>


            </div>
          </div>
        </div>
      </div>

      <!-- ############################################### -->
      <!-- The map -->
      <!-- ############################################### -->
      <div *ngIf="!showCompact" class="card-row">
        <div *ngIf="item.scan" id="scanMap" class="subtleBoxShadow">
          <div *ngIf="item.scan.latitude === 0 && item.scan.longitude === 0" class="p-2">
            GPS coordinates not available
          </div>
          <google-map *ngIf="item.scan.latitude !== 0 && item.scan.longitude !== 0" width="100%" height="100%" [options]="mapOptions">
            <map-marker [position]="{ lat: item.scan.latitude, lng: item.scan.longitude }"></map-marker>
          </google-map>
        </div>
      </div>

    </div>
  </div>





  <!-- ################################################ -->
  <!-- Right card - Resolution details -->
  <!-- ################################################ -->
  <div class="cph-card vehicle-modal-card">

    <div class="cph-card-header">
      <span class="cph-card-header-title">Status</span>
    </div>

    <div class="cph-card-body" [ngClass]="{ 'showResolutionButtons': vehicleIsAnIssue() && !showCompact && (checkForChangesMade() || item.resolutionId) }">

      <!-- If stock matches scan -->
      <table class="explanationTable" *ngIf="stockMatchesScan()">
        <tbody>
          <tr>
            <td>
              <div class="holdingNote holdingNoteGood">
                <fa-icon [icon]="icon.faCheckCircle"></fa-icon>
                <div>Stock item Matched to Scan</div>
              </div>
            </td>
          </tr>
        </tbody>
      </table>


      <!-- If matches intercompany  -->
      <table class="explanationTable" *ngIf="itemMatchesIntercompany()">
        <tbody>
          <tr>
            <td colspan="2">
              <div class="holdingNote holdingNoteGood">
                <fa-icon [icon]="icon.faCheckCircle"></fa-icon>
                <div *ngIf="isStockItem()">Resolved - Our StockItem Matches to Other Site Scan</div>
                <div *ngIf="!isStockItem()">Resolved - Our Scan Matches to Other Site StockItem</div>
              </div>
            </td>
          </tr>

        </tbody>
      </table>





      <!-- if has matched with a report -->
      <table [ngClass]="{'compact':showCompact}" *ngIf="isMatchedToReconcilingItem()" class="explanationTable">
        <tbody>
          <tr>
            <td colspan="2">
              <div class="holdingNote holdingNoteGood">
                <fa-icon [icon]="icon.faFileCircleCheck"></fa-icon>
                <div>Matched - {{isStockItem() ? 'Stock item' : 'Scan'}} Matched to Report</div>
              </div>
            </td>
          </tr>

          <tr>
            <td>Matched to</td>
            <td>
              <div>{{ item.reconcilingItemTypeDescription }}</div>
            </td>
          </tr>
          <tr class="topBorder">
            <td>Id</td>
            <td>{{isStockItem() ? item.stockItem?.stockItemId : item.scan?.scanId }}</td>
          </tr>
          <tr class="topBorder">
            <td>Reference</td>
            <td>{{item.reconcilingItemRef}}</td>
          </tr>
          <tr class="topBorder">
            <td>Registration</td>
            <td>
              <div class="regPlate">{{item.reconcilingItemReg | cph:'numberPlate':0
                }}</div>
            </td>
          </tr>
          <tr class="topBorder">
            <td>VIN</td>
            <td>
              <div class=" chassis">{{item.reconcilingItemVin | cph:'chassis':0 }}
              </div>
            </td>
          </tr>
          <tr class="topBorder">
            <td>Description</td>
            <td>{{item.reconcilingItemDesc}}</td>
          </tr>
          <tr class="topBorder">
            <td>Notes</td>
            <td>{{item.reconcilingItemComment}}</td>
          </tr>
        </tbody>
      </table>

      <!-- Vehicle is a duplicate -->
      <table [ngClass]="{'compact':showCompact}" *ngIf="isDuplicate()" class="explanationTable">
        <!-- Item is a duplicate -->
        <tbody>
          <tr>
            <td colspan="2">
              <div class="holdingNote holdingNoteGood">
                <fa-icon [icon]="icon.faCheckCircle"></fa-icon>
                <div *ngIf="!isStockItem()">Resolved - Is a Duplicate Scan</div>
                <div *ngIf="isStockItem()">Resolved - Is a Duplicate Stock Item</div>
              </div>
            </td>
          </tr>
          <ng-container *ngIf="isStockItem()">
            <tr>
              <td>
                Original StockItem Id
              </td>
              <td>
                #{{item.originalId}}
              </td>
            </tr>
            <tr class="topBorder">
              <td>
                Original Item StockType
              </td>
              <td>
                {{item.originalStockType}}
              </td>
            </tr>
            <tr class="topBorder">
              <td>
                Original Item Notes
              </td>
              <td>
                {{item.originalComment}}
              </td>
            </tr>
            <tr class="topBorder">
              <td>
                Original Item Reference
              </td>
              <td>
                {{item.originalReference}}
              </td>
            </tr>
          </ng-container>

          <ng-container *ngIf="!isStockItem()">
            <tr>
              <td>Original Scan Id</td>
              <td>{{item.originalId}}</td>
            </tr>
            <tr class="topBorder">
              <td>Original Location</td>
              <td>{{item.originalLocationDescription}}</td>
            </tr>
            <tr class="topBorder">
              <td>Original Scanned By</td>
              <td>{{item.originalScannedBy}}</td>
            </tr>
            <tr class="topBorder">
              <td>Original Scanned Date</td>
              <td>
                {{ item.originalScannedDate | cph:'day':0 }}
                &nbsp; {{ item.originalScannedDate | cph:'time':0 }}
              </td>
            </tr>

          </ng-container>

        </tbody>
      </table>


      <!--  Vehicle requires a resolution  -->
      <table [ngClass]="{'compact':showCompact}" *ngIf="vehicleIsAnIssue()" class="explanationTable">
        <tbody>
          <tr>
            <td colspan="2">

              <div *ngIf="!item.resolutionId" class="holdingNote holdingNoteBad">
                <fa-icon [icon]="icon.faMemoCircleInfo"></fa-icon>
                <div >Resolution Required</div>
              </div>

              <div *ngIf="item.resolutionId  && !item.isResolved" class="holdingNote holdingNoteMeh">
                <fa-icon [icon]="icon.faMemoCircleInfo"></fa-icon>
                <div >Resolution in Progress</div>
              </div>

              <div *ngIf="item.resolutionId && item.isResolved" class="holdingNote holdingNoteGood">
                <fa-icon [icon]="icon.faMemoCircleInfo"></fa-icon>
                <div >Resolved - {{item.resolutionTypeDescription}}</div>
              </div>

            </td>
          </tr>

          <tr>
            <td>Resolution</td>
            <td *ngIf="showCompact">
              {{ !item.resolutionTypeDescription ? 'No resolution' :
                  item.resolutionTypeDescription }}
            </td>
            <td *ngIf="!showCompact">
              <div ngbDropdown container="body" placement="bottom-right" class="d-inline-block">
                <button class="btn btn-primary" ngbDropdownToggle [disabled]="selections.stockCheck.statusId > 3">
                  {{ !item.resolutionTypeDescription ? 'Choose resolution' :
                  item.resolutionTypeDescription }}
                </button>
                <div ngbDropdownMenu class="dropdown-menu-left" aria-labelledby="dropdownBasic1">
                  <button *ngFor="let resolutionTypes of getResolutionTypes()" ngbDropdownItem
                    (click)="chooseResolution(resolutionTypes)">
                    {{ resolutionTypes.description }}
                  </button>
                </div>
              </div>
            </td>
          </tr>
          <tr *ngIf="showBackupRow() ">
            <td>
              Backup required
            </td>
            <td>
              <div class="infoPanel">
                {{ item.resolutionTypeBackup }}
              </div>
            </td>
          </tr>
          <tr class="topBorder">
            <td>Resolved By</td>
            <td>
              {{item.resolvedBy}}
            </td>
          </tr>
          <tr class="topBorder">
            <td>Resolution Date</td>
            <td>
              {{ item.resolutionDate | cph:'shortDate':0 }}
              {{ item.resolutionDate | cph:'shortTime':0 }}
            </td>
          </tr>
          <tr class="topBorder">
            <td>Resolution Detail</td>
            <td>
              <span *ngIf="showCompact">{{item.resolutionNotes}}</span>
              <textarea *ngIf="!showCompact" [(ngModel)]="item.resolutionNotes" placeholder="Add notes here" class="notes subtleBoxShadow" [readonly]="selections.stockCheck.statusId > 3">
                  </textarea>
            </td>
          </tr>
          <tr class="topBorder">
            <td>Is Now Resolved?</td>
            <td>
              <button class="custom-checkbox" [ngClass]="{ 'checked': item.isResolved }"
                (click)="item.isResolved = !item.isResolved" [disabled]="!canResolve()">
                <fa-icon *ngIf="item.isResolved" [icon]="icon.faCheck"></fa-icon>
              </button>
              <span *ngIf="!canResolve()" class="text-danger">
                * Backup image required to mark as resolved
              </span>
            </td>
          </tr>
          <tr *ngIf="!showCompact" class="topBorder">
            <td>Resolution Backup Files</td>
            <td>
              <span *ngIf="!item.resolutionImages  || item.resolutionImages.length === 0">
                You currently have no backup files.
              </span>
              <p *ngIf="item.resolutionImages && item.resolutionImages.length > 0" class="text-info">
                * Clicking an image will zoom in. Clicking a document will download the file.
              </p>
              <div id="resolutionImageHolder" *ngIf="item.resolutionImages && item.resolutionImages.length!==0">
                <div *ngFor="let image of item.resolutionImages; index as i" class="backupAndButton">
                  <img *ngIf="image.url && image.status !== 'DELETE'" [id]="'backup-image-'+i" (click)="zoomInBackup(image)" 
                    [src]="image.url" class="animated backupImage fadeIn" (error)="updateUrl(i, image.fileName)">
                  <button *ngIf="image.status !== 'DELETE' && selections.stockCheck.statusId <= 3" (click)="deleteMissingResolutionImage(image)"
                    class="btn btn-danger">&times;</button>
                </div>
              </div>
            </td>
          </tr>
          <tr *ngIf="!showCompact && selections.stockCheck.statusId <= 3" class="topBorder">
            <td colspan="2">
              <div id="backupContainer">
                <!-- Drag drop area -->
                <div id="fileDropArea" appDragAndDrop contenteditable="true" (filesDropped)="onFileDropped($event)"
                (paste)="onFilePaste($event)">
                <div id="instructionPopoverHolder">
                  <instructionRowPopoverStyle *ngIf="thereAreFiles()"  [fullMessage]="instructionRowMessage()">
                  </instructionRowPopoverStyle>

                </div>
                <instructionRow *ngIf="!thereAreFiles()" [message]="instructionRowMessage()"></instructionRow>
                   
            

                  <div class="uploadFileWrapper mt-2">
                    <ng-container >
                      <input #fileDropRef id="file" type="file" multiple="multiple" class="chooseFileInput"
                        (click)="$event.target.value = null" (change)="fileBrowseHandler($event)" />
                      <label for="file">
                        <fa-icon [icon]="icon.faUpload"></fa-icon>Choose <span *ngIf="thereAreFiles()">Additional</span> File(s)
                      </label>
                    </ng-container>
                    <!-- <ng-container *ngIf="filesChosen">
                      <span class="fileName">File(s): {{ fileName }}</span>
                      <button class="btn btn-primary" (click)="fileChosen = false">Choose Different File(s)</button>
                    </ng-container> -->
                  </div>
                </div>

                <!-- Files uploaded, but not yet saved -->
                <span *ngIf="fileSizeExceeded" class="text-danger">
                  1 or more files exceeded the 10mb size limit and were not uploaded.
                </span>
                <div *ngIf="files.length" id="filesList">
                  <div *ngFor="let file of files; let i = index" class="singleFile">
                    <div class="d-flex align-items-center">
                      <img class="fileThumbnail" [src]="getThumbnail(file)">
                      <div class="info">
                        <span class="name">{{ file.name }}</span>
                        <br>
                        <span class="size">{{ formatBytes(file?.size) }}</span>
                      </div>
                    </div>
                    <fa-icon [icon]="icon.faTrash" width="20px" alt="delete" class="deleteIcon"
                      (click)="deleteFile(i)"></fa-icon>
                  </div>
                </div>
              </div>
            </td>
          </tr>


          <!-- Buttons for editing missing resolution -->
          <!-- <tr *ngIf="vehicleIsAnIssue() && !showCompact && selections.stockCheck.statusId <= 3">
            <td colspan="2">

              <div class="okCancelButtons">
                <button *ngIf="item.resolutionId" class="btn btn-danger" (click)="maybeDeleteResolution()">
                  Delete resolution (!)
                </button>
                <button *ngIf="checkForChangesMade()" class="btn btn-success" (click)="saveResolution()">
                  Save changes
                </button>
                <button *ngIf="checkForChangesMade()" class="btn btn-primary" (click)="cancelEditProblemCarUpdate()">
                  Cancel
                </button>
              </div>
            </td>
          </tr> -->
        </tbody>

      </table>
    </div>

    <!-- Buttons for editing missing resolution -->
    <div *ngIf="vehicleIsAnIssue() && !showCompact && (checkForChangesMade() || item.resolutionId)" class="cph-card-footer resolutionButtons">
      <div class="okCancelButtons">
        <button *ngIf="item.resolutionId" class="btn btn-danger" (click)="maybeDeleteResolution()">
          Delete resolution (!)
        </button>
        <button *ngIf="checkForChangesMade()" class="btn btn-success" (click)="saveResolution()">
          Save changes
        </button>
        <button *ngIf="checkForChangesMade()" class="btn btn-primary" (click)="cancelEditProblemCarUpdate()">
          Cancel
        </button>
      </div>
    </div>
  </div>

</div>

<!-- Change location modal -->
<ng-template #changeLocationModal let-modal>
  <div class="modal-header">
    <h4 class="modal-title" id="modal-basic-title">
      Update Location
    </h4>
    <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body newLocationChoice">
    <button *ngFor="let location of constants.Locations" class="btn btn-primary"
      [ngClass]="{ 'active': location.description === chosenLocation }" (click)="chooseLocation(location) ">
      {{ location.description }}
    </button>
  </div>
  <div class="modal-footer">
    <button type="button" class="btn btn-primary" (click)="modal.close('Ok')">OK</button>
    <button type="button" class="btn btn-primary" (click)="modal.dismiss('Cancelled')">Cancel</button>
  </div>
</ng-template>

<!-- Enlarged image -->
<div *ngIf="modalImage" class="enlarged-image-overlay" (click)="modalImage = null">
  <div
    class="image-backdrop"
    [ngStyle]="{ 'background-image': 'linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), url(' + modalImage + ')' }">
  </div>
  <div class="image-container">
    <button class="image-close" (click)="modalImage = null">&times;</button>
    <img [src]="modalImage" alt="Stock check scan">
  </div>
</div>