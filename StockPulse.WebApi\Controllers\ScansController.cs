﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using StockPulse.WebApi.ViewModel;
using StockPulse.WebApi.Service;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using static StockPulse.WebApi.Attribute.UserLevelAccessAttribute;
using StockPulse.WebApi.Attribute;
using System;

namespace StockPulse.WebApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]

    [UserLevelAccess(new[] { UserRole.SysAdministrator, UserRole.Reconciler, UserRole.Resolver, UserRole.Scanner, UserRole.Approver, UserRole.GeneralManager, UserRole.ReadOnly, UserRole.ScanAndPrint, UserRole.ScanAndView })]
    public class ScansController : ControllerBase, IAttributeValueProvider
    {

        private readonly IScanService scanService;
        private readonly int userId;
        private readonly string userRole;

        public UserRole attributeUserRole => GetUserRole();
        private UserRole GetUserRole()
        {
            return (UserRole)Enum.Parse(typeof(UserRole), userRole);
        }

        public ScansController(IScanService scanService, IUserService userService)
        {
            this.scanService = scanService;
            userId = userService.GetUserId();
            userRole = userService.GetUserRole();
        }

        [HttpGet]
        [Route("GetScans")]
        public async Task<IEnumerable<ViewModel.Scan>> GetScans(int stockcheckId)
        {
            return await scanService.GetScans(stockcheckId, userId);
        }

       


        [HttpGet]
        [Route("CheckIfRegSeenBefore")]
        public async Task<ScanSeenBefore> CheckIfRegSeenBefore(string reg, int stockCheckId)
        {
            return await scanService.CheckIfRegSeenBefore(reg, stockCheckId, userId);
        }

        [HttpGet]
        [Route("CheckIfVinSeenBefore")]
        public async Task<ScanSeenBefore> CheckIfVinSeenBefore(string vin, int stockCheckId)
        {
            return await scanService.CheckIfVinSeenBefore(vin, stockCheckId, userId);
        }




        [HttpGet]
        [Route("GetScansWithResolution")]
        public async Task<IEnumerable<ScanWithResolution>> GetScansWithResolution(int stockCheckId)
        {
            return await scanService.GetScansWithResolution(stockCheckId, userId);
        }

      

        [HttpGet]
        [Route("GetScanRegDifference")]
        public async Task<IEnumerable<ScanRegDifference>> GetScanRegDifference(int stockCheckId)
        {
            return await scanService.GetScanRegDifference(stockCheckId, userId);
        }


        [HttpGet]
        [Route("UpdateLocation")]
        public async Task UpdateLocation(int newLocationId, int scanId)
        {
            await scanService.UpdateLocation(newLocationId, scanId, userId);
        }


        [HttpGet]
        [Route("GetRepeatUnknowns")]
        public async Task<List<ScanWithResolution>> GetRepeatUnknowns(string stockcheckIds)
        {
            List<int> stockCheckIds = stockcheckIds.Split(',').Select(int.Parse).ToList();
            return await scanService.GetRepeatUnknowns(stockCheckIds, userId);
        }
               

        [HttpGet]
        [Route("UpdateScanReg")]
        public async Task UpdateScanReg(int scanId, string reg, bool isMobileApp)
        {
            await scanService.UpdateScanReg(scanId, reg, userId,isMobileApp);
        }

        [HttpGet]
        [Route("UpdateScanVin")]
        public async Task UpdateScanVin(int scanId, string vin, bool isMobileApp)
        {
            await scanService.UpdateScanVin(scanId, vin, userId, isMobileApp);
        }

        [HttpGet]
        [Route("UpdateScanNote")]
        public async Task UpdateScanNote(int scanId, string note)
        {
            await scanService.UpdateScanNote(scanId, note, userId);
        }

       

        [HttpPost]
        [Route("SaveUnknownResolution")]
        [UserLevelAccess(new[] { UserRole.SysAdministrator, UserRole.Reconciler, UserRole.Resolver, UserRole.Approver })]
        public async Task SaveUnknownResolution(Resolution resolution)
        {
            await scanService.SaveUnknownResolution(resolution, userId);
        }

        [HttpPost]
        [Route("DeleteResolution")]
        [UserLevelAccess(new[] { UserRole.SysAdministrator, UserRole.Reconciler, UserRole.Resolver, UserRole.Approver })]
        public async Task DeleteResolution (ResolutionDeleteParams parms)
        {
            await scanService.DeleteUnknownResolution(parms.ResolutionId, parms.StockCheckId, userId);
        }

        // POST: api/Scans/Upload
        [HttpPost]
        [Route("Upload")]
        public async Task<List<ScanUploadConfirmation>> UploadScans(List<ScanUpload> scanWithImageStringsVMs)
        {
            return await scanService.UploadScans(scanWithImageStringsVMs, userId, scanWithImageStringsVMs.First().StockCheckId);
        }

      
        [HttpDelete]
        [Route("Delete")]
        public async Task DeleteScan(int scanId)
        {
            await scanService.DeleteScan(scanId, userId);
        }

    }
}
