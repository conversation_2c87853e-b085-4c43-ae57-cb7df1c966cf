﻿using StockPulse.WebApi.DataAccess;
using System.Threading.Tasks;

namespace StockPulse.WebApi.Service
{
    public interface IErrorReportService
    {
        Task SaveErrorReport(string errors, int userId);
    }

    public class ErrorReportService: IErrorReportService
    {
        //properties of the service
        private readonly IErrorReportDataAccess errorReportDataAccess ;

        //constructor
        public ErrorReportService(IErrorReportDataAccess errorReportDataAccess)
        {
            this.errorReportDataAccess = errorReportDataAccess;
        }



        public async Task SaveErrorReport(string errors, int userId)
        {
            await errorReportDataAccess.SaveErrorReport(errors, userId);
        }


    }
}
