﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace StockPulse.WebApi.Migrations
{
    /// <inheritdoc />
    public partial class addbackinFKsforimportStockItems : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateIndex(
                name: "IX_StockItems_SiteId",
                schema: "import",
                table: "StockItems",
                column: "SiteId");

            migrationBuilder.CreateIndex(
                name: "IX_StockItems_SourceReportId",
                schema: "import",
                table: "StockItems",
                column: "SourceReportId");

            migrationBuilder.AddForeignKey(
                name: "FK_StockItems_SourceReports_SourceReportId",
                schema: "import",
                table: "StockItems",
                column: "SourceReportId",
                principalSchema: "dbo",
                principalTable: "SourceReports",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_StockItems_Sites_SiteId",
                schema: "import",
                table: "StockItems",
                column: "SiteId",
                principalSchema: "dbo",
                principalTable: "Sites",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_StockItems_SourceReports_SourceReportId",
                schema: "import",
                table: "StockItems");

            migrationBuilder.DropForeignKey(
                name: "FK_StockItems_Sites_SiteId",
                schema: "import",
                table: "StockItems");

            migrationBuilder.DropIndex(
                name: "IX_StockItems_SiteId",
                schema: "import",
                table: "StockItems");

            migrationBuilder.DropIndex(
                name: "IX_StockItems_SourceReportId",
                schema: "import",
                table: "StockItems");
        }
    }
}
