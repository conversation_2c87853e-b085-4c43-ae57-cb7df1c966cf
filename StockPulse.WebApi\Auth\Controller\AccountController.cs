﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.IdentityModel.Tokens;
using StockPulse.WebApi.Auth.Model;
using System;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using StockPulse.WebApi.Auth.Service;
using Microsoft.AspNetCore.Authorization;
using StockPulse.WebApi.Service;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using StockPulse.WebApi.DataAccess;

namespace StockPulse.WebApi.Auth.Controller
{

    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class AccountController : ControllerBase
    {
        private readonly SignInManager<ApplicationUser> signInManager;
        private readonly UserManager<ApplicationUser> userManager;
        private readonly IRefreshTokenService refreshTokenService;
        private readonly IEmailSenderCustom emailSender;
        private readonly IUserService userService;
        private readonly IConfiguration config;
        private readonly string configSectionName = "WebApp";
        private readonly string configURL = "URL";
        private readonly IHttpContextAccessor httpContextAccessor;
        private readonly IGlobalParamService globalParamService;

        public AccountController(UserManager<ApplicationUser> userManager, IEmailSenderCustom emailSender, IRefreshTokenService refreshTokenService, IUserService userService, IConfiguration config, SignInManager<ApplicationUser> signInManager, IHttpContextAccessor httpContextAccessor, IGlobalParamService globalParamService)
        {
            this.userManager = userManager;
            this.emailSender = emailSender;
            this.refreshTokenService = refreshTokenService;
            this.userService = userService;
            this.config = config;
            this.signInManager = signInManager;
            this.httpContextAccessor = httpContextAccessor;
            this.globalParamService = globalParamService;
        }

        
        [HttpPost]
        [Route("Login")]
        [AllowAnonymous]
        //POST: /api/Account/Login
        public async Task<IActionResult> Login(LoginModel model)
        {
            try
            {
                model.UserName.Trim();

                string aspNetUserId = null;
                if (string.IsNullOrEmpty(model.DealerGroupName))
                {
                    aspNetUserId = await this.userService.GetAspnetUserIdFromCache(model.UserName);
                }
                else
                {
                    aspNetUserId = await this.userService.GetAspNetUserId(model.UserName, model.DealerGroupName);
                }
                if (string.IsNullOrEmpty(aspNetUserId))
                {
                    //could not find this username for this dealerGroupId
                    return incorrectUserOrPass();
                }

                var user = await userManager.FindByIdAsync(aspNetUserId);

                if (user != null)
                {
                    var isSSOEnabled = await IsSSOEnabledForDealergroup(model.UserName);

                    if (isSSOEnabled)
                    {
                        return Ok(new { error = "Please click 'Sign in with Microsoft' to log in" });
                    }

                    var loginCheck = await signInManager.CheckPasswordSignInAsync(user, model.Password, true);

                    if (loginCheck.Succeeded)
                    {
                        var usersRoles = await userManager.GetRolesAsync(user);
                        var usersRole = usersRoles.FirstOrDefault();
                        var roles = string.Join(",", usersRoles);

                        string usersName = string.Empty;
                        if (user.LinkedPersonId.HasValue)
                        {
                            usersName = user.UserName;// await userService.GetUsersName(user.Email);
                        }

                        string accessToken = GenerateAccessToken(user.Id, user.LinkedPersonId.ToString(), roles, usersName, user.Email);

                        UserRefreshToken userRefreshToken = refreshTokenService.GenerateRefreshToken(user);

                        await refreshTokenService.AddToken(userRefreshToken);

                        RefreshToken refreshToken = new RefreshToken()
                        {
                            Token = userRefreshToken.RefreshToken,
                            Expiration = userRefreshToken.ExpireAt // Verify - Make this configurable
                        };

                        setTokenCookie(refreshToken);

                        return Ok(new { accessToken, refreshToken, usersRole });
                    }
                    else
                    {
                        return incorrectUserOrPass();
                    }
                }
                else
                {
                    return incorrectUserOrPass();
                }
            }

            catch (Exception ex)
            {
                return incorrectUserOrPass();
            }
        }


        [HttpPost]
        [Route("Forgotpassword")]
        [AllowAnonymous]
        //POST: /api/Account/ForgotPassword
        public async Task<ActionResult> ForgotPassword(ForgotPasswordModel model)
        {
            model.Email = model.Email.Trim();

            //Look for username in the cache.
            var dealerGroupVMs = await this.userService.GetDealerGroupsForSpecificEmailAddress(model.Email);
            if (!dealerGroupVMs.Any())
            {
                return Ok();
            }

            string dealerGroupName = dealerGroupVMs.FirstOrDefault().Description;

            string aspNetUserId = await this.userService.GetAspNetUserId(model.Email, dealerGroupName);

            var user = await userManager.FindByIdAsync(aspNetUserId);
            if (user == null || !(await userManager.IsEmailConfirmedAsync(user)))
            {
                // Don't reveal that the user does not exist or is not confirmed
                //return View("ForgotPasswordConfirmation");
                return Ok(); //Verify
            }

            try
            {
                var isSSOEnabled = await IsSSOEnabledForDealergroup(model.Email);

                if (isSSOEnabled)
                {
                    return Ok(new { error = "Please click 'Sign in with Microsoft' to log in" });
                }

                // Send an email with this link
                string token = await userManager.GeneratePasswordResetTokenAsync(user);

                string WebURL = config[$"{ configSectionName }:{configURL}"];
                string Country = config[$"{configSectionName}:Country"];

                var callbackUrl = $"{WebURL}/resetpassword?token={token}&email={user.Email}&country={Country}";

                var userDealerGroup = await userService.GetDealerGroupIdDirectFromDb((int)user.LinkedPersonId);
                var globalParams = await globalParamService.GetAllParamsAsync(userDealerGroup);

                string message;

                if (globalParams.FirstOrDefault(x => x.DealerGroupId == userDealerGroup && x.Name == "isSSOEnabled").BoolValue == true)
                {
                    message = "Your organisation is configured to use single sign-on. Please log in using the ‘Sign in with Microsoft’ button.";
                } else
                {
                    message = "Please reset your password by clicking <a href=\"" + callbackUrl.ToString() + "\">here</a>";
                }

                await emailSender.SendEmailAsync(model.Email, "Reset Password", message);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message, ex);
            }
            return Ok();
        }

        [HttpPost]
        [Route("ResetPassword")]
        [AllowAnonymous]
        // POST: /api/Account/ResetPassword
        public async Task<ActionResult> ResetPassword(ResetPasswordModel model)
        {
            model.UserName = model.UserName.Trim();
            var dealerGroupVMs = await this.userService.GetDealerGroupsForSpecificEmailAddress(model.UserName);
            if (!dealerGroupVMs.Any())
            {
                return Ok();
            }

            string dealerGroupName = dealerGroupVMs.FirstOrDefault().Description;

            string aspNetUserId = await this.userService.GetAspNetUserId(model.UserName, dealerGroupName);
            var user = await userManager.FindByIdAsync(aspNetUserId);
            if (user == null)
            {
                // Don't reveal that the user does not exist
                //return RedirectToAction("ResetPasswordConfirmation", "Account");
                return Ok(); //Verify
            }

            var isSSOEnabled = await IsSSOEnabledForDealergroup(model.UserName);

            if (isSSOEnabled)
            {
                return Ok(new { error = "Please click 'Sign in with Microsoft' to log in" });
            }

            
            var isAccountDelete = user.LockoutEnd > DateTime.UtcNow.AddDays(999);
            if (isAccountDelete)
            {
                return Ok(new { error = "Account deleted." });
            }

            // Validate the token manually
            var isTokenValid = await userManager.VerifyUserTokenAsync(user,
                userManager.Options.Tokens.PasswordResetTokenProvider,
                "ResetPassword", model.Token);

            if (!isTokenValid)
            {
                return BadRequest("Invalid token.");
            }

            string passwordHash = userManager.PasswordHasher.HashPassword(user, model.Password);

            var resultCount = await ChangePasswordAcrossAllDealerGroups(passwordHash, user.Email);

            if (resultCount > 0)
            {
                return Ok();
            }
            else
            {
                return BadRequest("Invalid Token"); // Verify
            }

            var result = await userManager.ResetPasswordAsync(user, model.Token, model.Password);
            if (result.Succeeded)
            {
                await userManager.SetLockoutEndDateAsync(user, null);
                //return RedirectToAction("ResetPasswordConfirmation", "Account");
                return Ok();
            }
            else
            {
                return BadRequest("Invalid Token"); // Verify
            }
        }

        [HttpPost]
        [Route("RefreshToken")]
        [AllowAnonymous]
        public async Task<ActionResult> RefreshToken()
        {
            var refreshTokenCookie = Request.Headers["refresh_token"];
            var newUserRefreshToken = await refreshTokenService.RenewRefreshTokenAsync(refreshTokenCookie);

            if (newUserRefreshToken == null)
            {
                //return Unauthorized(new { message = "Invalid token" });
                return null;
            }

            var user = await userManager.FindByIdAsync(newUserRefreshToken.UserId);
            var userRoles = await userManager.GetRolesAsync(user);
            var roles = string.Join(",", userRoles);
            string usersName = string.Empty;
            if (user.LinkedPersonId.HasValue)
            {
                usersName = user.UserName;
            }

            string accessToken = GenerateAccessToken(newUserRefreshToken.UserId, newUserRefreshToken.LinkedPersonId, roles, usersName, user.Email);

            RefreshToken refreshToken = new RefreshToken()
            {
                Token = newUserRefreshToken.RefreshToken,
                Expiration = newUserRefreshToken.ExpireAt
            };

            setTokenCookie(refreshToken);

            return Ok(new { accessToken, refreshToken });

        }


        private string GenerateAccessToken(string userId, string linkedPersonId, string userRoles, string usersName, string email)
        {
            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(new Claim[]
                    {
                        new Claim("UserId", userId),
                        new Claim("LinkedPersonId",linkedPersonId),
                        new Claim("Roles", userRoles),
                        new Claim("UsersName", usersName),
                        new Claim("preferred_username", email)
                    }),
                Expires = DateTime.UtcNow.AddHours(12), //Verify the timeout
                SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(AuthConfiguration.GetTokenKey()), SecurityAlgorithms.HmacSha256Signature),

            };
            var tokenHandler = new JwtSecurityTokenHandler();
            var securityToken = tokenHandler.CreateToken(tokenDescriptor);
            return tokenHandler.WriteToken(securityToken);
        }

        private void setTokenCookie(RefreshToken refreshToken)
        {
            var cookieOptions = new CookieOptions
            {
                HttpOnly = true,
                Expires = refreshToken.Expiration,
                //Path = "/api/Account/RefreshToken",
                Secure = true,
                SameSite = SameSiteMode.Lax
                //Domain = httpContextAccessor.HttpContext.Request.Host.Host
            };
            Response.Cookies.Append("refreshToken", refreshToken.Token, cookieOptions);
        }

        private async Task<bool> IsSSOEnabledForDealergroup(string userName)
        {
            var userDealerGroupId = await userService.GetDealerGroupIdFromCache(userName);
            var isSSOenabled = await globalParamService.IsSSOEnabledFromCacheAsync(userDealerGroupId);
            return isSSOenabled;
        }

        private OkObjectResult incorrectUserOrPass()
        {
            //text is hard coded in mobile app, can be change once newer version of mobile app is released
            return Ok(new { error = "Incorrect username or password." });
        }

        private async Task<int> ChangePasswordAcrossAllDealerGroups(string passwordHash, string email)
        {
            return await userService.UpdateUserPasswordAcrossAllDealerGroups(passwordHash, email);
        }
    }
}
