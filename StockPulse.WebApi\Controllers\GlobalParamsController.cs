﻿using Microsoft.AspNetCore.Mvc;
using static StockPulse.WebApi.Attribute.UserLevelAccessAttribute;
using StockPulse.WebApi.Attribute;
using StockPulse.WebApi.Service;
using Microsoft.AspNetCore.Authorization;
using System;
using System.Threading.Tasks;

namespace StockPulse.WebApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    [UserLevelAccess(new[] { UserRole.SysAdministrator })]
    public class GlobalParamsController : ControllerBase, IAttributeValueProvider
    {
        private readonly IUserService userService;
        private readonly IGlobalParamService globalParamService;
        private readonly string userRole;

        public UserRole attributeUserRole => GetUserRole();
        private UserRole GetUserRole()
        {
            return (UserRole)Enum.Parse(typeof(UserRole), userRole);
        }
        public GlobalParamsController(
            IUserService userService,
            IGlobalParamService globalParamService)
        {
            this.userService = userService;
            this.globalParamService = globalParamService;
            userRole = userService.GetUserRole();
        }

        [HttpGet]
        [Route("ReloadCache")]
        [UserLevelAccess(new[] { UserRole.SysAdministrator })]
        public async Task ReLoadGlobalParamsCache()
        {
            await globalParamService.ReLoadGlobalParamsCache();
        }
    }
}
