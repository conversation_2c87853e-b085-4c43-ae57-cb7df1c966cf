.dropdown-menu {
    max-height: 50vh;
    overflow-y: auto;
    overflow-x: hidden;
    text-align: center;
    box-shadow: 5px 5px 10px var(--shadowColour);
    z-index: 10;
    background-color: var(--primaryLight) !important;

    .dropdown-item {
        text-align: left;
        color: var(--bodyColour) !important;
    }

    .dropdown-item:active,
    .dropdown-item.active {
        background-color: var(--secondary) !important;
        color: #000000 !important;
    }

    .dropdown-item:not(.disabled):hover {
        background-color: var(--secondary);
        color: #000000 !important;
    }

    .btn {
        margin: 0.0em auto;
    }
}


ngb-datepicker.dropdown-menu {
    background-color: #FFFFFF !important;
}

.dropdown {
    input {
        margin: 1em;
        background-color: transparent;
        border: 1px solid #FFFFFF;
        color: #FFFFFF;
        width: calc(100% - 2em);

        &::placeholder {
            color: #FFFFFF;
        }
    }

    .dropdown-item {
        padding: 0.25rem 1em;
    }
}