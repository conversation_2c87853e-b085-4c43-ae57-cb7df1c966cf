import { Component, ViewChild, ElementRef } from '@angular/core';
import { ILoadingOverlayAngularComp } from "ag-grid-angular";
import { IconService } from '../services/icon.service';

@Component({
    selector: 'customHeader',
    template: `
        <div class="" id="holder" >
            <div   *ngIf="sortType=='desc'" class="customSort customSortDownLabel">
            <fa-icon [icon]="icon.faLongArrowAltDown"></fa-icon>
            </div> 
            <div  *ngIf="sortType=='asc'"  class=" customSort customSortUpLabel">
            <fa-icon [icon]="icon.faLongArrowAltUp"></fa-icon>
            </div> 
            <div  *ngIf="params.column.filterActive"  class=" customFilter customSortUpLabel">
            <fa-icon [icon]="icon.faFilter"></fa-icon>
            </div> 
            <div [ngClass]="{'filtered':params.column.filterActive, 'centre': headerAlignment === 'agAlignCentre'}" (click)="onSortRequested()"  placement="left" container="body"    class="customHeaderLabel">{{params.displayName}}</div> 
           

        </div>




    `,
    styles: [
        `
        #holder{position:relative;display:flex;width:100%;}
        .customSort{transition: ease all 0.2s;position:absolute;right:0px;margin-top:-1px;}
        .customFilter{position:absolute;right:2px;}
        svg{color:var(--secondary);}
        .customHeaderLabel{padding-left: 0px;padding-right:0px;width:100%;text-align:left;transition: ease all 0.1s;overflow:hidden;white-space: break-spaces;}
        .customHeaderLabel.filterActive{font-weight:700;color:var(--secondaryDark);padding-right: 15px}
        #holder:hover .customHeaderLabel{color: var(--secondaryDark)}
        .customHeaderLabel.filtered{font-weight:700;color:var(--secondaryDark)}
    
        .customHeaderMenuButton{display:none;transition:ease all 0.2s;color:var(--secondary)}
        #holder:hover .customHeaderMenuButton{display:block;position:absolute;right:3px;}

        .customHeaderLabel.centre {
            width: 100%;
            text-align: center;
        }        
    `
    ]
})
export class CustomHeaderComponent {
    public params: any;
    public hasTooltip: boolean;

    headerAlignment: string;

    constructor(
        public icon: IconService
    ){}
    @ViewChild('menuButton', { static: true, read: ElementRef }) public menuButton;
    sortType: string;

    agInit(params): void {
        this.params = params;

        this.headerAlignment = this.params.column.colDef.cellClass;

        if (this.params.column.userProvidedColDef.headerTooltipCustom) { this.hasTooltip = true; }
        params.column.addEventListener('sortChanged', this.onSortChanged.bind(this));
       


    }

    onMenuClicked() {
        this.params.showColumnMenu(this.menuButton.nativeElement);
    };

    onSortRequested() {
        if (!this.params.column.isSortAscending() && !this.params.column.isSortDescending()) {
            this.params.setSort('desc')
        } else if (this.params.column.isSortDescending()) {
            this.params.setSort('asc')
        } else {
            this.params.setSort('')
        }
    }

    onSortChanged() {
        if (this.params.column.isSortAscending()) {
            this.sortType = 'asc';
        } else if (this.params.column.isSortDescending()) {
            this.sortType = 'desc'
        } else {
            this.sortType = '';
        }



    }

}
