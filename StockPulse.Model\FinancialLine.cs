﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using StockPulse.Model.Import;

namespace StockPulse.Model
{
    public class FinancialLine
    {
        [Key]
        public int Id { get; set; }
        public string Code { get; set; }
        public string AccountDescription { get; set; }


        public string Notes { get; set; }
        public bool IsExplanation { get; set; }
        public decimal Balance { get; set; }
        public int StockCheckId { get; set; }

        [ForeignKey("StockCheckId")]
        public virtual StockCheck StockChecks { get; set; }


        public int? FileImportId { get; set; }
        [ForeignKey("FileImportId")]
        public virtual FileImport FileImport { get; set; }
    }


}