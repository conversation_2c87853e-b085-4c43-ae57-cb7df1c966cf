<div id="gridHolder">
  <button class="btn floatTopRightOfGrid" (click)="generateExcelSheet()">
    <img style="width:2em;" [src]="logo.provideExcelLogo()">
  </button>

  <ag-grid-angular class="ag-theme-balham" [gridOptions]="mainTableGridOptions"
    (gridReady)="onGridReady($event)" [animateRows]="true" [ngStyle]="{ 'opacity': showGrid ? 1 : 0 }"
    [domLayout]="domLayout" [ngClass]="{ 'h-100': domLayout === 'normal' }">
  </ag-grid-angular>
</div>