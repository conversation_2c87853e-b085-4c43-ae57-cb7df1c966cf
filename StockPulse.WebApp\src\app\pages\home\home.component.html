<div *ngIf="constantsService.BackgroundImageURL" class="content-new" [ngStyle]="{ 'background-image': constantsService.BackgroundImageURL }">
    
    <div class="menuContainer" *ngIf="constantsService.Sites">
        <h1>Hello {{ getUserFirstName() }}. Welcome to StockPulse.</h1>
        
        <!-- If no stockcheck loaded -->
        <div *ngIf="!selectionsService.stockCheck" class="menu">
            <div class="columnAndHeaderContainer">
                <span class="listHeader h2Thin">Reconciliation</span>
                <div class="column">
                    <ul>
                        <li class="link h3Thin" (click)="goTo('stockChecks')">Load Stock Check</li>
                    </ul>
                </div>
            </div>
            <div class="columnAndHeaderContainer">
                <span class="listHeader h2Thin">Reports</span>
                <div class="column">
                </div>
            </div>
            <div class="columnAndHeaderContainer">
                <span class="listHeader h2Thin">Admin</span>
                <div class="column">
                </div>
            </div>
        </div>
        

        <!-- If stockcheck loaded -->
        <div *ngIf="selectionsService.stockCheck" class="menu">
            <div class="columnAndHeaderContainer">
                <span class="listHeader h2Thin">Reconciliation</span>
                <div class="column">
                    <ul>
                        <li class="link h3Thin" (click)="goTo('stockChecks')">Load Stock Check</li>
                        <li class="link h3Thin" [ngClass]="{ 'disabled': !selectionsService.stockCheck }"(click)="goTo('loadItems')">Import Data</li>
                        <li class="link h3Thin" [ngClass]="{ 'disabled': !selectionsService.stockCheck }" (click)="goTo('reconcile')">Reconcile</li>
                        <li class="link h3Thin" [ngClass]="{ 'disabled': !selectionsService.stockCheck }" (click)="goTo('signoff')">Sign-off</li>
                    </ul>
                </div>
            </div>
            <div class="columnAndHeaderContainer">
                <span class="listHeader h2Thin">Reports</span>
                <div class="column">
                    <ul>
                        <li class="link h3Thin" [ngClass]="{ 'disabled': !selectionsService.stockCheck }" (click)="goTo('photoMap')">Photo Map</li>
                        <li *ngIf="showRegVinMismatch()" class="link h3Thin" [ngClass]="{ 'disabled': !selectionsService.stockCheck }" (click)="goTo('vehiclesWithWrongReg')">Reg / VIN Mismatches</li>
                        <li class="link h3Thin" [ngClass]="{ 'disabled': !selectionsService.stockCheck }" (click)="goTo('repeatOffenders')">Recurring Issues</li>
                        <li class="link h3Thin" (click)="goTo('vehicleSearch')">Vehicle Search</li>
                    </ul>
                </div>
            </div>
            <div class="columnAndHeaderContainer">
                <span class="listHeader h2Thin">Admin</span>
                <div class="column">
                    <ul>
                        <li *ngIf="constantsService.showLabelPrintFeature" class="link h3Thin" (click)="goTo('labelPrinter')">Print Labels</li>
                        <li class="link h3Thin" (click)="goTo('userMaintenance')">User Maintenance</li>
                        <li class="link h3Thin" *ngIf="showTableMaintenance()" (click)="goTo('tableMaintenance')">Table Maintenance</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

