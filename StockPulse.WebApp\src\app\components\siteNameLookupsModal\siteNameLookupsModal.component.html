<div class="modal-header">
    <h4 class="modal-title" id="modal-basic-title">Manage Site Name Lookups</h4>
    <button type="button" class="close" aria-label="Close" (click)="close()">
        <span aria-hidden="true">&times;</span>
    </button>
</div>
<div class="modal-body alertModalBody lowHeight">
    <instructionRow [message]="guidanceNote"></instructionRow>
    <ag-grid-angular *ngIf="gridOptions" class="ag-theme-balham" [gridOptions]="gridOptions"></ag-grid-angular>
</div>
<div class="modal-footer">
    <button *ngIf="!service.editing" type="button" class="btn btn-success" (click)="edit()">Edit</button>
    <button *ngIf="service.editing" type="button" class="btn btn-success" (click)="add()">Add</button>
    <button *ngIf="service.editing && service.changes.length > 0" type="button" class="btn btn-success" (click)="maybeSave()">Save</button>
    <button *ngIf="service.editing" type="button" class="btn btn-danger" (click)="cancel()">Cancel</button>
    <button type="button" class="btn btn-primary" (click)="close()">Close</button>
</div>