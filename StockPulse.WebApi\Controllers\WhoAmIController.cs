﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using StockPulse.WebApi.ViewModel;
using StockPulse.WebApi.Service;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using StockPulse.WebApi.DataAccess;
using StockPulse.WebApi.Attribute;
using System.Reflection;
using static StockPulse.WebApi.Attribute.UserLevelAccessAttribute;
using System;
using System.Text.RegularExpressions;
using StockPulse.Model;

namespace StockPulse.WebApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    [UserLevelAccess(new[] { UserRole.SysAdministrator, UserRole.Reconciler, UserRole.Resolver, UserRole.Scanner, UserRole.Approver, UserRole.GeneralManager, UserRole.ReadOnly, UserRole.ScanAndPrint, UserRole.ScanAndView, UserRole.None })]
    public class WhoAmIController : ControllerBase, IAttributeValueProvider
    {
        private readonly ISiteService siteService;
        private readonly IUserService userService;
        private readonly IGlobalParamService globalParamService;
        private readonly string userRole;

        public UserRole attributeUserRole => GetUserRole();
        private UserRole GetUserRole()
        {
            return (UserRole)Enum.Parse(typeof(UserRole), userRole);
        }
        public WhoAmIController(ISiteService siteService, IUserService userService, IGlobalParamService globalParamService)
        {
            this.siteService = siteService;
            this.userService = userService;
            userRole = UserRole.None.ToString();
            this.globalParamService = globalParamService;

        }

        [HttpGet]
        public async Task<IActionResult> Get()
        {
            try
            {
                string userEmail = userService.GetUserEmailAddressFromToken();
                string usersRole = userService.GetUserRole();
                int userId = userService.GetUserId();
                string usersName = await userService.GetUsersName();
                string homeSiteName = await userService.GetUserDefaultSiteName();
                string userSiteIds = await userService.GetAllSiteIdsForUser(userId);
                string userUsername = await userService.GetUserUsername();
                IEnumerable<DealerGroupVM> dealerGroups = new List<DealerGroupVM>();

                int userDealerGroupId = await userService.GetDealerGroupIdFromCache();
                if (usersRole == "SysAdministrator")
                {
                    dealerGroups = await userService.GetAllDealerGroups();
                    dealerGroups.First(d => d.Id == userDealerGroupId).IsSelected = true;
                }

                var globalParams = await globalParamService.GetAllParamsAsync(userDealerGroupId);
                var resolutionTypes = await globalParamService.GetResolutionTypes(userId);
                IEnumerable<UserPreference> userPreferences = await userService.GetUserPreferences(userId);

                return Ok(new WhoAmIResult()
                {
                    Role = usersRole,
                    UserId = userId,
                    UsersName = usersName,
                    UserHomeSiteName = homeSiteName,
                    UserSiteIds = userSiteIds,
                    DealerGroupsForSysAdmin = dealerGroups,
                    GlobalParams = globalParams,
                    ResolutionTypes = resolutionTypes,
                    UserUsername = userUsername,
                    UserPreferences = userPreferences
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, string.Empty);
            }
        }


    }

    //private void tester()
    //{
    //    string sample = "*0ABCDEFGHI-";
    //    string tested = Regex.Replace(sample, @"[^a-zA-Z0-9]", string.Empty);


    //    { }
    //}
}
