﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace StockPulse.WebApi.Migrations
{
    /// <inheritdoc />
    public partial class addFKtofileimport : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "FileImportId",
                schema: "import",
                table: "StockItem_Loads",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.CreateIndex(
                name: "IX_StockItem_Loads_FileImportId",
                schema: "import",
                table: "StockItem_Loads",
                column: "FileImportId");

            migrationBuilder.AddForeignKey(
                name: "FK_StockItem_Loads_FileImports_FileImportId",
                schema: "import",
                table: "StockItem_Loads",
                column: "FileImportId",
                principalSchema: "dbo",
                principalTable: "FileImports",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_StockItem_Loads_FileImports_FileImportId",
                schema: "import",
                table: "StockItem_Loads");

            migrationBuilder.DropIndex(
                name: "IX_StockItem_Loads_FileImportId",
                schema: "import",
                table: "StockItem_Loads");

            migrationBuilder.DropColumn(
                name: "FileImportId",
                schema: "import",
                table: "StockItem_Loads");
        }
    }
}
