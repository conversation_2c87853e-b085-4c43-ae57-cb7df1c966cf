﻿using System.Collections.Generic;

namespace StockPulse.WebApi.ViewModel
{
    public class RepeatUnknownOffender : ScanWithResolution
    {
        public RepeatUnknownOffender(ScanWithResolution x)
        {
            ScanId = x.ScanId;
            //UserId = x.UserId;
            //StockCheckId = x.StockCheckId;
            //LastEditedById = x.LastEditedById;
            LocationDescription = x.LocationDescription;
            //LocationId = x.LocationId;
            //UnknownResolutionId = x.UnknownResolutionId;
            //StockItemId = x.StockItemId;
            //ReconcilingItemId = x.ReconcilingItemId;
            //IsDuplicate = x.IsDuplicate;
            //LastEditedDateTime= x.LastEditedDateTime;
            //RegConfidence = x.RegConfidence;
            //IsEdited = x.IsEdited;
            //Longitude = x.Longitude;
            //Latitude = x.Latitude;
            ScanDateTime = x.ScanDateTime;
            ScanComment = x.ScanComment;
            ScanReg = x.ScanReg;
            ScanVin = x.ScanVin;
            ScanDescription = x.ScanDescription;
            //CoordinatesJSON = x.CoordinatesJSON;
            HasVinImage = x.HasVinImage;
            LocationDescription = x.LocationDescription;
            ScannerName = x.ScannerName;
            ResolutionTypeDescription = x.ResolutionTypeDescription;
            IsResolved = x.IsResolved;
            ResolvedBy = x.ResolvedBy;
            ResolutionNotes = x.ResolutionNotes;
            ResolutionId = x.ResolutionId;
            ResolutionImageIds = x.ResolutionImageIds;
            previousStockCheckUnknownInstances = new List<ScanWithResolution>();
            StockCheckDate = x.StockCheckDate;
        }
        public List<ScanWithResolution> previousStockCheckUnknownInstances { get; set; }
    }
}
