import { Component, Input, OnInit, SimpleChanges } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ImageToUpdate } from 'src/app/model/ImageToUpdate';
import { ItemFullDetail } from 'src/app/model/ItemFullDetail';
import { ReconciliationState } from 'src/app/model/ReconciliationState';
import { Resolution } from 'src/app/model/Resolution';
import { ResolutionDeleteParams } from 'src/app/model/ResolutionDeleteParams';
import { ResolutionType } from 'src/app/model/ResolutionType';
import { ApiAccessService } from 'src/app/services/apiAccess.service';
import { ConstantsService } from 'src/app/services/constants.service';
import { IconService } from 'src/app/services/icon.service';
import { ToastService } from 'src/app/services/newToast.service';
import { SelectionsService } from 'src/app/services/selections.service';
import * as confetti from 'canvas-confetti';
import { StockChecksService } from 'src/app/pages/stockChecks/stockChecks.service';

export interface FileWithBase64 extends File {
  base64: string;
}

@Component({
  selector: 'app-explanation',
  templateUrl: './explanation.component.html',
  styleUrls: ['./explanation.component.scss']
})
export class ExplanationComponent implements OnInit {
  @Input() item: ItemFullDetail;
  @Input() showCompact: boolean;

  originalItem: ItemFullDetail;
  imagesURLs: ImageToUpdate[] = [];
  modalImage: string;
  showRegImage: boolean = true;
  files: FileWithBase64[];
  fileSizeExceeded: boolean;

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public modalService: NgbModal,
    public icon: IconService,
    public apiAccessService: ApiAccessService,
    public toastService: ToastService,
    public stockChecksService: StockChecksService
  ) { }

  ngOnInit() {
    this.initParams();
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.item) {
      this.initParams();
    }
  }

  isStockItem() {
    return !!this.item.stockItem;
  }

  initParams() {
    this.originalItem = this.constants.clone(this.item)
    this.item.id = this.isStockItem() ? this.item.stockItem.stockItemId : this.item.scan.scanId;
    this.showRegImage = true;
    this.imagesURLs = [];
    this.files = [];
    this.fileSizeExceeded = false;
  }

  private reloadContent(id: number, isStockItem: boolean) {
    const payload = [
      { key: 'stockCheckId', value: this.selections.stockCheck.id },
      { key: 'stockItemId', value: isStockItem ? id : 0 },
      { key: 'scanId', value: isStockItem ? 0 : id },
    ]
    this.apiAccessService.get('StockItems', 'GetItemFullDetail', payload).subscribe((res: ItemFullDetail) => {
      if (!!res.scan && !!res.scan.scanId) {
        //stockitem has a scan so generate the image url
        this.constants.addImageStringsToScan(res.scan)
      }
      this.item = res;
      this.item.id = this.isStockItem() ? this.item.stockItem.stockItemId : this.item.scan.scanId;
      this.originalItem = this.constants.clone(res)
    })
  }

  checkForChangesMade() {
    if (this.files?.length > 0) { return true; }
    return this.item.resolutionImageIds != this.originalItem.resolutionImageIds ||
      this.item.resolutionNotes != this.originalItem.resolutionNotes ||
      this.item.resolutionTypeDescription != this.originalItem.resolutionTypeDescription ||
      this.item.isResolved != this.originalItem.isResolved ||
      JSON.stringify(this.item.resolutionImages) != JSON.stringify(this.originalItem.resolutionImages);
  }

  resolutionTypesForMissing() {
    return this.constants.ResolutionTypesActive.filter(e => e.explainsMissingVehicle).sort((a, b) => a.description.localeCompare(b.description));
  }

  resolutionTypesForUnknowns() {
    return this.constants.ResolutionTypesActive.filter(e => !e.explainsMissingVehicle).sort((a, b) => a.description.localeCompare(b.description));
  }

  chooseResolution(problemResolution: ResolutionType) {
    this.item.resolutionTypeDescription = problemResolution.description;
    this.item.resolutionTypeBackup = problemResolution.backupRequired;
  }

  public onFilePaste(event: ClipboardEvent) {
    event.preventDefault();
    let file = event.clipboardData.items[0].getAsFile();
    this.prepareFilesList([file]);
  }

  cancelEditProblemCarUpdate() {
    this.fileSizeExceeded = false;
    this.reloadContent(this.item.id, this.isStockItem());
  }

  deleteMissingResolutionImage(image: ImageToUpdate) {
    if (image.id) //Blob
    {
      image.status = "DELETE";
      const idx = this.item.resolutionImages.findIndex(f => f.id === image.id);
      this.item.resolutionImages[idx] = image;

      if (this.item.resolutionImages.filter(x => x.status !== 'DELETE').length === 0) this.item.isResolved = false;
    }
    else {
      const idx = this.item.resolutionImages.findIndex(f => f.fileBase64 === image.fileBase64);
      this.item.resolutionImages.splice(idx, 1);

      if (this.item.resolutionImages.length === 0) this.item.isResolved = false;
    }
  }

  zoomInBackup(image) {
    let imageFileTypes: string[] = ['png', 'jpg', 'jpeg', 'webp'];
    let imageFileExtension: string = image.fileName.slice(image.fileName.lastIndexOf('.') + 1);

    if (image.fileName === '' || imageFileTypes.includes(imageFileExtension) || image.fileName === 'FileName') {
      this.modalImage = image.url;
    } else {
      let link = document.createElement("a");
      link.href = image.url;
      link.download = image.fileName;
      link.click();
    }
  }

  saveResolution() {
    const savingToast = this.toastService.loadingToast('Saving resolution...');

    if (!this.item.resolutionTypeDescription) {
      savingToast.close();
      this.toastService.errorToast('Please choose a resolution');
      return;
    }
    const controllerName = this.isStockItem() ? 'StockItems' : 'Scans';
    const methodName = this.isStockItem() ? 'SaveMissingResolution' : 'SaveUnknownResolution';

    //gather images to send
    const images: ImageToUpdate[] = [];

    //existing images from the item
    this.item.resolutionImages.forEach(image => {
      images.push(image)
    })

    //newly uploaded files
    const newImages = this.convertFilesToImages();
    newImages.forEach(image => {
      images.push(image);
    })


    //put any new images into array to save
    const payload: Resolution = {
      resolutionId: this.item.resolutionId,
      stockCheckId: this.selections.stockCheck.id,
      originalItemId: this.isStockItem() ? this.item.stockItem.stockItemId : this.item.scan.scanId,
      isResolved: this.item.isResolved,
      resolutionTypeId: this.constants.ResolutionTypes.find(x => x.description === this.item.resolutionTypeDescription && x.explainsMissingVehicle === this.isStockItem()).id,
      notes: this.item.resolutionNotes,
      images: images
    }

    this.apiAccessService.post(controllerName, methodName, payload).subscribe((data: any) => {
      this.toastService.successToast('Explanation Saved');
      if (this.item.isResolved) this.confettiExplosion();
      savingToast.close();
      setTimeout(() => {
        this.selections.stockCheckItemChanged.emit(true)
        let id = this.item.id;
        this.initParams();
        this.reloadContent(id, this.isStockItem());
        this.stockChecksService.loadStockCheck(this.selections.stockCheck, true);
      }, 200)

    }, error => {
      this.toastService.errorToast('Failed to save resolution');
      console.error('Failed to save resolution', error);
      savingToast.close();
    });
  }

  maybeDeleteResolution() {
    this.constants.alertModal.title = 'Really delete this problem resolution?'
    this.constants.alertModal.message = '';

    this.modalService.open(this.constants.alertModal.elementRef, { size: 'sm', keyboard: true, ariaLabelledBy: 'modal-basic-title' }).result.then((result) => {
      this.goAheadAndDeleteResolution();
    }, (reason) => {
      //chose to cancel, do nothing
      return
    });
  }

  private goAheadAndDeleteResolution() {
    const controllerName = this.isStockItem() ? 'StockItems' : 'Scans';
    const payload: ResolutionDeleteParams = { ResolutionId: this.item.resolutionId, StockCheckId: this.selections.stockCheck.id }
    this.apiAccessService.post(controllerName, 'DeleteResolution', payload).subscribe((data: any) => {
      this.reloadContent(this.item.id, this.isStockItem());
      this.toastService.successToast('Explanation deleted');
      this.selections.stockCheckItemChanged.emit(true);
    });
  }

  onFileDropped(event: File[]) {
    this.prepareFilesList(event);
  }

  fileBrowseHandler(event: any) {
    const files: File[] = event.target.files;
    this.prepareFilesList(files);
  }

  prepareFilesList(files: File[]) {
    for (let item of files) {
      if (item.size > 10000000) { // 10mb limit
        this.fileSizeExceeded = true;
      } else {
        let reader = new FileReader();
        reader.onload = (e) => {
          let base64 = e.target.result as string;
          let object: FileWithBase64 = Object.assign(item, { base64: base64 });
          this.files.push(object);
        }
        reader.readAsDataURL(item);
      }
    }
  }

  formatBytes(bytes: number, decimals: number = 2) {
    if (bytes === 0) {
      return "0 Bytes";
    }
    const k = 1024;
    const dm = decimals <= 0 ? 0 : decimals;
    const sizes = ["Bytes", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + " " + sizes[i];
  }

  deleteFile(index: number) {
    this.files.splice(index, 1);
    if (this.files.length === 0) this.item.isResolved = false;
  }

  convertFilesToImages(): ImageToUpdate[] {
    const images: ImageToUpdate[] = [];
    this.files.forEach(file => {
      images.push({ status: "ADD", fileBase64: file.base64, id: null, url: '', fileName: this.sanitiseFileName(file.name) })
    })

    return images;
  }

  getResolutionTypes() {
    return this.isStockItem() ? this.resolutionTypesForMissing() : this.resolutionTypesForUnknowns();
  }

  isDuplicate() {
    if (this.item.stockItem?.state == ReconciliationState.Duplicate) { return true; }
    if (this.item.scan?.scanState == ReconciliationState.Duplicate) { return true; }
    return false;
  }

  stockMatchesScan() {
    if (this.item.stockItem?.state === ReconciliationState.MatchedToStockOrScan) { return true; }
    return false;
  }

  itemMatchesIntercompany() {
    if (this.item.stockItem?.state === ReconciliationState.MatchedToOtherSite) { return true; }
    return false;
  }

  isMatchedToReconcilingItem() {
    if (this.item.stockItem?.state === ReconciliationState.MatchedToReport) { return true; }
    if (this.item.scan?.scanState === ReconciliationState.MatchedToReport) { return true; }
    return false;
  }

  public vehicleIsAnIssue() {
    if (this.item.stockItem?.state === ReconciliationState.OutstandingIssue) { return true; }
    if (this.item.stockItem?.state === ReconciliationState.Resolved) { return true; }
    if (this.item.scan?.scanState === ReconciliationState.OutstandingIssue) { return true; }
    if (this.item.scan?.scanState === ReconciliationState.Resolved) { return true; }
    return false;
  }

  showBackupRow() {
    const doShowBackupSetting = this.constants.GlobalParams.find(x => x.name === 'doShowBackupRequiredRow');
    return this.item.resolutionTypeDescription && doShowBackupSetting && doShowBackupSetting.boolValue;
  }

  getThumbnail(file: any) {
    if (file.base64.includes('data:image')) {
      return file.base64;
    } else {
      return this.imageForFileExtensions(file.name);
    }
  }

  updateUrl(imageIndex: number, fileName: string) {
    let imageElement: HTMLImageElement = document.getElementById(`backup-image-${imageIndex}`) as HTMLImageElement;
    imageElement.src = this.imageForFileExtensions(fileName);
  }

  imageForFileExtensions(nameOrSrc: string) {
    if (nameOrSrc.includes('.pdf')) {
      return './assets/imgs/backup-pdf.png';
    } else if (nameOrSrc.includes('.docx')) {
      return './assets/imgs/backup-word.png';
    } else if (nameOrSrc.includes('.xl') || nameOrSrc.includes('.csv')) {
      return './assets/imgs/backup-excel.png';
    } else {
      return './assets/imgs/backup-file.png';
    }
  }

  thereAreFiles() {
    if (this.files && this.files.length > 0) { return true; }
    if (this.item.resolutionImages && this.item.resolutionImages.length > 0) { return true; }
    return false;
  }

  instructionRowMessage() {
    return `Add files with the button below (10mb or less in size) or by dragging and dropping a file into this area.  You can also use the screen snipping tool by pressing windows key + shift + s, 
    dragging around an area on screen then pressing ctrl + v in this area to attach a screen snip.    The StockPulse mobile app allows you to add backup using your device's camera. 
    ** Please take care to obscure customer personal data from uploads ** `
  }

  canResolve() {
    if (this.showCompact || !this.selections.stockCheck) { return false; }
    if (this.selections.stockCheck.statusId > 3 || this.selections.userIsGeneralManager || this.selections.userIsReadOnly) { return false; }

    if (this.isStockItem()) {
      if (!this.constants.requireBackupForMissingResolution) { return true; }
    }
    else {
      if (!this.constants.requireBackupForUnknownResolution) { return true; }
    }

    if (this.item.resolutionImages && this.item.resolutionImages.length > 0 && this.item.resolutionImages.filter(x => x.status !== 'DELETE').length > 0) return true;
    if (this.files && this.files.length > 0) return true;
    return false;
  }

  backupRequired() {
    if (this.isStockItem()) {
      if (this.constants.requireBackupForMissingResolution) { return true; }
    }
    else {
      if (this.constants.requireBackupForUnknownResolution) { return true; }
    }
  }

  confettiExplosion(): void {
    const canvas = document.createElement('canvas');
    canvas.classList.add('confetti-canvas');
    document.body.appendChild(canvas);

    const confettiCanvas = confetti.create(canvas, {
      resize: true
    });

    this.playAudio();

    confettiCanvas({
      particleCount: 100,
      spread: 160
    })

    setTimeout(() => {
      document.body.removeChild(canvas);
    }, 1000)
  }

  playAudio() {
    let audio: HTMLAudioElement = new Audio();
    audio.src = "../../../assets/audio/success.mp3";
    audio.load();
    audio.play();
  }

  canAddBackup() {
    if (this.showCompact || !this.selections.stockCheck) {
      return false;
    } 
    
    if (this.selections.stockCheck.statusId > 3 || this.selections.userIsGeneralManager || this.selections.userIsReadOnly) {
      return false;
    }
    return true;
  }

  shouldShowReg() {
    return !this.constants.GlobalParams.find(x => x.name === 'NeverScanReg').boolValue;
  }

  sanitiseFileName(fileName: string): string {
    return fileName
    .normalize("NFD") // Normalize to decompose accented characters
    .replace(/[\u0300-\u036f]/g, ' ') // Remove diacritical marks
    .replace(/[^\w\s.]/g, ' '); // Remove special characters except letters, numbers, and spaces
  }
}
