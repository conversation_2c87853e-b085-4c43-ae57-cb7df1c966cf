<div class="d-inline-block" #dropdown="ngbDropdown" container="body" ngbDropdown (openChange)="reset()">
    <button id="dropdownTrigger" class="btn btn-primary" ngbDropdownToggle>
        {{ selectionsService.stockCheckLongName ? selectionsService.stockCheckLongName : 'Select a stock check...' }}
    </button>
    <div ngbDropdownMenu aria-labelledby="dropdownTrigger">
        <input type="text" placeholder="Search..." [(ngModel)]="searchString" (ngModelChange)="searchList()">
        <button *ngFor="let stockCheck of stockChecksCopy" ngbDropdownItem (click)="selectStockCheck(stockCheck)">
            {{ stockCheck.site }} - {{ formatUTC(stockCheck.date) | cph:'date':0 }}
        </button>
    </div>
</div>