﻿
using System;
using System.Data.SqlTypes;


namespace StockPulse.Loader.Services
{

    public static class RowInterpretationService
    {

        // Row interpretation functions
        public static bool GetVatQualifying(string input)
        {
            if (input == null) { return false; }
            if (input.Trim() == "Y") { return true; }
            if (input.Trim() == "Yes") { return true; }
            return false;
        }

        public static DateTime GetDateFromString(string inputString)
        {
            DateTime parsedDate = DateTime.ParseExact(inputString.Replace(" ", ""), "dd/MM/yyyy", System.Globalization.CultureInfo.InvariantCulture);

            if (parsedDate < (DateTime)SqlDateTime.MinValue)
            {
                return (DateTime)SqlDateTime.MinValue;
            }
            if (parsedDate > (DateTime)SqlDateTime.MaxValue)
            {
                return (DateTime)SqlDateTime.MaxValue;
            }

            return parsedDate;
        }


        public static decimal GetDecimal(object input)
        {
            return Convert.ToDecimal(input);
        }

        public static int? GetNullableInt(string input)
        {
            if(input == null) { return null; }
            if(input == "") { return null; }
            return int.Parse(input);
        }

        public static int GetInt(string input)
        {
            if (input == null) { return 0; }
            if (input == "") { return 0; }
            return int.Parse(input);
        }
    }
}
