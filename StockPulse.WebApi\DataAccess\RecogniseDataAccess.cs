﻿using Dapper;
using StockPulse.WebApi.Dapper;
using System.Threading.Tasks;

namespace StockPulse.WebApi.DataAccess
{
    public interface IRecogniseDataAccess
    {
        //Task<string> GetResultingReg(int regPictureId);
        //Task<int> SavePictureInDb(RegPicture _regPicture);
        Task SaveRegScanStat(RegScanStat stat);
        //Task DeleteResultingReg(int regPictureId);// Not in use
        //Task UpdatePictureInDb(RegPicture regPicture);
    }

    public class RecogniseDataAccess : IRecogniseDataAccess
    {
        private readonly IDapper dapper;

        public RecogniseDataAccess(IDapper dapper)
        {
            this.dapper = dapper;
        }


        //public async Task<int> SavePictureInDb(RegPicture _regPicture)
        //{
        //    var paramList = new DynamicParameters();
        //    paramList.Add("ImageBytes", _regPicture.ImageBytes);
        //    paramList.Add("RequestDate", _regPicture.RequestDate);
        //    paramList.Add("Priority", _regPicture.Priority);
        //    RegPicture result = await dapper.InsertAsync<RegPicture>("dbo.INSERT_RegPicture", paramList, System.Data.CommandType.StoredProcedure);
        //    return result.Id;
        //}


        //public async Task<string> GetResultingReg(int regPictureId)
        //{
        //    var paramList = new DynamicParameters();
        //    paramList.Add("RegPictureId", regPictureId);
        //    return await dapper.GetAsync<string>("dbo.GET_RegPictureResult", paramList, System.Data.CommandType.StoredProcedure);
        //}

        public async Task SaveRegScanStat(RegScanStat stat)
        {
            var paramList = new DynamicParameters();
            paramList.Add("UserId", stat.UserId);
            paramList.Add("ScanDate", stat.ScanDate);
            paramList.Add("Priority", stat.Priority);
            paramList.Add("WaitTime", stat.WaitTime);
            paramList.Add("DidSucceed", stat.DidSucceed);
            paramList.Add("FromLocallySaved", stat.FromLocallySaved);
            await dapper.ExecuteAsync("dbo.INSERT_RegScanStat", paramList, System.Data.CommandType.StoredProcedure);
        }

        /*
         * Not in use
        public async Task DeleteResultingReg(int regPictureId)
        {
            var paramList = new DynamicParameters();
            paramList.Add("RegPictureId", regPictureId);
            await dapper.ExecuteAsync("dbo.DELETE_RegPictureResult", paramList, System.Data.CommandType.StoredProcedure);
        }
        

        public async Task UpdatePictureInDb(RegPicture regPicture)
        {
            var paramList = new DynamicParameters();
            paramList.Add("Id", regPicture.Id);
            paramList.Add("IsDone", regPicture.IsDone);
            paramList.Add("Result", regPicture.Result);
            //paramList.Add("ImageBytes", regPicture.ImageBytes);
            await dapper.ExecuteAsync("dbo.UPDATE_RegPicture", paramList, System.Data.CommandType.StoredProcedure);
        }
        */
    }
}
