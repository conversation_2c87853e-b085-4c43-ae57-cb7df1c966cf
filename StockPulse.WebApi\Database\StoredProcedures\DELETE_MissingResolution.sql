﻿
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].[DELETE_MissingResolution]
(
    @StockCheckId INT,
	@UserId INT,
	@MissingResolutionId INT
)
AS
BEGIN

SET NOCOUNT ON;

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END
	
UPDATE StockItems 
SET MissingResolutionId = NULL
WHERE MissingResolutionId = @MissingResolutionId

DELETE [MissingResolutionImages]
WHERE MissingResolutionId = @MissingResolutionId

DELETE [MissingResolutions]
WHERE Id = @MissingResolutionId


EXEC [dbo].[UPDATE_StockCheckLastUpdate] @StockCheckId, @UserId


END
	
  
	


GO


