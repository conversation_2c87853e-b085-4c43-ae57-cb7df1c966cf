﻿
USE [stockpulsedev]
GO
CREATE NONCLUSTERED INDEX [NONCLUSTERED_IDX_StockItems_StockCheckId_MissingResolutionId]
ON [dbo].[StockItems] ([StockCheckId])
INCLUDE ([MissingResolutionId])
GO

USE [stockpulsedev]
GO
CREATE NONCLUSTERED INDEX [NONCLUSTERED_IDX_StockItems_ScanId_StockCheckId]
ON [dbo].[StockItems] ([ScanId])
INCLUDE ([StockCheckId])
GO

USE [stockpulsedev]
GO
CREATE NONCLUSTERED INDEX [NONCLUSTERED_IDX_Scans_StockCheckId_StockItemId_ReconcilingItemId]
ON [dbo].[Scans] ([StockCheckId],[StockItemId],[ReconcilingItemId])

GO


CREATE INDEX IDX_Scans_StockCheckId_StockItemId_IsDuplicate_Reg_Vin
ON dbo.Scans
(
	StockCheckId, StockItemId, IsDuplicate,<PERSON>,Vin
)
GO


USE [stockpulse]
GO
CREATE NONCLUSTERED INDEX IDX_StockItems_MissingResolutionId
ON [dbo].[StockItems] ([MissingResolutionId])

GO

USE [stockpulse]
GO
CREATE NONCLUSTERED INDEX IDX_Scans_UnknownResolutionId
ON [dbo].[Scans] ([UnknownResolutionId])

GO



CREATE NONCLUSTERED INDEX [StockItemsTable|Id_Reg_Vin_ScanId] ON [dbo].[StockItems]
(
	[StockCheckId] ASC
)
INCLUDE([Id],[Reg],[Vin],[ScanId]) WITH (STATISTICS_NORECOMPUTE = OFF, DROP_EXISTING = OFF, ONLINE = OFF, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
GO



CREATE UNIQUE INDEX UX_UserId_IsDefault
ON UserSites(UserId)
WHERE IsDefault = 1;


CREATE UNIQUE INDEX IX_AspNetUsers_UserName_Email
ON AspNetUsers (UserName, Email);


ALTER TABLE [dbo].[UserSites]
ADD CONSTRAINT [UQ_UserSites_UserId_SiteId] UNIQUE ([UserId], [SiteId]);


ALTER TABLE [dbo].[GlobalParams]
ADD CONSTRAINT [UQ_GlobalParams_DealerGroupId_Name] UNIQUE ([DealerGroupId], [Name]);