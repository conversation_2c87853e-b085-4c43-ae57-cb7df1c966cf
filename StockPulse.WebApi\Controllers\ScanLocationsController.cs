﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using StockPulse.WebApi.Service;
using System.Collections.Generic;
using System.Threading.Tasks;
using StockPulse.WebApi.ViewModel;
using StockPulse.Model;

namespace StockPulse.WebApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class ScanLocationsController : ControllerBase
    {
        private readonly IScanLocationsService scanLocationsService;

        public ScanLocationsController(IScanLocationsService scanLocationsService)
        {
            this.scanLocationsService = scanLocationsService;
        }

        [HttpGet]
        [Route("GetScanLocationsForSiteId")]
        public async Task<IEnumerable<Location>> GetScanLocationsForSiteId(int siteId)
        {
            return await scanLocationsService.GetScanLocationsForSiteId(siteId);
        }

        [HttpPost]
        [Route("SaveScanLocationForSiteId")]
        public async Task SaveScanLocationForSiteId(SaveScanLocationParams parms)
        {
            await scanLocationsService.SaveScanLocationForSiteId(parms);
        }

        [HttpPost]
        [Route("DeleteScanLocationForSiteId")]
        public async Task DeleteScanLocationForSiteId(DeleteScanLocationParams parms)
        {
            await scanLocationsService.DeleteScanLocationForSiteId(parms);
        }
    }
}
