# ASP.NET Core (.NET Framework)
# Build and test ASP.NET Core projects targeting the full .NET Framework.
# Add steps that publish symbols, save build artifacts, and more:
# https://docs.microsoft.com/azure/devops/pipelines/languages/dotnet-core

trigger:

  tags:
    include:
    - loader*
    - all*


pool:
  default

variables:
  solution: '**/*.sln'
  buildPlatform: 'Any CPU'
  buildConfiguration: 'Release'

jobs:
- job: BuildAndPublishStockpulseLoader
  displayName: Build And Publish Stockpulse Loader

  steps:
  - task: NuGetToolInstaller@1

 
  - task: NuGetCommand@2
    displayName: 'Restore nuget packages'
    inputs:
      restoreSolution: '$(solution)'
      feedsToUse: 'select'
      vstsFeed: 'CPHI_Feed'
      includeNuGetOrg: true

  # - task: DotNetCoreCLI@2
  #   displayName: 'Build All Projects'
  #   inputs:
  #     command: 'build'
  #     arguments: '-c Release -r=win-x64'
  #     projects: '**/*.csproj'


      
  - task: DotNetCoreCLI@2
    displayName: 'Build Loader Project'
    inputs:
      command: 'build'
      arguments: '-c Release'
      projects: '**/StockPulse.Loader.csproj'

  # - task: VSTest@2
  #   displayName: 'Run Unit Tests'
  #   inputs:
  #     testSelector: 'testAssemblies'
  #     testAssemblyVer2: |
  #       **/*UnitTests*.dll
  #       !**\*TestAdapter.dll
  #       !**\obj\**

  # - task: DotNetCoreCLI@2
  #   displayName: 'Publish FTPScraper'
  #   inputs:
  #     command: 'publish'
  #     publishWebProjects: false
  #     projects: '**/CPHI.Spark.FTPScraper.csproj'
  #     zipAfterPublish: true
  #     arguments: '-o $(Build.ArtifactStagingDirectory)/FTPScraper/ --runtime=win-x64 -c Release --no-build'
    
  # - task: DotNetCoreCLI@2
  #   displayName: 'Publish WindowsAppScraper'
  #   inputs:
  #     command: 'publish'
  #     publishWebProjects: false
  #     projects: '**/CPHI.Spark.WindowsAppScraper.csproj'
  #     zipAfterPublish: true
  #     arguments: '-o $(Build.ArtifactStagingDirectory)/WindowsAppScraper/ --runtime=win-x64 -c Release --no-build'
    
  - task: DotNetCoreCLI@2
    displayName: 'Publish Loader'
    inputs:
      command: 'publish'
      publishWebProjects: false
      projects: '**/StockPulse.Loader.csproj'
      zipAfterPublish: true
      arguments: '-o $(Build.ArtifactStagingDirectory)/Loader/ -c Release --no-build'

  # - task: DotNetCoreCLI@2
  #   displayName: 'Publish API Project'
  #   inputs:
  #     command: 'publish'
  #     publishWebProjects: true
  #     projects: '**/CPHI.Spark.WebApp.csproj'
  #     zipAfterPublish: true
  #     arguments: '-o $(Build.ArtifactStagingDirectory)/WebApp/ --runtime=win-x86 -c Release --no-build'
      
  
#   - task: PublishPipelineArtifact@1
#   displayName: 'Publish FTPScraper Artifact'
#   inputs:
#     targetPath: '$(Build.ArtifactStagingDirectory)/FTPScraper/' 
#     artifactName: 'FTPScraper'
    
  - task: PublishPipelineArtifact@1
    displayName: 'Publish Loader Artifact'
    inputs:
      targetPath: '$(Build.ArtifactStagingDirectory)/Loader/'
      artifactName: 'Loader'
    
# - task: PublishPipelineArtifact@1
#   displayName: 'Publish WindowsAppScraper Artifact'
#   inputs:
#     targetPath: '$(Build.ArtifactStagingDirectory)/WindowsAppScraper/' 
#     artifactName: 'WindowsAppScraper'


  # - task: PublishPipelineArtifact@1
  #   displayName: 'Publish Spark API Artifact'
  #   inputs:
  #     targetPath: '$(Build.ArtifactStagingDirectory)/WebApp/' 
  #     artifactName: 'SparkApi'
     
