import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { Observable } from 'rxjs';
import { FinancialLineVM } from "../model/FinancialLineVM";
import { KeyValuePair } from '../model/KeyValuePair';
import { UpdateImportMaskParams } from '../model/RenameImportMaskParams';
import { UpdateSiteNameLookupsParams } from '../model/UpdateSiteNameLookupsParams';
import { FileImportParams } from '../pages/loadItems/import/import.component';
import { ConstantsService } from './constants.service';
import { ToastService } from './newToast.service';
import { SelectionsService } from './selections.service';
import { BaseURLVM } from '../model/BaseURLVM';


@Injectable({
  providedIn: 'root'
})
export class ApiAccessService {



  constructor(
    public http: HttpClient,
    public constants: ConstantsService,
    public selections: SelectionsService,
    public router: Router,
    public toastService: ToastService
  ) {

  }

  provideBaseUrlUK(): string {
    return `${this.constants.baseURLUK}/api`;
  }
  provideBaseUrlUS(): string {
    return `${this.constants.baseURLUS}/api`;
  }

  getTextForCountry(country: string, controllerName: string, method: string, requestParams?: KeyValuePair[]): Observable<any> {

    return new Observable<BaseURLVM>(observer => {

      let baseURL = '';
      if (country == 'UK'){
        baseURL = this.provideBaseUrlUK()
      } else if (country == 'US'){
        baseURL = this.provideBaseUrlUS()
      }

      let paramsString = ""
      if (requestParams && requestParams.length > 0) {
        paramsString += "?"
        paramsString += requestParams.map(rp => `${rp.key}=${rp.value}`).join('&');
      }
      
      this.http.get<BaseURLVM>(`${baseURL}/${controllerName}/${method}${paramsString}`).subscribe(  //  //, this.getHeaders()

        (result: BaseURLVM) => {
          observer.next(result);
        },
        //errors
        error => {
          if (error.status !== 401 && controllerName !== 'whoami') {
            console.error('error when retrieving ' + controllerName + JSON.stringify(error))
            this.toastService.errorToast(`Error when retrieving ${controllerName}`)
          }
          observer.error('System error');
        }
        ,
        //finished
        () => {
          observer.complete();
        }
      )
    })

  }




  getText(controllerName: string, method: string, requestParams?: KeyValuePair[]): Observable<any> {

    let myObservable: Observable<any> = new Observable(observer => {


      let paramsString = ""
      if (requestParams && requestParams.length > 0) {
        paramsString += "?"
        requestParams.forEach((rp, i) => {
          if (i > 0) paramsString += '&';
          paramsString += `${rp.key}=${rp.value}`;
        })
      }
      this.http.get(`${this.constants.provideBaseUrl()}/${controllerName}/${method}${paramsString}`, {responseType: 'text'}).subscribe(  //  //, this.getHeaders()

        result => {
          observer.next(result);
        },
        //errors
        error => {
          if (error.status !== 401 && controllerName !== 'whoami') {
            console.error('error when retrieving ' + controllerName + JSON.stringify(error))
            this.toastService.errorToast(`Error when retrieving ${controllerName}`)
          }
          observer.error('System error');
        }
        ,
        //finished
        () => {
          observer.complete();
        }
      )
    })

    return myObservable;


  }

  get(controllerName: string, method: string, requestParams?: KeyValuePair[], ): Observable<any> {

    let myObservable: Observable<any> = new Observable(observer => {


      let paramsString = ""
      if (requestParams && requestParams.length > 0) {
        paramsString += "?"
        requestParams.forEach((rp, i) => {
          if (i > 0) paramsString += '&';
          paramsString += `${rp.key}=${rp.value}`;
        })
      }
      this.http.get(`${this.constants.provideBaseUrl()}/${controllerName}/${method}${paramsString}`).subscribe(  //  //, this.getHeaders()

        result => {
          observer.next(result);
        },
        //errors
        error => {
          if (error.status !== 401 && controllerName !== 'whoami') {
            console.error('error when retrieving ' + controllerName + JSON.stringify(error))
            this.toastService.errorToast(`Error when retrieving ${controllerName}`)
          }
          observer.error('System error');
        }
        ,
        //finished
        () => {
          observer.complete();
        }
      )
    })

    return myObservable;


  }




  patch(controllerName: string, method: string, payload: any): Observable<any> {

    let myObservable: Observable<any> = new Observable(observer => {

      this.http.patch(`${this.constants.provideBaseUrl()}/${controllerName}/${method}`, payload ).subscribe( //this.getHeaders()

        result => {
          observer.next(result);
        },
        //errors
        error => {
          console.error('System Error')
          observer.error('System error');
          //this.constants.toastDanger(`System error`)
        }
        ,
        //finished
        () => {
          observer.complete();
        }
      )
    })

    return myObservable;

  }



  post(controllerName: string, method: string, payload: any): Observable<any> {
    //console.log('in post');
    
    let myObservable: Observable<any> = new Observable(observer => {
      //console.log('header',this.getHeaders());
      
      this.http.post(`${this.constants.provideBaseUrl()}/${controllerName}/${method}`, payload).subscribe(  //, this.getHeaders()

        result => {
          observer.next(result);
        },
        //errors
        error => {
          if (controllerName === 'StockChecks' && method === 'ImportLatestData'){ // Its handled at the source of call
            observer.error(error);
          }
          else{
            console.error('System error');
            observer.error('System error');

          }
          //this.constants.toastDanger(`System error`)
        }
        ,
        //finished
        () => {
          observer.complete();
        }
      )
    })

    return myObservable;


  }

  postWithOptions(controllerName: string, method: string, payload: any, options: any): Observable<any> {
    //console.log('in post');
    
    let myObservable: Observable<any> = new Observable(observer => {
      //console.log('header',this.getHeaders());
      
      this.http.post(`${this.constants.provideBaseUrl()}/${controllerName}/${method}`, payload, options).subscribe(  //, this.getHeaders()

        result => {
          observer.next(result);
        },
        //errors
        error => {
          console.error('System error');
          observer.error(error);
          //this.constants.toastDanger(`System error`)
        }
        ,
        //finished
        () => {
          observer.complete();
        }
      )
    })

    return myObservable;


  }




  delete(controllerName: string, method: string, id: number): Observable<any> {
    let myObservable: Observable<any> = new Observable(observer => {

      this.http.delete(`${this.constants.provideBaseUrl()}/${controllerName}/${method}?stockCheckId=${this.selections.stockCheck.id}&id=${id}`).subscribe(  //, this.getHeaders()

        result => {
          observer.next(result);
        },
        //errors
        error => {
          if (error.status != 401) {
            console.error('error when deleting ' + controllerName + JSON.stringify(error))
            this.toastService.errorToast(`Error when deleting ${controllerName}`)
            observer.error('System error');
          }
        }
        ,
        //finished
        () => {
          observer.complete();
        }
      )
    })

    return myObservable;
  }

  deleteUser(controllerName: string, method: string, id: string): Observable<any> {
    let myObservable: Observable<any> = new Observable(observer => {

      this.http.delete(`${this.constants.provideBaseUrl()}/${controllerName}/${method}?id=${id}`).subscribe(  //, this.getHeaders()

        result => {
          observer.next(result);
        },
        //errors
        error => {
          if (error.status != 401) {
            console.error('error when deleting ' + controllerName + JSON.stringify(error))
            this.toastService.errorToast(`Error when deleting ${controllerName}`)
            observer.error('System error');
          }
        }
        ,
        //finished
        () => {
          observer.complete();
        }
      )
    })

    return myObservable;
  }

  restoreUser(controllerName: string, method: string, id: string): Observable<any> {
    let myObservable: Observable<any> = new Observable(observer => {

      this.http.patch(`${this.constants.provideBaseUrl()}/${controllerName}/${method}?id=${id}`, null).subscribe(  //, this.getHeaders()

        result => {
          observer.next(result);
        },
        //errors
        error => {
          if (error.status != 401) {
            console.error('error when deleting ' + controllerName + JSON.stringify(error))
            this.toastService.errorToast(`Error when deleting ${controllerName}`)
            observer.error('System error');
          }
        }
        ,
        //finished
        () => {
          observer.complete();
        }
      )
    })

    return myObservable;
  }

  updateLocation(scanId: number, newLocationId: number) {
    return this.http.get(`${this.constants.provideBaseUrl()}/Scans/UpdateLocation?newLocationId=${newLocationId}&scanId=${scanId}`)  //, this.getHeaders()
  }

  updateReg(scanId: number, reg: string) {
    return this.http.get(`${this.constants.provideBaseUrl()}/Scans/UpdateScanReg?reg=${reg}&scanId=${scanId}&isMobileApp=false`)  //, this.getHeaders()
  }
  updateVin(scanId: number, vin: string) {
    return this.http.get(`${this.constants.provideBaseUrl()}/Scans/UpdateScanVin?vin=${vin}&scanId=${scanId}&isMobileApp=false`)  //, this.getHeaders()
  }




  updateFinancialLine(id: number, description: string, notes: string, balance: number) {
    let stockCheckId: number = this.selections.stockCheck.id;
    return this.http.get(`${this.constants.provideBaseUrl()}/FinancialLines/UpdateLine?stockCheckId=${stockCheckId}&financialLineId=${id}&description=${description}&notes=${notes}&balance=${balance}`)  //, this.getHeaders()
  }



  bulkSaveFinancialLines(financialLines: FinancialLineVM[], fileImportId: number) {
    let stockCheckId: number = this.selections.stockCheck.id;
    let payload = { stockCheckId, financialLines, fileImportId }
    return this.http.post(`${this.constants.provideBaseUrl()}/FinancialLines/FinancialLines`, payload);  //, this.getHeaders()
  }

  deleteImportMask(importMaskId: number) {
    return this.http.delete(`${this.constants.provideBaseUrl()}/ImportMasks/Delete?importMaskId=${importMaskId}`);
  }
 
  renameImportMasks(params: UpdateImportMaskParams) {
    return this.http.put(`${this.constants.provideBaseUrl()}/ImportMasks/Update`, params);
  }

  
  saveNewUser(payload: any): Observable<any> {    
    let myObservable: Observable<any> = new Observable(observer => {
      this.http.post(`${this.constants.provideBaseUrl()}/User/UserAndLogin`, payload).subscribe(
        result => {
          observer.next(result);
        }, error => {
          console.error('System error', error);
          observer.error(error);
        }, () => {
          observer.complete();
        }
      )
    })

    return myObservable;
  }

  updateSiteNameLookups(params: UpdateSiteNameLookupsParams) {
    return this.http.put(`${this.constants.provideBaseUrl()}/Sites/UpdateSiteNameLookups`, params);
  }

  addStockItems(payload) {
    return this.http.post(this.constants.provideBaseUrl() + '/StockItems/AddStockItems', payload);
  }

  addFileImport(params: FileImportParams) {
    return this.http.post(this.constants.provideBaseUrl() + '/FileImport/AddFileImport', params);
  }
}
