pool:
  Default

variables:
  solution: '**/*.sln'
  buildPlatform: 'Any CPU'
  buildConfiguration: 'Release'

jobs:
- job: BuildAndAuditStockpulse
  displayName: Build And Audit StockPulse

  steps:
  - checkout: self
    fetchDepth: 1
    fetchTags: false
    clean: true
    persistCredentials: true

  - script: dotnet restore StockPulseP.sln
    displayName: 'Restore NuGet Packages'


    
  # # Microsoft scanner
  # - task: MicrosoftSecurityDevOps@1
  #   inputs:
  #     scanFolder: '$(Build.SourcesDirectory)/StockPulse.WebApi'

  # # 🧪 Download and run OWASP Dependency-Check
  # - powershell: |
  #     $version = "9.0.9"
  #     $url = "https://github.com/jeremylong/DependencyCheck/releases/download/v$version/dependency-check-$version-release.zip"
  #     $output = "$(Agent.TempDirectory)\dependency-check.zip"
  #     $extractPath = "$(Agent.TempDirectory)\dependency-check"
  #     Invoke-WebRequest -Uri $url -OutFile $output
  #     Expand-Archive -Path $output -DestinationPath $extractPath
  #     $odcPath = Join-Path $extractPath "dependency-check"
  #     New-Item -ItemType Directory -Path "$(Build.SourcesDirectory)\StockPulse.WebApi\odc-report" -Force | Out-Null
  #     & "$odcPath\bin\dependency-check.bat" `
  #         --project "StockPulse.WebApi" `
  #         --scan "$(Build.SourcesDirectory)\StockPulse.WebApi" `
  #         --out "$(Build.SourcesDirectory)\StockPulse.WebApi\odc-report" `
  #         --format HTML
  #   displayName: 'Run OWASP Dependency-Check (No Docker)'

  # # 📤 Publish OWASP report
  # - task: PublishBuildArtifacts@1
  #   inputs:
  #     PathtoPublish: '$(Build.SourcesDirectory)/StockPulse.WebApi/odc-report'
  #     ArtifactName: 'OWASP-Report'
  #     publishLocation: 'Container'



  - powershell: |
      $output = dotnet list StockPulseP.sln package --vulnerable --include-transitive
      Write-Output $output

      if ($output -match "(?i)>\s+.*\s+(High|Critical)\b") {
        Write-Error "High or Critical vulnerabilities found! Failing pipeline."
        exit 1
      } else {
        Write-Output "No High or Critical vulnerabilities found."
      }
    displayName: 'Fail if High or Critical Vulnerabilities Found'
    failOnStderr: true

