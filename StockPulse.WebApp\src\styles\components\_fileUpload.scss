.uploadFileWrapper {
    display: flex;
    align-items: center;

    .chooseFileInput {
        width: 0.1px;
        height: 0.1px;
        opacity: 0;
        overflow: hidden;
        position: absolute;
        z-index: -1;
    }

    .chooseFileInput+label {
        background-color: #323130;
        color: #ffffff;
        padding: 0.375rem 0.75rem;
        display: inline-block;
        font-weight: 400;
        text-align: center;
        vertical-align: middle;
        margin-bottom: 0;
        transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out;
        cursor: pointer;

        fa-icon {
            margin-right: 0.75em;
        }
    }

    .chooseFileInput:focus+label,
    .chooseFileInput+label:hover {
        background-color: var(--secondaryLight);
        color: #323130;
    }

    .fileName {
        margin-right: 0.75em;
        color: var(--bodyColour);
    }
}