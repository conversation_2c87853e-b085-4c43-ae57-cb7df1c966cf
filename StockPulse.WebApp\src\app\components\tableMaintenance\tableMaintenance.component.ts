import { Component, OnInit } from '@angular/core';
import { ColDef } from 'ag-grid-community';
import { DeleteButtonTableMaintenanceComponent } from './deleteButtonTableMaintenance.component';
import { TableMaintenanceService } from './tableMaintenance.service';
import { SaveButtonTableMaintenanceComponent } from './saveButtonTableMaintenance.component';
import { ToastService } from 'src/app/services/newToast.service';
import { ConstantsService } from 'src/app/services/constants.service';
import { SelectionsService } from 'src/app/services/selections.service';
import { ApiAccessService } from 'src/app/services/apiAccess.service';

@Component({
  selector: 'app-maintenancetable',
  templateUrl: './tableMaintenance.component.html',
  styleUrls: ['./tableMaintenance.component.scss']
})
export class TableMaintenanceComponent implements OnInit {

  constructor(
    public service: TableMaintenanceService,
    public constants: ConstantsService,
    public toastService: ToastService,
    public selections: SelectionsService,
    public apiAccess: ApiAccessService
    ) { }

  ngOnInit(): void {
    this.loadData();
  }


  loadData() {
    this.service.getTables();
    this.setSubscriptions();
  }

  setSubscriptions(){
    this.service.dataUpdatedEvent.subscribe((res) => {
      this.getTableData(this.service.selectedTableId);
    })
  }

  getTableData(tableId: number) {
    this.toastService.loadingToast('Loading...');
    this.service.getTableData(tableId).subscribe((res: any) => {
      this.service.tableData = res;

      //Get columns 
      this.service.tableCols = [];

      const keys = Object.keys(this.service.tableData[0])
      keys.forEach(element => {
        this.service.tableCols.push({ field: element, editable: true, filter: 'agTextColumnFilter' });
      });

      

      this.setTableColumnDef(this.service.tableCols)
      this.setTableRowData(this.service.tableData);

      // console.log(this.service.tableCols);
      // console.log(this.service.tableData);

    }, e => {
      console.error("Error in table data: ", JSON.stringify(e))
    }, () => {
      this.toastService.destroyToast();
    })


  }

  setTableColumnDef(colDef: ColDef[]) {

    this.service.gridApi.setColumnDefs(null);

    colDef.push(
      { headerName: '', colId: 'Save', cellRendererFramework: SaveButtonTableMaintenanceComponent, width: 50 },
      { headerName: '', colId: 'Delete', cellRendererFramework: DeleteButtonTableMaintenanceComponent, width: 50 }
    )
    this.service.gridApi.setColumnDefs(colDef);
    this.service.gridColumnApi.getAllColumns()[0].getColDef().editable = false;
    
  }

  setTableRowData(rows: any) {
    this.service.gridApi.setRowData(rows);
    //Adds black row at the bottom
    this.service.gridApi.applyTransaction({ add: [{}] });
  }

  refreshGlobalParams() {
    this.apiAccess.get('GlobalParams','ReLoadCache').subscribe(()=>{
      this.toastService.successToast('Successfully reloaded cache');
    });
  }


}

