<!-- importVehiclesModal Modal -->


  <div id="topSetupBit">

    <!-- Instructions row -->

    <instructionRow [message]="instructionRowMessage()"></instructionRow>
    

    <div class="topRowButtons">
      <div class="d-flex">
        <!-- Choose file input -->
        <div class="uploadFileWrapper">
          <ng-container *ngIf="!loadItemsService.importFile">
            <input class="chooseFileInput" id="file" type="file" (change)="onFileChange($event)" />
            <label for="file"><fa-icon [icon]="icon.faUpload"></fa-icon>Choose File</label>
          </ng-container>
          <ng-container *ngIf="loadItemsService.importFile">
            <span class="fileName">File: {{ fileName }}</span>
            <button class="btn btn-primary" (click)="resetImportSettings()">Choose Different File</button>
          </ng-container>
        </div>

        <!-- Dropdown to choose import mask -->
        <div ngbDropdown id="inputMaskChoose" class="d-inline-block" *ngIf="loadItemsService.importFile">

          <!-- If not chosen import map -->
          <button  *ngIf="!loadItemsService.chosenImportMask" class="btn btn-success" id="" ngbDropdownToggle>
            Choose Import Map
          </button>

          <!-- Have chosen import map -->
          <button  *ngIf="loadItemsService.chosenImportMask" class="btn btn-primary" id="" ngbDropdownToggle>
              {{loadItemsService.chosenImportMask.name ? loadItemsService.chosenImportMask.name : 'New map'}}
          </button>


          <div id="importMaskDropdown" ngbDropdownMenu aria-labelledby="dropdownBasic1">

            <!-- Create new import mask button -->
            <button (click)="newImportMask()" ngbDropdownItem>
              
              <fa-icon [icon]="icon.faPlusCircle"></fa-icon>
              Create New Map
            </button>

            

            <div class="dropdown-divider"></div>

            <!-- Repeat buttons -->
            <div *ngIf="standardImportMasks.length > 0" class="maskSubHeader">
              Standard Import Maps
            </div>
            <button (click)="chooseImportMask(importMask)" *ngFor="let importMask of standardImportMasks"
              ngbDropdownItem>
              <div class="spaceBetween">
                <div class="me-4">
                  {{importMask.name}}
                  <span *ngIf="importMask.isMultiSite">(Multi-site)</span>
                </div>
                <div>{{importMask.createdBy}}</div>
                </div>
            </button>
            
            <div *ngIf="myImportMasks.length > 0" class="maskSubHeader mt-2">
              My Import Maps
            </div>
            <button (click)="chooseImportMask(importMask)" *ngFor="let importMask of myImportMasks"
              ngbDropdownItem>
              <div class="spaceBetween">
                <div class="me-4">
                  {{importMask.name}}
                  <span *ngIf="importMask.isMultiSite">(Multi-site)</span>
                </div>
                <div>{{importMask.createdBy}}</div>
                </div>
            </button>

            <div *ngIf="othersImportMasks.length > 0" class="maskSubHeader mt-2">
              Other Users' Maps
            </div>
            <button (click)="chooseImportMask(importMask)" *ngFor="let importMask of othersImportMasks"
              ngbDropdownItem>
              <div class="spaceBetween">
                <div class="me-4">
                  {{importMask.name}}
                  <span *ngIf="importMask.isMultiSite">(Multi-site)</span>
                </div>
                <div>{{importMask.createdBy}}</div>
                </div>
            </button>

          </div>

          
        </div>

        <!-- Save import map -->
        <button id="saveMask" class="btn btn-success" (click)="possiblySaveImportMap()"
          *ngIf="maybeShowSaveImportMask()">
          <fa-icon [icon]="icon.faSave"></fa-icon>
          Save import map
        </button>

        <!-- Multisite-->
        <button *ngIf="maybeShowSaveImportMask()" 
        id="multiSite" 
        class="btn btn-primary" 
        (click)="selectMultiSite()" 
        [ngClass]="{'highlight':loadItemsService.multiSite, 'noHighlight': !loadItemsService.multiSite}"
        >Multi-site Import
        </button>

      </div>



      <div>

        <div *ngIf="reconcilingItemsToImport">

          <button
            type="button"
            class="btn btn-success importVehicleDetails"
            [ngClass]="{ 'disable': validateImport() }"
            [ngbPopover]="missingFieldsPopover"
            [disablePopover]="!missingFieldsFromImport"
            triggers="mouseenter:mouseleave"
            (click)="maybeSaveRecItems(reconcilingItemsToImport)">
            <div class="flex">
              <fa-icon [icon]="icon.faCar"></fa-icon>
              Import data
            </div>
          </button>

          <!-- <button
            type="button"
            class="btn btn-success importVehicleDetails"
            [ngClass]="{ 'disable': validateImport() }"
            [ngbPopover]="missingFieldsPopover"
            [disablePopover]="!missingFieldsFromImport"
            triggers="mouseenter:mouseleave"
            (click)="saveRecItems(reconcilingItemsToImport)">
            <div class="flex">
              <fa-icon [icon]="icon.faCar"></fa-icon>
              <span>{{ loadItemsService.multiSite ? 'Import to Stock Checks' : 'Import to this Stock Check' }}</span>
            </div>
          </button>

          <button
            *ngIf="!loadItemsService.multiSite"
            type="button"
            class="btn btn-success importVehicleDetails"
            [ngClass]="{ 'disable': validateImport() }"
            [ngbPopover]="missingFieldsPopover"
            [disablePopover]="!missingFieldsFromImport"
            triggers="mouseenter:mouseleave"
            (click)="saveRecItems(reconcilingItemsToImport, true)">
            <div class="flex">
              <fa-icon [icon]="icon.faCars"></fa-icon>
              Import to all Active Stock Checks
            </div>
          </button> -->

          <button type="button" class="btn btn-primary" (click)="cancelImport()">Cancel</button>

        </div>
      
        <div *ngIf="stockItemsToImport">

          <button
            *ngIf="loadItemsService.chosenReconcilingItemType.description === 'DMS Stock'"
            type="button"
            class="btn btn-success importVehicleDetails"
            [ngClass]="{ 'disable': validateImport() }"
            [ngbPopover]="missingFieldsPopover"
            [disablePopover]="!missingFieldsFromImport"
            triggers="mouseenter:mouseleave"
            (click)="bulkSaveStockItems(stockItemsToImport)">
            <div class="flex">
              <fa-icon [icon]="icon.faFilesMedical"></fa-icon>
              <span>{{ loadItemsService.multiSite ? 'Import to Stock Checks' : 'Import to this Stock Check' }}</span>
            </div>
          </button>

          <button
          *ngIf="loadItemsService.chosenReconcilingItemType.description === 'Agency Stock'"
          type="button"
          class="btn btn-success importVehicleDetails"
          [ngClass]="{ 'disable': validateImport() }"
          [ngbPopover]="missingFieldsPopover"
          [disablePopover]="!missingFieldsFromImport"
          triggers="mouseenter:mouseleave"
          (click)="bulkSaveStockItems(stockItemsToImport)">
          <div class="flex">
            <fa-icon [icon]="icon.faFilesMedical"></fa-icon>
            <span>{{ loadItemsService.multiSite ? 'Import to Stock Checks' : 'Import to this Stock Check' }}</span>
          </div>
        </button>


          <button type="button" class="btn btn-primary" (click)="cancelImport()">Cancel</button>
        </div>

        <div *ngIf="financialLinesToImport">
          <button
            type="button"
            class="btn btn-success importVehicleDetails"
            [ngClass]="{ 'disable': validateImport() }"
            [ngbPopover]="missingFieldsPopover"
            [disablePopover]="!missingFieldsFromImport"
            triggers="mouseenter:mouseleave"
            (click)="bulkSaveFinancialLines(financialLinesToImport)">
            <div class="flex">
              <fa-icon [icon]="icon.faFilesMedical"></fa-icon>
              <span>{{ loadItemsService.multiSite ? 'Import to Stock Checks' : 'Import to this Stock Check' }}</span>
            </div>
          </button>
          <button type="button" class="btn btn-primary" (click)="cancelImport()">Cancel</button>
        </div>
      </div>
    </div>

    <div class="buttonGroup m-0">
      <span *ngIf="workSheetNames" class="sheetName">Sheet:</span>
      <button *ngFor="let name of workSheetNames" class="btn btn-primary" [ngClass]="{ 'active': chosenWorksheetName === name }"
        (click)="chooseNewWorksheet(name)">
        {{ name }}
      </button>
    </div>

    <!-- Header row count choice-->
    <div id="headerRowPicker" *ngIf="loadItemsService.chosenImportMask">

      <instructionRow [message]="'1. Choose how many rows in the source Excel file are header rows.  StockPulse will skip over these when finding items to import.'"></instructionRow>
     
      <div class="label">Header Rows to skip:</div>

      <div class="d-flex">
        <button class="btn btn-primary" (click)="changeHeaderRow(-1)">
          <fa-icon [icon]="icon.faMinusCircle"></fa-icon>
        </button>
        <input [ngModel]="loadItemsService.chosenImportMask.topRowsToSkip">

        <button class="btn btn-primary" (click)="changeHeaderRow(1)">
          <fa-icon [icon]="icon.faPlusCircle"></fa-icon>
        </button>

        <button *ngIf="loadItemsService.chosenReconcilingItemType.description === 'DMS Stock'"
          class="btn btn-primary ms-4"
          [ngClass]="{ 'highlight': ignoreZeroValueRows }"
          (click)="toggleIgnoreZeroValueRows()"
        >
          Ignore Zero Value Rows
        </button>
      </div>
    </div>

  </div>

  <div class="tablesHolder">
    <!-- The interpreted file -->
    <div id="excelTableHolderHolder">
    
      <div id="excelTableHolder" *ngIf="loadItemsService.importFile">

        <instructionRow *ngIf="loadItemsService.chosenImportMask" [message]="'2. Click a column letter label to set a filter on which rows to import.'"></instructionRow>
        
        <!-- Table -->
        <div id="excelMockTableContainer">
          <table id="excelMockTable">
            <thead>
              <tr class="lettersRow">
                <th id="indexHeader">
                  <!-- <div id="hint" [ngbPopover]="HintPopover" container="body" triggers="mouseenter:mouseleave" popoverClass="solidBackground hintPopover"  [openDelay]="300" [closeDelay]="500"  
                    popoverTitle="Mapping columns" placement="right" popoverClass="solid">
                    <fa-icon [icon]="icon.faQuestion"></fa-icon>
                  </div> -->

                </th>
                <th (click)="editColumnFilter(each.letter)" [ngClass]="{'highlight':checkCellHighlight(j+1)}"
                  *ngFor="let each of excelColumnLetters;let j = index">
                  <div class="headerLetterHolder">
                    <div>{{each.letter}}</div>
                    <div *ngIf="each.columnIsFiltered" class="headerFilterIndicator">
                      <fa-icon [icon]="icon.faFilter"></fa-icon>
                    </div>
                  </div>

                </th>
              </tr>
              <tr class="headingRow" *ngFor="let eachRow of excelColumnHeadingRows;let i = index" [ngStyle]="{ 'top': (2.2 * (i+1)) + 'em' }">
                <th>{{i+1}}</th>
                <th [ngClass]="{'highlight':checkCellHighlight(j+1)}" *ngFor="let each of eachRow; let j = index">{{each}}
                </th>
              </tr>
            </thead>
            <tbody [attr.data-header-rows]="excelColumnHeadingRows.length">
              <tr *ngFor="let eachRow of excelBodyData;let i = index">
                <td>{{i+1+excelColumnHeadingRows.length}}</td>
                <td [ngClass]="{'highlight':checkCellHighlight(j+1)}" 
                    *ngFor="let each of eachRow; let j = index">
                  {{each}}
                </td>
              </tr>
              <tr *ngIf="excelBodyData">
                <td>...</td>
                <td [ngClass]="{'highlight':checkCellHighlight(j+1)}" 
                    *ngFor="let each of excelBodyData[0]; let j = index">
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- The resulting table this.loadItemsService.haveChosenToShowStock-->
    <div *ngIf="loadItemsService.chosenImportMask" id="tableHolder">
      <div id="bottomInstructionRowContainer">
        <instructionRow  [message]="bottomTableMessage()" ></instructionRow>
      </div>
      
      <importTable *ngIf="stockItemsToImport" [rowData]="stockItemsToImport" #importTable ></importTable>
      <importTable *ngIf="financialLinesToImport" [rowData]="financialLinesToImport" #importTable ></importTable>
      <importTable *ngIf="reconcilingItemsToImport" [rowData]="reconcilingItemsToImport" #importTable ></importTable>
    </div>
  </div>


<!-- Save Import Mask Modal -->

<ng-template #saveMaskChoiceModal let-modal>

  <div class="modal-header">
    <h4 class="modal-title" id="modal-basic-title">
      Save Import Map
    </h4>
    <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body ">
    <table class="cph glowRows saveTable ">
      <h4>Choose Map To Replace</h4>

      <tbody>

        <tr (click)="probablyOverwriteImportMask(null)">

          <td>
            <fa-icon [icon]="icon.faPlusCircle"></fa-icon>
            Save as New Map
          </td>

          <td>
            <div>
              <fa-icon [icon]="icon.faSave"></fa-icon>
            </div>
          </td>
        </tr>

        <!-- Existing maps -->
        <tr *ngFor="let importMask of constants.ImportMasks" (click)="probablyOverwriteImportMask(importMask)">
          <td>
            {{importMask.name}}
          </td>

          <td>
            <div>
              <fa-icon [icon]="icon.faSave"></fa-icon>
            </div>
          </td>
        </tr>
        
       
      </tbody>

    </table>





  </div>
  <div class="modal-footer">
    <button type="button" class="btn btn-primary" (click)="modal.dismiss('Cancelled')">Close</button>
  </div>

</ng-template>

<!-- New map name modal -->
<ng-template #saveMaskNameModal let-modal>

  <div class="modal-header">
    <h4 class="modal-title" id="modal-basic-title">
      Choose Map Name
    </h4>
    <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body ">

    <input class="mapName" ngbAutofocus [(ngModel)]="newMapName" maxlength="200" />

    <div *ngIf="selections.userRole === 'SysAdministrator'">
      <span>Set as standard?</span>
      <button class="custom-checkbox" [ngClass]="{ 'checked': loadItemsService.chosenImportMask.isStandard }"
        (click)="loadItemsService.chosenImportMask.isStandard = !loadItemsService.chosenImportMask.isStandard">
        <fa-icon *ngIf="loadItemsService.chosenImportMask.isStandard" [icon]="icon.faCheck"></fa-icon>
      </button>
    </div>

  </div>
  <div class="modal-footer">
    <button type="button" class="btn btn-success" (click)="modal.close()"
      [disabled]="!newMapName || (newMapName && newMapName === '')">Save</button>
    <button type="button" class="btn btn-primary" (click)="modal.dismiss('Cancelled')">Close</button>
  </div>

</ng-template>




<!-- Filter rows modal -->
<ng-template #filterRowsModal let-modal>

  <div class="modal-header">
    <h4 class="modal-title" id="modal-basic-title">
      Filters for column {{chosenColumnLetter}}
    </h4>
    <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">

    <h4>Only import rows where</h4>

    <table id="importFilters">
      <tbody>
        <tr>
          <td>Column {{chosenColumnLetter}} includes the text</td>
          <td><input (ngModelChange)="updateColumnValueEquals(chosenColumnLetter, $event)"
              [ngModel]="chosenColumnColumnValueEqualses.matchingValue" /> </td>
        </tr>
        <tr>
          <td>Column {{chosenColumnLetter}} does not include the text</td>
          <td><input (ngModelChange)="updateColumnValueDifferentFrom(chosenColumnLetter, $event)"
              [ngModel]="chosenColumnValueDifferentFroms.matchingValue" /> </td>
        </tr>
        <tr>
          <td>Column {{chosenColumnLetter}} is not empty</td>
          <td>
            <button class="custom-checkbox" [ngClass]="{ 'checked': chosenColumnValueNotNulls }" 
              (click)="updateColumnValueNotNull(chosenColumnLetter, !chosenColumnValueNotNulls)">
              <fa-icon *ngIf="chosenColumnValueNotNulls" [icon]="icon.faCheck"></fa-icon>
            </button>
          </td>
        </tr>
      </tbody>

    </table>



  </div>
  <div class="modal-footer">
    <button type="button" class="btn btn-success" (click)="modal.close()">OK</button>
    <button type="button" class="btn btn-primary" (click)="modal.dismiss('Cancelled')">Cancel</button>
  </div>

</ng-template>

<!-- <ng-template #HintPopover>
  Click a column letter label to set a filter on which rows to import.
</ng-template> -->

<ng-template #missingFieldsPopover>
  You are missing the following fields:

  <p *ngFor="let field of missingFieldsFromImport">
    - {{ field }}
  </p>
</ng-template>

<!-- Maybe load to all stock checks modal -->
<ng-template #importToAllStockChecksModal let-modal>
  <div class="modal-header">
    <h4 class="modal-title" id="modal-basic-title">
      Import to all stock checks
    </h4>
    <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <span>Would you like to import these vehicles into '{{loadItemsService.chosenReconcilingItemType.description}}' for all active stock checks?</span>
  </div>
  <div class="modal-footer">
    <button type="button" class="btn btn-success" (click)="modal.close()">OK</button>
    <button type="button" class="btn btn-primary" (click)="modal.dismiss('Cancelled')">Continue with just this site</button>
  </div>
</ng-template>













<ng-template #maybeImportModal let-modal>
  <div class="modal-header">
    <h4 class="modal-title" id="modal-basic-title">
      Import data to one or multiple Stock Checks
    </h4>
    <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    
    <div class="d-inline-block" ngbDropdown container="body" [autoClose]="false">
      <button id="dropdown" class="btn btn-primary d-flex align-items-center justify-content-between" type="button"
          ngbDropdownToggle [disabled]="disabled" [ngStyle]="{ 'width.px': width }">
          {{ label }}
      </button>
      <div id="sitesDropdownMenu" aria-labelledby="dropdown" ngbDropdownMenu>
        <input type="text" placeholder="Search..." [(ngModel)]="searchString" (ngModelChange)="searchList()">
          <div id="sitesMenuItems">
              <ng-container *ngFor="let sc of stockChecksForPicker">
                  <button class="btn btn-primary" (click)="toggleItem(sc)">
                      <span *ngIf="sc.isSelected" class="me-2">
                          <fa-icon [icon]="icon.faCheckSquare" class="checkboxIcon"></fa-icon>
                      </span>
                      <span *ngIf="!sc.isSelected" class="me-2">
                          <fa-icon [icon]="icon.faSquare" class="checkboxIcon"></fa-icon>
                      </span>
                      {{ sc.description }}
                  </button>
              </ng-container>
          </div>
          <div id="selectAll">
              <button class="btn btn-primary" (click)="toggleStockChecks()">
                  All
              </button>
          </div>
          <div id="confirmCancelButtons">
              <button class="btn btn-primary toggleNoCaret" ngbDropdownToggle>OK</button>
              <button class="btn btn-primary toggleNoCaret" ngbDropdownToggle (click)="clearSelection()">Cancel</button>
          </div>
      </div>
  </div>    

  </div>
  <div class="modal-footer">
    <button type="button" class="btn btn-success" [disabled]="selectedStockChecks?.length === 0" (click)="modal.close()">OK</button>
    <button type="button" class="btn btn-primary" (click)="modal.dismiss('Cancelled')">Cancel</button>
  </div>
</ng-template>
