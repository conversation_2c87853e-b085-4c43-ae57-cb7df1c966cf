﻿using Dapper;
using StockPulse.Loader.Services;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;


namespace StockPulse.Loader
{
    public class Dapperr : IDapper
    {
        //private readonly IConfiguration _config;

        //private string Connectionstring = "DefaultConnection";

        public Dapperr()
        {
            //_config = config;
        }
        public void Dispose()
        {

        }

        public async Task<DataTable> GetDataTableAsync(string query, DynamicParameters parms, CommandType commandType = CommandType.StoredProcedure, bool isUS = false)
        {
            var dataTable = new DataTable();
            using SqlConnection conn = isUS ? new SqlConnection(ConfigService.connectionNameUS) : new SqlConnection(ConfigService.connectionNameUK);
            using SqlCommand cmd = new SqlCommand(query, conn);
            cmd.CommandType = commandType;
            using SqlDataAdapter da = new SqlDataAdapter(cmd);
            await Task.Run(() => da.Fill(dataTable));
            return dataTable;
        }

        //public async Task<DealsAndComments> GetMultipleAsync(string sp, DynamicParameters parms, CommandType commandType = CommandType.StoredProcedure)
        //{
        //    DealsAndComments dealsAndComments = new DealsAndComments();
        //    using IDbConnection db = new SqlConnection(_config.GetConnectionString(Connectionstring));
        //    using var result = await db.QueryMultipleAsync(sp, parms, commandType: commandType);
        //    dealsAndComments.deals = result.Read<>

        //}

        public async Task<int> ExecuteWithConnectionAsync(string sp, DynamicParameters parms, CommandType commandType = CommandType.StoredProcedure, bool isUS = false)
        {
            using IDbConnection db = isUS ? new SqlConnection(ConfigService.connectionNameUS) : new SqlConnection(ConfigService.connectionNameUK);
            return await db.ExecuteAsync(sp, parms, commandType: commandType, commandTimeout: 300);
        }
        
        public async Task<int> ExecuteAsync(string sp, DynamicParameters parms, CommandType commandType = CommandType.StoredProcedure, bool isUS = false)
        {
            using IDbConnection db = isUS ? new SqlConnection(ConfigService.connectionNameUS) : new SqlConnection(ConfigService.connectionNameUK);
            return await db.ExecuteAsync(sp, parms, commandType: commandType, commandTimeout: 300);
        }

        public T Get<T>(string sp, DynamicParameters parms, CommandType commandType = CommandType.Text, bool isUS = false)
        {
            using IDbConnection db = isUS ? new SqlConnection(ConfigService.connectionNameUS) : new SqlConnection(ConfigService.connectionNameUK);
            return db.Query<T>(sp, parms, commandType: commandType).FirstOrDefault();
        }

        public async Task<T> GetAsync<T>(string sp, DynamicParameters parms, CommandType commandType = CommandType.StoredProcedure, bool isUS = false)
        {
            using IDbConnection db = isUS ? new SqlConnection(ConfigService.connectionNameUS) : new SqlConnection(ConfigService.connectionNameUK);
            return await db.QueryFirstOrDefaultAsync<T>(sp, parms, commandType: commandType);
        }

        public IEnumerable<T> GetAll<T>(string sp, DynamicParameters parms, CommandType commandType = CommandType.StoredProcedure, bool isUS = false)
        {
            using IDbConnection db = isUS ? new SqlConnection(ConfigService.connectionNameUS) : new SqlConnection(ConfigService.connectionNameUK);
            return db.Query<T>(sp, parms, commandType: commandType);
        }

        public async Task<IEnumerable<T>> GetAllAsync<T>(string sp, DynamicParameters parms, CommandType commandType = CommandType.StoredProcedure, bool isUS = false)
        {
            using IDbConnection db = isUS ? new SqlConnection(ConfigService.connectionNameUS) : new SqlConnection(ConfigService.connectionNameUK);
            return await db.QueryAsync<T>(sp, parms, commandType: commandType);
        }


        public DbConnection GetDbconnection(bool isUS = false)
        {
            if(isUS)
            {
                return new SqlConnection(ConfigService.connectionNameUS);
            }
            else
            {
                return new SqlConnection(ConfigService.connectionNameUK);
            }
            
        }

        public async Task<T> InsertAsync<T>(string sp, DynamicParameters parms, IDbTransaction tran, IDbConnection db, CommandType commandType = CommandType.StoredProcedure)
        {
            return await db.QueryFirstOrDefaultAsync<T>(sp, parms, commandType: commandType, transaction: tran);
        }

        public T Insert<T>(string sp, DynamicParameters parms, CommandType commandType = CommandType.StoredProcedure, bool isUS = false)
        {
            T result;

            using IDbConnection db = isUS ? new SqlConnection(ConfigService.connectionNameUS) : new SqlConnection(ConfigService.connectionNameUK);

            try
            {
                if (db.State == ConnectionState.Closed)
                    db.Open();

                using var tran = db.BeginTransaction();
                try
                {
                    result = db.Query<T>(sp, parms, commandType: commandType, transaction: tran).FirstOrDefault();
                    tran.Commit();
                }
                catch (Exception ex)
                {
                    tran.Rollback();
                    throw ex;
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
            finally
            {
                if (db.State == ConnectionState.Open)
                    db.Close();
            }

            return result;
        }

        public async Task<T> InsertAsync<T>(string sp, DynamicParameters parms, CommandType commandType = CommandType.StoredProcedure, bool isUS = false)
        {
            T result;
            using IDbConnection db = isUS ? new SqlConnection(ConfigService.connectionNameUS) : new SqlConnection(ConfigService.connectionNameUK);

            try
            {
                if (db.State == ConnectionState.Closed)
                    db.Open();

                using var tran = db.BeginTransaction();
                try
                {
                    result = await db.QueryFirstOrDefaultAsync<T>(sp, parms, commandType: commandType, transaction: tran);
                    tran.Commit();
                }
                catch (Exception ex)
                {
                    tran.Rollback();
                    throw ex;
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
            finally
            {
                if (db.State == ConnectionState.Open)
                    db.Close();
            }

            return result;
        }

        public T Update<T>(string sp, DynamicParameters parms, CommandType commandType = CommandType.StoredProcedure, bool isUS = false)
        {
            T result;
            using IDbConnection db = isUS ? new SqlConnection(ConfigService.connectionNameUS) : new SqlConnection(ConfigService.connectionNameUK);

            try
            {
                if (db.State == ConnectionState.Closed)
                    db.Open();

                using var tran = db.BeginTransaction();
                try
                {
                    result = db.Query<T>(sp, parms, commandType: commandType, transaction: tran).FirstOrDefault();
                    tran.Commit();
                }
                catch (Exception ex)
                {
                    tran.Rollback();
                    throw ex;
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
            finally
            {
                if (db.State == ConnectionState.Open)
                    db.Close();
            }

            return result;
        }
    }
}
