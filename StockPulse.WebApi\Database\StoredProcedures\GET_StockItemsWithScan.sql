

DROP PROC IF EXISTS [dbo].[GET_StockItemsWithScan]  
GO
CREATE OR ALTER PROCEDURE [dbo].[GET_StockItemsWithScan]  
(  
    @StockCheckId INT = NULL,  
    @UserId INT = NULL   
)  
AS  
BEGIN  
  
SET NOCOUNT ON;  
  
  
DECLARE @isRegional INT;    
DECLARE @isTotal INT;    
DECLARE @StockCheckDate DateTime;  
DECLARE @DivisionId INT = NULL;  
DECLARE @DealerGroupId INT = NULL;  
DECLARE @SCId INT = NULL;  
  
  
IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END

  
SELECT   
@isRegional = IsRegional,   
@isTotal = IsTotal,   
@StockCheckDate = Date   
FROM StockChecks    
WHERE StockChecks.Id = @StockCheckId  
  
  

IF @isRegional = 0 AND @isTotal = 0  
  
    BEGIN  
   
 SET @SCId = @StockCheckId;  
  
  
  
  
    END  
  
ELSE IF @isRegional = 1 AND @isTotal = 0  
  
    BEGIN  
  
  
    SET @DivisionId = (SELECT DivisionId FROM StockChecks  
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId  
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id  
                        WHERE StockChecks.Id = @StockCheckId AND StockChecks.IsRegional = 1)  
  
      
  
    END  
  
ELSE IF @isRegional = 0 AND @isTotal = 1  
  
    BEGIN  
  
      
    SET @DealerGroupId = (SELECT DealerGroupId FROM StockChecks  
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId  
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id  
                        WHERE StockChecks.Id = @StockCheckId)  
  
     
  
    END  
  
    SELECT   
    st.[Id] as StockItemId, 
    st.Reg,
    st.Vin,
    st.Description, 
    st.DIS,
    st.GroupDIS, 
    st.Branch, 
    st.StockType, 
    st.Comment, 
    st.Reference, 
    st.StockValue,  
    S.Description as ScanDescription,   
    usrs.Name as ScannedBy,  
    S.ScanDateTime,  
    locns.Description as LocationDescription,  
    S.Id as ScanId, 
    st.Flooring, 
    Sites.Description AS SiteName  ,
    s.Longitude,s.Latitude,
    Sites.Longitude as StockCheckLongitude,
    Sites.Latitude as StockCheckLatitude,
    CASE
        WHEN S.IsRegEditedOnWeb = 1 THEN 'Web app'
        WHEN S.IsRegEditedOnDevice = 1 THEN 'Mobile app'
        ELSE NULL
    END AS RegEditStatus,
    CASE
        WHEN S.IsVinEditedOnWeb = 1 THEN 'Web app'
        WHEN S.IsVinEditedOnDevice = 1 THEN 'Mobile app'
        ELSE NULL
    END AS VinEditStatus
  
    from StockItems  st   
    inner join Scans AS S on S.Id = st.ScanId   
    inner join Users usrs on usrs.Id = S.UserId  
    inner join Locations locns on locns.Id = S.LocationId  
    INNER JOIN StockChecks AS SC ON SC.Id=S.StockCheckId  
    INNER JOIN Sites ON SC.SiteId=Sites.Id  
 INNER JOIN Divisions AS D ON D.Id=Sites.DivisionId  
 INNER JOIN DealerGroup AS DG ON D.DealerGroupId=DG.Id  
    WHERE   
 --S.StockCheckId = @StockCheckId   
    ScanId is not null    
    --AND st.StockCheckId = @StockCheckId  
 AND S.StockCheckId = St.StockCheckId  
 AND SC.Id = ISNULL(@SCId, SC.Id)  
 AND SC.Date = @StockCheckDate  
 AND D.Id = ISNULL(@DivisionId, D.Id)  
 AND DG.Id = ISNULL(@DealerGroupId, DG.Id)  

   
   
END  
  


GO