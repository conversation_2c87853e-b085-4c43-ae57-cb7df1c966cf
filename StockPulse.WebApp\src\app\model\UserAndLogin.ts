

export interface UserAndLogin {
  appUserId?: string;
  code?: number;
  name: string;
  nameShort: string;
  userName: string;
  roleName: string; //The 'Name' of the item within AspNetRoles.  Ensure manually validate.
  email: string;
  sites: string;
  siteCode: number;
  newEmail?: string;
  employeeNumber?: string;

  isDeleted: boolean;
  isLocked: boolean;

  newValues?: {
    name: string;
    nameShort: string;
    roleName: string; //The 'Name' of the item within AspNetRoles.  Ensure manually validate.
    sites: string;
    siteCode: number;
    email?: string;
    employeeNumber?: string;
  };
  originalValues?: {
    name: string;
    nameShort: string;
    roleName: string; //The 'Name' of the item within AspNetRoles.  Ensure manually validate.
    sites: string;
    siteCode: number;
    email?: string;
    employeeNumber?: string;
  };
  hasChanged?: boolean;
}
