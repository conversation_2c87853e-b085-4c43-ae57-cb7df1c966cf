﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace StockPulse.WebApi.Migrations
{
    /// <inheritdoc />
    public partial class addSiteDescriptionDictionary : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "SiteDescriptionDictionary",
                schema: "import",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false),
                    Description = table.Column<string>(type: "nvarchar(250)", maxLength: 250, nullable: true),
                    DealerGroupId = table.Column<int>(type: "int", nullable: false),
                    SiteId = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.ForeignKey(
                        name: "FK_SiteDescriptionDictionary_DealerGroup_DealerGroupId",
                        column: x => x.DealerGroupId,
                        principalSchema: "dbo",
                        principalTable: "DealerGroup",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_SiteDescriptionDictionary_Sites_SiteId",
                        column: x => x.SiteId,
                        principalSchema: "dbo",
                        principalTable: "Sites",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_SiteDescriptionDictionary_DealerGroupId",
                schema: "import",
                table: "SiteDescriptionDictionary",
                column: "DealerGroupId");

            migrationBuilder.CreateIndex(
                name: "IX_SiteDescriptionDictionary_SiteId",
                schema: "import",
                table: "SiteDescriptionDictionary",
                column: "SiteId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "SiteDescriptionDictionary",
                schema: "import");
        }
    }
}
