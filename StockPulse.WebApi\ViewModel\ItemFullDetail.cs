﻿
using System;
using System.Collections.Generic;

namespace StockPulse.WebApi.ViewModel
{
    public class ItemFullDetail 
    {
        public ItemFullDetail(ScanFullDetail scan)
        {
            Scan = new Scan(scan);
            if (scan.StockItemId!=null && (int)scan.StockItemId>0)
            {
                StockItem = new StockItem(scan);
            }

            SiteName = scan.SiteName;

            ReconcilingItemId = scan.ReconcilingItemId;
            ReconcilingItemTypeId = scan.ReconcilingItemTypeId;
            ReconcilingItemTypeDescription = scan.ReconcilingItemTypeDescription;
            ReconcilingItemReg = scan.ReconcilingItemReg;
            ReconcilingItemVin = scan.ReconcilingItemVin;
            ReconcilingItemDesc = scan.ReconcilingItemDesc;
            ReconcilingItemComment = scan.ReconcilingItemComment;
            ReconcilingItemRef = scan.ReconcilingItemReference;
            ResolutionId = scan.ResolutionId;
            ResolutionTypeId = scan.ResolutionTypeId;
            ResolutionTypeDescription = scan.ResolutionTypeDescription;
            ResolutionTypeBackup = scan.ResolutionTypeBackup;
            IsResolved = scan.IsResolved;
            ResolvedBy = scan.ResolvedBy;
            ResolutionDate = scan.ResolutionDate;
            ResolutionNotes = scan.ResolutionNotes;
            ResolutionImageIds = scan.ResolutionImageIds;
            OtherSiteName = scan.SiteName;
            OriginalId = scan.OriginalId;
            OriginalLocationDescription = scan.OriginalLocationDescription;
            OriginalScannedBy = scan.OriginalScannedBy;
            OriginalScannedDate = scan.OriginalScannedDate;
            IsRegEditedOnDevice = scan.IsRegEditedOnDevice;
            IsRegEditedOnWeb = scan.IsRegEditedOnWeb;
            InterpretedReg = scan.InterpretedReg;
            IsVinEditedOnDevice = scan.IsVinEditedOnDevice;
            IsVinEditedOnWeb = scan.IsVinEditedOnWeb;
            InterpretedVin = scan.InterpretedVin;
        }

        public ItemFullDetail(StockItemFullDetail stockItem)
        {
            StockItem = new StockItem(stockItem);
            if (stockItem.ScanId!=null)
            {
                Scan = new Scan(stockItem);
                Scan.DistanceFromDealershipInMiles = (decimal)stockItem.DistanceFromDealershipInMiles;
            }

            SiteName = stockItem.Site;
            ReconcilingItemId = stockItem.ReconcilingItemId;
            ReconcilingItemTypeId = stockItem.ReconcilingItemTypeId;
            ReconcilingItemTypeDescription = stockItem.ReconcilingItemTypeDescription;
            
            ReconcilingItemDesc = stockItem.ReconcilingItemDesc;
            ReconcilingItemReg = stockItem.ReconcilingItemReg;
            ReconcilingItemVin = stockItem.ReconcilingItemVin;
            ReconcilingItemComment = stockItem.ReconcilingItemComment;
            ReconcilingItemRef = stockItem.ReconcilingItemRef;
            ResolutionId = stockItem.ResolutionId;
            ResolutionTypeId = stockItem.ResolutionTypeId;
            ResolutionTypeDescription = stockItem.ResolutionTypeDescription;
            ResolutionTypeBackup = stockItem.ResolutionTypeBackup;
            IsResolved = stockItem.IsResolved;
            ResolvedBy = stockItem.ResolvedBy;
            ResolutionDate = stockItem.ResolutionDate;
            ResolutionNotes = stockItem.ResolutionNotes;
            ResolutionImageIds = stockItem.ResolutionImageIds;
            OtherSiteName = stockItem.ScanSiteName;
            OriginalId = stockItem.OriginalId;
            OriginalStockType = stockItem.OriginalStockType;
            OriginalComment = stockItem.OriginalComment;
            OriginalReference = stockItem.OriginalReference;
            IsRegEditedOnDevice = stockItem.IsRegEditedOnDevice;
            IsRegEditedOnWeb = stockItem.IsRegEditedOnWeb;
            InterpretedReg = stockItem.InterpretedReg;
            IsVinEditedOnDevice = stockItem.IsVinEditedOnDevice;
            IsVinEditedOnWeb = stockItem.IsVinEditedOnWeb;
            InterpretedVin = stockItem.InterpretedVin;
    }


        public StockItem StockItem { get; set; }
        public Scan Scan { get; set; }
        
        //Site that it relates to
        public string SiteName { get; set; }


        //Reconciling item things
        public int ReconcilingItemId { get; set; }
        public int ReconcilingItemTypeId { get; set; }
        public string ReconcilingItemTypeDescription { get; set; }
        
        public string ReconcilingItemReg { get; set; }
        public string ReconcilingItemVin { get; set; }
        public string ReconcilingItemDesc { get; set; }
        public string ReconcilingItemComment { get; set; }
        public string ReconcilingItemRef { get; set; }

        

        //Resolution things
        public int ResolutionId { get; set; }
        public int ResolutionTypeId { get; set; }
        public string ResolutionTypeDescription { get; set; }
        public string ResolutionTypeBackup { get; set; }
        public bool IsResolved { get; set; }
        public string ResolvedBy { get; set; }
        public DateTime? ResolutionDate { get; set; }
        public string ResolutionNotes { get; set; }
        public string ResolutionImageIds { get; set; }
        


        //Matching Site name stuff
        public string OtherSiteName { get; set; }

        //Extra props if it is a duplicate
        public int OriginalId { get; set; }
        public string OriginalLocationDescription { get; set; }
        public string OriginalScannedBy { get; set; }
        public DateTime OriginalScannedDate { get; set; }
        public string OriginalStockType { get; set; }
        public string OriginalComment { get; set; }
        public string OriginalReference { get; set; }

        //resolution image URLS
        public List<ImageToUpdate> ResolutionImages { get; set; }

        public bool IsRegEditedOnDevice { get; set; }
        public bool IsRegEditedOnWeb { get; set; }
        public string InterpretedReg { get; set; }
        public bool IsVinEditedOnDevice { get; set; }
        public bool IsVinEditedOnWeb { get; set; }
        public string InterpretedVin { get; set; }

    }
}
