import { HttpClient } from '@angular/common/http';
import { Component, ElementRef, HostListener, OnInit, ViewChild } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ItemFullDetail } from 'src/app/model/ItemFullDetail';
import { ItemToOpen } from 'src/app/model/ItemToOpen';
import { ReconciliationState } from 'src/app/model/ReconciliationState';
import { ApiAccessService } from 'src/app/services/apiAccess.service';
import { ToastService } from 'src/app/services/newToast.service';
import { SaveDataService } from 'src/app/services/saveData.service';
import { CphPipe } from '../../cph.pipe';
import { ConstantsService } from '../../services/constants.service';
import { GetDataService } from '../../services/getData.service';
import { IconService } from '../../services/icon.service';
import { SelectionsService } from '../../services/selections.service';
import { VehicleModalService } from './vehicleModal.service';

interface FileWithBase64 extends File {
  base64: string;
}

@Component({
  selector: 'vehicleModal',
  templateUrl: './vehicleModal.component.html',
  styleUrls: ['./vehicleModal.component.scss']
})
export class VehicleModalComponent implements OnInit {
  // @Output() scanIdToUpdate = new EventEmitter<{ scanId: number, field: string, newValue: string }>();

  @ViewChild('vehicleModalRef', { static: true }) vehicleModalRef: ElementRef;
  @ViewChild('zoomImageModal', { static: true }) zoomImageModal: ElementRef;
  @ViewChild('changeLocationModal', { static: true }) changeLocationModal: ElementRef;


  modalRef: Promise<void>;
  modalIsOpen: boolean;



  isStockItem: boolean;
  item: ItemFullDetail;


  states = ReconciliationState
 
  idsToNavigateBetween: ItemToOpen[];
  currentVehicleIndex: number;


  @HostListener('window:keydown', ['$event'])
  onKeyDown(event: KeyboardEvent) {
    this.handleKeyboardEvent(event);
  }


  //apiLoaded: Observable<boolean>;
  location: string;
  amSavingReg: boolean;
  amSavingVin: boolean;
  fileSizeExceeded: boolean = false;
  fallbackTitle: string;

  constructor(

    public constants: ConstantsService,
    public selections: SelectionsService,
    public data: GetDataService,
    public modalService: NgbModal,
    public cphPipe: CphPipe,
    public icon: IconService,
    public save: SaveDataService,
    public apiAccessService: ApiAccessService,
    public http: HttpClient,
    public toastService: ToastService,
    public service: VehicleModalService
  ) { }



  ngOnInit() {
    
  }


 



  private handleKeyboardEvent(event: KeyboardEvent) {
    if (!this.modalIsOpen) { return; }
    if (!this.idsToNavigateBetween || this.idsToNavigateBetween.length < 1) { return; }
    if (this.service.amEditingReg || this.service.amEditingVin) return;


    //let scanOrStockItemId: number = this.item.scan ? this.item.scan.scanId : this.item.stockItem.stockItemId;
    //let currentVehicleIndex = this.getCurrentVehicleIndex();

    if (event.key === 'ArrowDown') {
      if (this.canGoNextItem()) {
        this.goToItem(false);
      }
    }

    if (event.key === 'ArrowUp') {
      if (this.canGoPreviousItem()) {
        this.goToItem(true);
      }
    }

  }


  public loadItemAndOpenModal(isStockItem: boolean, id: number, allIdsInList: ItemToOpen[], fallbackTitle?: string) {

    this.isStockItem = isStockItem;
    this.idsToNavigateBetween = allIdsInList;
    this.fallbackTitle = fallbackTitle;
    this.getDataAndShowModal(id, isStockItem, true);

  }


  private getDataAndShowModal(id: number, isStockItem: boolean, andShowModal: boolean) {
    this.toastService.loadingToast();

    const payload = [
      { key: 'stockCheckId', value: this.selections.stockCheck ? this.selections.stockCheck.id : 0 },
      { key: 'stockItemId', value: isStockItem ? id : 0 },
      { key: 'scanId', value: isStockItem ? 0 : id },
    ]
    this.apiAccessService.get('StockItems', 'GetItemFullDetail', payload).subscribe((res: ItemFullDetail) => {
      if (!!res.scan && !!res.scan.scanId) {
        //stockitem has a scan so generate the image url
        this.constants.addImageStringsToScan(res.scan)
      }
      this.item = res;
      this.toastService.destroyToast();
     
      setTimeout(()=>{
      },50)
      if (andShowModal) {
        this.showModal();
      }

    })

  }




  showModal() {



    this.modalIsOpen = true;
    this.modalService.open(this.vehicleModalRef, { windowClass: "", size: 'lg', keyboard: true, ariaLabelledBy: 'modal-basic-title' }).result.then((result) => { //reallyWideModal
      //'ok'
      this.selections.showVehicleModal = false;
      this.modalIsOpen = false;

    }, (reason) => {
      this.selections.showVehicleModal = false;
      this.modalIsOpen = false;
    });
  }



  closeModal() {
    this.modalService.dismissAll();
  }

  getCurrentVehicleIndex() {
    let currentVehicleIndex: number = -1;

    // this.idsToNavigateBetween.forEach((item, i) => {
    //   if (item.isStockItem && item.id === this.item.stockItem.stockItemId || !item.isStockItem && item.id === this.item.scan.scanId ){
    //     currentVehicleIndex = i;
    //   }
    // })

    let j=0;
    while(currentVehicleIndex===-1 && j < this.idsToNavigateBetween.length){
      const item = this.idsToNavigateBetween[j];
      if (item.isStockItem && item.id === this.item.stockItem.stockItemId || !item.isStockItem && item.id === this.item.scan.scanId ){
        currentVehicleIndex = j;
      }
      j++;
    }

    this.currentVehicleIndex = currentVehicleIndex;

    return currentVehicleIndex;
  }

  goToItem(previous: boolean) {
    if (this.service.amEditingReg || this.service.amEditingVin) return;
    
    this.currentVehicleIndex = this.getCurrentVehicleIndex();
    const newItem = this.idsToNavigateBetween[previous ? this.currentVehicleIndex - 1 : this.currentVehicleIndex + 1];
    this.getDataAndShowModal(newItem.id,newItem.isStockItem,  false);
  }

  canGoPreviousItem(){
    this.currentVehicleIndex = this.getCurrentVehicleIndex();
    return this.currentVehicleIndex!==0
  }

  canGoNextItem(){
    this.currentVehicleIndex = this.getCurrentVehicleIndex();
    return this.currentVehicleIndex < this.idsToNavigateBetween.length - 1;
  }

}
