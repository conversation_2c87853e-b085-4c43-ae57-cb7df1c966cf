﻿using Microsoft.ApplicationInsights;
using Microsoft.ApplicationInsights.Extensibility;
using Microsoft.Extensions.Configuration;
using StockPulse.WebApi.DataAccess;

using StockPulse.WebApi.ViewModel;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using static StockPulse.WebApi.ViewModel.SearchResult;

namespace StockPulse.WebApi.Service
{
    public interface IStockItemService
    {
        Task<IEnumerable<ViewModel.StockItem>> GetStockItems(int stockcheckId, int userId);
        Task<IEnumerable<ViewModel.StockItem>> GetDMSStockItems(int stockcheckId, int userId);
        Task<IEnumerable<ViewModel.StockItem>> GetAgencyStockItems(int stockcheckId, int userId);
        Task<IEnumerable<ViewModel.StockItemThatIsADuplicate>> GetDuplicateStockItems(int stockcheckId, int userId);
        Task<IEnumerable<StockItemWithLocation>> GetStocksWithLocation(int stockcheckId, int userId);
        Task<IEnumerable<StockItemWithStockType>> GetStocksWithStockType(int stockcheckId, int userId);
        Task<IEnumerable<StockItemWithResolution>> GetStockWithResolution(int stockcheckId, int userId);
        Task<IEnumerable<StockLoadReportSummary>> GetStockReport(int stockcheckId, int userId);
        Task<IEnumerable<ReconciliationBucket>> GetReconciliationBucket(int stockcheckId, int userId);
        Task<IEnumerable<ResolutionBucket>> GetResolutionBucket(int stockcheckId, int userId);
        Task<IEnumerable<StockItemWithScan>> GetStockItemsWithScan(int stockcheckId, int userId);
        Task<IEnumerable<StockItemMatchedToRecItem>> GetMissingMatched(int stockcheckId, int userId);
        Task<IEnumerable<StockItemMatchedToRecItem>> GetStockItemsMatchedToRecItem(int stockCheckId, int reconcilingItemTypeId, int userId);
        Task<IEnumerable<StockItemWithResolution>> GetStockItemsWithResolution(int stockcheckId, int userId);
        Task<IEnumerable<StockItemMatchedToOtherSiteScan>> GetStockItemMatchedToOtherSiteScan(int stockcheckId, int userId);
        Task<int> DeleteAllDMSStockItems(int stockcheckId, int userId);
        Task<int> DeleteAllAgencyStockItems(int stockcheckId, int userId);
        Task<int> GetTotalItems(int stockItemId, int userId);
        Task SaveMissingResolution(Resolution missingResolutionVM, int userId);
        //Task AddStockItem(ViewModel.StockItem newStockItem, int stockCheckId, int userId);
        Task AddStockItems(List<ViewModel.StockItem> newStockItem, int stockCheckId, int userId, int fileImportId);
        Task<StockConsignmentVM> GetStockConsignment(int stockcheckId, int userId);
        //Task<IEnumerable<SearchResultVM>> GetSearchStockItem(int stockcheckId, int userId, string reg, string vin);
        Task DeleteMissingResolution(int resolutionId, int stockCheckId, int userId);
        Task<ItemFullDetail> GetItemFullDetail(int? stockItemId, int? scanId, int? stockCheckId, int userId);
        Task<IEnumerable<GlobalSearchResultItem>> GetGlobalSearchResults(int userId, string reg, string vin, bool requireAndMatch);
        List<StockItemWithResolution> GetRepeatMissings(List<int> stockCheckIds, int userId);
        Task<int> DeleteStockItem(int stockcheckId, int userId, int itemId);
        Task<int> GetTotalStockItemsCount(List<int> stockCheckIds, int userId);
        Task<int> GetAgencyStock(int stockCheckId, int userId);
        Task<IEnumerable<ViewModel.StockItem>> GetStockItemsForSite(int siteId);
    }

    public class StockItemService : IStockItemService
    {
        //properties of the service
        private readonly IStockItemDataAccess stockItemDataAccess;
        private readonly IStockCheckDataAccess stockCheckDataAccess;
        private readonly IScanDataAccess scanDataAccess;
        private readonly IImageService imageService;
        private readonly IConfiguration _config;
        private string Connectionstring = "DefaultConnection";

        //constructor
        public StockItemService(
            IStockItemDataAccess stockItemDataAccess,
            IStockCheckDataAccess stockCheckDataAccess,
            IImageService imageService,
            IConfiguration config,
            IScanDataAccess scanDataAccess
            )
        {
            this.stockItemDataAccess = stockItemDataAccess;
            this.stockCheckDataAccess = stockCheckDataAccess;
            this.imageService = imageService;
            this._config = config;
            this.scanDataAccess = scanDataAccess;
        }

        //methods of the service
        public async Task<IEnumerable<ViewModel.StockItem>> GetStockItems(int stockcheckId, int userId)
        {

            IEnumerable<ViewModel.StockItem> result = await stockItemDataAccess.GetStockItems(stockcheckId, userId);
            return result;
        }

        public async Task<IEnumerable<ViewModel.StockItem>> GetDMSStockItems(int stockcheckId, int userId)
        {

            IEnumerable<ViewModel.StockItem> result = await stockItemDataAccess.GetStockItems(stockcheckId, userId);
            return result.Where(x => !x.IsAgencyStock);
        }

        public async Task<IEnumerable<ViewModel.StockItem>> GetAgencyStockItems(int stockcheckId, int userId)
        {
            IEnumerable<ViewModel.StockItem> allItems = await stockItemDataAccess.GetStockItems(stockcheckId, userId);

            return allItems.Where(x => x.IsAgencyStock);
        }

        public async Task<IEnumerable<ViewModel.StockItemThatIsADuplicate>> GetDuplicateStockItems(int stockcheckId, int userId)
        {
            return await stockItemDataAccess.GetStockItemsThatAreDuplicates(stockcheckId, userId);
        }


        public async Task<ItemFullDetail> GetItemFullDetail(int? stockItemId, int? scanId, int? stockCheckId, int userId)
        {
            //get the scan and return
            if (scanId != 0 && scanId != null)
            {
                ScanFullDetail scan = await scanDataAccess.GetFullScanDetailAcrossSites((int)scanId, userId);
                CoordinateSet stockCheckCoordinates = await stockCheckDataAccess.GetStockCheckCoordinates(scan.StockCheckId);

                if (scan != null)
                {   
                    scan.DistanceFromDealershipInMiles = ConstantsService.CalculateScanDistance(stockCheckCoordinates.Longitude, stockCheckCoordinates.Latitude, scan.Longitude, scan.Latitude);

                    var result = new ItemFullDetail(scan);
                    BuildOutImageURLs(result, false);
                    return result;
                }
            }

            //if no scan, get the stockItem and return
            if (stockItemId != null && stockItemId != 0)
            {
                StockItemFullDetail stockItem = await stockItemDataAccess.GetStockItemFullDetailAcrossSites((int)stockItemId, userId);
                CoordinateSet stockCheckCoordinates = await stockCheckDataAccess.GetStockCheckCoordinates(stockItem.StockCheckId);
                stockItem.DistanceFromDealershipInMiles = ConstantsService.CalculateScanDistance(stockCheckCoordinates.Longitude, stockCheckCoordinates.Latitude, stockItem.Longitude, stockItem.Latitude);
                if (stockItem != null)
                {
                    var result = new ItemFullDetail(stockItem);
                    BuildOutImageURLs(result, true);
                    return result;
                }

            }

            //if still here, we have a problem
            throw new Exception("Not found");
        }

        private void BuildOutImageURLs(ItemFullDetail scan, bool isStockItem)
        {
            //now fill out the urls for the missing resolutions
            scan.ResolutionImages = new List<ImageToUpdate>();
            if (scan.ResolutionImageIds != null)
            {

                foreach (var resolutionImage in scan.ResolutionImageIds.Split("::"))
                {
                    var resolutionId = resolutionImage.Split("|")[0];
                    var resolutionImageName = resolutionImage.Split("|")[1];
                    string imageUrl = string.Empty;

                    if (isStockItem)
                    {
                        imageUrl = imageService.GetMissingImageURL(resolutionId);
                    }
                    else
                    {
                        imageUrl = imageService.GetUnknownImageURL(resolutionId);
                    }

                    scan.ResolutionImages.Add(new ImageToUpdate() { Id = int.Parse(resolutionId), Status = "BLOB", FileBase64 = "", Url = imageUrl, FileName = resolutionImageName });
                }
            }
        }

        public async Task<int> GetTotalItems(int stockCheckId, int userId)
        {
           return await stockItemDataAccess.GetTotalItems(stockCheckId, userId);
        }

        public async Task<int> GetAgencyStock(int stockCheckId, int userId)
        {
            return await stockItemDataAccess.GetAgencyStock(stockCheckId, userId);
        }

        // For Resulting Charts page
        public async Task<IEnumerable<StockItemWithLocation>> GetStocksWithLocation(int stockcheckId, int userId)
        {
            return await stockItemDataAccess.GetStocksWithLocation(stockcheckId, userId);
        }

        // For Resulting Charts page
        public async Task<IEnumerable<StockItemWithStockType>> GetStocksWithStockType(int stockcheckId, int userId)
        {
            return await stockItemDataAccess.GetStocksWithStockType(stockcheckId, userId);
        }

        public async Task<IEnumerable<StockItemWithResolution>> GetStockWithResolution(int stockcheckId, int userId)
        {
            return await stockItemDataAccess.GetStockItemsWithResolution(stockcheckId, userId);
        }

        public async Task<IEnumerable<StockLoadReportSummary>> GetStockReport(int stockcheckId, int userId)
        {
            return await stockItemDataAccess.GetStockReport(stockcheckId, userId);
        }

        public async Task<IEnumerable<ReconciliationBucket>> GetReconciliationBucket(int stockcheckId, int userId)
        {
            return await stockCheckDataAccess.GetReconciliationBuckets(stockcheckId, userId);
        }

        public async Task<IEnumerable<ResolutionBucket>> GetResolutionBucket(int stockcheckId, int userId)
        {
            return await stockItemDataAccess.GetResolutionBucket(stockcheckId, userId);
        }

        public async Task<IEnumerable<StockItemMatchedToRecItem>> GetStockItemsMatchedToRecItem(int stockcheckId, int reconcilingItemTypeId, int userId)
        {
            IEnumerable<StockItemMatchedToRecItem> allRecItemResults = await stockItemDataAccess.GetStockItemMatchedToRecItem(stockcheckId, userId);
            if (reconcilingItemTypeId == 0) return allRecItemResults;
            return allRecItemResults.Where(x => x.ReconcilingItemTypeId == reconcilingItemTypeId);
        }

        public async Task<IEnumerable<StockItemWithScan>> GetStockItemsWithScan(int stockcheckId, int userId)
        {
            //CoordinateSet stockCheckCoordinates = await stockCheckDataAccess.GetStockCheckCoordinates(stockcheckId);
            var items = await stockItemDataAccess.GetStockItemsWithScan(stockcheckId, userId);
            foreach (var item in items)
            {
                item.PopulateDistance(item.StockCheckLongitude, item.StockCheckLatitude);
            }
            return items;
        }

        public async Task<IEnumerable<StockItemWithResolution>> GetStockItemsWithResolution(int stockcheckId, int userId)
        {
            return await stockItemDataAccess.GetStockItemsWithResolution(stockcheckId, userId);
        }


        public async Task<IEnumerable<StockItemMatchedToRecItem>> GetMissingMatched(int stockcheckId, int userId)
        {
            return await stockItemDataAccess.GetMissingMatched(stockcheckId, userId);
        }


        public async Task<IEnumerable<StockItemMatchedToOtherSiteScan>> GetStockItemMatchedToOtherSiteScan(int stockcheckId, int userId)
        {
            return await stockItemDataAccess.GetStockItemMatchedToOtherSiteScan(stockcheckId, userId);
        }



        public List<StockItemWithResolution> GetRepeatMissings(List<int> stockCheckIds, int userId)
        {
            //Get this stockchecks missings
            List<StockItemWithResolution> firstStockCheckResult = stockItemDataAccess.GetStockItemsWithResolution(stockCheckIds[0], userId).Result.ToList();


            foreach (var stockCheckId in stockCheckIds.Skip(1))
            {
                List<int> stockItemIdsToRetain = new List<int>();
                List<StockItemWithResolution> thisStockCheckMissings = stockItemDataAccess.GetStockItemsWithResolution(stockCheckId, userId).Result.ToList();
                foreach (var missing in firstStockCheckResult)
                {
                    StockItemWithResolution matchingInCurrentCheck = thisStockCheckMissings.FirstOrDefault(x =>
                        (x.Reg != "INPUT" && x.Reg != "TBC" && x.Reg != null && x.Reg != string.Empty && x?.Reg == missing.Reg) ||
                        (x.Vin != "INPUT" && x.Vin != "TBC" && x.Vin != null && x.Vin != string.Empty && x?.Vin == missing.Vin));

                    if (matchingInCurrentCheck != null)
                    {
                        stockItemIdsToRetain.Add((int)missing.StockItemId);
                    }
                }
                firstStockCheckResult = firstStockCheckResult.Where(x=>stockItemIdsToRetain.Contains((int)x.StockItemId)).ToList();
            }

            return firstStockCheckResult;
        }

        public async Task<int> DeleteAllDMSStockItems(int stockcheckId, int userId)
        {
            return await stockItemDataAccess.DeleteAllDMSStockItems(stockcheckId, userId);
        }

        public async Task<int> DeleteAllAgencyStockItems(int stockcheckId, int userId)
        {
            return await stockItemDataAccess.DeleteAllAgencyStockItems(stockcheckId, userId);
        }

        public async Task SaveMissingResolution(Resolution resolution, int userId)
        {

            using IDbConnection db = new SqlConnection(_config.GetConnectionString(Connectionstring));
            try
            {
                if (db.State == ConnectionState.Closed)
                {
                    db.Open();
                }

                using var tran = db.BeginTransaction();
                try
                {
                    //Add data in the DB
                    if (resolution.ResolutionId == 0)
                    {
                        try
                        {
                            resolution.ResolutionId = await stockItemDataAccess.AddMissingResolutionAsync(resolution, userId);
                            await stockItemDataAccess.UpdateStockItemWithMissingResolutions(resolution.StockCheckId, userId, resolution.OriginalItemId, resolution.ResolutionId, db, tran);
                        }
                        catch (Exception ex)
                        {
                            await stockItemDataAccess.DeleteMissingResolution(resolution.ResolutionId, resolution.StockCheckId, userId);
                            string errMsg = $"Adding MissingResolutions failed for id: {resolution.ResolutionId}. Err Message: {ex.Message}";
                            throw new Exception(errMsg);
                        }
                    }
                    else
                    {
                        //update 
                        await stockItemDataAccess.UpdateMissingResolution(resolution, userId, db, tran);
                    }

                    foreach (var image in resolution.Images)
                    {
                        if (image.Status.ToUpper() == "ADD")
                        {
                            string cleanFileName = imageService.RemoveUnwantedCharsFromFileName(image.FileName);
                            int id = await stockItemDataAccess.AddMissingResolutionImage(cleanFileName, resolution.ResolutionId, resolution.StockCheckId, userId, db, tran);

                            using (Stream stream = imageService.convertBase64ToStream(image.FileBase64))
                            {
                                string contentType = imageService.GetContentType(image.FileBase64);
                                var result = await imageService.UploadMissingResolutionImage(stream, id, cleanFileName, contentType);
                                if (result.Equals(false))
                                {
                                    string errMsg = $"Upload Failed for StockcheckId: {resolution.OriginalItemId}| MissingResolutionId: {resolution.ResolutionId}| Id: {id}";
                                    throw new Exception(errMsg);
                                }
                            }

                        }
                        else if (image.Status.ToUpper() == "DELETE")
                        {
                            await stockItemDataAccess.DeleteMissingResolutionImage(resolution.ResolutionId, image.Id.Value, resolution.StockCheckId, userId, db, tran);

                            var result = await imageService.DeleteMissingResolutionImage(image.Id.Value);
                            if (result.Equals(false))
                            {
                                string errMsg = $"Delete Failed for StockcheckId: {resolution.OriginalItemId}| MissingResolutionId: {resolution.ResolutionId}| ImageId: {image.Id.Value}";
                                throw new Exception(errMsg);
                            }

                        }
                    }

                    tran.Commit();
                }
                catch (Exception ex)
                {
                    tran.Rollback();
                    throw new Exception(ex.Message, ex);
                }
            }
            catch (Exception ex)
            {
                var config = TelemetryConfiguration.CreateDefault();
                var client = new TelemetryClient(config);
                client.TrackException(ex);
                throw new Exception(ex.Message, ex);
            }
            finally
            {
                if (db.State == ConnectionState.Open)
                    db.Close();
            }

        }

        public async Task DeleteMissingResolution(int resolutionId, int stockCheckId, int userId)
        {
            IEnumerable<int> resolutionImageIds = await stockItemDataAccess.GetMissingResolutionImageIds(resolutionId);
            await stockItemDataAccess.DeleteMissingResolution(resolutionId, stockCheckId, userId);

            foreach (var imageId in resolutionImageIds)
            {
                var result = await imageService.DeleteMissingResolutionImage(imageId);
                if (result.Equals(false))
                {
                    throw new Exception("Delete Failed");
                }
            }
        }

        //public async Task AddStockItem(ViewModel.StockItem newStockItem, int stockCheckId, int userId)
        //{
        //    await stockItemDataAccess.AddStockItem(newStockItem, stockCheckId, userId);
        //}

        public async Task AddStockItems(List<ViewModel.StockItem> newStockItems, int stockCheckId, int userId, int fileImportId)
        {
            await stockItemDataAccess.AddStockItems(newStockItems, stockCheckId, userId, fileImportId);
        }

        public async Task<StockConsignmentVM> GetStockConsignment(int stockcheckId, int userId)
        {
            return await stockItemDataAccess.GetStockConsignment(stockcheckId, userId);
        }

        public async Task<IEnumerable<GlobalSearchResultItem>> GetGlobalSearchResults(int userId, string reg, string vin, bool requireAndMatch)
        {
            //List<SearchResultVM> searchResultVMs = new List<SearchResultVM>();
            //SearchResultVM searchResultVM;

            SearchResult result = await stockItemDataAccess.GetGlobalSearchResults(userId, reg, vin, requireAndMatch);

            //new approach.  we will get a full item for each result
            List<GlobalSearchResultItem> results = new List<GlobalSearchResultItem>();
            foreach (var scanResult in result.scanResults)
            {
                var item = await (GetItemFullDetail(null, scanResult.ScanId, scanResult.StockCheckId, userId));

                results.Add(new GlobalSearchResultItem(item, scanResult.StockCheckSiteName, scanResult.StockcheckDate));
            }

            foreach (var stockItemResult in result.stockItemResults)
            {
                var item = await (GetItemFullDetail(stockItemResult.StockitemId, null, stockItemResult.StockCheckId, userId));
                results.Add(new GlobalSearchResultItem(item, stockItemResult.StockCheckSiteName, stockItemResult.StockcheckDate));
            }

            return results;

            //foreach (var r in result.scanResults)
            //{
            //    ScanFullDetail scanResult = await scanDataAccess.GetFullScanDetailAcrossSites(r.ScanId, userId);

            //    searchResultVM = new SearchResultVM();
            //    searchResultVM.Item = new ItemFullDetail(scanResult);
            //    searchResultVM.StockCheckSiteName = r.StockCheckSiteName;
            //    searchResultVM.StockCheckDate = r.StockcheckDate;

            //    if (r.StockitemId != null)
            //    {
            //        searchResultVM.ResultType = ResultType.ScannedAndInStock;
            //    }
            //    else
            //    {
            //        searchResultVM.ResultType = ResultType.UnknownResolution;
            //    }

            //    searchResultVMs.Add(searchResultVM);
            //}

            //foreach (var r in result.stockItemResults)
            //{
            //    var stockResult = await stockItemDataAccess.GetStockItemFullDetailAcrossSites(r.StockitemId, userId);
            //    stockResult.DistanceFromDealershipInMiles = 0;
            //    searchResultVM = new SearchResultVM();
            //    searchResultVM.Item = new ItemFullDetail(stockResult);
            //    searchResultVM.StockCheckSiteName = r.StockCheckSiteName;
            //    searchResultVM.StockCheckDate = r.StockcheckDate;

            //    if (r.ScanId != null)
            //    {
            //        searchResultVM.ResultType = ResultType.ScannedAndInStock;
            //    }
            //    else
            //    {
            //        searchResultVM.ResultType = ResultType.MissingResolution;
            //    }

            //    searchResultVMs.Add(searchResultVM);
            //}


            //return searchResultVMs;

        }

        public async Task<int> DeleteStockItem(int stockcheckId, int userId, int itemId)
        {
            return await stockItemDataAccess.DeleteStockItem(stockcheckId, userId, itemId);
        }

        public async Task<int> GetTotalStockItemsCount(List<int> stockcheckIds, int userId)
        {
            return await stockItemDataAccess.GetTotalStockItemsCount(stockcheckIds, userId);
        }

        public async Task<IEnumerable<ViewModel.StockItem>> GetStockItemsForSite(int siteId)
        {

            return await stockItemDataAccess.GetStockItemsForSite(siteId);
        }
    }
}
