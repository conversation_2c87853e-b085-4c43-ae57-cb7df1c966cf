﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace StockPulse.WebApi.Migrations
{
    /// <inheritdoc />
    public partial class addlithiafinancialines : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "LithiaFinancialLines",
                schema: "import",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false),
                    Code = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    AccountDescription = table.Column<string>(type: "nvarchar(250)", maxLength: 250, nullable: true),
                    Balance = table.Column<decimal>(type: "decimal(15,3)", nullable: false),
                    SiteId = table.Column<int>(type: "int", nullable: false),
                    FileImportId = table.Column<int>(type: "int", nullable: true)
                },
                constraints: table =>
                {
                    table.ForeignKey(
                        name: "FK_LithiaFinancialLines_FileImports_FileImportId",
                        column: x => x.FileImportId,
                        principalSchema: "import",
                        principalTable: "FileImports",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_LithiaFinancialLines_Sites_SiteId",
                        column: x => x.SiteId,
                        principalSchema: "dbo",
                        principalTable: "Sites",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_LithiaFinancialLines_FileImportId",
                schema: "import",
                table: "LithiaFinancialLines",
                column: "FileImportId");

            migrationBuilder.CreateIndex(
                name: "IX_LithiaFinancialLines_SiteId",
                schema: "import",
                table: "LithiaFinancialLines",
                column: "SiteId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "LithiaFinancialLines",
                schema: "import");
        }
    }
}
