﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Graph;
using StockPulse.WebApi.Service;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using static StockPulse.WebApi.Attribute.UserLevelAccessAttribute;

namespace StockPulse.WebApi.Attribute
{
    public interface IAttributeValueProvider
    {
        UserRole attributeUserRole { get; }
    }

    [AttributeUsage(AttributeTargets.All)]
    public class UserLevelAccessAttribute: System.Attribute, IActionFilter
    {

        public readonly UserRole[] requiredUserRoles;
        public UserLevelAccessAttribute(UserRole[] requiredUserRoles)
        {
            this.requiredUserRoles = requiredUserRoles;
        }

        public void OnActionExecuted(ActionExecutedContext context)
        {
            //throw new System.NotImplementedException();
        }

        public void OnActionExecuting(ActionExecutingContext context)
        {
            //var path = context.HttpContext.Request.Path.Value;0

            UserRole currentUserRole = UserRole.None;
            if(context.Controller is IAttributeValueProvider attributeValueProvider)
            {
                currentUserRole = attributeValueProvider.attributeUserRole;
            }

            var position = Array.IndexOf(requiredUserRoles, currentUserRole);

            if (position == -1) context.Result = new ForbidResult();

            
        }

        

       

        public enum UserRole
        {
            None,
            MissingRole,
            Scanner,
            Approver,
            Reconciler,
            Resolver,
            SysAdministrator,
            Admin,
            All,
            GeneralManager,
            ReadOnly,
            ScanAndPrint,
            ScanAndView
        }
    }

   
    
  




}
