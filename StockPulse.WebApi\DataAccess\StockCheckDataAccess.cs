﻿using Dapper;
using StockPulse.WebApi.Controllers;
using StockPulse.WebApi.Dapper;
using StockPulse.Model;
using StockPulse.WebApi.ViewModel;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;

namespace StockPulse.WebApi.DataAccess
{
   public interface IStockCheckDataAccess
   {
      Task<IEnumerable<StockCheckVM>> GetStockChecksOverview(int userId, bool isActive, DateTime? fromDate = null, DateTime? toDate = null, int? stockCheckId = null);
      Task<IEnumerable<int>> GetLastFourStockCheckIds(int stockcheckId, int userId);
      Task<IEnumerable<ReconciliationBucket>> GetReconciliationBuckets(int stockCheckId, int userId);
      Task<IEnumerable<ResolutionBucket>> GetResolutionBuckets(int stockcheckId, int userId);
      Task UpdateStatus(int stockCheckId, int newStatusId, int userId, IDbConnection db, IDbTransaction tran);
      Task UpdateSignoffImageFlag(int stockCheckId, int hasImage, int userId, IDbConnection db, IDbTransaction tran);
      Task UpdateSignoffImageFlag(int stockCheckId, int v, int userId);
      Task ScansStarted(int stockCheckId, int userId, IDbConnection db, IDbTransaction tran);
      Task<int> GetStockCheckStatus(SignoffPictureSubmission pictureDetails, int userId);
      //Task<IEnumerable<StockCheckVM>> GetStockChecksForSite(int stockCheckId, int userId);
      Task SaveReconciliationResults(ReconciliationResults results, int stockCheckId, int userId);
      Task<bool> GetRecProcessMatchChoice(int stockCheckId, int userId);
      Task<CoordinateSet> GetStockCheckCoordinates(int stockCheckId);
      Task<string> GetStockCheckSiteName(int stockCheckId, int userId);
      Task<StockCheckMobileApp> GetStockCheck(int userId, int stockCheckId);
      Task<StockCheckVM> GetStockChecks(int stockCheckId, int userId);
      Task CreateStockCheck(CreateStockCheckParams parms, int userId);
      Task CreateStockChecksForAllSites(DateTime selectedDate, int userId, int dealerGroupId);
      Task Archive(List<int> stockCheckIds, int userId);
      Task UnArchive(List<int> stockCheckIds, int userId);
      Task Delete(List<int> stockCheckIds, int userId);
      Task<IEnumerable<int>> CheckIfAlreadyExists(string stockCheckDate, List<int> siteIds);
      Task<IEnumerable<int>> GetStockCheckIdsForUserDealerGroup(int userId);
      Task<IEnumerable<int>> ImportLatestDataMMG(ImportLatestDataParams parms, int userId);
      Task<IEnumerable<int>> ImportLatestDataLithia(ImportLatestDataParams parms, int userId);
      Task<IEnumerable<int>> ImportLatestDataJardine(ImportLatestDataParams parms, int userId);
      Task<DateTime> GetLatestDataRecievedDate(int dealerGroupId);
   }

   public class StockCheckDataAccess : IStockCheckDataAccess
   {
      private readonly IDapper dapper;

      public StockCheckDataAccess(IDapper dapper)
      {
         this.dapper = dapper;
      }

      public async Task<IEnumerable<int>> ImportLatestDataJardine(ImportLatestDataParams parms, int userId)
      {
         var paramlist = new DynamicParameters();
         string stockCheckIdStr = string.Join(",", parms.StockcheckIds);
         paramlist.Add("StockChecks", stockCheckIdStr);

         return await dapper.GetAllAsync<int>("[import].[Jardine_ImportData]", paramlist, System.Data.CommandType.StoredProcedure, 3000);
      }

      public async Task<IEnumerable<int>> ImportLatestDataMMG(ImportLatestDataParams parms, int userId)
      {
         var paramlist = new DynamicParameters();
         string stockCheckIdStr = string.Join(",", parms.StockcheckIds);
         paramlist.Add("StockChecks", stockCheckIdStr);

         return await dapper.GetAllAsync<int>("[import].[MMG_ImportData]", paramlist, System.Data.CommandType.StoredProcedure, 3000);
      }

      public async Task<IEnumerable<int>> ImportLatestDataLithia(ImportLatestDataParams parms, int userId)
      {
         var paramlist = new DynamicParameters();
         string stockCheckIdStr = string.Join(",", parms.StockcheckIds);
         paramlist.Add("StockChecks", stockCheckIdStr);

         return await dapper.GetAllAsync<int>("[import].[Lithia_ImportData]", paramlist, System.Data.CommandType.StoredProcedure, 3000);
      }

      public async Task<DateTime> GetLatestDataRecievedDate(int dealerGroupId)
      {
         var paramList = new DynamicParameters();
         paramList.Add("dealerGroupId", dealerGroupId);

         return await dapper.GetAsync<DateTime>("dbo.GET_LatestDataRecievedDate", paramList, System.Data.CommandType.StoredProcedure);
      }

      public async Task<IEnumerable<StockCheckVM>> GetStockChecksOverview(int userId, bool isActive, DateTime? fromDate, DateTime? toDate, int? stockCheckId)
      {

         var paramList = new DynamicParameters();
         paramList.Add("UserId", userId);
         paramList.Add("IsActive", isActive);
         paramList.Add("StockCheckId", stockCheckId?.ToString());
         paramList.Add("FromDate", fromDate);
         paramList.Add("ToDate", toDate);

         return await dapper.GetAllAsync<StockCheckVM>("dbo.GET_StockChecksOverview", paramList, System.Data.CommandType.StoredProcedure);

      }

      public async Task<StockCheckMobileApp> GetStockCheck(int userId, int stockCheckId)
      {
         var paramList = new DynamicParameters();
         paramList.Add("UserId", userId);
         paramList.Add("IsActive", null);
         paramList.Add("StockCheckId", $"{stockCheckId}");
         paramList.Add("FromDate", null);
         paramList.Add("ToDate", null);

         var stockCheckVM = await dapper.GetAllAsync<StockCheckVM>("dbo.GET_StockChecksOverview", paramList, System.Data.CommandType.StoredProcedure);
         return new StockCheckMobileApp(stockCheckVM.First());
      }
      public async Task<StockCheckVM> GetStockChecks(int stockCheckId, int userId)
      {
         var paramList = new DynamicParameters();
         paramList.Add("UserId", userId);
         paramList.Add("IsActive", null);
         paramList.Add("StockCheckId", stockCheckId);
         paramList.Add("FromDate", null);
         paramList.Add("ToDate", null);

         return (await dapper.GetAllAsync<StockCheckVM>("dbo.GET_StockChecksOverview", paramList, System.Data.CommandType.StoredProcedure)).First();

      }


      public async Task<string> GetStockCheckSiteName(int stockCheckId, int userId)
      {
         var paramList = new DynamicParameters();
         paramList.Add("StockCheckId", stockCheckId);
         paramList.Add("UserId", userId);

         return await dapper.GetAsync<string>("dbo.GET_StockCheckSiteName", paramList, System.Data.CommandType.StoredProcedure);
      }


      public async Task CreateStockCheck(CreateStockCheckParams parms, int userId)
      {
         var paramList = new DynamicParameters();

         paramList.Add("SiteIds", string.Join(',', parms.SiteIds));
         paramList.Add("UserId", userId);
         paramList.Add("Date", parms.Date);

         await dapper.ExecuteAsync("dbo.INSERT_Stockcheck", paramList, System.Data.CommandType.StoredProcedure);
      }

      public async Task CreateStockChecksForAllSites(DateTime selectedDate, int userId, int dealerGroupId)
      {
         var paramList = new DynamicParameters();

         paramList.Add("SelectedDate", selectedDate);
         paramList.Add("UserId", userId);
         paramList.Add("DealerGroupId", dealerGroupId);

         await dapper.GetAsync<int>("dbo.INSERT_StockchecksForAllSites", paramList, System.Data.CommandType.StoredProcedure);
      }

      //public async Task ReconcileStockCheck(int stockcheckId, int userId)
      //{
      //    var paramList = new DynamicParameters();

      //    paramList.Add("StockCheckId", stockcheckId);
      //    paramList.Add("UserId", userId);

      //    await dapper.ExecuteAsync("dbo.EXECUTE_ReconcileStockCheck", paramList, System.Data.CommandType.StoredProcedure);
      //}

      public async Task<IEnumerable<int>> GetLastFourStockCheckIds(int stockcheckId, int userId)
      {
         var paramList = new DynamicParameters();

         paramList.Add("StockCheckId", stockcheckId);
         paramList.Add("UserId", userId);

         return await dapper.GetAllAsync<int>("dbo.GET_Last4StockCheckIds", paramList, System.Data.CommandType.StoredProcedure);
      }


      public async Task<IEnumerable<ReconciliationBucket>> GetReconciliationBuckets(int stockcheckId, int userId)
      {
         var paramList = new DynamicParameters();
         paramList.Add("StockCheckId", stockcheckId);
         paramList.Add("UserId", userId);

         return await dapper.GetAllAsync<ReconciliationBucket>("dbo.GET_ReconciliationBuckets", paramList, System.Data.CommandType.StoredProcedure);
      }

      public async Task<CoordinateSet> GetStockCheckCoordinates(int stockCheckId)
      {
         return await dapper.GetAsync<CoordinateSet>(
             $"SELECT si.Longitude, si.Latitude FROM StockChecks sc INNER JOIN Sites si on si.Id = sc.SiteId WHERE sc.Id = {stockCheckId}",
             null, System.Data.CommandType.Text);
      }

      public async Task<IEnumerable<ResolutionBucket>> GetResolutionBuckets(int stockcheckId, int userId)
      {
         var paramList = new DynamicParameters();
         paramList.Add("StockCheckId", stockcheckId);
         paramList.Add("UserId", userId);

         return await dapper.GetAllAsync<ResolutionBucket>("dbo.GET_ResolutionBuckets", paramList, System.Data.CommandType.StoredProcedure);
      }

      public async Task UpdateStatus(int stockCheckId, int newStatusId, int userId, IDbConnection db, IDbTransaction tran)
      {
         var paramList = new DynamicParameters();
         paramList.Add("StockCheckId", stockCheckId);
         paramList.Add("NewStatusId", newStatusId);
         paramList.Add("UserId", userId);
         await dapper.InsertAsync<int>("dbo.UPDATE_StockCheckStatus", paramList, tran, db, CommandType.StoredProcedure);
      }

      public async Task UpdateSignoffImageFlag(int stockCheckId, int hasImage, int userId, IDbConnection db, IDbTransaction tran)
      {
         var paramList = new DynamicParameters();
         paramList.Add("StockCheckId", stockCheckId);
         paramList.Add("HasSignOffImage", hasImage);
         paramList.Add("UserId", userId);

         await dapper.InsertAsync<int>("dbo.UPDATE_StockCheckHasImageflag", paramList, tran, db, CommandType.StoredProcedure);
      }
      public async Task UpdateSignoffImageFlag(int stockCheckId, int hasImage, int userId)
      {
         var paramList = new DynamicParameters();
         paramList.Add("StockCheckId", stockCheckId);
         paramList.Add("HasSignOffImage", hasImage);
         paramList.Add("UserId", userId);

         await dapper.GetAsync<int>("dbo.UPDATE_StockCheckHasImageflag", paramList, CommandType.StoredProcedure);
      }
      public async Task ScansStarted(int stockCheckId, int userId, IDbConnection db, IDbTransaction tran)
      {
         var paramList = new DynamicParameters();
         paramList.Add("StockCheckId", stockCheckId);
         paramList.Add("UserId", userId);

         await dapper.ExecuteTranAsync("dbo.UPDATE_ScansStarted", paramList, tran, db, CommandType.StoredProcedure);
      }

      //public async Task<IEnumerable<StockCheckVM>> GetStockChecksForSite(int stockCheckId, int userId)
      //{
      //    var paramList = new DynamicParameters();
      //    paramList.Add("StockCheckId", stockCheckId);
      //    paramList.Add("UserId", userId);

      //    return await dapper.GetAllAsync<StockCheckVM>("dbo.GET_StockChecksOverviewForSite", paramList, System.Data.CommandType.StoredProcedure);
      //}

      //public async Task<IEnumerable<LabelAndValue>> GetStockCheckResultsTest(int stockCheckId)
      //{
      //    return await dapper.GetAllAsync<LabelAndValue>("dbo.GET_StockCheckResultsTest", new DynamicParameters(new {stockCheckId}), CommandType.StoredProcedure);
      //}

      public async Task<int> GetStockCheckStatus(SignoffPictureSubmission pictureDetails, int userId)
      {
         var paramList = new DynamicParameters();
         paramList.Add("StockCheckId", pictureDetails.StockCheckId);
         paramList.Add("UserId", userId);

         return await dapper.GetAsync<int>("dbo.[Get_StockCheckStatus]", paramList, System.Data.CommandType.StoredProcedure);
      }

      public async Task<bool> GetRecProcessMatchChoice(int stockCheckId, int userId)
      {

         var paramList = new DynamicParameters();
         paramList.Add("StockCheckId", stockCheckId);
         paramList.Add("UserId", userId);

         return await dapper.GetAsync<bool>("dbo.[GET_RecProcessMatchChoice]", paramList, System.Data.CommandType.StoredProcedure);
      }

      public async Task SaveReconciliationResults(ReconciliationResults results, int stockCheckId, int userId)
      {

         var paramList = new DynamicParameters();
         paramList.Add("duplicatedScans", CreateTabledParam(results.duplicatedScans), DbType.Object);
         paramList.Add("scansMatchedToStockItems", CreateTabledParam(results.scansMatchedToStockItems), DbType.Object);
         paramList.Add("scansMatchedToOtherSiteStockItems", CreateTabledParam(results.scansMatchedToOtherSiteStockItems), DbType.Object);
         paramList.Add("scansMatchedToRecItems", CreateTabledParam(results.scansMatchedToRecItems), DbType.Object);
         paramList.Add("remainingUnmatchedScans", CreateTabledParam(results.remainingUnmatchedScans), DbType.Object);

         paramList.Add("duplicatedStockItems", CreateTabledParam(results.duplicatedStockItems), DbType.Object);
         paramList.Add("stockItemsMatchedToScans", CreateTabledParam(results.stockItemsMatchedToScans), DbType.Object);
         paramList.Add("stockItemsMatchedToOtherSiteScans", CreateTabledParam(results.stockItemsMatchedToOtherSiteScans), DbType.Object);
         paramList.Add("stockItemsMatchedToRecItems", CreateTabledParam(results.stockItemsMatchedToRecItems), DbType.Object);
         paramList.Add("remainingUnmatchedStockItems", CreateTabledParam(results.remainingUnmatchedStockItems), DbType.Object);

         paramList.Add("StockCheckId", stockCheckId);
         paramList.Add("UserId", userId);


         var res = await dapper.ExecuteAsync("dbo.UPDATE_SaveReconciliationResults", paramList, CommandType.StoredProcedure);

         { }
         ;
      }




      private static DataTable CreateTabledParam(IEnumerable<MatchedItem> items)
      {
         var result = new DataTable("MatchedItem");
         result.Columns.Add("ItemId", typeof(int));
         result.Columns.Add("MatchItemId", typeof(int));
         foreach (var item in items)
         {
            result.Rows.Add(item.ItemId, (item.MatchItemId ?? 0));
         }

         return result;
      }

      public async Task Archive(List<int> stockCheckIds, int userId)
      {
         var paramList = new DynamicParameters();
         paramList.Add("StockCheckIds", string.Join(',', stockCheckIds));
         paramList.Add("UserId", userId);
         await dapper.ExecuteAsync("dbo.UPDATE_StockCheckToArchiveState", paramList, CommandType.StoredProcedure);
      }

      public async Task UnArchive(List<int> stockCheckIds, int userId)
      {
         var paramList = new DynamicParameters();
         paramList.Add("StockCheckIds", string.Join(',', stockCheckIds));
         paramList.Add("UserId", userId);
         var result = await dapper.ExecuteScaler<int>("dbo.UPDATE_StockCheckToUnArchiveState", paramList, CommandType.StoredProcedure);
         if (result == 0)
         {
            throw new Exception("There is already an Active Stock Check for this Site");
         }
      }

      public async Task Delete(List<int> stockCheckIds, int userId)
      {
         var paramList = new DynamicParameters();
         paramList.Add("StockCheckIds", string.Join(',', stockCheckIds));
         paramList.Add("UserId", userId);
         await dapper.ExecuteAsync("dbo.DELETE_StockCheck", paramList, CommandType.StoredProcedure);
      }

      public async Task<IEnumerable<int>> CheckIfAlreadyExists(string stockCheckDate, List<int> siteIds)
      {
         var paramList = new DynamicParameters();
         paramList.Add("StockCheckDate", stockCheckDate);
         paramList.Add("SiteIds", string.Join(',', siteIds));
         return await dapper.GetAllAsync<int>("dbo.GET_SitesWithStockChecksForDate", paramList, CommandType.StoredProcedure);
      }

      public async Task<IEnumerable<int>> GetStockCheckIdsForUserDealerGroup(int userId)
      {
         var paramList = new DynamicParameters();
         paramList.Add("UserId", userId);
         return await dapper.GetAllAsync<int>("dbo.GET_StockCheckIdsForUserDealerGroup", paramList, System.Data.CommandType.StoredProcedure);
      }
   }
}
