﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace StockPulse.WebApi.Migrations
{
    /// <inheritdoc />
    public partial class addnewimporttables : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "FinancialLines",
                schema: "import",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false),
                    Code = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    AccountDescription = table.Column<string>(type: "nvarchar(250)", maxLength: 250, nullable: true),
                    Balance = table.Column<decimal>(type: "decimal(15,3)", nullable: false),
                    SiteId = table.Column<int>(type: "int", nullable: false),
                    FileImportId = table.Column<int>(type: "int", nullable: true),
                    DealerGroupId = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.ForeignKey(
                        name: "FK_FinancialLines_DealerGroup_DealerGroupId",
                        column: x => x.DealerGroupId,
                        principalSchema: "dbo",
                        principalTable: "DealerGroup",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_FinancialLines_FileImports_FileImportId",
                        column: x => x.FileImportId,
                        principalSchema: "import",
                        principalTable: "FileImports",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_FinancialLines_Sites_SiteId",
                        column: x => x.SiteId,
                        principalSchema: "dbo",
                        principalTable: "Sites",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "ReconcilingItems",
                schema: "import",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false),
                    ReconcilingItemTypeId = table.Column<int>(type: "int", nullable: false),
                    Reg = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Vin = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Description = table.Column<string>(type: "nvarchar(250)", maxLength: 250, nullable: true),
                    Comment = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    Reference = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    SourceReportId = table.Column<int>(type: "int", nullable: false),
                    SiteId = table.Column<int>(type: "int", nullable: false),
                    FileImportId = table.Column<int>(type: "int", nullable: true),
                    DealerGroupId = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.ForeignKey(
                        name: "FK_ReconcilingItems_DealerGroup_DealerGroupId",
                        column: x => x.DealerGroupId,
                        principalSchema: "dbo",
                        principalTable: "DealerGroup",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_ReconcilingItems_FileImports_FileImportId",
                        column: x => x.FileImportId,
                        principalSchema: "import",
                        principalTable: "FileImports",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_ReconcilingItems_ReconcilingItemTypes_ReconcilingItemTypeId",
                        column: x => x.ReconcilingItemTypeId,
                        principalSchema: "dbo",
                        principalTable: "ReconcilingItemTypes",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_ReconcilingItems_Sites_SiteId",
                        column: x => x.SiteId,
                        principalSchema: "dbo",
                        principalTable: "Sites",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_ReconcilingItems_SourceReports_SourceReportId",
                        column: x => x.SourceReportId,
                        principalSchema: "dbo",
                        principalTable: "SourceReports",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "StockItems",
                schema: "import",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false),
                    SourceReportId = table.Column<int>(type: "int", nullable: false),
                    Reg = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Vin = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Description = table.Column<string>(type: "nvarchar(250)", maxLength: 250, nullable: true),
                    DIS = table.Column<int>(type: "int", nullable: true),
                    GroupDIS = table.Column<int>(type: "int", nullable: true),
                    Branch = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Comment = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    StockType = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Reference = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    StockValue = table.Column<decimal>(type: "decimal(15,3)", nullable: false),
                    SiteId = table.Column<int>(type: "int", nullable: false),
                    FileImportId = table.Column<int>(type: "int", nullable: true),
                    DealerGroupId = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.ForeignKey(
                        name: "FK_StockItems_DealerGroup_DealerGroupId",
                        column: x => x.DealerGroupId,
                        principalSchema: "dbo",
                        principalTable: "DealerGroup",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_StockItems_FileImports_FileImportId",
                        column: x => x.FileImportId,
                        principalSchema: "import",
                        principalTable: "FileImports",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_StockItems_Sites_SiteId",
                        column: x => x.SiteId,
                        principalSchema: "dbo",
                        principalTable: "Sites",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_StockItems_SourceReports_SourceReportId",
                        column: x => x.SourceReportId,
                        principalSchema: "dbo",
                        principalTable: "SourceReports",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_FinancialLines_DealerGroupId",
                schema: "import",
                table: "FinancialLines",
                column: "DealerGroupId");

            migrationBuilder.CreateIndex(
                name: "IX_FinancialLines_FileImportId",
                schema: "import",
                table: "FinancialLines",
                column: "FileImportId");

            migrationBuilder.CreateIndex(
                name: "IX_FinancialLines_SiteId",
                schema: "import",
                table: "FinancialLines",
                column: "SiteId");

            migrationBuilder.CreateIndex(
                name: "IX_ReconcilingItems_DealerGroupId",
                schema: "import",
                table: "ReconcilingItems",
                column: "DealerGroupId");

            migrationBuilder.CreateIndex(
                name: "IX_ReconcilingItems_FileImportId",
                schema: "import",
                table: "ReconcilingItems",
                column: "FileImportId");

            migrationBuilder.CreateIndex(
                name: "IX_ReconcilingItems_ReconcilingItemTypeId",
                schema: "import",
                table: "ReconcilingItems",
                column: "ReconcilingItemTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_ReconcilingItems_SiteId",
                schema: "import",
                table: "ReconcilingItems",
                column: "SiteId");

            migrationBuilder.CreateIndex(
                name: "IX_ReconcilingItems_SourceReportId",
                schema: "import",
                table: "ReconcilingItems",
                column: "SourceReportId");

            migrationBuilder.CreateIndex(
                name: "IX_StockItems_DealerGroupId",
                schema: "import",
                table: "StockItems",
                column: "DealerGroupId");

            migrationBuilder.CreateIndex(
                name: "IX_StockItems_FileImportId",
                schema: "import",
                table: "StockItems",
                column: "FileImportId");

            migrationBuilder.CreateIndex(
                name: "IX_StockItems_SiteId",
                schema: "import",
                table: "StockItems",
                column: "SiteId");

            migrationBuilder.CreateIndex(
                name: "IX_StockItems_SourceReportId",
                schema: "import",
                table: "StockItems",
                column: "SourceReportId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_FinancialLines_FileImports_FileImportId",
                schema: "dbo",
                table: "FinancialLines");

            migrationBuilder.DropForeignKey(
                name: "FK_ReconcilingItems_FileImports_FileImportId",
                schema: "dbo",
                table: "ReconcilingItems");

            migrationBuilder.DropForeignKey(
                name: "FK_ReconcilingItems_ReconcilingItemTypes_ReconcilingItemTypeId",
                schema: "dbo",
                table: "ReconcilingItems");

            migrationBuilder.DropForeignKey(
                name: "FK_ReconcilingItems_SourceReports_SourceReportId",
                schema: "dbo",
                table: "ReconcilingItems");

            migrationBuilder.DropForeignKey(
                name: "FK_StockItems_FileImports_FileImportId",
                schema: "dbo",
                table: "StockItems");

            migrationBuilder.DropForeignKey(
                name: "FK_StockItems_SourceReports_SourceReportId",
                schema: "dbo",
                table: "StockItems");

            migrationBuilder.DropTable(
                name: "FinancialLines",
                schema: "import");

            migrationBuilder.DropTable(
                name: "ReconcilingItems",
                schema: "import");

            migrationBuilder.DropTable(
                name: "StockItems",
                schema: "import");
        }
    }
}
