﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace StockPulse.WebApi.Migrations
{
    /// <inheritdoc />
    public partial class StockcheckrenameReconciliationcol : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "ReconcilliationCompletedDate",
                schema: "dbo",
                table: "StockChecks",
                newName: "ReconciliationCompletedDate");

            migrationBuilder.RenameColumn(
                name: "ReconcilliationApprovedDate",
                schema: "dbo",
                table: "StockChecks",
                newName: "ReconciliationApprovedDate");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "ReconciliationCompletedDate",
                schema: "dbo",
                table: "StockChecks",
                newName: "ReconcilliationCompletedDate");

            migrationBuilder.RenameColumn(
                name: "ReconciliationApprovedDate",
                schema: "dbo",
                table: "StockChecks",
                newName: "ReconcilliationApprovedDate");
        }
    }
}
