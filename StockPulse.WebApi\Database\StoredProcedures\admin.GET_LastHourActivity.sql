﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE OR ALTER PROCEDURE [admin].[GET_LastHourActivity] 
AS  
BEGIN  
  
SET NOCOUNT ON;  

DECLARE @MissingResolutions int = 
(
	SELECT
	COUNT(Id)
	FROM MissingResolutions
	WHERE ResolutionDate >= DATEADD(hour,-1,getDate())
)

DECLARE @UnknownResolutions int = 
(
	SELECT
	COUNT(Id)
	FROM UnknownResolutions
	WHERE ResolutionDateTime >= DATEADD(hour,-1,getDate())
)

DECLARE @Scans int = 
(
	SELECT
	COUNT(Id)
	FROM Scans
	WHERE ScanDateTime >= DATEADD(hour,-1,getDate())
)

SELECT
@Scans as ScansTaken,
@MissingResolutions as MissingsResolved,
@UnknownResolutions as UnknownsResolved

END  
  
GO
