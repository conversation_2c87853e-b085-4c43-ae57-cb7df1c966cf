﻿using Dapper;
using StockPulse.Model;
using StockPulse.WebApi.Dapper;
using StockPulse.WebApi.ViewModel;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace StockPulse.WebApi.DataAccess
{
    public interface IGlobalParamDataAccess
    {
        Task<IEnumerable<GlobalParam>> GetAllParamsAsync(int userId);
        //Task<IEnumerable<ResolutionType>> GetSitesForDivision(int userId);
        Task<IEnumerable<ResolutionTypeVM>> GetResolutionTypes(int userId);
        Task<IEnumerable<GlobalParam>> GetAllAsync();
    }

    public class GlobalParamDataAccess : IGlobalParamDataAccess
    {
        private readonly IDapper dapper;

        public GlobalParamDataAccess(IDapper dapper)
        {
            this.dapper = dapper;
        }

        public async Task<IEnumerable<GlobalParam>> GetAllParamsAsync(int userId)
        {
            var paramList = new DynamicParameters();
            paramList.Add("UserId", userId);
            return await dapper.GetAllAsync<GlobalParam>("dbo.GET_GlobalParams", paramList, System.Data.CommandType.StoredProcedure);
        }

        

        public async Task<IEnumerable<GlobalParam>> GetAllAsync()
        {
            var query = $"SELECT * FROM GlobalParams";
            return await dapper.GetAllAsync<GlobalParam>(query, null, System.Data.CommandType.Text);
        }

        public async Task<IEnumerable<ResolutionTypeVM>> GetResolutionTypes(int userId)
            {
                var paramList = new DynamicParameters();
                paramList.Add("UserId", userId);

                return await dapper.GetAllAsync<ResolutionTypeVM>("dbo.GET_ResolutionTypes", paramList, System.Data.CommandType.StoredProcedure);
            }

            //public async Task<IEnumerable<ResolutionType>> GetSitesForDivision(int userId)
            //{
            //    var paramList = new DynamicParameters();
            //    paramList.Add("UserId", userId);

            //    return await dapper.GetAllAsync<ResolutionType>("dbo.GET_SitesForDivision", paramList, System.Data.CommandType.StoredProcedure);
            //}
        }
    }
