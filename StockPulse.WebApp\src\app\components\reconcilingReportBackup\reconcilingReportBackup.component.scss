#fileDropAreaContainer  {
    width: 100%;
    display: flex;
    justify-content: center;

    #fileDropArea {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-align: center;
        width: 100%;
        max-width: 500px;
        border: 1px dashed;
        padding: 1em;
        caret-color: transparent;
    
        &:focus-visible {
            outline: none !important;
        }
    }
}

#filesList {
    padding: 1em 0;
    max-height: 40vh;
    overflow-y: auto;

    fa-icon.fileIcon {
        margin-right: 1em;
    }

    fa-icon.deleteIcon {
        color: var(--danger);
        cursor: pointer;
    }

    .singleFile {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        margin-bottom: 0.5em;

        .fileThumbnail {
            width: 15%;
            margin-right: 0.5em;
        }

        .info {
            max-width: 85%;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    }
}

#existingBackupFiles {
    display: flex;
    margin-bottom: 2em;

    .existingBackupFile {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: center;
        width: 25%;
        cursor: pointer;
        position: relative;

        span {
            text-overflow: ellipsis;
            width: 100%;
            overflow: hidden;
        }

        img {
            margin-top: 0.5em;
            width: 50%;
        }

        .deleteIcon {
            position: absolute;
            top: 0;
            right: 0;
            color: red;
        }
    }
}