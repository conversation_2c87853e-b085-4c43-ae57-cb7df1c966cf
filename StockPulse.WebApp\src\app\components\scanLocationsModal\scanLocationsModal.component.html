<ng-template #modalRef let-modal>
    <div class="modal-header">
        <h4 class="modal-title" id="modal-basic-title">Scan Locations</h4>
        <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <div class="modal-body alertModalBody lowHeight">
        <div class="d-inline-block mb-3" #dropdown="ngbDropdown" container="body" ngbDropdown>
            <button id="dropdownTrigger" class="btn btn-primary" ngbDropdownToggle>
                {{ selectedSite ? selectedSite.description : 'Select a site...' }}
            </button>
            <div ngbDropdownMenu aria-labelledby="dropdownTrigger">
                <input type="text" placeholder="Search..." [(ngModel)]="searchString" (ngModelChange)="searchList()">
                <button *ngFor="let site of sitesCopy" ngbDropdownItem (click)="selectSite(site)">
                    {{ site.description }}
                </button>
            </div>
        </div>

        <ng-container *ngIf="scanLocationsForSite">
            <instructionRow
                message="Use the input box below to create new scanning locations for this site. You can remove scan locations by clicking the trash icon.">
            </instructionRow>

            <table>
                <tbody>
                    <tr *ngIf="scanLocationsForSite.length === 0">
                        <td colspan="2">
                            There are currently no scan locations for {{ selectedSite.description }}. Use the input box
                            below to create some.
                        </td>
                    </tr>
                    <tr>
                        <td colspan="2">
                            <div id="inputAndSaveContainer">
                                <input type="text" placeholder="Enter new scan location" [(ngModel)]="newLocation" (keydown.enter)="saveScanLocationForSiteId()">
                                <button *ngIf="newLocation && newLocation !== ''" (click)="saveScanLocationForSiteId()">
                                    <fa-icon [icon]="iconService.faSave"></fa-icon>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <tr *ngFor="let location of scanLocationsForSite">
                        <td>{{ location.description }}</td>
                        <td>
                            <fa-icon [icon]="iconService.faTrash" (click)="deleteScanLocationForSiteId(location.id)">
                            </fa-icon>
                        </td>
                    </tr>
                </tbody>
            </table>
        </ng-container>
    </div>
</ng-template>