import { Injectable } from "@angular/core";
import { GlobalSearchResultItem } from "src/app/model/GlobalSearchResultItem";
import { ItemFullDetail } from "src/app/model/ItemFullDetail";
import { SearchResultVM } from "src/app/model/SearchResultVM";
import { StockCheck } from "src/app/model/StockCheck";
import { ApiAccessService } from "src/app/services/apiAccess.service";
import { ConstantsService } from "src/app/services/constants.service";
import { ToastService } from "src/app/services/newToast.service";
import { SelectionsService } from "src/app/services/selections.service";

@Injectable({
  providedIn: 'root'
})

export class GlobalSearchService {
  resultItems: GlobalSearchResultItem[] = [];

  reg: string;
    chassis: string;
    requireAndMatch:boolean = false;
    //stageValue: string;
    data: any[];
    stockChecks:StockCheck[];
    


  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public apiService: ApiAccessService,
    public toastService: ToastService
  ) { }



}