<div
    [ngClass]="{ 'showResolutionButtons': vehicleIsAnIssue() && !showCompact && (checkForChangesMade() || item.resolutionId) }">

    <!-- If stock matches scan -->
    <table class="explanationTable" *ngIf="stockMatchesScan()">
        <tbody>
            <tr>
                <td>
                    <div class="holdingNote holdingNoteGood">
                        <fa-icon [icon]="icon.faCheckCircle" class="h1"></fa-icon>
                        <h3>Stock item Matched to Scan</h3>
                    </div>
                </td>
            </tr>
        </tbody>
    </table>


    <!-- If matches intercompany  -->
    <table class="explanationTable" *ngIf="itemMatchesIntercompany()">
        <tbody>
            <tr>
                <td colspan="2">
                    <div class="holdingNote holdingNoteGood">
                        <fa-icon [icon]="icon.faCheckCircle" class="h1"></fa-icon>
                        <h3 *ngIf="isStockItem()">Resolved - Our StockItem Matches to Other Site Scan</h3>
                        <h3 *ngIf="!isStockItem()">Resolved - Our Scan Matches to Other Site StockItem</h3>
                    </div>
                </td>
            </tr>

        </tbody>
    </table>





    <!-- if has matched with a report -->
    <table [ngClass]="{'compact':showCompact}" *ngIf="isMatchedToReconcilingItem()" class="explanationTable">
        <tbody>
            <tr>
                <td colspan="2">
                    <div class="holdingNote holdingNoteGood">
                        <fa-icon [icon]="icon.faFileCircleCheck" class="h1"></fa-icon>
                        <h3>Matched - {{isStockItem() ? 'Stock item' : 'Scan'}} Matched to Report</h3>
                    </div>
                </td>
            </tr>

            <tr>
                <td>Matched to</td>
                <td>
                    <div>{{ item.reconcilingItemTypeDescription }}</div>
                </td>
            </tr>
            <tr class="topBorder">
                <td>Id</td>
                <td>{{isStockItem() ? item.stockItem?.stockItemId : item.scan?.scanId }}</td>
            </tr>
            <tr class="topBorder">
                <td>Reference</td>
                <td>{{item.reconcilingItemRef}}</td>
            </tr>
            <tr *ngIf="shouldShowReg()" class="topBorder">
                <td>Registration</td>
                <td>
                    <div class="regPlate">{{item.reconcilingItemReg | cph:'numberPlate':0
                        }}</div>
                </td>
            </tr>
            <tr class="topBorder">
                <td>VIN</td>
                <td>
                    <div class=" chassis">{{item.reconcilingItemVin | cph:'chassis':0 }}
                    </div>
                </td>
            </tr>
            <tr class="topBorder">
                <td>Description</td>
                <td>{{item.reconcilingItemDesc}}</td>
            </tr>
            <tr class="topBorder">
                <td>Notes</td>
                <td>{{item.reconcilingItemComment}}</td>
            </tr>
        </tbody>
    </table>

    <!-- Vehicle is a duplicate -->
    <table [ngClass]="{'compact':showCompact}" *ngIf="isDuplicate()" class="explanationTable">
        <!-- Item is a duplicate -->
        <tbody>
            <tr>
                <td colspan="2">
                    <div class="holdingNote holdingNoteGood">
                        <fa-icon [icon]="icon.faCheckCircle" class="h1"></fa-icon>
                        <h3 *ngIf="!isStockItem()">Resolved - Is a Duplicate Scan</h3>
                        <h3 *ngIf="isStockItem()">Resolved - Is a Duplicate Stock Item</h3>
                    </div>
                </td>
            </tr>
            <ng-container *ngIf="isStockItem()">
                <tr>
                    <td>
                        Original StockItem Id
                    </td>
                    <td>
                        #{{item.originalId}}
                    </td>
                </tr>
                <tr class="topBorder">
                    <td>
                        Original Item StockType
                    </td>
                    <td>
                        {{item.originalStockType}}
                    </td>
                </tr>
                <tr class="topBorder">
                    <td>
                        Original Item Notes
                    </td>
                    <td>
                        {{item.originalComment}}
                    </td>
                </tr>
                <tr class="topBorder">
                    <td>
                        Original Item Reference
                    </td>
                    <td>
                        {{item.originalReference}}
                    </td>
                </tr>
            </ng-container>

            <ng-container *ngIf="!isStockItem()">
                <tr>
                    <td>Original Scan Id</td>
                    <td>{{item.originalId}}</td>
                </tr>
                <tr class="topBorder">
                    <td>Original Location</td>
                    <td>{{item.originalLocationDescription}}</td>
                </tr>
                <tr class="topBorder">
                    <td>Original Scanned By</td>
                    <td>{{item.originalScannedBy}}</td>
                </tr>
                <tr class="topBorder">
                    <td>Original Scanned Date</td>
                    <td>
                        {{ item.originalScannedDate | cph:'day':0 }}
                        &nbsp; {{ item.originalScannedDate | cph:'time':0 }}
                    </td>
                </tr>

            </ng-container>

        </tbody>
    </table>


    <!--  Vehicle requires a resolution  -->
    <table [ngClass]="{'compact':showCompact}" *ngIf="vehicleIsAnIssue()" class="explanationTable">
        <tbody>
            <tr>
                <td colspan="2">

                    <div *ngIf="!item.resolutionId" class="holdingNote holdingNoteBad">
                        <fa-icon [icon]="icon.faMemoCircleInfo" class="h1"></fa-icon>
                        <h3>Resolution Required</h3>
                    </div>

                    <div *ngIf="item.resolutionId  && !item.isResolved" class="holdingNote holdingNoteMeh">
                        <fa-icon [icon]="icon.faMemoCircleInfo" class="h1"></fa-icon>
                        <h3>Resolution in Progress</h3>
                    </div>

                    <div *ngIf="item.resolutionId && item.isResolved" class="holdingNote holdingNoteGood">
                        <fa-icon [icon]="icon.faMemoCircleInfo" class="h1"></fa-icon>
                        <h3>Resolved - {{item.resolutionTypeDescription}}</h3>
                    </div>

                </td>
            </tr>

            <tr>
                <td>Resolution</td>
                <td *ngIf="showCompact">
                    {{ !item.resolutionTypeDescription ? 'No resolution' :
                    item.resolutionTypeDescription }}
                </td>
                <td *ngIf="!showCompact">
                    <div ngbDropdown container="body" placement="bottom-right" class="d-inline-block">
                        <button class="btn btn-primary" ngbDropdownToggle
                            [disabled]="!selections.stockCheck || (selections.stockCheck?.statusId > 3 || selections.userIsGeneralManager || selections.userIsReadOnly)">
                            {{ !item.resolutionTypeDescription ? 'Choose resolution' :
                            item.resolutionTypeDescription }}
                        </button>
                        <div ngbDropdownMenu class="dropdown-menu-left" aria-labelledby="dropdownBasic1">
                            <button *ngFor="let resolutionTypes of getResolutionTypes()" ngbDropdownItem
                                (click)="chooseResolution(resolutionTypes)">
                                {{ resolutionTypes.description }}
                            </button>
                        </div>
                    </div>
                </td>
            </tr>
            <tr *ngIf="showBackupRow() ">
                <td>
                    Backup required
                </td>
                <td>
                    <div class="infoPanel">
                        {{ item.resolutionTypeBackup }}
                    </div>
                </td>
            </tr>
            <tr class="topBorder">
                <td>Resolved By</td>
                <td>
                    {{item.resolvedBy}}
                </td>
            </tr>
            <tr class="topBorder">
                <td>Resolution Date</td>
                <td>
                    {{ item.resolutionDate | cph:'shortDate':0 }}
                    {{ item.resolutionDate | cph:'shortTime':0 }}
                </td>
            </tr>
            <tr class="topBorder">
                <td>Resolution Detail</td>
                <td>
                    <span *ngIf="showCompact">{{item.resolutionNotes}}</span>
                    <textarea *ngIf="!showCompact" [(ngModel)]="item.resolutionNotes" placeholder="Add notes here"
                        class="notes subtleBoxShadow"
                        [readonly]="!selections.stockCheck || selections.stockCheck?.statusId > 3 || selections.userIsGeneralManager || selections.userIsReadOnly">
                    </textarea>
                </td>
            </tr>
            <tr class="topBorder">
                <td>Resolved</td>
                <td>
                    <button class="btn btn-primary" [ngClass]="{ 'active': item.isResolved }" style="min-width: 40px;"
                        (click)="item.isResolved = !item.isResolved" [disabled]="!canResolve()">
                        {{ item.isResolved ? 'Yes' : 'No' }}
                    </button>
                    <span *ngIf="backupRequired()" class="text-danger">
                        * Backup image required to mark as resolved
                    </span>
                </td>
            </tr>
            <tr *ngIf="!showCompact" class="topBorder">
                <td>Resolution Backup Files</td>
                <td>
                    <span *ngIf="!item.resolutionImages  || item.resolutionImages.length === 0">
                        You currently have no backup files.
                    </span>
                    <instructionRow *ngIf="item.resolutionImages && item.resolutionImages.length > 0"
                        [message]="'Clicking an image will zoom in. Clicking a document will download the file'">
                    </instructionRow>
                    <div id="resolutionImageHolder" *ngIf="item.resolutionImages && item.resolutionImages.length!==0">
                        <div *ngFor="let image of item.resolutionImages; index as i" class="backupAndButton">
                            <img *ngIf="image.url && image.status !== 'DELETE'" [id]="'backup-image-'+i"
                                (click)="zoomInBackup(image)" [src]="image.url" class="animated backupImage fadeIn"
                                (error)="updateUrl(i, image.fileName)">
                            <button *ngIf="image.status !== 'DELETE' && selections.stockCheck && selections.stockCheck?.statusId <= 3"
                                (click)="deleteMissingResolutionImage(image)" class="btn btn-danger">&times;</button>
                        </div>
                    </div>
                </td>
            </tr>
            <tr *ngIf="canAddBackup()" class="topBorder">
                <td colspan="2">
                    <div id="backupContainer">
                        <!-- Drag drop area -->
                        <div id="fileDropArea" appDragAndDrop contenteditable="true"
                            (filesDropped)="onFileDropped($event)" (paste)="onFilePaste($event)">
                            <!-- <div id="instructionPopoverHolder">
                                <instructionRowPopoverStyle *ngIf="thereAreFiles()"
                                    [fullMessage]="instructionRowMessage()">
                                </instructionRowPopoverStyle>

                            </div> -->
                            <instructionRow [message]="instructionRowMessage()">
                            </instructionRow>



                            <div class="uploadFileWrapper mt-2">
                                <ng-container>
                                    <input #fileDropRef id="file" type="file" multiple="multiple"
                                        class="chooseFileInput" (click)="$event.target.value = null"
                                        (change)="fileBrowseHandler($event)" />
                                    <label for="file">
                                        <fa-icon [icon]="icon.faUpload"></fa-icon>Choose <span
                                            *ngIf="thereAreFiles()">Additional</span> File(s)
                                    </label>
                                </ng-container>
                            </div>
                        </div>

                        <!-- Files uploaded, but not yet saved -->
                        <span *ngIf="fileSizeExceeded" class="text-danger">
                            1 or more files exceeded the 10mb size limit and were not uploaded.
                        </span>
                        <div *ngIf="files.length" id="filesList">
                            <div *ngFor="let file of files; let i = index" class="singleFile">
                                <div class="d-flex align-items-center">
                                    <img class="fileThumbnail" [src]="getThumbnail(file)">
                                    <div class="info">
                                        <span class="name">{{ file.name }}</span>
                                        <br>
                                        <span class="size">{{ formatBytes(file?.size) }}</span>
                                    </div>
                                </div>
                                <fa-icon [icon]="icon.faTrash" width="20px" alt="delete" class="deleteIcon"
                                    (click)="deleteFile(i)"></fa-icon>
                            </div>
                        </div>
                    </div>
                </td>
            </tr>
        </tbody>

    </table>
</div>

<!-- Buttons for editing missing resolution -->
<div *ngIf="vehicleIsAnIssue() && !showCompact && (checkForChangesMade() || item.resolutionId) && !selections.userIsGeneralManager && !selections.userIsReadOnly"
    class="cph-card-footer resolutionButtons">
    <div class="okCancelButtons">
        <button *ngIf="selections.stockCheck && item.resolutionId && selections.stockCheck?.statusId <= 3" class="btn btn-danger" (click)="maybeDeleteResolution()">
            Delete resolution (!)
        </button>
        <button *ngIf="checkForChangesMade()" class="btn btn-success" (click)="saveResolution()">
            Save changes
        </button>
        <button *ngIf="checkForChangesMade()" class="btn btn-primary" (click)="cancelEditProblemCarUpdate()">
            Cancel
        </button>
    </div>
</div>

<!-- Enlarged image -->
<div *ngIf="modalImage" class="enlarged-image-overlay" (click)="modalImage = null">
    <div class="image-backdrop"
        [ngStyle]="{ 'background-image': 'linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), url(' + modalImage + ')' }">
    </div>
    <div class="image-container">
        <button class="image-close" (click)="modalImage = null">&times;</button>
        <img [src]="modalImage" alt="Stock check scan">
    </div>
</div>