@echo off
REM set /P db="Which database do you want to export from? "
REM set /P user="What is your username?"
REM set /P pass="What is your password?"

REM echo [ echo Hello %user%! 
set db= "stockpulseDemo"
set user= "richardprocter"
set pass= "PASSWORD"

 

echo( & echo( & echo ---------- Importing --------------- & echo( & echo

echo "divisions" & bcp divisions in "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\divisions.csv" -S cphi.database.windows.net -d stockpulse -U %user% -P %pass% -e "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\error.txt" -E -q -c -t "|^|"
echo "locations" & bcp locations in "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\locations.csv" -S cphi.database.windows.net -d stockpulse -U %user% -P %pass% -e "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\error.txt" -E -q -c -t "|^|"
echo "resolutiontypes" & bcp resolutiontypes in "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\resolutiontypes.csv" -S cphi.database.windows.net -d stockpulse -U %user% -P %pass% -e "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\error.txt" -E -q -c -t "|^|"
echo "reconcilingitemtypes" & bcp reconcilingitemtypes in "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\reconcilingitemtypes.csv" -S cphi.database.windows.net -d stockpulse -U %user% -P %pass% -e "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\error.txt" -E -q -c -t "|^|"
echo "sites" & bcp sites in "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\sites.csv" -S cphi.database.windows.net -d stockpulse -U %user% -P %pass% -e "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\error.txt" -E -q -c -t "|^|"
echo "statuses" & bcp statuses in "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\statuses.csv" -S cphi.database.windows.net -d stockpulse -U %user% -P %pass% -e "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\error.txt" -E -q -c -t "|^|"
echo "stockchecks" & bcp stockchecks in "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\stockchecks.csv" -S cphi.database.windows.net -d stockpulse -U %user% -P %pass% -e "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\error.txt" -E -q -c -t "|^|"
echo "scans" & bcp scans in "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\scans.csv" -S cphi.database.windows.net -d stockpulse -U %user% -P %pass% -e "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\error.txt" -E -q -c -t "|^|"
echo "users" & bcp users in "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\users.csv" -S cphi.database.windows.net -d stockpulse -U %user% -P %pass% -e "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\error.txt" -E -q -c -t "|^|"
echo "globalparams" & bcp globalparams in "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\globalparams.csv" -S cphi.database.windows.net -d stockpulse -U %user% -P %pass% -e "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\error.txt" -E -q -c -t "|^|"
echo "usersites" & bcp usersites in "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\usersites.csv" -S cphi.database.windows.net -d stockpulse -U %user% -P %pass% -e "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\error.txt" -E -q -c -t "|^|"
echo "sitelocations" & bcp sitelocations in "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\sitelocations.csv" -S cphi.database.windows.net -d stockpulse -U %user% -P %pass% -e "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\error.txt" -E -q -c -t "|^|"
echo "sourcereports" & bcp sourcereports in "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\sourcereports.csv" -S cphi.database.windows.net -d stockpulse -U %user% -P %pass% -e "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\error.txt" -E -q -c -t "|^|"
echo "stockitems" & bcp stockitems in "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\stockitems.csv" -S cphi.database.windows.net -d stockpulse -U %user% -P %pass% -e "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\error.txt" -E -q -c -t "|^|"
echo "missingresolutions" & bcp missingresolutions in "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\missingresolutions.csv" -S cphi.database.windows.net -d stockpulse -U %user% -P %pass% -e "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\error.txt" -E -q -c -t "|^|"
echo "missingresolutionimages" & bcp missingresolutionimages in "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\missingresolutionimages.csv" -S cphi.database.windows.net -d stockpulse -U %user% -P %pass% -e "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\error.txt" -E -q -c -t "|^|"
echo "unknownresolutions" & bcp unknownresolutions in "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\unknownresolutions.csv" -S cphi.database.windows.net -d stockpulse -U %user% -P %pass% -e "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\error.txt" -E -q -c -t "|^|"
echo "unknownresolutionimages" & bcp unknownresolutionimages in "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\unknownresolutionimages.csv" -S cphi.database.windows.net -d stockpulse -U %user% -P %pass% -e "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\error.txt" -E -q -c -t "|^|"
 echo "reconcilingitems" & bcp reconcilingitems in "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\reconcilingitems.csv" -S cphi.database.windows.net -d stockpulse -U %user% -P %pass% -e "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\error.txt" -E -q -c -t "|^|"
echo "financiallines" & bcp financiallines in "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\financiallines.csv" -S cphi.database.windows.net -d stockpulse -U %user% -P %pass% -e "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\error.txt" -E -q -c -t "|^|"
echo "importmasks" & bcp importmasks in "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\importmasks.csv" -S cphi.database.windows.net -d stockpulse -U %user% -P %pass% -e "C:\Users\<USER>\cphi.co.uk\cphi.co.uk Team Site - Documents\Stockpulse\dataMigration\exportfiles\error.txt" -E -q -c -t "|^|"

cmd /k 
@echo on