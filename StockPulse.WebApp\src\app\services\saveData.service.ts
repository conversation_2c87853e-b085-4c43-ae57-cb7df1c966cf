import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ImportMask } from "../model/ImportMask";
import { ImportMaskSave } from "../model/ImportMaskSave";
import { ImportMaskSaveWithId } from "../model/ImportMaskSaveWithId";
import { ReconcilingItemSave } from "../model/ReconcilingItemSave";
import { ReconcilingItemVM } from "../model/ReconcilingItemVM";
import { StockItem } from '../model/StockItem';
import { ConstantsService } from './constants.service';
import { GetDataService } from './getData.service';
import { SelectionsService } from './selections.service';
import { LoadItemsService } from '../pages/loadItems/loadItems.service';



@Injectable({
  providedIn: 'root'
})
export class SaveDataService {
  




  constructor(
    public http: HttpClient,
    public constants: ConstantsService,
    public selections: SelectionsService,
    public data: GetDataService,
    public loadItemsService: LoadItemsService

  ) {

  }


  getOptions(){
    let headers = new HttpHeaders({
      'Accept': 'application/json', 'Content-Type': 'application/json'
    });
    let options = { headers: headers };

    return options
  }

  saveImportMap(importMap: ImportMask):Observable<any> {
    
    
    let url = this.constants.provideBaseUrl() + '/ImportMasks';
    //let options = this.getHeaders()

    //build objectToPersist
    let importMask: ImportMaskSave = {
      name: importMap.name,
      topRowsToSkip:importMap.topRowsToSkip,
      columnValueEqualsesJSON:JSON.stringify(importMap.columnValueEqualses),
      columnValueDifferentFromsJSON:JSON.stringify(importMap.columnValueDifferentFroms),
      columnValueNotNullsJSON:JSON.stringify(importMap.columnValueNotNulls),
      columnsWeWantJSON:JSON.stringify(importMap.columnsWeWant),
      isStandard: importMap.isStandard,
      isMultiSite: importMap.isMultiSite,
      ignoreZeroValues: importMap.ignoreZeroValues
    }
    
    if (importMap.id) {
      //must be existing, so replace
      let importMaskWithId: ImportMaskSaveWithId = {...importMask, id:importMap.id};
      return this.http.put(`${url}/SaveExistingImportMask`, importMaskWithId);
    } else {
      //no id so new
      return this.http.post(`${url}/SaveNewImportMask`, importMask);
    }
  }

 


  bulkSaveNewStockItems(stockItems: StockItem[]) {
    
    return this.http.post(this.constants.provideBaseUrl() + '/StockItems/AddStockItems', stockItems)
  }


  bulkSaveNewReconcilingItems(recItems: ReconcilingItemVM[], reconcilingItemTypeId:number, stockCheckId?: number, fileImportId?: number) {
    
    let items: ReconcilingItemSave[] = []

    recItems.forEach(i=>{
      let item:ReconcilingItemSave = {
        reg:i.reg,
        vin:i.vin,
        reference:i.reference,
        description:i.description,
        comment: i.comment,
        stockCheckId: stockCheckId ? stockCheckId : this.selections.stockCheck.id,
        sourceReportId:1,
        reconcilingItemTypeId:reconcilingItemTypeId,
        site: this.loadItemsService.multiSite ? i.site : null,
        multiSite: this.loadItemsService.multiSite,
        fileImportId: fileImportId
      }

      items.push(item);

    })
    

    return this.http.post(this.constants.provideBaseUrl() + '/ReconcilingItems/ReconcilingItems', items)
  }

}

