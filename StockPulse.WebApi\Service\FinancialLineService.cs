﻿using StockPulse.WebApi.DataAccess;
using StockPulse.WebApi.ViewModel;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace StockPulse.WebApi.Service
{
    public interface IFinancialLineService
    {
        Task<IEnumerable<FinancialLineVM>> GetFinancialLines(int stockcheckId, int userId);
        Task UpdateLine(int stockCheckId, int financialLineId, string description, string notes, decimal balance, int userId);
        Task DeleteLine(int financialLineId, int stockCheckId, int userId);
        Task PostFinancialLines(List<FinancialLineVM> financialLines, int userId, int stockCheckId, int fileImportId);
        Task DeleteAllLines(int stockCheckId, int userId);
    }

    public class FinancialLineService : IFinancialLineService
    {
        //properties of the service
        private readonly IFinancialLineDataAccess financialLineDataAccess;

        //constructor
        public FinancialLineService(IFinancialLineDataAccess financialLineDataAccess)
        {
            this.financialLineDataAccess = financialLineDataAccess;
        }


        //methods of the service
        public async Task<IEnumerable<FinancialLineVM>> GetFinancialLines(int stockcheckId, int userId)
        {
            return await financialLineDataAccess.GetFinancialLines(stockcheckId, userId);
        }

        

        

        public async Task PostFinancialLines(List<FinancialLineVM> financialLines, int userId, int stockCheckId, int fileImportId)
        {
            await financialLineDataAccess.PostFinancialLines(financialLines, userId, stockCheckId, fileImportId);
        }

        public async Task UpdateLine(int stockCheckId, int financialLineId, string description, string notes, decimal balance, int userId)
        {
            await financialLineDataAccess.UpdateLine(stockCheckId, financialLineId, description, notes, balance, userId);
        }

        public async Task DeleteLine(int financialLineId, int stockCheckId, int userId)
        {
            await financialLineDataAccess.DeleteLine(financialLineId, stockCheckId, userId);
        }

        public async Task DeleteAllLines(int stockCheckId, int userId)
        {
            await financialLineDataAccess.DeleteAllLines(stockCheckId, userId);
        }
    }
}
