import { Component, OnInit,  Input, HostListener } from "@angular/core";
import { IndexComponent } from "../../_cellRenderers/index.component";
import { GetDataService } from '../../services/getData.service';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { SelectionsService } from '../../services/selections.service';
import { ConstantsService } from '../../services/constants.service';
import { CphPipe } from '../../cph.pipe';
import { CustomHeaderComponent } from '../../_cellRenderers/customHeader.component';
import { FinancialLineVM } from "src/app/model/FinancialLineVM";
import { GridOptions } from "ag-grid-community";


@Component({
  selector: 'importTbTable',
  template:    `

    <div id="tableContainer">
      <!-- Counter -->
    <div id="counter">
     <h4 *ngIf="rowData">{{constants.pluralise(rowData.length, 'tb line','tb lines')}} to import</h4>
    </div>
    <ag-grid-angular  id="importTable" class="ag-theme-balham" [gridOptions]="mainTableGridOptions" [rowData]="rowData"
    (gridReady)="onGridReady($event)" [getRowId]="getRowNodeId" [frameworkComponents]="frameworkComponents">
    </ag-grid-angular>
    </div>
    
   



    `
  ,
  styles: [
    `
    #tableContainer{position:relative;height:100%;margin: 0em 2em;}
    #counter{position:absolute;top:-2em;height:2em;right:0em;    background: var(--secondaryLighter);      padding: 0.2em 1em;      border-radius: 0.3em 0.3em 0 0;}
    h4{font-weight:700;}
    #importTable{height: 100%}
    .numberChip{position:fixed;top:60px;right:20px;}
  `
  ]
})


export class ImportTbTableComponent implements OnInit {
  @Input() rowData: FinancialLineVM[];

  @HostListener('window:resize', [])
  private onresize (event) {
    if (this.gridApi) this.gridApi.sizeColumnsToFit();
  }

  mainTableGridOptions: GridOptions;
  frameworkComponents: { agColumnHeader: any; };

  //for grid
  public gridApi;
  public importGridApi;
  public gridColumnApi;
  public columnDefs;
  public defaultColDef;
  public getRowNodeId;
  
  

  constructor(
   public data: GetDataService,
   public modalService: NgbModal,
   public selections: SelectionsService,
   public constants: ConstantsService,
   public cphPipe: CphPipe,
  ) {

    this.frameworkComponents = { agColumnHeader: CustomHeaderComponent };
    this.mainTableGridOptions = {
      columnTypes: {
        "numberColumn":{ cellRenderer: (params) => { return this.cphPipe.transform(params.value, 'number', 0) } },
        "labelColumn": { cellClass: 'agAlignCentre', filter: 'agTextColumnFilter' }
      },


    
      columnDefs : [
        { headerName: "", field: "index", cellClass: 'indexCell', cellRendererFramework: IndexComponent, autoHeight: true, width: 100 },
            { headerName: "Code", valueGetter: (params) => this.accountCodeToInt(params.data), width: 100, type: "labelColumn" },
            { headerName: "Account Description", field: "description", width: 300, type: 'labelColumn' },
           
            { headerName: "Balance", field: "balance", width: 100, type: 'numberColumn' },
        //     { headerName: "", field: "", width: 60, type: 'labelColumn', filter: null, cellRendererFramework: DeleteButtonComponent, cellRendererParams: { onClick: this.deleteItem.bind(this), } },
      ]
    }

  }

  accountCodeToInt(data: any) {
    if (!data.accountCode) return '';
    return data.accountCode.replace(/['"]+/g, '');
  }


  ngOnInit(): void {
    this.initParams()
    
  }
  

  initParams() {
   // this.selections.dmsStock.filter.valueChanges.subscribe(result=>this.search(result))
  }
  
  onGridReady(params) {
    this.gridApi = params.api;
    this.gridApi.sizeColumnsToFit();
    
  }



  
 

}


