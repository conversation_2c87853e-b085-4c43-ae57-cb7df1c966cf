
export enum PreferenceKey {
  KeepMenuFixed = 'KeepMenuFixed'
}

export type PreferenceTypeMap = {
  [PreferenceKey.KeepMenuFixed]: boolean;
};

type PreferenceType = 'string' | 'stringArray' | 'boolean';
export const PreferenceTypeMeta: { [key in PreferenceKey]: PreferenceType } = {
  [PreferenceKey.KeepMenuFixed]: 'boolean'
};

export class UserPreference {
  preferenceName: string;
  preference: string

  constructor(pref?: UserPreference) {
    if (pref) {
      this.preferenceName = pref.preferenceName;
      this.preference = pref.preference;
    }
  }

  public getPreference<prefKey extends PreferenceKey>(key: prefKey): PreferenceTypeMap[prefKey] {
    const type = PreferenceTypeMeta[key];

    if (type === 'boolean') {
      return (this.preference === 'true') as PreferenceTypeMap[prefKey]
    }
  }

  public setPreference<prefKey extends PreferenceKey>(key: prefKey, value: PreferenceTypeMap[prefKey]) {
    const type = PreferenceTypeMeta[key];
    this.preferenceName = key.toString();

    if (type === 'boolean') {
      this.preference = value as boolean ? 'true' : 'false';
    }
  }
}
