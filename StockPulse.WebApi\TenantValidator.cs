using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.IdentityModel.JsonWebTokens;

namespace StockPulse.WebApi
{

    public static class TokenValidator
    {
        //public static List<string> validTenantIds = new List<string>() {
        //    "https://sts.windows.net/7def64f8-8b3f-400a-8ad0-e50eb7e77eef/", // CPHI
        //    "https://sts.windows.net/7c646534-1708-41c7-a4b2-a981495b4ebe/", // Stockpulse SSO
        //    "https://sts.windows.net/4f2307dc-bbad-4e77-b0fa-8739782da8a6/", // Vindis
        //    "https://sts.windows.net/f1a215ee-6910-4213-aec6-ac36fdca6048/", // Lithia
        //};


        public static string ValidateTenantId(string issuer, SecurityToken securityToken, TokenValidationParameters validationParameters)
        {
            JsonWebToken jwtToken = securityToken as JsonWebToken;
            if (jwtToken == null)
            {
                throw new ArgumentNullException(nameof(securityToken), $"{nameof(securityToken)} cannot be null.");
            }

            if (validationParameters == null)
            {
                throw new ArgumentNullException(nameof(validationParameters), $"{nameof(validationParameters)} cannot be null.");
            }

            // Extract the tenant Id from the claims
            string tenantId = jwtToken.Claims.FirstOrDefault(c => c.Type == "tid")?.Value;
            if (string.IsNullOrWhiteSpace(tenantId))
            {
                throw new SecurityTokenInvalidIssuerException("The 'tid' claim is not present in the token obtained from Azure Active Directory.");
            }

            // Consider the aliases (https://login.microsoftonline.com (v2.0 tokens) => https://sts.windows.net (v1.0 tokens) )
            issuer = issuer.Replace("https://login.microsoftonline.com", "https://sts.windows.net");

            //// Consider tokens provided both by v1.0 and v2.0 issuers
            issuer = issuer.Replace("/v2.0", "/");

            //if (!validTenantIds.Contains(issuer))
            //{
            //    throw new SecurityTokenInvalidIssuerException("Issuer does not match any of the valid issuers provided for this application.");
            //}
            //else
            //{
                return issuer;
            //}
        }


    }


 
}
