﻿using System.Collections.Generic;

namespace StockPulse.WebApi.ViewModel
{
    public class ReconciliationResults
    {
        public ReconciliationResults()
        {
            duplicatedScans = new List<MatchedItem>();
            scansMatchedToStockItems = new List<MatchedItem>();
            scansMatchedToOtherSiteStockItems = new List<MatchedItem>();
            scansMatchedToRecItems = new List<MatchedItem>();
            remainingUnmatchedScans = new List<MatchedItem>();
            
            duplicatedStockItems = new List<MatchedItem>();
            stockItemsMatchedToOtherSiteScans = new List<MatchedItem>();
            stockItemsMatchedToScans = new List<MatchedItem>();
            stockItemsMatchedToRecItems = new List<MatchedItem>();
            remainingUnmatchedStockItems = new List<MatchedItem>();
        }



        public List<MatchedItem> duplicatedScans { get; set; }
        public List<MatchedItem> scansMatchedToStockItems { get; set; }
        public List<MatchedItem> scansMatchedToOtherSiteStockItems { get; set; }
        public List<MatchedItem> scansMatchedToRecItems { get; set; }
        public List<MatchedItem> remainingUnmatchedScans { get; set; }
        
        
        public List<MatchedItem> duplicatedStockItems { get; set; }
        public List<MatchedItem> stockItemsMatchedToScans { get; set; }
        public List<MatchedItem> stockItemsMatchedToOtherSiteScans { get; set; }
        public List<MatchedItem> stockItemsMatchedToRecItems { get; set; }
        public List<MatchedItem> remainingUnmatchedStockItems { get; set; }
    }


}
