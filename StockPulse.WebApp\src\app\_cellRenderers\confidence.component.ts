import { Component } from "@angular/core";

import { ICellRendererAngularComp } from "ag-grid-angular";
import { CphPipe } from "../cph.pipe";

// both this and the parent component could be folded into one component as they're both simple, but it illustrates how
// a fuller example could work
@Component({
    selector: 'confidence-cell',
    template:        `
    <div
        *ngIf="interpretedConfidenceAsNumber !== 0"
        class="confidence"
        [ngClass]="{
            'ok': interpretedConfidenceAsNumber >= 80 && interpretedConfidenceAsNumber <= 90,
            'bad': interpretedConfidenceAsNumber < 80
        }">
     <div>{{interpretedConfidence}}</div>
    
  </div>`
    ,
    styles: [
        ` .confidence{  min-height: 1.3em;
    max-width: 5em;
    min-width: 5em;
    border-radius: 1em;
    background-color: #56A369;
    color: #000000;
    height: 2.2em;
    padding: 0px;
    margin: 0px;
    display: flex;
    align-items: center;
    justify-content: center;
      }

            .confidence.ok{background-color:var(--warning);}

        body{
            --badColourDark:hsl(0, 65%, 35%);
            --meh:#ffce00;
        }
      `
    ]
})
export class ConfidenceComponent implements ICellRendererAngularComp {

    params: string = '';
    interpretedConfidence: string = '';
    interpretedConfidenceAsNumber: number = 0;

    constructor(
        public cphPipe: CphPipe,


    ) { }

    agInit(params: any): void {


        if (params.value) {
            this.params = params.value;
            this.interpretedConfidence = this.cphPipe.transform(params.value, 'regConfidence', 0) 
            this.interpretedConfidenceAsNumber = parseInt(params.value);
        } else {
            this.interpretedConfidenceAsNumber = 0;
        }


    }

    refresh(): boolean {
        return false;
    }
}


