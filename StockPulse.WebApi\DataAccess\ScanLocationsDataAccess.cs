﻿using Dapper;
using StockPulse.WebApi.Dapper;
using StockPulse.Model;
using StockPulse.WebApi.ViewModel;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace StockPulse.WebApi.DataAccess
{
    public interface IScanLocationsDataAccess
    {
        Task<IEnumerable<Location>> GetScanLocationsForSiteId(int siteId);
        Task SaveScanLocationForSiteId(SaveScanLocationParams parms);
        Task DeleteScanLocationForSiteId(DeleteScanLocationParams parms);
    }

    public class ScanLocationsDataAccess : IScanLocationsDataAccess
    {
        private readonly IDapper dapper;

        public ScanLocationsDataAccess(IDapper dapper)
        {
            this.dapper = dapper;
        }

        public async Task<IEnumerable<Location>> GetScanLocationsForSiteId(int siteId)
        {
            var paramList = new DynamicParameters();
            paramList.Add("SiteId", siteId);
            return await dapper.GetAllAsync<Location>("dbo.GET_ScanLocationsForSiteId", paramList, System.Data.CommandType.StoredProcedure);
        }

        public async Task SaveScanLocationForSiteId(SaveScanLocationParams parms)
        {
            var paramList = new DynamicParameters();
            paramList.Add("SiteId", parms.siteId);
            paramList.Add("NewLocation", parms.newLocation);
            await dapper.ExecuteAsync("dbo.INSERT_ScanLocationForSiteId", paramList, System.Data.CommandType.StoredProcedure);
        }

        public async Task DeleteScanLocationForSiteId(DeleteScanLocationParams parms)
        {
            var paramList = new DynamicParameters();
            paramList.Add("SiteId", parms.siteId);
            paramList.Add("LocationId", parms.locationId);
            await dapper.ExecuteAsync("dbo.DELETE_ScanLocationForSiteId", paramList, System.Data.CommandType.StoredProcedure);
        }
    }
}
