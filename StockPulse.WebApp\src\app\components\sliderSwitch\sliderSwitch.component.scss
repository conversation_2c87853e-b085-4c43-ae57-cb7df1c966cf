.buttonWithSlider {
    display: flex;
    align-items: center;
    //margin: 0 1em;

    span.sliderText {
        margin-right: 0.5em;
    }
}

.switch {
    position: relative;
    display: inline-block;
    height: 1.8em;
    width: 3.6em;
    margin-bottom: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--primaryDark);
    border: .1em solid var(--primaryDark);
    border-radius: 1.8em;
    -webkit-transition: .4s;
    transition: .4s;
}

.toggle {
    position: absolute;
    content: "";
    -webkit-transition: .4s;
    transition: .4s;
    height: 1.2em;
    width: 1.2em;
    left: .4em;
    bottom: .3em;
    border-radius: 50%;
    background-color: var(--bodyColour) !important;
    z-index: 1;
}

.toggle.active {
    -webkit-transform: translateX(1.6em);
    -ms-transform: translateX(1.6em);
    transform: translateX(1.6em);
}