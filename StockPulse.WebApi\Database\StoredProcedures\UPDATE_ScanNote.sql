﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].[UPDATE_ScanNote]
(
    @ScanId INT = NULL	,
    @NewNote nvarchar(500) = NULL	,
	@UserId INT = NULL

)
AS
BEGIN

SET NOCOUNT ON

Declare @StockCheckId int = null
set @StockCheckId = (select StockCheckId from Scans where Id = @ScanId)

IF (dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0 OR dbo.[CanSaveScans](@StockCheckId) = 0)
BEGIN 
	RETURN
END
	

update Scans set Comment = @NewNote where Id = @ScanId

EXEC [dbo].[UPDATE_StockCheckLastUpdate] @StockCheckId, @UserId

	

END

GO


