


export interface StockCheckExcel {
  Id: number;
  Site: string;
  Region: string;
  StockcheckDate: Date;
  FirstScan: Date;
  LastScan: Date;
  LastUpdated: Date;
  LastUpdatedBy: string;
  Status: string;
  MarkedCompleteBy: string;
  ApprovedBy: string;
  ScannedAndInStock: number;
  TotalIssuesMissingVehicles: number;
  TotalIssuesUnknownVehicles: number;
  OutstandingIssuesMissingVehicles: number;
  OutstandingIssuesMissingValue: number;
  OutstandingIssuesUnknownVehicles: number;
  PercentageComplete: number;
  InStock: number;
  Scans: number;
  IsRegional?: boolean;
  IsTotal?: boolean;
  InStockValue: number;
  GlValue: number;
  Variance: number;
}
