/****** Object:  StoredProcedure [dbo].[INSERT_StockchecksForAllSites] ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].[INSERT_StockchecksForAllSites]
(
	@SelectedDate Date,
    @DealerGroupId INT,
	@UserId INT
)
AS
BEGIN

SET NOCOUNT ON

	DECLARE @firstDayOfMonth Date = DATEADD(DAY, 1 - DAY(GETDATE()), CAST(GETDATE() AS Date));
	DECLARE @todayDate Date = GETDATE();

	UPDATE SC
	SET IsActive = 0
	FROM StockChecks AS SC
	WHERE SC.SiteId IN (

		SELECT S.ID from Sites AS S 
		INNER JOIN Divisions AS D ON S.DivisionId = D.Id
		WHERE D.DealerGroupId = @DealerGroupId
	)

	--Insert new stockchecks and make them Active
	INSERT INTO [dbo].[StockChecks]
			   ([SiteId]
			   ,[StatusId]
			   ,[UserId]
			   ,[ApprovedByAccountantId]
			   ,[ApprovedByGMId]
			   ,[ApprovedById]
			   ,[Date]
			   ,[LastUpdated]
			   ,[IsActive]
			   ,[HasSignoffImage]
			   ,[IsRegional]
			   ,[IsTotal])
		   
	SELECT S.Id, 1, @UserId, NULL, NULL, NULL, @SelectedDate, @todayDate,1,0,0,0 
	FROM Sites AS S 
	INNER JOIN Divisions AS D ON S.DivisionId = D.Id
	WHERE D.DealerGroupId = @DealerGroupId AND S.IsActive = 1

END

GO