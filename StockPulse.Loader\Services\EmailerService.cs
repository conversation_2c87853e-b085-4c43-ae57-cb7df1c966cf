﻿using System;
using System.Collections.Generic;
using Microsoft.Exchange.WebServices.Data;
using Microsoft.Identity.Client;

namespace StockPulse.Loader.Services
{
    public static class EmailerService
    {

        public static void SendErrorMail(string subject, string message, List<string> recipients = null )
        {
            string devMessage = ConfigService.isDev ? "Dev " : string.Empty;
            SendNotificationMail(subject, message, recipients);
        }


        public static void SendNotificationMail(string subject, string message, List<string> recipients = null)
        {
            recipients = recipients ?? new List<string> { "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>" }; //if not specified, use these

            var ewsClient = ProvideExchangeService();

            EmailMessage m = new EmailMessage(ewsClient);

            m.ToRecipients.AddRange(recipients);
            m.Body = new MessageBody(BodyType.HTML, message);
            m.Subject = subject;
            m.Send();
        }

        private static ExchangeService ProvideExchangeService()
        {
            ///SPK-2789 migrated to new approach
            var cca = ConfidentialClientApplicationBuilder
                   .Create(ConfigService.mailAppId)
                   .WithClientSecret(ConfigService.mailSecretValue)
                   .WithTenantId(ConfigService.mailAppTenantId)
                   .Build();
            var ewsScopes = new string[] { "https://outlook.office365.com/.default" };
            AuthenticationResult authResult = cca.AcquireTokenForClient(ewsScopes).ExecuteAsync().Result;
            var ewsClient = new ExchangeService();
            ewsClient.Url = new Uri("https://outlook.office365.com/EWS/Exchange.asmx");
            ewsClient.Credentials = new OAuthCredentials(authResult.AccessToken);
            ewsClient.ImpersonatedUserId = new ImpersonatedUserId(ConnectingIdType.SmtpAddress, ConfigService.mailAccountOutbound);

            return ewsClient;
        }





    }
}
