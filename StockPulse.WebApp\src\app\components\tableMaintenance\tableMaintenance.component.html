<nav class="page-specific-navbar">
  <div class="buttonGroup">
    <div ngbDropdown container="body" class="d-inline-block bringToFont">
      <button class=" btn btn-primary navBarDropDown noRightBorder tableNameDropdown" ngbDropdownToggle placement="bottom"
       triggers="mouseenter:mouseleave">
        <div *ngIf="!service.selectedTableName">Choose Table</div>
        <div *ngIf="service.selectedTableName">
          {{ service.selectedTableName}}</div>
      </button>

      <div  ngbDropdownMenu aria-labelledby="dropdownBasic1">
        <button *ngFor="let table of service.alltables" (click)="getTableData(table.id)" ngbDropdownItem>
          {{ table.name}}
        </button>
      </div>

      
    </div>
  </div>
  <button class="btn btn-primary" (click)="refreshGlobalParams()">Refresh Global Params</button>

</nav>



<div class="content-new">
  <ng-container >
    <tableMaintenanceTable [style]="{'height': '100%'}"></tableMaintenanceTable>
  </ng-container>
</div>

