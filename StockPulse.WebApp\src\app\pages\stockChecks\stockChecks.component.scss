#newStockCheckDate {
  width: 20em;
  border: 1px solid var(--grey80);
}

#importButton {
  margin-left: 1em;
}

.cardCph {
  background: rgba(255, 255, 255, 0.95);
  padding: 4em 1em;
}

table.cph tbody td {
  line-height: 3em;
  padding: 2em 0.5em;
  text-align: right;
}

.newStockCheck {
  width: 60%;
  margin: 0em auto;
  td{padding:1em;}
}

#newStockCheckAllSiteButton {
  margin-left: 1em !important;
  margin-right: 1em !important;
}

#warningMessage {
  margin-top: 1.5em !important;
}

.datePickers {
  display: flex;
  margin: 0 1em;

  .datePicker {
    display: flex;
    white-space: nowrap;
    align-items: center;

    input {
      max-width: 150px;
    }

    button {
      height: auto;
      padding: 1em;
      border-radius: 0 0.375rem 0.375rem 0 !important;
    }
  }

  button {
    height: auto;
  }
}