﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace StockPulse.WebApi.Migrations
{
    /// <inheritdoc />
    public partial class addstockitemandfileitemloadertables : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "FileImport_Loads",
                schema: "import",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false),
                    FileName = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    FileDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    LoadDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    LoadedByUserId = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.ForeignKey(
                        name: "FK_FileImport_Loads_Users_LoadedByUserId",
                        column: x => x.LoadedByUserId,
                        principalSchema: "dbo",
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "StockItem_Loads",
                schema: "import",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false),
                    SourceReportId = table.Column<int>(type: "int", nullable: false),
                    Reg = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Vin = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Description = table.Column<string>(type: "nvarchar(250)", maxLength: 250, nullable: true),
                    DIS = table.Column<int>(type: "int", nullable: true),
                    GroupDIS = table.Column<int>(type: "int", nullable: true),
                    Branch = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Comment = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    StockType = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Reference = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    StockValue = table.Column<decimal>(type: "decimal(15,3)", nullable: false),
                    SiteId = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.ForeignKey(
                        name: "FK_StockItem_Loads_Sites_SiteId",
                        column: x => x.SiteId,
                        principalSchema: "dbo",
                        principalTable: "Sites",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_StockItem_Loads_SourceReports_SourceReportId",
                        column: x => x.SourceReportId,
                        principalSchema: "dbo",
                        principalTable: "SourceReports",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_FileImport_Loads_LoadedByUserId",
                schema: "import",
                table: "FileImport_Loads",
                column: "LoadedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_StockItem_Loads_SiteId",
                schema: "import",
                table: "StockItem_Loads",
                column: "SiteId");

            migrationBuilder.CreateIndex(
                name: "IX_StockItem_Loads_SourceReportId",
                schema: "import",
                table: "StockItem_Loads",
                column: "SourceReportId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "FileImport_Loads",
                schema: "import");

            migrationBuilder.DropTable(
                name: "StockItem_Loads",
                schema: "import");
        }
    }
}
