import { Component, ElementRef, Input, OnInit, SimpleChanges, ViewChild } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ItemFullDetail } from 'src/app/model/ItemFullDetail';
import { ReconciliationState } from 'src/app/model/ReconciliationState';
import { ReconcileService } from 'src/app/pages/reconcile/reconcile.service';
import { ApiAccessService } from 'src/app/services/apiAccess.service';
import { ConstantsService } from 'src/app/services/constants.service';
import { IconService } from 'src/app/services/icon.service';
import { ToastService } from 'src/app/services/newToast.service';
import { SelectionsService } from 'src/app/services/selections.service';
import { Location } from "../../../model/Location";
import { VehicleModalService } from '../vehicleModal.service';
import * as confetti from 'canvas-confetti';

@Component({
  selector: 'vehicleModalBodyNew',
  templateUrl: './vehicleModalBodyNew.component.html',
  styleUrls: ['./vehicleModalBodyNew.component.scss']
})
export class VehicleModalBodyNewComponent implements OnInit {
  @ViewChild('changeLocationModal', { static: true }) changeLocationModal: ElementRef;

  @Input() item: ItemFullDetail;
  @Input() showCompact: boolean;

  originalItem: ItemFullDetail;
  states = ReconciliationState;
  chosenLocation: string = null;
  modalImage: string;
  showRegImage: boolean = true;
  newReg: string;
  newVin: string;
  mapOptions: any;
  amSavingReg: boolean;
  amSavingVin: boolean;
  hoveringVinInfo: boolean;
  amPreviewingVin: boolean;

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public modalService: NgbModal,
    public icon: IconService,
    public apiAccessService: ApiAccessService,
    public toastService: ToastService,
    public service: VehicleModalService,
    public reconcileService: ReconcileService
  ) { }

  ngOnInit() {
    this.initParams();

    this.originalItem = this.constants.clone(this.item)
    this.item.id = this.isStockItem() ? this.item.stockItem.stockItemId : this.item.scan.scanId;
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.item) {
      this.initParams();
    }
  }

  isStockItem() {
    return !!this.item.stockItem;
  }

  initParams() {
    this.showRegImage = true;
    this.service.amEditingReg = false;
    this.service.amEditingVin = false;
    this.newReg = this.item.scan?.scanReg;
    this.newVin = this.item.scan?.scanVin;
    this.mapOptions = this.setMapOptions(this.item);

    if (this.modalImage) {
      this.modalImage = this.amPreviewingVin ? this.item.scan?.vinImageUrl : this.item.scan?.regImageLargeUrl
    }
  }

  private reloadContent(item: ItemFullDetail, isStockItem: boolean) {
    const payload = [
      { key: 'stockCheckId', value: this.selections.stockCheck.id },
      { key: 'stockItemId', value: isStockItem ? item.scan.scanId : 0 },
      { key: 'scanId', value: isStockItem ? 0 : item.scan.scanId },
    ]
    this.apiAccessService.get('StockItems', 'GetItemFullDetail', payload).subscribe((res: ItemFullDetail) => {
      if (!!res.scan && !!res.scan.scanId) {
        //stockitem has a scan so generate the image url
        this.constants.addImageStringsToScan(res.scan)
      }
      this.item = res;
      this.item.id = this.isStockItem() ? this.item.stockItem.stockItemId : this.item.scan.scanId;
      this.originalItem = this.constants.clone(res)

      this.mapOptions = this.setMapOptions(this.item);

      if (!item.stockItem && res.stockItem) { this.confettiExplosion(); }
    })
  }

  cancelEditingReg() {
    this.service.amEditingReg = false;
    this.newReg = this.item.scan?.scanReg;
  }

  cancelEditingVin() {
    this.service.amEditingVin = false;
    this.newVin = this.item.scan?.scanVin;
  }

  editReg() {
    this.service.amEditingReg = true
  }

  editVin() {
    this.service.amEditingVin = true;
  }

  saveNewReg() {
    const savingToast = this.toastService.loadingToast('Saving...');

    let newReg: string = this.newReg.replace(/ /g, '').toUpperCase();
    let scanId: number = this.item.scan.scanId;
    this.amSavingReg = true;

    this.apiAccessService.updateReg(scanId, newReg).subscribe(res => {
      this.toastService.successToast('Updated reg');

      this.item.scan.scanReg = newReg;
      this.selections.stockCheckItemChanged.next(true);
      this.newReg = newReg;
      this.service.amEditingReg = false;
      this.amSavingReg = false;
      savingToast.close();
      this.reloadContent(this.item, false);
    }, e => {
      savingToast.close();
      this.toastService.errorToast('Failed to update reg');
    })
  }

  saveNewVin() {
    const savingToast = this.toastService.loadingToast('Saving...');

    let newVin: string = this.newVin.replace(/ /g, '').toUpperCase();
    let scanId: number = this.item.scan.scanId;
    this.amSavingVin = true;

    this.apiAccessService.updateVin(scanId, newVin).subscribe(res => {
      this.toastService.successToast('Updated VIN');

      this.item.scan.scanVin = newVin;
      this.selections.stockCheckItemChanged.next(true);
      this.newVin = "";
      this.service.amEditingVin = false;
      this.amSavingVin = false;
      savingToast.close();
      this.reloadContent(this.item, false);
    }, e => {
      savingToast.close();
      this.toastService.errorToast('Failed to update VIN');
    })
  }

  reviewLocation() {
    this.chosenLocation = this.item.scan.locationDescription;
    this.modalService.open(this.changeLocationModal, { keyboard: true, ariaLabelledBy: 'modal-basic-title' }).result.then((result) => {
      // this.saveNewLocation();
    })
  }

  saveNewLocation(location: Location) {
    this.chosenLocation = location.description;

    this.apiAccessService.updateLocation(this.item.scan.scanId, this.constants.Locations.find(x => x.description === this.chosenLocation).id).subscribe(res => {
      //ok it's updated
      this.item.scan.locationDescription = this.chosenLocation;
      // this.constants.refreshPage.emit(true);
      this.reconcileService.reloadBar.emit();
    }, e => {
      this.toastService.errorToast('Failed to update scan location');
    })
  }

  chooseLocation(location: Location) {
    this.chosenLocation = location.description;
  }

  setMapOptions(item: ItemFullDetail) {
    return {
      center: {
        lat: item.scan?.latitude,
        lng: item.scan?.longitude
      },
      zoom: 16,
      disableDefaultUI: true,
      streetViewControl: false,
      styles: [
        {
          featureType: "poi.business",
          stylers: [{ visibility: "off" }]
        },
        {
          featureType: "transit",
          elementType: "labels.icon",
          stylers: [{ visibility: "off" }]
        }
      ]
    }
  }

  shouldShowReg() {
    return !this.constants.GlobalParams.find(x => x.name === 'NeverScanReg').boolValue;
  }

  maybeCloseImage(event: any) {
    if (event.target !== event.currentTarget) { return; }
    this.modalImage = null
  }

  confettiExplosion(): void {
    const canvas = document.createElement('canvas');
    canvas.classList.add('confetti-canvas');
    document.body.appendChild(canvas);

    const confettiCanvas = confetti.create(canvas, {
      resize: true
    });

    this.playAudio();

    confettiCanvas({
      particleCount: 100,
      spread: 160
    })

    setTimeout(() => {
      document.body.removeChild(canvas);
    }, 1000)
  }

  playAudio() {
    let audio: HTMLAudioElement = new Audio();
    audio.src = "../../assets/audio/success.mp3";
    audio.load();
    audio.play();
  }
}
