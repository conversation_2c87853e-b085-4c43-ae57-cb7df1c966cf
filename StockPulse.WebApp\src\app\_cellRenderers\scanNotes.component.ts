import { Component } from "@angular/core";

import { ICellRendererAngularComp } from "ag-grid-angular";


// both this and the parent component could be folded into one component as they're both simple, but it illustrates how
// a fuller example could work
@Component({
    selector: 'scanNotes-cell',
    template:
        `<div class="cellContentBox">{{show}}</div>`
    ,
    styles: [
        ` .cellContentBox{    white-space: normal;            line-height: 1.5em;            }
      `
    ]
})
export class ScanNotesComponent implements ICellRendererAngularComp {
    show: string = '';
    constructor(    ) { }

    agInit(params: any): void {

        if (params.value  ) {
            this.show = params.value;
            
        } else {
        this.show = '';    
        
        }
    }
    refresh(): boolean {
        return false;
    }
}


