<ng-template #modalRef let-modal>
    <div class="modal-header">
        <h4 class="modal-title" id="modal-basic-title">{{ confirmModalHeader }}</h4>
        <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <div class="modal-body alertModalBody lowHeight">

    </div>
    <div class="modal-footer">
        <button type="button" [ngClass]="isDestructive ? 'btn-danger' : 'btn-primary'" class="btn" (click)="modal.close()">OK</button>
        <button type="button" class="btn btn-primary" (click)="modal.dismiss('Cancelled')">Cancel</button>
    </div>
</ng-template>