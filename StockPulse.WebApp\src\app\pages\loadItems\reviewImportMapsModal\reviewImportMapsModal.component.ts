import { Component, OnInit } from '@angular/core';
import { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ColumnApi, GridApi } from 'ag-grid-community';
import { GridOptionsCph } from 'src/app/model/GridOptionsCph';
import { ImportMaskVM } from 'src/app/model/ImportMaskVM';
import { Changes, UpdateImportMaskParams } from 'src/app/model/RenameImportMaskParams';
import { ApiAccessService } from 'src/app/services/apiAccess.service';
import { ConstantsService } from 'src/app/services/constants.service';
import { ToastService } from 'src/app/services/newToast.service';
import { SelectionsService } from 'src/app/services/selections.service';
import { CheckboxComponent } from 'src/app/_cellRenderers/checkBox';
import { DeleteImportMapButtonComponent } from './deleteButton';
import { ReviewImportMapsModalService } from './reviewImportMapsModal.service';

@Component({
    selector: 'reviewImportMapsModal',
    templateUrl: './reviewImportMapsModal.component.html',
    styleUrls: ['./reviewImportMapsModal.component.scss']
})
export class ReviewImportMapsModal implements OnInit {
    gridOptions: GridOptionsCph;
    gridApi: GridApi;
    columnApi: ColumnApi;
    changes: Changes[] = [];
    guidanceNote: string;

    constructor(
        public activeModal: NgbActiveModal,
        public constantsService: ConstantsService,
        public apiAccessService: ApiAccessService,
        public modalService: NgbModal,
        public toastService: ToastService,
        public selectionsService: SelectionsService,
        public service: ReviewImportMapsModalService
    ) { }

    ngOnInit() {
        this.toastService.loadingToast();
        this.getImportMasks();
        this.guidanceNote = 'Click edit to rename or delete an import map. You may only modify maps which you created. These will be highlighted once you start editing.'
    }

    getImportMasks() {
        this.apiAccessService.get('ImportMasks', 'GetImportMasks').subscribe((res: ImportMaskVM[]) => {
            this.service.importMasks = res.sort((a, b) => a.name.trim().localeCompare(b.name.trim()));
            this.service.importMasksCopy = JSON.parse(JSON.stringify(this.service.importMasks));
            this.initialiseGrid();
        })
    }

    initialiseGrid() {
        if (this.gridApi) {
            this.gridApi.setRowData(this.constantsService.clone(this.service.importMasks));
            this.gridApi.redrawRows();
        } else {
            this.gridOptions = {
                context: { componentParent: this },
                rowData: this.constantsService.clone(this.service.importMasks),
                defaultColDef: {
                    floatingFilter: true,
                    editable: (params) => (params.data.userId === this.selectionsService.userId || this.selectionsService.userRole === 'SysAdministrator') && this.service.editing && params.colDef.colId === 'name' ? true : false
                },
                singleClickEdit: true,
                columnTypes: {
                    "label": { cellClass: 'agAlignLeft', filter: 'agTextColumnFilter', wrapText: true, autoHeight: true }
                },
                columnDefs: this.getColumnDefs(),
                onGridReady: (params) => this.onGridReady(params),
                onCellEditingStopped: (params) => this.onCellEditingStopped(params),
                // getRowClass: (params) => {
                //     if ((params.data.userId === this.selectionsService.userId || this.selectionsService.userRole === 'SysAdministrator') && this.service.editing) return 'userCreatedMask';
                // }
            }
        }
    }

    getColumnDefs() {
        return [
            { headerName: 'Import Map Title', field: 'name', colId: 'name', type: 'label', cellEditorParams: { maxLength: 50 } },
            { headerName: 'Created By', field: 'createdBy', colId: 'createdBy', type: 'label' },
            {
                headerName: 'Standard', field: 'isStandard', colId: 'isStandard', cellRenderer: CheckboxComponent,
                cellRendererParams: { disabled: !this.service.editing, parent: this, nameField: 'name' }, maxWidth: 75
            },
            { headerName: '', cellRenderer: DeleteImportMapButtonComponent, colId: 'edit', maxWidth: 50 }
        ]
    }

    onGridReady(params) {
        this.gridApi = params.api;
        this.columnApi = params.columnApi;
        this.gridApi.sizeColumnsToFit();
        this.toastService.destroyToast();
    }

    edit() {
        this.service.editing = true;
        this.gridApi.setColumnDefs(this.getColumnDefs());
        this.gridApi.redrawRows();
    }

    maybeSave() {
        this.constantsService.alertModal.title = 'Confirm changes';

        let message: string = 'You have made the following changes:\n\n';

        this.changes.forEach(change => {
            if (change.oldValue !== change.newValue) {
                message += `● "${change.oldValue}" updated to "${change.newValue}"\n`;
            } else {
                if (change.isStandard) {
                    message += `● "${change.oldValue}" set to standard\n`;
                } else {
                    message += `● "${change.oldValue}" no longer standard\n`;
                }
            }
        })

        message += '\nAre you sure you want to continue?'

        this.constantsService.alertModal.message = message;
        this.constantsService.alertModal.showOkInSuccessColour = true;

        this.modalService.open(this.constantsService.alertModal.elementRef, { size: 'sm', keyboard: true, ariaLabelledBy: 'modal-basic-title' }).result.then((result) => {
            this.save();
        }, (reason) => {
            this.cancel();
            return;
        });
    }

    save() {
        const params: UpdateImportMaskParams = {
            MasksToUpdate: this.changes
        }

        this.apiAccessService.renameImportMasks(params).subscribe(res => {
            this.toastService.successToast('Import maps updated');
            this.getImportMasks();
            this.constantsService.importMasksUpdatedEmitter.emit();
        }, e => {
            this.toastService.errorToast('Failed to update import maps');
        })

        this.cancel();
    }

    cancel() {
        this.changes = [];
        this.service.editing = false;
        this.gridApi.setColumnDefs(this.getColumnDefs());
        this.gridApi.setRowData(this.constantsService.clone(this.service.importMasks));
        this.gridApi.redrawRows();
    }

    onCellEditingStopped(params) {
        if (params.oldValue === params.newValue) { return; }
        let existingChange: Changes = this.changes.find(x => x.id === params.data.id);
        this.updateChanges(params.data.id, existingChange ? existingChange.isStandard : params.data.isStandard, params.newValue);
    }

    close() {
        this.cancel();
        this.activeModal.dismiss();
    }

    updateChanges(id: number, isStandard: boolean, name: string) {
        let original: ImportMaskVM = this.service.importMasksCopy.find(x => x.id === id);
        let existingChange: Changes = this.changes.find(x => x.id === id);

        if (existingChange) {
            const noChangeIsStandard: boolean = original.isStandard === isStandard;
            const noChangeName: boolean = original.name === name;

            if (noChangeIsStandard && noChangeName) {
                this.changes = this.changes.filter(x => x.id !== id);
            } else {
                existingChange.isStandard = isStandard;
                existingChange.newValue = name;
            }
        } else {
            this.changes.push({
                oldValue: original.name,
                newValue: name,
                id: original.id,
                isStandard: isStandard
            })
        }

        console.log(this.changes);
    }
}