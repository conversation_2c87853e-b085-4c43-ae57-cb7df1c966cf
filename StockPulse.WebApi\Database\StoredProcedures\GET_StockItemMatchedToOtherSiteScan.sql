

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO
  
CREATE OR ALTER PROCEDURE [dbo].[GET_StockItemMatchedToOtherSiteScan]  
(  
    @StockCheckId INT = NULL,  
    @UserId INT = NULL  
)  
AS  
BEGIN  
  
SET NOCOUNT ON;  
  
DECLARE @isRegional INT;    
DECLARE @isTotal INT;    
DECLARE @StockCheckDate DateTime;  
DECLARE @DivisionId INT = NULL;  
DECLARE @DealerGroupId INT = NULL;  
DECLARE @SCId INT = NULL;  
  
  
IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END

  
SELECT   
@isRegional = IsRegional,   
@isTotal = IsTotal,   
@StockCheckDate = Date   
FROM StockChecks    
WHERE StockChecks.Id = @StockCheckId  
    
  

  
  
   
IF @isRegional = 0 AND @isTotal = 0    
    
    BEGIN    
  
 SET @SCId = @StockCheckId;  
    
  
    END    
    
ELSE IF @isRegional = 1 AND @isTotal = 0    
    
    BEGIN    
    
    
    SET @DivisionId = (SELECT DivisionId FROM StockChecks    
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId    
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id    
                        WHERE StockChecks.Id = @StockCheckId AND StockChecks.IsRegional = 1)    
    
    END    
    
ELSE IF @isRegional = 0 AND @isTotal = 1    
    
    BEGIN    
    
        
    SET @DealerGroupId = (SELECT DealerGroupId FROM StockChecks    
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId    
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id    
                        WHERE StockChecks.Id = @StockCheckId)    
    
    
    END   
  
  
     
        SELECT StockItems.[Id]   as StockItemId
        ,StockItems.ScanId  
        ,StockItems.[ReconcilingItemId]  
        ,StockItems.[MissingResolutionId]  
        ,StockItems.[StockCheckId]  
        ,StockItems.[SourceReportId]  
        ,StockItems.[Reg]  
        ,StockItems.[Vin]  
        ,StockItems.[Description]  
        ,StockItems.[DIS]  
        ,StockItems.[GroupDIS]  
        ,StockItems.[Branch]  
        ,StockItems.[Comment]  
        ,StockItems.[StockType]  
        ,StockItems.[Reference]  
        ,StockItems.[StockValue]  
        ,StockItems.[Flooring]
        ,Sites.Id AS matchingSiteId   
        ,matchSite.Description AS MatchingSiteDescription  
        ,Scans.Id AS matchingScanId  
        ,Scans.[Description] AS matchingScanDescription  
        ,Locations.[Description] AS matchingScanLocationDesc  
        ,Users.Name AS matchingScanScannedBy  
        ,Scans.[ScanDateTime] AS matchingScanDateTime  
        ,Sites.Description AS SiteName  ,
        'MatchedToOtherSite' as State
        FROM [dbo].[StockItems]   
        INNER JOIN Scans ON Scans.Id=StockItems.ScanId  
        INNER JOIN StockChecks AS SC ON SC.Id=[StockItems].StockCheckId  
        INNER JOIN Sites ON Sites.Id=SC.SiteId 
		INNER JOIN StockChecks matchScheck on matchScheck.id = scans.StockCheckId
		INNER JOIN Sites matchSite on matchSite.id = matchScheck.SiteId
      INNER JOIN Users ON Users.Id=Scans.UserId  
      INNER JOIN Locations ON Locations.Id=Scans.LocationId  
      INNER JOIN Divisions AS D ON D.Id=Sites.DivisionId  
      INNER JOIN DealerGroup AS DG ON D.DealerGroupId=DG.Id  
        WHERE   
  Scans.StockCheckId <> StockItems.StockCheckId   
  AND SC.Id = ISNULL(@SCId, SC.Id)  
  AND SC.Date = @StockCheckDate  
  AND D.Id = ISNULL(@DivisionId, D.Id)  
  AND DG.Id = ISNULL(@DealerGroupId, DG.Id)  
  
  

  
END  
  
GO