﻿/*using Dapper;
using StockPulse.WebApi.Dapper;

using System.Collections.Generic;
using System.Threading.Tasks;

namespace StockPulse.WebApi.DataAccess
{
    public interface ISourceReportDataAccess
    {
        public Task<IEnumerable<SourceReport>> GetSourceReports(int userIds);
    }

    public class SourceReportDataAccess : ISourceReportDataAccess
    {
        private readonly IDapper dapper;

        public SourceReportDataAccess(IDapper dapper)
        {
            this.dapper = dapper;
        }

        public async Task<IEnumerable<SourceReport>> GetSourceReports(int userId)
        {
            var paramList = new DynamicParameters();
            paramList.Add("UserId", userId);

            return await dapper.GetAllAsync<SourceReport>("dbo.GET_SourceReports", paramList, System.Data.CommandType.StoredProcedure);

        }

    }
}
*/