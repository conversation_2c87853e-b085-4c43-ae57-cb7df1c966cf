.bad {
    background: var(--dangerLighter) !important;
}

.meh {
    background: var(--grey70) !important;
}

.badFont {
    color: var(--danger);
    font-weight: 700;
}

.badFontRegular {
    color: var(--danger);
}

body {
    height: 100vh;
    width: 100vw;
    overflow: auto;
    margin: 0 auto;
    font-family: apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", <PERSON>l, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}

a {
    text-decoration: none;
    cursor: pointer;
}

.flex {
    display: flex;
}

.spaceBetween {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%
}

.spaceBetween.column {
    flex-direction: column;
}

::selection {
    background: var(--secondaryLighter);
}

.regPlate,
.chassis {
    font-weight: 700;
    text-align: center;
    padding: 0em 0.5em;
    display: flex !important;
    justify-content: center;
    align-items: center;
    height: 2em;
    position: relative;
    overflow: hidden;
}

.regPlate,
.chassis,
.plateBox {
    &.strikeThrough::before {
        content: "";
        position: absolute;
        bottom: 0;
        left: 0;
        width: 110%;
        height: 100%;
        transform-origin: bottom left;
        transform: rotate(-13deg);
        overflow: hidden;
    }
}

.regPlate {
    background-color: var(--numberPlate);
    color: black;
    font-family: 'NumberPlate';
    border-radius: 0.2em;
    border: 1px solid hsl(210, 13%, 70%) !important;
    width: 6.5em;
}

.regPlate,
.regBox {
    &.strikeThrough::before {
        border-bottom: 1px solid black;
    }
}

.chassis {
    background-color: black;
    color: var(--grey80);
    font-family: 'VIN';
    border: 1px solid black;
    letter-spacing: 2px;
    width: 7em;
}

.chassis,
.vinBox {
    &.strikeThrough::before {
        border-bottom: 1px solid white;
    }
}

.regPlate.inNav,
.chassis.inNav {
    height: 30px;
}

input {
    border: 1px solid var(--grey80);
    background-color: white;
    padding: 0em 0.5em;
}

input:focus {
    position: relative;
    z-index: 2;
    box-shadow: 0 0 5px 2px var(--secondary) !important;
    outline: none;
}

input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}


.hot-toast-bar-base-container {
    top: 35px !important; // Clear navbar
}




.subtleBoxShadow{
    box-shadow: var(--grey90) 0px 0px 10px;
}

.spinAndColour {
    color: var(--secondary);
    animation: spin 2s linear infinite;
}

.spinnerIcon .svg-inline--fa {
    display: block;
}

.ag-row-selected::before {
    background-color: var(--secondaryLight);
}