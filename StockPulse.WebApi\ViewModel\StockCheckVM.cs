﻿using System;

namespace StockPulse.WebApi.ViewModel
{
   


    public class StockCheckVM
    {
        public int Id { get; set; }
        public string Site { get; set; }
        public int SiteId { get; set; }
        public string Person { get; set; }
        public DateTime Date { get; set; }
        public DateTime LastUpdated { get; set; }
        public string Status { get; set; }
        public int StatusId { get; set; }
        public int ScannedInStock { get; set; }
        public int UnknownsTot { get; set; }
        public int MissingsTot { get; set; }
        public int Unknowns { get=>UnknownsTot - UnknownOs; }
        public int Missings { get => MissingsTot - MissingOs; }
        public int UnknownOs { get; set; }
        public int MissingOs { get; set; }
        public bool IsRegional { get; set; }
        public bool IsTotal { get; set; }
        public string ApprovedByAccountant { get; set; }
        public string ApprovedByGM{ get; set; }
        public string ApprovedBy{ get; set; }
        public bool HasSignoffImage { get; set; }
        public DateTime? ReconciliationCompletedDate { get; set; }
        public DateTime? ReconciliationApprovedDate { get; set; }
        public decimal StockItemsCount { get; set; }
        public decimal Scans { get; set; }
        public decimal SiteLongitude { get; set; }
        public decimal SiteLatitude { get; set; }

        public decimal PercentageComplete { get {
                if (StockItemsCount == 0 && Scans == 0) return 0;
                if (StockItemsCount == 0) return Math.Max(1 - (UnknownOs + MissingOs) / (Scans > 0 ? Scans : 1), 0);
                return Math.Max(1 - ((UnknownOs + MissingOs) / Math.Max(StockItemsCount, Scans)), 0);
            }
        }

        public bool OverrideLongLat { get; set; }
        public string SiteRegion { get; set; }
        public DateTime? FirstScan { get; set; }
        public DateTime? LastScan { get; set; }

        public decimal InStockValue { get; set; }
        public decimal UnresolvedMissingValue { get; set; }
        public decimal GLValue {  get; set; }
        public decimal Variance { get => InStockValue - GLValue; }
    }
}
