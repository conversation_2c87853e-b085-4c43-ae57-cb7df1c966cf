<nav class="page-specific-navbar doNotPrint">
  <div class="page-title">
    <singleSitePickerWithSearch></singleSitePickerWithSearch>
  </div>

  <ng-container *ngIf="constants.shouldUploadSignoffImage">
    <span class="me-2">Update Status:</span>
    <status ></status>
  </ng-container>

  <button class="btn btn-primary" (click)="takeScreenshot()">
    Download as PDF
  </button>

  <statusAndBarChart></statusAndBarChart>
</nav>

<div id="screenshot-area" class="content-new">

  <div id="hideOnPrint" class="doNotPrint">
    <instructionRow [message]="'Left table reconciles stock report value to trial balance. Right table summarises the book to physical reconciliation'"></instructionRow>
  </div>

  <div id="stockCheckTitleForPrint" class="hidden">
    <span>{{ selections.stockCheckLongName }}</span>
  </div>

  <div *ngIf="financialLines && reconciliationBuckets && resolutionBuckets" class="d-flex print-stacked" id="cards-container">
    <!-- Left side -->
    <div id="trial-balance-card">
      <table>
        <thead>
          <tr>
            <th>Account Code</th>
            <th>Description</th>
            <th>Balance</th>
            <th class="doNotPrint"></th>
          </tr>
        </thead>
        <tbody>
          <!-- Financial lines -->
          <tr *ngFor="let financialLine of financialLines">
            <td>{{ accountCodeToInt(financialLine.accountCode) }}</td>
            <td>{{ financialLine.description }}</td>
            <td>{{ financialLine.balance | cph:'currency':2 }}</td>
            <td class="doNotPrint">
              <!-- <button class="btn deleteIcon text-danger"
                [disabled]="selections.stockCheck.statusId > 3 || selections.stockCheck.isRegional || selections.stockCheck.isTotal"
                (click)="deleteExplanationLine(financialLine, true)">
                <fa-icon [icon]="icon.faTrash"></fa-icon>
              </button> -->
            </td>
          </tr>
          <tr *ngIf="allowTrialBalanceUpload()">
            <td colspan="4" class="text-center">
              <importTbItems></importTbItems>
            </td>
          </tr>
          <!-- Trial balance -->
          <tr class="sub-header">
            <td>Total trial balance</td>
            <td></td>
            <td>{{ totalTrialBalance | cph:'currency':2 }}</td>
            <td></td>
          </tr>
          <tr *ngFor="let line of financialLinesExplanations">
            <td>{{ line.description }}</td>
            <td>{{ line.notes }}</td>
            <td>{{ line.balance | cph:'currency':2 }}</td>
            <td>
              <div class="d-flex justify-content-end">
                <button class="btn editIcon me-2"
                  [disabled]="selections.stockCheck.statusId > 3 || selections.stockCheck.isRegional || selections.stockCheck.isTotal"
                  (click)="editItem(line)">
                  <fa-icon [icon]="icon.faEdit"></fa-icon>
                </button>
                <button class="btn deleteIcon text-danger"
                  [disabled]="selections.stockCheck.statusId > 3 || selections.stockCheck.isRegional || selections.stockCheck.isTotal"
                  (click)="deleteExplanationLine(line)">
                  <fa-icon [icon]="icon.faTrash"></fa-icon>
                </button>
              </div>
            </td>
          </tr>
          <tr class="doNotPrint">
            <td colspan="4" class="text-center">
              <button class="btn btn-primary"
                [disabled]="selections.stockCheck.statusId > 3  || selections.stockCheck.isRegional || selections.stockCheck.isTotal || selections.userIsGeneralManager || selections.userIsReadOnly"
                (click)="addNewItem()">
                Add new reconciling adjustment
                <fa-icon [icon]="icon.faPlusCircle"></fa-icon>
              </button>
            </td>
          </tr>
          <tr class="sub-header">
            <td>Total adjusted trial balance</td>
            <td></td>
            <td>{{ adjustedTrialBalance | cph:'currency':2 }}</td>
            <td></td>
          </tr>
          <!-- Loaded to StockPulse -->
          <ng-container *ngIf="stockLoadReportSummary && stockLoadReportSummary.length === 0; else loadedToStockPulse">
            <tr>
              <td colspan="4">Nothing loaded to StockPulse</td>
            </tr>
          </ng-container>
          <ng-template #loadedToStockPulse>
            <tr *ngFor="let report of stockLoadReportSummary">
              <td>{{ report.filename }}</td>
              <td class="text-right">{{ report.units }} units</td>
              <td>{{ report?.balance | cph:'currency':2 }}</td>
              <td></td>
            </tr>
            <tr *ngIf="constants.IsSignoffConsignedEnabled">
              <td>Of which consigned:</td>
              <td class="text-right">{{ -stockConsignment?.units }} units</td>
              <td>{{ -stockConsignment?.balance | cph:'currency':2 }}</td>
              <td></td>
            </tr>
          </ng-template>
          <tr class="sub-header">
            <td>Total loaded to StockPulse</td>
            <td class="text-right">{{ totals?.loadedUnits | cph:'number':0 }} units</td>
            <td>{{ totals?.loaded | cph:'currency':2 }}</td>
            <td></td>
          </tr>
          <tr>
            <td>Difference</td>
            <td></td>
            <td>
              <span [ngClass]="{ 'text-danger': totals?.difference !== 0 }">
                {{ totals?.difference | cph:'currency':2 }}
              </span>
            </td>
            <td></td>
          </tr>
        </tbody>
      </table>
    </div>
    <!-- Right side -->
    <div id="reconciliation-signoff-card">
      <!-- Tables -->
      <div id="reconciliation">
        <div class="d-flex gap-4">
          <!-- Left side -->
          <div class="card-section">
            <table>
              <tbody>
                <tr *ngFor="let bucket of reconciliationBuckets" [ngClass]="{ 'sub-header': bucket.isFullHeight }">
                  <td>{{ bucket.description }}</td>
                  <td>
                    {{ bucket.vehicleCount | cph:'number':0 }}
                    <div *ngIf="bucket.description === 'Missing resolved'" class="brace brace-top">
                      <span>(A)</span>
                    </div>
                    <div *ngIf="bucket.description === 'Unknown resolved'" class="brace brace-top">
                      <span>(B)</span>
                    </div>
                    <div *ngIf="bucket.description === 'Missing unresolved' || bucket.description === 'Unknown unresolved'" class="brace brace-bottom"></div>
                  </td>
                  <td>
                    <span *ngIf="bucket.isStock">{{ bucket.inStockValue | cph:'currency':2 }}</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <!-- Right side -->
          <div class="card-section">
            <table>
              <tbody>
                <!-- Missings -->
                <tr class="sub-header">
                  <td>(A) Missing vehicles</td>
                  <td>{{ totalMissings | cph:'number':0 }}</td>
                  <td>{{ totalInStockValue | cph:'currency':2 }}</td>
                </tr>
                <tr>
                  <td colspan="3">of which:</td>
                </tr>
                <tr *ngFor="let each of resolutionBucketsMissingResolved">
                  <td>{{ each.description }}</td>
                  <td>{{ each.vehicleCount | cph:'number':0 }}</td>
                  <td>{{ each.inStockValue | cph:'currency':2 }}</td>
                </tr>
                <tr>
                  <td>Unresolved missing vehicles</td>
                  <td [ngClass]="{ 'text-danger': totalMissingsUnresolved > 0 }">
                    {{ totalMissingsUnresolved | cph:'number':0 }}
                  </td>
                  <td></td>
                </tr>
                <!-- Unknowns -->
                <tr class="sub-header">
                  <td>(B) Unknown vehicles</td>
                  <td>{{ totalUnknowns | cph:'number':0 }}</td>
                  <td></td>
                </tr>
                <tr>
                  <td colspan="3">of which:</td>
                </tr>
                <tr *ngFor="let each of resolutionBucketsUnknownResolved">
                  <td>{{ each.description }}</td>
                  <td>{{ each.vehicleCount | cph:'number':0 }}</td>
                  <td></td>
                </tr>
                <tr>
                  <td>Unresolved unknown vehicles</td>
                  <td [ngClass]="{ 'text-danger': totalUnknownsUnResolved > 0 }">
                    {{ totalUnknownsUnResolved | cph:'number':0 }}
                  </td>
                  <td></td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
      <!-- Signature panel -->
      <div id="signoff" [hidden]="constants.shouldUploadSignoffImage">
        <div id="signoffInteractions">
          <div *ngIf="!constants.shouldUploadSignoffImage && selections.stockCheck.statusId >= 4 && !selections.userIsApprover" class="mb-2">
            To make changes to this stock check, please ask an Approver to move the status below 'Reconciliation
            Complete' state.
          </div>
          <div *ngIf="!constants.shouldUploadSignoffImage" id="status">
            <div id="current-status">
              Current stock check status:

              <div ngbDropdown container="body" placement="bottom-right" class="d-inline-block">
                <button class="btn btn-primary" ngbDropdownToggle
                  [disabled]="(selections.stockCheck.statusId > 3 && !selections.userIsApprover) || selections.userIsReadOnly">
                  <span *ngIf="chosenNewStatus && chosenNewStatus.description">
                    {{ chosenNewStatus.description }}
                  </span>
                  <span *ngIf="!chosenNewStatus || !chosenNewStatus.description">
                    {{ selections.stockCheck.status }}
                  </span>
                </button>
                <div ngbDropdownMenu class="dropdown-menu-left" aria-labelledby="dropdownBasic1">
                  <button
                    *ngFor="let status of constants.Statuses"
                    ngbDropdownItem
                    (click)="chooseNewStatus(status)"
                    [disabled]="constants.disableStatus(status, totals)"
                    [ngbPopover]="
                      status.id === 5 && selections.stockCheck.statusId < 4
                        ? 'Stock check must be set to complete before it can be approved'
                        : null
                    "
                    placement="end"
                    triggers="mouseenter:mouseleave"
                  >
                    {{ status.description }}
                  </button>
                </div>
              </div>
            </div>

            <div *ngIf="chosenNewStatus && selections.stockCheck.statusId !== chosenNewStatus.id && !hideSaveCancelButtons"
              class="d-flex justify-content-end ms-2">
              <button class="btn btn-success" (click)="saveNewStatus()">
                Confirm
              </button>
              <button class="btn btn-primary" (click)="cancelNewStatus()">
                Cancel
              </button>
            </div>
          </div>
          <div class="d-flex">
            <table>
              <tbody>
                <tr>
                  <td>
                    <div class="signature-box left">
                      <span class="signature-box-header">Marked complete by</span>
                      <div *ngIf="selections.stockCheck.statusId > 3 && !constants.shouldUploadSignoffImage" class="signature h1">
                        {{ selections.stockCheck.approvedByAccountant }}
                      </div>
                      <div *ngIf="selections.stockCheck.reconciliationCompletedDate && !constants.shouldUploadSignoffImage"
                        class="d-flex justify-content-between">
                        {{ selections.stockCheck.reconciliationCompletedDate | cph:'date':0 }} {{
                        selections.stockCheck.reconciliationCompletedDate | cph:'time':0 }}
                        <span *ngIf="!constants.shouldUploadSignoffImage">Reconciler</span>
                      </div>
                    </div>
                  </td>
                  <td>
                    <div *ngIf="selections.stockCheck.statusId > 3" class="signature-box right">
                      <span class="signature-box-header">Stock check approved by</span>
                      <div *ngIf="selections.stockCheck.statusId === 5 && !constants.shouldUploadSignoffImage" class="signature h1">
                        {{ selections.stockCheck.approvedBy }}
                      </div>
                      <div *ngIf="selections.stockCheck.reconciliationApprovedDate && !constants.shouldUploadSignoffImage"
                        class="d-flex justify-content-between">
                        {{ selections.stockCheck.reconciliationApprovedDate | cph:'date':0 }} {{
                        selections.stockCheck.reconciliationApprovedDate | cph:'time':0 }}
                        <span *ngIf="!constants.shouldUploadSignoffImage">Reviewer</span>
                      </div>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <div id="signoffLogs">
          <h4>Status Audit History</h4>
          <div *ngIf="signOffLogs && signOffLogs.length > 0">
            <p *ngFor="let log of signOffLogs" class="mb-1">
              {{ log.date | cph:'dateTime':0 }}: Set to {{ log.status }} by {{ log.userName }}
            </p>
          </div>
        </div>
      </div>
      <div id="signoff" *ngIf="constants.shouldUploadSignoffImage">
        <div class="d-flex">
          <table>
            <tbody>
              <tr>
                <td>
                  <div class="signature-box">
                    <span class="signature-box-header">Marked complete by</span>
                    <div class="signature">
                    </div>
                  </div>
                </td>
                <td>
                  <div class="signature-box">
                    <span class="signature-box-header">Stock check approved by</span>
                    <div class="signature">
                    </div>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>

  <div *ngIf="!financialLines || !reconciliationBuckets || !resolutionBuckets" class="d-flex">
    <div id="trial-balance-card">
      <div class="placeholder col-12 placeholder-lg mb-2 placeholder-wave"></div>
      <div class="placeholder col-12 placeholder-wave"></div>
      <div class="placeholder col-12 placeholder-wave"></div>
      <div class="placeholder col-12 placeholder-wave"></div>
      <div class="placeholder col-12 placeholder-wave"></div>
      <div class="placeholder col-12 placeholder-wave"></div>
      <div class="placeholder col-12 placeholder-lg mt-2 placeholder-wave"></div>
      <a tabindex="-1" class="btn btn-primary disabled placeholder col-6 offset-3 my-2 placeholder-wave"></a>
      <div class="placeholder col-12 placeholder-lg mb-2 placeholder-wave"></div>
      <div class="placeholder col-12 placeholder-wave"></div>
      <div class="placeholder col-12 placeholder-lg my-2 placeholder-wave"></div>
      <div class="placeholder col-12 placeholder-wave"></div>
    </div>

    <div id="reconciliation-signoff-card">
      <div id="reconciliation">
        <div class="d-flex gap-4">
          <div class="card-section">
            <div class="placeholder col-12 placeholder-lg mb-2 placeholder-wave"></div>
            <div class="placeholder col-12 placeholder-wave"></div>
            <div class="placeholder col-12 placeholder-wave"></div>
            <div class="placeholder col-12 placeholder-wave"></div>
            <div class="placeholder col-12 placeholder-wave"></div>
            <div class="placeholder col-12 placeholder-wave"></div>
            <div class="placeholder col-12 placeholder-lg my-2 placeholder-wave"></div>
            <div class="placeholder col-12 placeholder-wave"></div>
            <div class="placeholder col-12 placeholder-wave"></div>
            <div class="placeholder col-12 placeholder-wave"></div>
            <div class="placeholder col-12 placeholder-wave"></div>
            <div class="placeholder col-12 placeholder-wave"></div>
          </div>
          <div class="card-section">
            <div class="placeholder col-12 placeholder-lg mb-2 placeholder-wave"></div>
            <div class="placeholder col-12 placeholder-wave"></div>
            <div class="placeholder col-12 placeholder-wave"></div>
            <div class="placeholder col-12 placeholder-wave"></div>
            <div class="placeholder col-12 placeholder-wave"></div>
            <div class="placeholder col-12 placeholder-wave"></div>
            <div class="placeholder col-12 placeholder-lg my-2 placeholder-wave"></div>
            <div class="placeholder col-12 placeholder-wave"></div>
            <div class="placeholder col-12 placeholder-wave"></div>
            <div class="placeholder col-12 placeholder-wave"></div>
            <div class="placeholder col-12 placeholder-wave"></div>
            <div class="placeholder col-12 placeholder-wave"></div>
          </div>
        </div>
      </div>
      <div id="signoff">
        <div class="placeholder col-4 offset-1 placeholder-xl mt-4 placeholder-wave"></div>
        <div class="placeholder col-4 offset-2 placeholder-xl mt-4 placeholder-wave"></div>
      </div>
    </div>
  </div>
</div>

<!-- Add new line Modal -->

<ng-template #newFinancialLine let-modal>
  <form>
    <div class="modal-header">
      <h4 class="modal-title" id="modal-basic-title">
        Add new Trial Balance Reconciling Adjustment
      </h4>
      <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    <div class="modal-body ">
      <table id="newFinancialLineTable" class=" newItem ">
        <tbody>
          <tr>
            <td>Description</td>
            <td>
              <input name="description" [(ngModel)]="description" ngbAutofocus
                [ngClass]="{ 'is-invalid': description  === '' }" type="text" class="w-100" />
            </td>
          </tr>
          <tr>
            <td>Notes</td>
            <td>
              <textarea name="notes" [(ngModel)]="notes" ngbAutofocus [ngClass]="{ 'is-invalid': notes === '' }"
                type="text" class="w-100" rows="3"></textarea>
            </td>
          </tr>
          <tr>
            <td>Balance</td>
            <td>
              <input name="balance" type="text" ngbAutofocus [ngClass]="{ 'is-invalid': balance === '' }"
                [ngModel]="balance" (ngModelChange)="currencyPipe($event)" class="newBalance" />

              <!-- <input class="number" formControlName='Balance' ngbAutofocus
                [ngClass]="{'is-invalid': f.Balance.errors}" type="text" (blur)="currencyPipe($event)" (keyup)="removeNonNumeric($event)" /> -->
            </td>
          </tr>

          <tr>
            <td></td>
            <td></td>
          </tr>
        </tbody>

      </table>


    </div>
    <div class="modal-footer">
      <button class="btn btn-success" [disabled]="!validateForm()" (click)="submitItem()">
        {{ editedLine ? 'Save' : 'Save New Item' }}
      </button>
      <button type="button" class="btn btn-primary" (click)="modal.dismiss('Cancelled')">Close</button>
    </div>
  </form>
</ng-template>


<ng-template #confirmModal let-modal>

  <div class="modal-header">
    <h4 class="modal-title" id="modal-basic-title">
      Are you sure?
    </h4>
    <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body lowHeight">

    <div class="warningNote">
      If you move the stock check status to {{statusRequested.description}}, only an approver can move it back.
      Continue?
    </div>



  </div>
  <div class="modal-footer">

    <button type="button" class="btn btn-success" (click)="modal.close('Ok')">OK</button>
    <button type="button" class="btn btn-primary" (click)="modal.dismiss('Cancelled')">Cancel</button>
  </div>



</ng-template>


<ng-template #HintPopover>
  Set stock check status to {{hoveredStatus.description}}
</ng-template>