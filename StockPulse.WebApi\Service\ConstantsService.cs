﻿using PlateRecognizer;
using System;

namespace StockPulse.WebApi.Service
{
    public static class ConstantsService
    {
        public static decimal CalculateScanDistance(decimal dealershipLongitude, decimal dealershipLatitude, decimal? scanLongitude, decimal? scanLatitude)
        {
            /// https://stackoverflow.com/questions/6366408/calculating-distance-between-two-latitude-and-longitude-geocoordinates
            /// 
            if (scanLongitude != null && scanLatitude != null)
            {
                double latDealership = Decimal.ToDouble(dealershipLatitude);
                double longDealership = Decimal.ToDouble(dealershipLongitude);
                double latScan = Decimal.ToDouble((decimal)scanLatitude);
                double longScan = Decimal.ToDouble((decimal)scanLongitude);
                
                double d1 = latDealership * Math.PI / 180.0;
                double num1 = longDealership * Math.PI / 180.0;
                double d2 = latScan * (Math.PI / 180.0);
                double num2 = longScan * (Math.PI / 180.0) - num1;
                double d3 = (Math.Pow(Math.Sin((d2 - d1) / 2.0), 2.0) + Math.Cos(d1) * Math.Cos(d2) * Math.Pow(Math.Sin(num2 / 2.0), 2.0));

                return (decimal)( 6376500.0 * (2.0 * Math.Atan2(Math.Sqrt(d3), Math.Sqrt(1.0 - d3))) / 1609); /// /1609 converts metres to miles
            }
            else
            {
                return 0;
            }
        }

        public static void TidyUpResponses(PlateReaderResult processResult)
        {
            foreach (var result in processResult.Results)
            {
                result.Plate = result.Plate.Replace(" ", "");   //eliminate all spaces from the results
                if (result.Plate.Length == 7)
                {
                    result.Plate = FixCommonErrorForPlate(result.Plate);
                }

                foreach (var scoreAndPlate in result.ScoresAndPlates)
                {
                    scoreAndPlate.Plate = scoreAndPlate.Plate.Replace(" ", ""); //eliminate all spaces from the results
                    if (scoreAndPlate.Plate.Length == 7)
                    {
                        scoreAndPlate.Plate = FixCommonErrorForPlate(scoreAndPlate.Plate);
                    }
                }
            }
        }


        public static string FixCommonErrorForPlate(string reg)
        {
            char[] characters = reg.ToCharArray();

            // should be letters
            int[] letterIndices = { 0, 1, 4, 5, 6 };
            foreach (var i in letterIndices)
            {
                characters[i] = ConvertCommonMistakes(characters[i], true);
            }

            // should be numbers
            int[] numberIndices = { 2, 3 };
            foreach (var i in numberIndices)
            {
                characters[i] = ConvertCommonMistakes(characters[i], false);
            }

            // put characters back together into reg
            return new string(characters);

        }

        private static char ConvertCommonMistakes(char character, bool shouldBeLetter)
        {
            if (shouldBeLetter)
            {
                switch (character)
                {
                    case '0': return 'O';   //change these numbers to the most likely letter
                    case '1': return 'L';
                    case '8': return 'B';
                    case '5': return 'S';
                    default: return character;
                }
            }
            else
            {
                //should be a number
                switch (character)
                {
                    case 'O': return '0';  //change these letters to the most likely number
                    case 'L': case 'I': return '1';
                    case 'B': return '8';
                    case 'S': return '5';
                    default: return character;
                }
            }
        }
    }
}
