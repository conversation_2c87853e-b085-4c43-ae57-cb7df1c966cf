﻿namespace StockPulse.WebApi.ViewModel
{
    public class ReconciliationBucket
    {
        public string Description { get; set; }
        public int VehicleCount { get; set; }
        public decimal InStockValue { get; set; }
        public decimal Flooring { get; set; }
        public bool IsFullHeight { get; set; }
        public int Order { get; set; }
        public bool IsProblem { get; set; }
        public bool IsStock { get; set; }
        public bool IsScan { get; set; }
        //public bool IsReconciling { get; set; }
        public int? ReconciliationTypeId { get; set; }
        public ReconciliationState ReconciliationState { get; set; }
    }
}
