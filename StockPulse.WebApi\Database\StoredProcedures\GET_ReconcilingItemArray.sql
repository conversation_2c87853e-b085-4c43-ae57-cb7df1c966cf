﻿

CREATE OR ALTER PROCEDURE [dbo].[GET_ReconcilingItemArray]
(
    @StockCheckId INT = NULL,
    @UserId INT = NULL,
    @ReconcilingItemTypeId INT = NULL
)
AS
BEGIN

SET NOCOUNT ON;

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END


SELECT ri.[Id]
    ,ri.[ReconcilingItemTypeId]
    ,ri.[Reg]
    ,ri.[Vin]
    ,ri.[Description]
    ,ri.[Comment]
    ,ri.[Reference]
    ,ri.[SourceReportId]
    ,ri.[StockCheckId]
	,IIF(s.Id IS NOT NULL, 1, 0) AS IsScanned
	,s.Id AS ScanId
    ,fi.FileName
    ,fi.FileDate
    ,fi.LoadDate
	,u.Name As UserName
FROM [dbo].[ReconcilingItems] ri
LEFT JOIN Scans s ON s.ReconcilingItemId = ri.Id
LEFT JOIN [import].[FileImports] fi ON fi.Id = ri.FileImportId
LEFT JOIN Users u ON u.Id = fi.LoadedByUserId
WHERE ri.StockCheckId = @StockCheckId
AND ri.ReconcilingItemTypeId = @ReconcilingItemTypeId

END
