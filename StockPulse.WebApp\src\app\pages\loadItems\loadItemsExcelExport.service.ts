import { Injectable,  } from '@angular/core';
import { SheetToExtractOld } from "../../model/SheetToExtractOld";
import * as excelJS from 'exceljs';
import * as fs from 'file-saver';
import { CphPipe } from '../../cph.pipe';
import { SelectionsService } from '../../services/selections.service';
import { LogoService } from '../../services/logo.service';
import { ApiAccessService } from 'src/app/services/apiAccess.service';
import { ToastService } from 'src/app/services/newToast.service';
import { FinancialLineVM } from 'src/app/model/FinancialLineVM';

@Injectable({
  providedIn: 'root'
})
export class LoadItemsExcelExportService {
  constructor(
    public cphPipe: CphPipe,
    public logo: LogoService,
    public selections: SelectionsService,
    public api: ApiAccessService,
    public toastService: ToastService
  ) { }

  generateDmsExcelSheet(rowData, title: string) {
    let rowsToInclude = [];
    rowData.forEach(row => {
      rowsToInclude.push({
        Reg: row.reg,
        Chassis: row.vin,
        Description: row.description,
        DIS: row.dis,
        GroupDIS: row.groupDIS,
        Branch: row.branch,
        StockType: row.stockType,
        Comment: row.comment,
        Reference: row.reference,
        StockValue: row.stockValue
      });
    })

    let sheet = {
      tableData: rowsToInclude,
    tableName: `${title} - ${rowsToInclude.length}`,
      columnWidths: [20, 20, 100, 10, 10, 20, 20, 30, 30, 10]
    }

    this.downloadExcelFile(sheet);
  }

  generateReconcilingExcelSheet(rowData, explainsMissing?: boolean) {
    let rowsToInclude = [];
    rowData.forEach(row => {
      let rowToInclude = {
        Reg: row.reg,
        Chassis: row.vin,
        Description: row.description,
        Comment: row.comment,
        Reference: row.reference,
      }

      if (!explainsMissing) {
        rowToInclude['MatchedToScan'] = row.scanId !== 0 ? 'Yes' : 'No';
      }
      
      rowsToInclude.push(rowToInclude);
    })

    let sheet = {
      tableData: rowsToInclude,
      tableName: `Reconciling - ${rowsToInclude.length}`,
      columnWidths: [20, 20, 100, 40, 20, 20]
    }

    if (!explainsMissing) {
      sheet.columnWidths.push(20);
    }

    this.downloadExcelFile(sheet);
  }

  generateTrialBalanceSheet(rowData: FinancialLineVM[]) {

    let rowsToInclude = [];

    rowData.forEach(row => {
      rowsToInclude.push({
        AccountCode: row.accountCode,
        AccountName: row.description,
        Balance: row.balance,
        Notes: row.notes
      });
    })

    let sheet = {
      tableData: rowsToInclude,
      tableName: `Trial Balance - ${rowsToInclude.length}`,
      columnWidths: [20, 100, 50, 100, 20]
    }

    this.downloadExcelFile(sheet);
  }

  downloadExcelFile(sheet: SheetToExtractOld) {
    let workbook = new excelJS.Workbook();

    let imageId = workbook.addImage({
      base64: this.logo.provideStockPulseLogo(),
      extension: 'png'
    });

    try {
      //define worksheet
      let worksheet = workbook.addWorksheet(sheet.tableName)

      //generic stuff for worksheet
      worksheet.views = [
        { state: 'frozen', xSplit: 1, ySplit: 3, zoomScale: 85 }
      ];

      //columns things
      let columns = []
      sheet.columnWidths.forEach(w => {
        columns.push({ width: w })
      })
      worksheet.columns = columns;

      //rows
      let titleRow = worksheet.addRow([sheet.tableName])//title
      titleRow.font = { name: 'Calibri', family: 4, size: 16, bold: true, color: 'white', fill: { bgColor: { rgb: '220,230,241' } } };
      worksheet.addRow([]);//blank

      //the table headerRow      
      let keys = [];
      Object.keys(sheet.tableData[0]).forEach(key=>{
        if(key==='Chassis'){keys.push('VIN')}
        // if(key==='MatchedToScan'){keys.push('Matched to Scan')}
        else{keys.push(key)}
      })
      worksheet.addRow(keys)
      
      let colCount = keys.length

      //loop through each column in active range and colour cells
      for (let i = 0; i < colCount; i++) {
        let colLetter = String.fromCharCode(65 + i)
        worksheet.getCell(colLetter + '3').font = { name: 'Calibri', family: 4, size: 11, color: { argb: 'FFFFFFFF' } }
        worksheet.getCell(colLetter + '3').fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF252424' } }
      }

      //the data rows
      sheet.tableData.forEach(x => {
        worksheet.addRow(Object.values(x))
      })

      //images
      let columnCount = worksheet.columns.length
      worksheet.addImage(imageId, {
        tl: { col: columnCount - 1, row: 0 },
        ext: { width: 168, height: 36 },
        editAs: 'absolute'
      });
    }
    catch (e) {
      //carry on
    }

    let workbookName = 'StockPulse Extract ' + new Date().getDate() + new Date().toLocaleString('en-gb', { month: 'short' }) + new Date().getFullYear();
    workbook.xlsx.writeBuffer().then((data) => {
      let blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      fs.saveAs(blob, workbookName + '.xlsx');
    });

    this.toastService.destroyToast();
  }
}
