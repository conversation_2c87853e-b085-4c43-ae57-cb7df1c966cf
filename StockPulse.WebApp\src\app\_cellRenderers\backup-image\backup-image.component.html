<img *ngIf="resolutionImage" [id]="'backup-image-'+resolutionImage?.id" [src]="resolutionImage?.url" class="animated backupImage fadeIn"
    (error)="updateUrl(resolutionImage)" alt="Backup image" [ngbPopover]="imagePopover" [openDelay]="300"
    [closeDelay]="500" container="body" placement="auto" triggers="mouseenter:mouseleave"
    popoverClass="scanImagePopover" (click)="maybeDownloadFile(resolutionImage)">

<ng-template #imagePopover>
    <img class="popoverImage" *ngIf="!showDownloadFilePrompt" [src]="resolutionImage?.url" alt="Backup image">
    <span *ngIf="showDownloadFilePrompt" class="m-2">Click the icon to download the file</span>
</ng-template>