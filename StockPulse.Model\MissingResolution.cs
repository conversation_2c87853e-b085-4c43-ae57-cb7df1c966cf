﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System;

namespace StockPulse.Model
{
    public class MissingResolution
    {
        [Key]
        public int Id { get; set; }

        //public int StockItemId { get; set; }
        //[ForeignKey("StockItemId")]
        //public virtual StockItem StockItem { get; set; }

        public int? ResolutionTypeId { get; set; }
        [ForeignKey("ResolutionTypeId")]
        public virtual ResolutionType ResolutionType { get; set; }


        [Column(TypeName = "datetime")]
        public DateTime? ResolutionDate { get; set; }

        public int UserId { get; set; }
        [ForeignKey("UserId")]
        public virtual User User { get; set; }

        public string Notes { get; set; }

        public bool IsResolved { get; set; }
        public string StockcheckIdAndReference { get; set; }


    }

}