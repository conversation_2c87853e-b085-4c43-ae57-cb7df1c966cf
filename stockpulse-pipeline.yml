# ASP.NET Core (.NET Framework)
# Build and test ASP.NET Core projects targeting the full .NET Framework.
# Add steps that publish symbols, save build artifacts, and more:
# https://docs.microsoft.com/azure/devops/pipelines/languages/dotnet-core

trigger:

  tags:
    include:
    - webapp*
    - all*

pool:
  Default

variables:
  solution: '**/*.sln'
  buildPlatform: 'Any CPU'
  buildConfiguration: 'Release'

jobs:
- job: BuildAndPublishStockpulse
  displayName: Build And Publish StockPulse

  steps:
  - checkout: self
    fetchDepth: 1
    fetchTags: false  # Don't fetch unnecessary tags
    clean: true  # Ensures a fresh checkout
    persistCredentials: true

  - task: NuGetToolInstaller@1

  - task: NuGetCommand@2
    displayName: 'Restore nuget packages'
    inputs:
      restoreSolution: '$(solution)'
      
  - task: DotNetCoreCLI@2
    displayName: 'Build API Project'
    inputs:
      command: 'build'
      arguments: '-c Release -r=linux-x64'
      projects: '**/StockPulse.WebApi.csproj'

  # - task: VSTest@2
  #   displayName: 'Run Unit Tests'
  #   inputs:
  #     testSelector: 'testAssemblies'
  #     testAssemblyVer2: |
  #       **/*UnitTests*.dll
  #       !**\*TestAdapter.dll
  #       !**\obj\**

  - task: DotNetCoreCLI@2
    displayName: 'Publish API Project'
    inputs:
      command: 'publish'
      publishWebProjects: false
      projects: '**/StockPulse.WebApi.csproj'
      zipAfterPublish: true
      arguments: '-o $(Build.ArtifactStagingDirectory)/WebApp/ --runtime=linux-x64 -c Release --no-build'
            
  - task: PublishPipelineArtifact@1
    displayName: 'Publish StockPulse API Artifact'
    inputs:
      targetPath: '$(Build.ArtifactStagingDirectory)/WebApp/' 
      artifactName: 'StockPulseApi'
     
