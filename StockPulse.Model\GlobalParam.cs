﻿using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace StockPulse.Model
{
    public class GlobalParam
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public decimal NumberValue { get; set; }
        public DateTime DateValue { get; set; }
        public bool BoolValue { get; set; }
        public string StringValue { get; set; }

        public int DealerGroupId { get; set; }
        [ForeignKey("DealerGroupId")]
        public virtual DealerGroup DealerGroup { get; set; }
    }
}
