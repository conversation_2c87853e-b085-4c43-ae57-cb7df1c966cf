﻿using Dapper;
using StockPulse.Model;
using StockPulse.WebApi.Dapper;
using StockPulse.Model.Import;
using StockPulse.WebApi.ViewModel;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace StockPulse.WebApi.DataAccess
{
    public interface ISiteDataAccess
    {
        Task<IEnumerable<SiteVM>> GetSites(int userId);
        Task<IEnumerable<Location>> GetLocationsForStockCheck(int siteId, int userId);

        //Task<IEnumerable<Site>> GetSitesForDivision(int divisionId, int userId);
        Task<IEnumerable<SiteVM>> GetAllSites(int userId);
        //Task<SiteLongLat> GetLongAndLat(int stockcheckId, int userId);
        Task<IEnumerable<SiteDescriptionDictionary>> GetAllSitesFromDictionary(int userId);
        Task<IEnumerable<SiteNameLookup>> GetSiteNameLookups(int userId);
        Task UpdateSiteNameLookups(UpdateSiteNameLookupsParams parms, int userId);
        Task DeleteSiteNameLookup(int id, int userId);
    }

    public class SiteDataAccess : ISiteDataAccess
    {
        private readonly IDapper dapper;

        public SiteDataAccess(IDapper dapper)
        {
            this.dapper = dapper;
        }

        // Get all sites a user has permissions to view
        public async Task<IEnumerable<SiteVM>> GetSites(int userId)
        {
            var paramList = new DynamicParameters();
            paramList.Add("UserId", userId);

            return await dapper.GetAllAsync<SiteVM>("dbo.GET_Sites",paramList, System.Data.CommandType.StoredProcedure);

        }

        // Get all Locations linked to a site(seems a bit pointless to have seperate Location controller?)
        public async Task<IEnumerable<Location>> GetLocationsForStockCheck(int stockCheckId, int userId)
        {
            var paramList = new DynamicParameters();
            paramList.Add("StockCheckId", stockCheckId);
            paramList.Add("UserId", userId);
            return await dapper.GetAllAsync<Location>("dbo.GET_LocationsForStockCheck", paramList, System.Data.CommandType.StoredProcedure);

        }

        //public async Task<IEnumerable<Site>> GetSitesForDivision(int divisionId, int userId)
        //{
        //    var paramList = new DynamicParameters();
        //    paramList.Add("DivisionId", divisionId);
        //    paramList.Add("UserId", userId);

        //    return await dapper.GetAllAsync<Site>("dbo.GET_SitesForDivision", paramList, System.Data.CommandType.StoredProcedure);

        //}

        //public async Task<SiteLongLat> GetLongAndLat(int stockcheckId, int userId)
        //{
        //    var paramList = new DynamicParameters();
        //    paramList.Add("StockcheckId", stockcheckId);
        //    paramList.Add("UserId", userId);

        //    return (await dapper.GetAllAsync<SiteLongLat>("dbo.GET_LongAndLat", paramList, System.Data.CommandType.StoredProcedure)).First();
        //}

        public async Task<IEnumerable<SiteVM>> GetAllSites(int userId)
        {
            var paramList = new DynamicParameters();
            paramList.Add("UserId", userId);

            return await dapper.GetAllAsync<SiteVM>("dbo.GET_AllSites", paramList, System.Data.CommandType.StoredProcedure);
        }

        public async Task<IEnumerable<SiteDescriptionDictionary>> GetAllSitesFromDictionary(int userId)
        {
            var paramList = new DynamicParameters();
            paramList.Add("UserId", userId);
            return await dapper.GetAllAsync<SiteDescriptionDictionary>("dbo.GET_AllSitesFromDictionary", paramList, System.Data.CommandType.StoredProcedure);
        }

        public async Task<IEnumerable<SiteNameLookup>> GetSiteNameLookups(int userId)
        {
            var paramList = new DynamicParameters();
            paramList.Add("UserId", userId);
            return await dapper.GetAllAsync<SiteNameLookup>("dbo.GET_SiteNameLookups", paramList, System.Data.CommandType.StoredProcedure);
        }

        public async Task UpdateSiteNameLookups(UpdateSiteNameLookupsParams parms, int userId)
        {
            foreach (var item in parms.changes)
            {
                var paramList = new DynamicParameters();
                paramList.Add("Name", item.newValue);
                paramList.Add("IsPrimary", item.isPrimarySiteId);
                paramList.Add("UserId", userId);
                paramList.Add("SiteId", item.siteId);

                if (item.id == 0)
                {
                    await dapper.ExecuteAsync("dbo.INSERT_SiteNameLookup", paramList, System.Data.CommandType.StoredProcedure);
                }
                else
                {
                    paramList.Add("Id", item.id);
                    await dapper.ExecuteAsync("dbo.UPDATE_SiteNameLookups", paramList, System.Data.CommandType.StoredProcedure);
                }
            }
        }

        public async Task DeleteSiteNameLookup(int id, int userId)
        {
            var paramList = new DynamicParameters();
            paramList.Add("Id", id);
            paramList.Add("UserId", userId);
            await dapper.GetAllAsync<SiteNameLookup>("dbo.DELETE_SiteNameLookup", paramList, System.Data.CommandType.StoredProcedure);
        }
    }
}
