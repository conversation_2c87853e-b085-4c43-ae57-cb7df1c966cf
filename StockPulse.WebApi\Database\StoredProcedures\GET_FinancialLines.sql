﻿
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].[GET_FinancialLines]  
(  
    @StockCheckId INT = NULL,  
    @UserId INT = NULL  
)  
AS  
BEGIN  
  
SET NOCOUNT ON;  
  
IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END

  
DECLARE @isRegional INT;  
SET @isRegional = (SELECT IsRegional FROM StockChecks  
                    WHERE StockChecks.Id = @StockCheckId)  
  
DECLARE @isTotal INT;  
SET @isTotal = (SELECT IsTotal FROM StockChecks  
                    WHERE StockChecks.Id = @StockCheckId)  

 DECLARE @StockCheckDate Date;
  SET @StockCheckDate = (SELECT Date FROM StockChecks WHERE StockChecks.Id = @StockCheckId)
  
IF @isRegional = 0 AND @isTotal = 0  
  
    BEGIN  
   
    SELECT fl.Id,
		fl.[Code] AS AccountCode  
        ,fl.[AccountDescription] AS Description  
        ,fl.[Notes]  
        ,fl.[IsExplanation] AS IsReconcilingAdj  
        ,fl.[Balance] AS Balance
        ,fi.FileName
        ,fi.FileDate
        ,fi.LoadDate
	    ,u.Name As UserName
    FROM [dbo].[FinancialLines] fl
    LEFT JOIN [import].[FileImports] fi ON fi.Id = fl.FileImportId
    LEFT JOIN Users u ON u.Id = fi.LoadedByUserId
    WHERE fl.StockCheckId = @StockCheckId    
  
    END  
  
IF @isRegional = 1 AND @isTotal = 0  
  
    BEGIN  
  
    DECLARE @DivisionId INT;  
  
    SET @DivisionId = (SELECT DivisionId FROM StockChecks
					    INNER JOIN Sites ON Sites.Id=StockChecks.SiteId  
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id  
                        WHERE StockChecks.Id = @StockCheckId AND StockChecks.IsRegional = 1)  
  
    SELECT [Code] AS AccountCode  
    ,[AccountDescription] AS Description  
    ,[Notes]  
    ,[IsExplanation] AS IsReconcilingAdj  
    ,sum([Balance]) AS Balance 
    ,fi.FileName
    ,fi.FileDate
    ,fi.LoadDate
	,u.Name As UserName
    FROM [dbo].[FinancialLines]  
    INNER JOIN StockChecks ON StockChecks.Id=FinancialLines.StockCheckId  
    INNER JOIN Sites ON Sites.Id=StockChecks.SiteId  
    INNER JOIN Divisions ON Divisions.Id=Sites.DivisionId  
    LEFT JOIN [import].[FileImports] fi ON fi.Id = FinancialLines.FileImportId
    LEFT JOIN Users u ON u.Id = fi.LoadedByUserId
    WHERE Divisions.Id = @DivisionId AND StockChecks.Date = @StockCheckDate
    GROUP BY [Code],[AccountDescription],[Notes],[IsExplanation],fi.FileName,fi.FileDate,fi.LoadDate,u.Name
  
    END  
  
IF @isRegional = 0 AND @isTotal = 1  
  
    DECLARE @DealerGroupId INT;  
  
    SET @DealerGroupId = (SELECT DealerGroupId FROM StockChecks  
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId  
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id  
                        WHERE StockChecks.Id = @StockCheckId)  
  
    SELECT [Code] AS AccountCode  
    ,[AccountDescription] AS Description  
    ,[Notes]  
    ,[IsExplanation] AS IsReconcilingAdj  
    ,sum([Balance]) AS Balance 
    ,fi.FileName
    ,fi.FileDate
    ,fi.LoadDate
	,u.Name As UserName
    FROM [dbo].[FinancialLines]  
    INNER JOIN StockChecks ON StockChecks.Id=FinancialLines.StockCheckId  
    INNER JOIN Sites ON Sites.Id=StockChecks.SiteId  
    INNER JOIN Divisions ON Divisions.Id=Sites.DivisionId  
    INNER JOIN DealerGroup ON Divisions.DealerGroupId=DealerGroup.Id  
    LEFT JOIN [import].[FileImports] fi ON fi.Id = FinancialLines.FileImportId
    LEFT JOIN Users u ON u.Id = fi.LoadedByUserId
    WHERE DealerGroup.Id = @DealerGroupId  AND StockChecks.Date = @StockCheckDate
	GROUP BY [Code],[AccountDescription],[Notes],[IsExplanation],fi.FileName,fi.FileDate,fi.LoadDate,u.Name
   
END  
  
Go


