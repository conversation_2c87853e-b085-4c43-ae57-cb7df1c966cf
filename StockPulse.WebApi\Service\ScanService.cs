﻿using Microsoft.ApplicationInsights;
using Microsoft.ApplicationInsights.Extensibility;
using Microsoft.Extensions.Configuration;
using StockPulse.WebApi.DataAccess;
using StockPulse.WebApi.ViewModel;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace StockPulse.WebApi.Service
{
    public interface IScanService
    {
        Task<IEnumerable<ViewModel.Scan>> GetScans(int stockcheckId, int userId);
        Task<IEnumerable<ScanWithResolution>> GetScansWithResolution(int stockcheckId, int userId);
        Task<IEnumerable<ScanRegDifference>> GetScanRegDifference(int stockcheckId, int userId);
        Task<IEnumerable<ScanMatchedToRecItem>> GetScansMatchedToRecItem(int stockcheckId, int? reconcilingItemTypeId, int userId);
        Task UpdateScanReg(int scanId, string reg, int userId, bool isMobileApp);
        Task UpdateLocation(int newLocationId, int scanId, int userId);
        Task UpdateScanVin(int scanId, string vin, int userId, bool isMobileApp);
        Task<IEnumerable<ScanThatIsADuplicate>> GetDuplicateScans(int stockcheckId, int userId);
        Task<IEnumerable<ScanMatchedToOtherSiteStockItem>> GetScanMatchedToOtherSiteStockItem(int stockcheckId, int userId);
        Task<List<ScanUploadConfirmation>> UploadScans(List<ScanUpload> scanWithImageStringsVMs, int userId, int stockcheckId);
        Task SaveUnknownResolution(Resolution resolution, int userId);
        Task DeleteUnknownResolution(int resolutionId, int stockCheckId, int userId);
        Task DeleteScan(int scanId, int userId);
        Task UpdateScanNote(int scanId, string note, int userId);
        Task<ScanSeenBefore> CheckIfRegSeenBefore(string reg, int stockCheckId, int userId);
        Task<List<ScanWithResolution>> GetRepeatUnknowns(List<int> stockCheckIds, int userId);
        Task<ScanSeenBefore> CheckIfVinSeenBefore(string vin, int stockCheckId, int userId);
    }

    public class ScanService : IScanService
    {
        //properties of the service
        private readonly IImageService imageService;
        private readonly IScanDataAccess scanDataAccess;
        private readonly IStockItemDataAccess stockItemDataAccess;
        private readonly IStockCheckDataAccess stockCheckDataAccess;
        private readonly IStockCheckReconciliationService stockCheckReconciliationService;
        private readonly IStockItemService stockItemService;
        private readonly IConfiguration _config;
        private string Connectionstring = "DefaultConnection";

        //constructor
        public ScanService(
            IScanDataAccess scanDataAccess,
            IStockItemDataAccess stockItemDataAccess,
            IStockCheckDataAccess stockCheckDataAccess,
            IImageService imageService,
            IConfiguration config,
            IStockCheckReconciliationService stockCheckReconciliationService,
            IStockItemService stockItemService
            )
        {
            this.scanDataAccess = scanDataAccess;
            this.stockItemDataAccess = stockItemDataAccess;
            this.stockCheckDataAccess = stockCheckDataAccess;
            this.imageService = imageService;
            this._config = config;
            this.stockCheckReconciliationService = stockCheckReconciliationService;
            this.stockItemService = stockItemService;
        }


        public async Task<IEnumerable<ViewModel.Scan>> GetScans(int stockcheckId, int userId)
        {
            return await scanDataAccess.GetScans(stockcheckId, userId);
        }

        public async Task<ScanSeenBefore> CheckIfRegSeenBefore(string reg, int stockCheckId, int userId)
        {
            var res = await scanDataAccess.CheckIfRegSeenBefore(reg, stockCheckId, userId);
            var returnItem = new ScanSeenBefore() { };
            if (res != null)
            {
                return res;
            }
            return returnItem;
        }
        
        public async Task<ScanSeenBefore> CheckIfVinSeenBefore(string vin, int stockCheckId, int userId)
        {
            var res = await scanDataAccess.CheckIfVinSeenBefore(vin, stockCheckId, userId);
            var returnItem = new ScanSeenBefore() { };
            if (res != null)
            {
                return res;
            }
            return returnItem;
        }

        public async Task<IEnumerable<ScanWithResolution>> GetScansWithResolution(int stockcheckId, int userId)
        {
            return await scanDataAccess.GetScansWithResolution(stockcheckId, userId);
        }

        public async Task<IEnumerable<ScanMatchedToRecItem>> GetScansMatchedToRecItem(int stockcheckId, int? reconcilingItemTypeId, int userId)
        {
            var allTypesResults = await scanDataAccess.GetScansMatchedToRecItem(stockcheckId, null, userId);
            if (reconcilingItemTypeId == 0) return allTypesResults;
            return allTypesResults.Where(x => x.ReconcilingItemTypeId == reconcilingItemTypeId);
        }

        public async Task<IEnumerable<ScanRegDifference>> GetScanRegDifference(int stockcheckId, int userId)
        {
            return await scanDataAccess.GetScanRegDifference(stockcheckId, userId);
        }

        public async Task<List<ScanWithResolution>> GetRepeatUnknowns(List<int> stockCheckIds, int userId)
        {
            //Find last 4 stockchecks
            //List<int> last4StockCheckIds = stockCheckDataAccess.GetLastFourStockCheckIds(stockcheckId, userId).Result.ToList();

            //Get this stockchecks unknowns
            List<ScanWithResolution> firstCheckUnknowns = (await scanDataAccess.GetScansWithResolution(stockCheckIds[0], userId)).ToList();


            foreach (var stockCheckId in stockCheckIds.Skip(1))
            {
                List<int> firstCheckIdsToRetain = new List<int>();
                List<ScanWithResolution> thisStockCheckUnknowns = (await scanDataAccess.GetScansWithResolution(stockCheckId, userId)).ToList();
                foreach (var unknown in firstCheckUnknowns)
                {
                    ScanWithResolution matchingInCurrentCheck = thisStockCheckUnknowns.FirstOrDefault(x =>
                        (x.ScanReg != "INPUT" && x.ScanReg != "TBC" && x.ScanReg != null && x.ScanReg != string.Empty && x?.ScanReg == unknown.ScanReg) ||
                        (x.ScanVin != "INPUT" && x.ScanVin != "TBC" && x.ScanVin != null && x.ScanVin != string.Empty && x?.ScanVin == unknown.ScanVin));

                    if (matchingInCurrentCheck != null)
                    {
                        firstCheckIdsToRetain.Add(unknown.ScanId);
                    }
                }
                //now filter down the firstCheck for only those that have a match
                firstCheckUnknowns = firstCheckUnknowns.Where(x => firstCheckIdsToRetain.Contains(x.ScanId)).ToList();

            }

            return firstCheckUnknowns;
        }

        public async Task UpdateLocation(int newLocationId, int scanId, int userId)
        {
            await scanDataAccess.UpdateLocation(newLocationId, scanId, userId);
        }

        public async Task UpdateScanReg(int scanId, string reg, int userId, bool isMobileApp)
        {
            int stockCheckId = await scanDataAccess.UpdateScanReg(scanId, reg, userId, isMobileApp);
            await stockCheckReconciliationService.ReconcileStockCheck(stockCheckId, userId);
        }

        public async Task UpdateScanVin(int scanId, string vin, int userId, bool isMobileApp)
        {
            int stockCheckId = await scanDataAccess.UpdateScanVin(scanId, vin, userId, isMobileApp);
            await stockCheckReconciliationService.ReconcileStockCheck(stockCheckId, userId);
        }

        public async Task UpdateScanNote(int scanId, string note, int userId)
        {
            await scanDataAccess.UpdateScanNote(scanId, note, userId);
        }

        public async Task<IEnumerable<ScanThatIsADuplicate>> GetDuplicateScans(int stockcheckId, int userId)
        {
            return await scanDataAccess.GetDuplicateScans(stockcheckId, userId);
        }

        public async Task<IEnumerable<ScanMatchedToOtherSiteStockItem>> GetScanMatchedToOtherSiteStockItem(int stockcheckId, int userId)
        {
            return await scanDataAccess.GetScanMatchedToOtherSiteStockItem(stockcheckId, userId);
        }

        public async Task<List<ScanUploadConfirmation>> UploadScans(List<ScanUpload> scans, int userId, int stockcheckId)
        {
            // Check if all are valid to upload
            foreach (var scan in scans)
            {
                if (scan.HasVinImage)
                {
                    if (string.IsNullOrEmpty(scan.VinImagebase64.Trim()))
                    {
                        throw new Exception("Upload failed.   Expected vinImage");
                    }

                }

                if (string.IsNullOrEmpty(scan.ImageBase64.Trim()) || string.IsNullOrEmpty(scan.ImageBase64Sm.Trim()))
                {
                    throw new Exception("Upload failed.  Missing Reg Image");
                }
            }

            List<ScanUploadConfirmation> results = new List<ScanUploadConfirmation>();
            using IDbConnection db = new SqlConnection(_config.GetConnectionString(Connectionstring));
            try
            {
                if (db.State == ConnectionState.Closed)
                {
                    db.Open();
                }

                using var tran = db.BeginTransaction();
                try
                {
                    foreach (var scan in scans)
                    {
                        scan.Reg = Regex.Replace(scan.Reg, "[^A-Za-z0-9]", "");
                        scan.Vin = Regex.Replace(scan.Vin, "[^A-Za-z0-9]", "");

                        //Check for NoVin and update it. 
                        if (scan.Vin.ToUpper() == "NOVIN")
                        {
                            var noVinScans = (await scanDataAccess.GetScans(stockcheckId, userId)).Where(s => s.ScanVin.StartsWith("NoVIN", StringComparison.OrdinalIgnoreCase));
                            if (noVinScans.Any())
                            {
                                var noVinScanHighestNumber = noVinScans
                                    .Select(s => s.ScanVin.Replace("NoVIN", string.Empty, StringComparison.OrdinalIgnoreCase))
                                    .Where(s => int.TryParse(s, out int x))
                                    .Select(int.Parse);

                                if (noVinScanHighestNumber.Any())
                                {
                                    scan.Vin += noVinScanHighestNumber.OrderByDescending(s => s).First() + 1;
                                }
                                else
                                {
                                    scan.Vin = "NoVIN1";
                                }
                            }
                            else
                            {
                                scan.Vin = "NoVIN1";
                            }
                        }

                        //Create a new entry in DB - Get Id back
                        var scanResult = await scanDataAccess.CreateScan(userId, scan, db, tran);
                        
                        if (scanResult == null)
                        {
                            throw new Exception($"Failed to create scan Stockcheckid: {stockcheckId} ");
                        }

                        scan.Id = scanResult.ScanId;

                        if (scanResult.IsExistingScan == false)
                        {
                            //Large
                            using (Stream stream = imageService.convertBase64ToStream(scan.ImageBase64))
                            {
                                var result = await imageService.UploadScanImage(stream, scan.Id, true, false);
                                if (result.Equals(false))
                                {
                                    throw new Exception($"Large Upload Failed | Stockcheckid: {stockcheckId} |ScanId: {scan.Id}");
                                }
                            }

                            // Small
                            using (Stream stream = imageService.convertBase64ToStream(scan.ImageBase64Sm))
                            {
                                var result = await imageService.UploadScanImage(stream, scan.Id, false, false);
                                if (result.Equals(false))
                                {
                                    throw new Exception($"Small Upload Failed | Stockcheckid: {stockcheckId} |ScanId: {scan.Id}");
                                }
                            }

                            if (scan.HasVinImage)
                            {
                                // Vin
                                using (Stream stream = imageService.convertBase64ToStream(scan.VinImagebase64))
                                {
                                    var result = await imageService.UploadScanImage(stream, scan.Id, false, true);
                                    if (result.Equals(false))
                                    {
                                        throw new Exception($"Vin Upload Failed | Stockcheckid: {stockcheckId} |ScanId: {scan.Id}");
                                    }
                                }
                            }
                        }
                    }

                    await stockCheckDataAccess.ScansStarted(stockcheckId, userId, db, tran);

                    tran.Commit();
                }
                                catch (Exception ex)
                {
                    tran.Rollback();
                    throw ex;
                }
            }
            catch (Exception ex)
            {
                var config = TelemetryConfiguration.CreateDefault();
                var client = new TelemetryClient(config);
                client.TrackException(ex);
                throw ex;
            }
            finally
            {

                if (db.State == ConnectionState.Open)
                {
                    db.Close();
                }
                foreach (var scan in scans)
                {
                    results.Add(new ScanUploadConfirmation(scan));
                }
            }

            return results;

        }

        public async Task SaveUnknownResolution(Resolution resolution, int userId)
        {
            using IDbConnection db = new SqlConnection(_config.GetConnectionString(Connectionstring));
            try
            {
                if (db.State == ConnectionState.Closed)
                {
                    db.Open();
                }

                using var tran = db.BeginTransaction();
                try
                {
                    //Add data in the DB
                    if (resolution.ResolutionId == 0)
                    {
                        try
                        {
                            resolution.ResolutionId = await scanDataAccess.AddMissingResolutionAsync(resolution, userId);
                            await scanDataAccess.UpdateScanItemWithUnknownResolution(resolution.StockCheckId, userId, resolution.OriginalItemId, resolution.ResolutionId, db, tran);
                        }
                        catch (Exception ex)
                        {
                            await scanDataAccess.DeleteUnknownResolution(resolution.OriginalItemId, resolution.StockCheckId, userId);
                            throw new Exception($"Adding AddUnknownResolutions failed: StockCheckId:{resolution.StockCheckId} |ScanId:{resolution.OriginalItemId}| UnknownId:{resolution.ResolutionId}" + ex.Message);
                        }
                    }
                    else
                    {
                        //update 
                        await scanDataAccess.UpdateUnknownResolutions(resolution, userId, db, tran);
                    }

                    foreach (var image in resolution.Images)
                    {
                        if (image.Status.ToUpper() == "ADD")
                        {
                            string cleanFileName = imageService.RemoveUnwantedCharsFromFileName(image.FileName);
                            int id = await scanDataAccess.AddUnknownResolutionImage(cleanFileName, resolution.ResolutionId, resolution.StockCheckId, userId, db, tran);

                            using (Stream stream = imageService.convertBase64ToStream(image.FileBase64))
                            {
                                string contentType = imageService.GetContentType(image.FileBase64);
                                var result = await imageService.UploadUnknownResolutionImage(stream, id, cleanFileName, contentType);
                                if (result.Equals(false))
                                {
                                    throw new Exception($"AddUnknownResolutions Update failed: StockCheckId:{resolution.StockCheckId} | UnknownId {resolution.ResolutionId} | Id:{id}");
                                }
                            }

                        }
                        else if (image.Status.ToUpper() == "DELETE")
                        {
                            await scanDataAccess.DeleteUnknownResolutionImage(resolution.ResolutionId, image.Id.Value, resolution.StockCheckId, userId, db, tran);

                            var result = await imageService.DeleteUnknownResolutionImage(image.Id.Value);
                            if (result.Equals(false))
                            {
                                throw new Exception($"AddUnknownResolutions Delete failed: StockCheckId:{resolution.StockCheckId} | UnknownId {resolution.ResolutionId} | ImageId:{image.Id.Value}");
                            }

                        }
                    }


                    tran.Commit();
                }
                catch (Exception ex)
                {
                    tran.Rollback();
                    throw ex;
                }
            }
            catch (Exception ex)
            {
                var config = TelemetryConfiguration.CreateDefault();
                var client = new TelemetryClient(config);
                client.TrackException(ex);
            }
            finally
            {
                if (db.State == ConnectionState.Open)
                    db.Close();
            }
        }

        public async Task DeleteUnknownResolution(int resolutionId, int stockCheckId, int userId)
        {
            IEnumerable<int> resolutionImageIds = await scanDataAccess.GetUnknownResolutionImageIds(resolutionId);
            await scanDataAccess.DeleteUnknownResolution(resolutionId, stockCheckId, userId);

            foreach (var imageId in resolutionImageIds)
            {
                var result = await imageService.DeleteUnknownResolutionImage(imageId);
                if (result.Equals(false))
                {
                    //throw new Exception("Delete Failed");
                }
            }
        }


        public async Task DeleteScan(int scanId, int userId)
        {
            await scanDataAccess.DeleteScan(scanId, userId);
        }
    }
}
