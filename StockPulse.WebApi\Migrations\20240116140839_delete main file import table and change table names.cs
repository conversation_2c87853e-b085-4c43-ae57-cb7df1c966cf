﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace StockPulse.WebApi.Migrations
{
    /// <inheritdoc />
    public partial class deletemainfileimporttableandchangetablenames : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_StockItem_Loads_FileImports_FileImportId",
                schema: "import",
                table: "StockItem_Loads");

            migrationBuilder.DropTable(
                name: "FileImports",
                schema: "dbo");

            migrationBuilder.DropIndex(
                name: "IX_StockItem_Loads_FileImportId",
                schema: "import",
                table: "StockItem_Loads");

            migrationBuilder.DropColumn(
                name: "FileImportId",
                schema: "import",
                table: "StockItem_Loads");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "FileImportId",
                schema: "import",
                table: "StockItem_Loads",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.CreateTable(
                name: "FileImports",
                schema: "dbo",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    LoadedByUserId = table.Column<int>(type: "int", nullable: false),
                    FileDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    FileName = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    LoadDate = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_FileImports", x => x.Id);
                    table.ForeignKey(
                        name: "FK_FileImports_Users_LoadedByUserId",
                        column: x => x.LoadedByUserId,
                        principalSchema: "dbo",
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_StockItem_Loads_FileImportId",
                schema: "import",
                table: "StockItem_Loads",
                column: "FileImportId");

            migrationBuilder.CreateIndex(
                name: "IX_FileImports_LoadedByUserId",
                schema: "dbo",
                table: "FileImports",
                column: "LoadedByUserId");

            migrationBuilder.AddForeignKey(
                name: "FK_StockItem_Loads_FileImports_FileImportId",
                schema: "import",
                table: "StockItem_Loads",
                column: "FileImportId",
                principalSchema: "dbo",
                principalTable: "FileImports",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
