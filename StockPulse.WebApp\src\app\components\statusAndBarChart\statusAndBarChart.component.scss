.statusAndBarContainer {
    position: absolute;
    right: 0;
    top: 2.5px;
    height: 30px;
    display: flex;
    align-items: center;

    @media (max-width: 1680px) {
        height: 25px;
    }
    
    @media (max-width: 1280px) {
        height: 20px;
    }
}

.barHolder {
    position: relative;
    width: 175px;
    height: 25px;
    display: flex;
    overflow: hidden;
    background: var(--grey90);
    margin: 0 auto;
    padding: 0.25em;

    @media (max-width: 1680px) {
        width: 150px;
        height: 20px;
    }
    
    @media (max-width: 1280px) {
        width: 125px;
        height: 15px;
    }
}

.barActual {
    position: relative;
    transition: ease all 0.3s;
}

.barLabel {
    position: absolute;
    width: 90%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    top: 0;
    bottom: 0;
    padding-right: 10px;
    color: #000000;
}

.regionalTotalLabel {
    position: absolute;
    top: 0;
    left: 0;
    padding-left: calc(5% + 10px);
}

.ok {
    background-color: var(--secondary);
}

.good {
    background-color: var(--success);
}

.bad {
    background-color: var(--danger);
}