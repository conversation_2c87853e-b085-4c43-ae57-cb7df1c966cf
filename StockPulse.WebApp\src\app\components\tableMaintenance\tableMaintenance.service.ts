import { EventEmitter, Injectable } from '@angular/core';
import { ColDef, ColumnApi, GridApi, } from 'ag-grid-community';
import { MaintenanceTable } from './tableMaintenance.model';
import { TableMaintenanceTableComponent } from './tableMaintenanceTable.component';
import { ToastService } from 'src/app/services/newToast.service';
import { GetDataService } from 'src/app/services/getData.service';
import { ConstantsService } from 'src/app/services/constants.service';

@Injectable({
  providedIn: 'root'
})
export class TableMaintenanceService {

  alltables: MaintenanceTable[] = [];
  tableData: any = [];
  tableCols: ColDef[] =[];
  selectedTableName: string;
  selectedTableId: number;

  dataUpdatedEvent: EventEmitter<boolean> = new EventEmitter();

  gridApi: GridApi;
  gridColumnApi: ColumnApi;
  gridReference: TableMaintenanceTableComponent;

  constructor(
    //public constants: ConstantsService,
    
    public data: GetDataService,
    public constants: ConstantsService,
    public toastService: ToastService,

  ) { 
  }


  getTables(){
    this.toastService.loadingToast('Loading...');
    this.data.getMaintenanceTables().subscribe((res: MaintenanceTable[]) => {
      this.alltables = res;
    }, e => {
      console.error("Error in getting tables: ", JSON.stringify(e))
    }, () => {
      this.toastService.destroyToast();
    })

  }

  getTableData(id: number){
    this.selectedTableName =  this.alltables.find(t => t.id == id).name;
    this.selectedTableId = id;
    return this.data.getMaintenanceTableData(id)
  }




  saveRow(tableName: string, rowData: any){
    this.toastService.loadingToast('Saving...');
    const JSONRowData = JSON.stringify(rowData);
    this.data.saveMaintenanceTableRowData(tableName, JSONRowData).subscribe((res)=> {
      this.dataUpdatedEvent.emit(true);
    }, e => {
      console.error("Error in saving row: ", JSON.stringify(e))
    }, () => {
      this.toastService.loadingToast('Changes successfully made');
    })

  }

  deleteRow(tableName: string, rowId: number){
    this.toastService.loadingToast('Deleting...');
    this.data.deleteMaintenanceTableRowData(tableName, rowId).subscribe((res)=> {
      this.dataUpdatedEvent.emit(true);
    }, e => {
      console.error("Error in deleting row: ", JSON.stringify(e))
    }, () => {
      this.toastService.successToast('Changes successfully made');
    })

  }


}
