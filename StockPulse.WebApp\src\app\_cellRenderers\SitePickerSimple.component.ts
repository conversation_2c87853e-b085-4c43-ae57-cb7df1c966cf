import { Component, ViewChild } from "@angular/core";
import { NgbDropdown } from "@ng-bootstrap/ng-bootstrap";
import { ICellRendererAngularComp } from "ag-grid-angular";
import { SiteVM } from "src/app/model/SiteVM";
import { SiteNameLookupsModalService } from "../components/siteNameLookupsModal/siteNameLookupsModal.service";
import { SiteNameLookup } from "../model/SiteNameLookup";
import { SiteVMWithSelected } from "../model/SiteVMWithSelected";
import { ConstantsService } from "../services/constants.service";
import { IconService } from "../services/icon.service";

@Component({
    selector: 'sitePickerSimple-cell',
    template: `
        <div *ngIf="editing" class="d-inline-block" ngbDropdown container="body" [placement]="['auto']" #dropdown="ngbDropdown">
            <button id="mainButton" class="btn btn-primary" ngbDropdownToggle>
                {{ params.data.stockPulseName }}
            </button>
            <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
                <input type="text" placeholder="Search..." [(ngModel)]="searchString" (ngModelChange)="searchList()">
                <button *ngFor="let site of sitesCopy" ngbDropdownItem (click)="chooseSite(site)">
                    {{ site.description }}
                </button>
            </div>
        </div>
        <div *ngIf="!editing" (click)="enableEditing()">{{ params.data.stockPulseName }}</div>
    `,
    styles: [`
        .dropdown-menu {
            max-height: 500px;
        }
        #mainButton {
            width: 200px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .dropdown-menu input {
            margin: 1em;
            background-color: transparent;
            border: 1px solid #FFFFFF;
            color: #FFFFFF;
            width: calc(100% - 2em);
        }

        .dropdown-menu input::placeholder {
            color: #FFFFFF;
        }
    `]
})
export class SitePickerSimple implements ICellRendererAngularComp {
    params;
    searchString: string = '';
    sitesCopy: SiteVMWithSelected[];
    disabled: boolean;
    editing: boolean;
    @ViewChild('dropdown', { static: false }) dropdown!: NgbDropdown;

    constructor(
        public icon: IconService,
        public constants: ConstantsService,
        public service: SiteNameLookupsModalService
    ) { }

    agInit(params: any): void {
        this.params = params;
        this.sitesCopy = JSON.parse(JSON.stringify(this.constants.Sites));
        this.disabled = params.disabled;
    }

    refresh(params?: any): boolean {
        return false;
    }

    chooseSite(site: SiteVM) {
        let data: SiteNameLookup = this.params.node.data;

        if (data.id !== 0) {
            data.oldStockPulseName = data.stockPulseName;
        }

        data.stockPulseName = site.description;
        data.siteId = site.id;
        this.params.node.setData(data);
        this.service.newSiteNameLookupEmitter.emit(data);
    }
    
    searchList() {
        let sites: SiteVMWithSelected[] = JSON.parse(JSON.stringify(this.constants.Sites));
        this.sitesCopy = sites.filter(s => s.description.toLocaleLowerCase().includes(this.searchString.toLocaleLowerCase()));
    }

    enableEditing() {
        if (this.disabled) { return; }

        this.editing = true;

        setTimeout(() => {
            this.dropdown.open();
        }, 50);
    }
}
