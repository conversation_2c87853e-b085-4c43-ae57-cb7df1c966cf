﻿using Microsoft.Graph;
using System;
using System.Collections.Generic;

namespace StockPulse.WebApi.ViewModel
{
    public class ScanFullDetail : Scan
    {
        public string SiteName { get; set; }

        //stockItem stuff
        public int? StockItemId { get; set; }
        public string Reg { get; set; }
        public string Vin { get; set; }
        public string Description { get; set; }
        public int? DIS { get; set; }
        public int? GroupDIS { get; set; }
        public string Branch { get; set; }
        public string StockType { get; set; }
        public string Comment { get; set; }
        public string Reference { get; set; }
        public decimal? StockValue { get; set; }
        public decimal? Flooring { get; set; }
        public ReconciliationState? State { get; set; }




        //Reconciling Item stuff
        public int ReconcilingItemId { get; set; }
        public int ReconcilingItemTypeId { get; set; }
        public string ReconcilingItemTypeDescription { get; set; }
        public string ReconcilingItemReg { get; set; }
        public string ReconcilingItemVin { get; set; }
        public string ReconcilingItemDesc { get; set; }
        public string ReconcilingItemComment { get; set; }
        public string ReconcilingItemReference { get; set; }


        
        //Resolution stuff
        public int ResolutionId { get; set; }
        public int ResolutionTypeId { get; set; }
        public string ResolutionTypeDescription  { get; set; } //ResolutionResult
        public string ResolutionTypeBackup { get; set; }
        public bool IsResolved { get; set; }
        public string ResolvedBy { get; set; }
        public DateTime? ResolutionDate { get; set; }
        public string ResolutionNotes { get; set; }
        public string ResolutionImageIds { get; set; }

        //other site
        public string OtherSiteName { get; set; }

        //extra props if it's a duplicate
        public int OriginalId { get; set; }
        public string OriginalLocationDescription { get; set; }
        public string OriginalScannedBy { get; set; }
        public DateTime OriginalScannedDate { get; set; }


        public bool IsRegEditedOnDevice { get; set; }
        public bool IsRegEditedOnWeb { get; set; }
        public string InterpretedReg { get; set; }
        public bool IsVinEditedOnDevice { get; set; }
        public bool IsVinEditedOnWeb { get; set; }
        public string InterpretedVin { get; set; }
        public int StockCheckId { get; set; }

    }
}
