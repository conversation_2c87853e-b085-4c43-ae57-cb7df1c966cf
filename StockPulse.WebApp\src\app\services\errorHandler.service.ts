import { ErrorHandler, Injectable } from '@angular/core';
import { ApplicationInsightLoggingService } from './applicationInsightLogging.service';
import { ConstantsService } from './constants.service';

@Injectable()
export class ErrorHandlerService extends E<PERSON>r<PERSON>andler {

    constructor(
        private applicationInsightLoggingService: ApplicationInsightLoggingService,
        private constants:ConstantsService
        
        ) {
        super();
    }

    handleError(error: Error) {
        if(this.constants.isDevelopmentEnvironment){
            console.error(error)
        }
        this.applicationInsightLoggingService.logException(error); // Manually log exception
    }
}