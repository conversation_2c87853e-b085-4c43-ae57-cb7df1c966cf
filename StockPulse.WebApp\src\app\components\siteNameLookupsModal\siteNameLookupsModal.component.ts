import { Compo<PERSON>, <PERSON><PERSON>hil<PERSON>, <PERSON>ement<PERSON>ef, OnInit } from "@angular/core";
import { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { SelectionsService } from '../../services/selections.service';
import { ConstantsService } from "src/app/services/constants.service";
import { ApiAccessService } from "src/app/services/apiAccess.service";
import { ToastService } from "src/app/services/newToast.service";
import { GridOptionsCph } from "src/app/model/GridOptionsCph";
import { Column, ColumnApi, GridApi } from "ag-grid-community";
import { SiteNameLookupsModalService } from "./siteNameLookupsModal.service";
import { CheckboxComponent } from "src/app/_cellRenderers/checkBox";
import { SiteNameLookup } from "src/app/model/SiteNameLookup";
import { Changes, UpdateSiteNameLookupsParams } from "src/app/model/UpdateSiteNameLookupsParams";
import { SitePickerSimple } from "src/app/_cellRenderers/SitePickerSimple.component";
import { Subscription } from "rxjs";
import { DeleteSiteNameLookupComponent } from "./deleteSiteNameLookup";

@Component({
  selector: 'siteNameLookupsModal',
  templateUrl: './siteNameLookupsModal.component.html',
  styleUrls: ['./siteNameLookupsModal.component.scss']
})
export class SiteNameLookupsModalComponent implements OnInit {
  @ViewChild('modalRef', { static: true }) modalRef: ElementRef;

  gridOptions: GridOptionsCph;
  gridApi: GridApi;
  columnApi: ColumnApi;
  guidanceNote: string;
  valid: boolean;
  newSiteNameLookupSubscription: Subscription;

  constructor(
    public constantsService: ConstantsService,
    public apiAccessService: ApiAccessService,
    public modalService: NgbModal,
    public toastService: ToastService,
    public selectionsService: SelectionsService,
    public service: SiteNameLookupsModalService,
    public activeModal: NgbActiveModal
  ) { }

  ngOnInit(): void {
    this.toastService.loadingToast();
    this.getSiteNameLookups();

    this.newSiteNameLookupSubscription = this.service.newSiteNameLookupEmitter.subscribe((data: SiteNameLookup) => {
      let existingChange: Changes = this.service.changes.find(x => x.newValue === data.inputName);

      if (existingChange) {
        existingChange.siteId = data.siteId;
      } else {
        this.service.changes.push({
          id: data.id,
          newValue: data.inputName,
          oldValue: null,
          isPrimarySiteId: true,
          siteId: data.siteId,
          tempId: data.oldStockPulseName ? null : Math.floor(100000 + Math.random() * 900000),
          oldStockPulseName: data.oldStockPulseName,
          newStockPulseName: data.stockPulseName
        })
      }
    })
  }

  getSiteNameLookups() {
    this.apiAccessService.get('Sites', 'GetSiteNameLookups').subscribe((res: SiteNameLookup[]) => {
      this.service.siteNameLookups = res.sort((a, b) => a.stockPulseName.localeCompare(b.stockPulseName.trim()));
      this.service.siteNameLookupsCopy = JSON.parse(JSON.stringify(this.service.siteNameLookups));
      this.initialiseGrid();
    })
  }

  initialiseGrid() {
    if (this.gridApi) {
      this.gridApi.setRowData(this.constantsService.clone(this.service.siteNameLookups));
      this.gridApi.redrawRows();
    } else {
      this.gridOptions = {
        context: { componentParent: this },
        rowData: this.constantsService.clone(this.service.siteNameLookups),
        defaultColDef: {
          floatingFilter: true
        },
        singleClickEdit: true,
        columnTypes: {
          "label": { cellClass: 'agAlignLeft', filter: 'agTextColumnFilter', wrapText: true, autoHeight: true }
        },
        columnDefs: this.getColumnDefs(),
        onGridReady: (params) => this.onGridReady(params),
        onCellEditingStopped: (params) => this.onCellEditingStopped(params),
        rowHeight: 35
      }
    }
  }

  getColumnDefs() {
    return [
      {
        headerName: 'Input Name', field: 'inputName', colId: 'inputName', type: 'label', cellEditorParams: { maxLength: 50 },
        editable: this.selectionsService.userRole === 'SysAdministrator' && this.service.editing
      },
      {
        headerName: 'StockPulse Site Name', field: 'stockPulseName', colId: 'stockPulseName', type: 'label',
        cellRenderer: SitePickerSimple, cellRendererParams: { disabled: !this.service.editing }
      },
      {
        headerName: 'Primary Mapping', field: 'isPrimarySiteId', colId: 'isPrimarySiteId', cellRenderer: CheckboxComponent,
        cellRendererParams: { disabled: !this.service.editing, parent: this, nameField: 'inputName' }, maxWidth: 100
      },
      {
        headerName: '', colId: 'delete', cellRenderer: DeleteSiteNameLookupComponent, maxWidth: 50
      }
    ]
  }

  onGridReady(params) {
    this.gridApi = params.api;
    this.columnApi = params.columnApi;
    this.gridApi.sizeColumnsToFit();
    this.toastService.destroyToast();
  }

  edit() {
    this.service.editing = true;
    this.gridApi.setColumnDefs(this.getColumnDefs());
    this.gridApi.redrawRows();
  }

  maybeSave() {
    this.constantsService.alertModal.title = 'Confirm changes';

    let message: string = 'You have made the following changes:\n\n';

    this.service.changes.forEach(change => {
      if (change.id === 0) {
        const siteName: string = this.constantsService.Sites.find(x => x.id === change.siteId).description;
        message += `● New mapping for ${siteName}: "${change.newValue}"\n`;
      } else if (change.oldStockPulseName) {
        message += `● StockPulse site name "${change.oldStockPulseName}" updated to "${change.newStockPulseName}"\n`;
      } else if (change.oldValue && change.oldValue !== change.newValue) {
        message += `● "${change.oldValue}" updated to "${change.newValue}"\n`;
      } else {
        if (change.isPrimarySiteId) {
          message += `● "${change.oldValue}" set to primary\n`;
        } else {
          message += `● "${change.oldValue}" no longer primary\n`;
        }
      }
    })

    message += '\nAre you sure you want to continue?'

    this.constantsService.alertModal.message = message;
    this.constantsService.alertModal.showOkInSuccessColour = true;

    this.modalService.open(this.constantsService.alertModal.elementRef, { size: 'sm', keyboard: true, ariaLabelledBy: 'modal-basic-title' }).result.then((result) => {
      this.save();
    }, (reason) => {
      return;
    });
  }

  save() {
    const params: UpdateSiteNameLookupsParams = {
      changes: this.service.changes
    }

    this.apiAccessService.updateSiteNameLookups(params).subscribe(res => {
      this.toastService.successToast('Site name lookups updated');
      this.getSiteNameLookups();
      this.constantsService.importMasksUpdatedEmitter.emit();
    }, e => {
      this.toastService.errorToast('Failed to update site name lookups');
    })

    this.cancel();
  }

  cancel() {
    this.service.changes = [];
    this.service.editing = false;
    this.gridApi.setColumnDefs(this.getColumnDefs());
    this.gridApi.setRowData(this.constantsService.clone(this.service.siteNameLookupsCopy));
    this.gridApi.redrawRows();
  }

  onCellEditingStopped(params) {
    if (params.oldValue === params.newValue) { return; }
    
    let existingChange: Changes; 
    
    if (params.data.id === 0) {
      existingChange = this.service.changes.find(x => x.tempId === params.data.tempId);
      this.updateChangesNewRow(existingChange, params);
    } else {
      existingChange = this.service.changes.find(x => x.id === params.data.id);
      this.updateChanges(params.data.id, existingChange ? existingChange.isPrimarySiteId : params.data.isPrimarySiteId, params.newValue, params.siteId);
    }
  }

  close() {
    this.cancel();
    this.activeModal.close();
  }

  updateChanges(id: number, isPrimarySiteId: boolean, name: string, siteId: number) {
    let original: any = this.service.siteNameLookups.find(x => x.id === id);
    let existingChange: Changes = this.service.changes.find(x => x.id === id);

    if (!original) {
      let params = {
        data: {
          isPrimarySiteId: isPrimarySiteId
        },
        newValue: existingChange.newValue
      }
      this.updateChangesNewRow(existingChange, params)
    }
    
    if (existingChange) {
      const noChangeIsPrimary: boolean = original.isPrimarySiteId === isPrimarySiteId;
      const noChangeName: boolean = original.inputName === name;

      if (noChangeIsPrimary && noChangeName) {
        this.service.changes = this.service.changes.filter(x => x.id !== id);
      } else {
        existingChange.isPrimarySiteId = isPrimarySiteId;
        existingChange.newValue = name;
      }
    } else {
      this.service.changes.push({
        oldValue: original.inputName,
        newValue: name,
        id: original.id,
        isPrimarySiteId: isPrimarySiteId,
        siteId: siteId
      })
    }
  }

  add() {
    const newRow: SiteNameLookup = {
      id: 0,
      inputName: 'Start typing...',
      isPrimarySiteId: true,
      stockPulseName: this.constantsService.Sites[0].description,
      siteId: this.constantsService.Sites[0].id,
      tempId: Math.floor(100000 + Math.random() * 900000) // Random 6 digit Id
    };

    this.gridApi.applyTransaction({add:[newRow]});
    this.handleNewRow();
  }

  handleNewRow() {
    // this.gridApi.refreshCells({ force: true });
    let newlyCreatedRowIndex: number = this.gridApi.getDisplayedRowCount() - 1;
    let newlyCreatedRowFirstColumn: Column = this.columnApi.getAllDisplayedColumns()[0];
    this.gridApi.setFocusedCell(newlyCreatedRowIndex, newlyCreatedRowFirstColumn);
    this.gridApi.ensureIndexVisible(newlyCreatedRowIndex);
    this.valid = false;
  }

  updateChangesNewRow(existingChange: Changes, params: any) {
    if (existingChange) {
      existingChange.isPrimarySiteId = params.data.isPrimarySiteId;
      existingChange.newValue = params.newValue;
    } else {
      this.service.changes.push({
        id: 0,
        isPrimarySiteId: params.data.isPrimarySiteId,
        newValue: params.newValue,
        oldValue: null,
        siteId: params.data.siteId,
        tempId: params.data.tempId
      })
    }
  }
}
