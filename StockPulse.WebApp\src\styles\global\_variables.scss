@import "./colours";

:root {
    // Global app colours
    --success: #{$success};
    --successLight: #{$successLight};
    --successLighter: #{$successLighter};
    --successLightest: #{$successLightest};
    --successDark: #{$successDark};
    --successDarker: #{$successDarker};

    --danger: #{$danger};
    --dangerLight: #{$dangerLight};
    --dangerLighter: #{$dangerLighter};
    --dangerLightest: #{$dangerLightest};
    --dangerDark: #{$dangerDark};
    --dangerDarker: #{$dangerDarker};

    --warning: #{$warning};
    --warningLight: #{$warningLight};
    --warningLighter: #{$warningLighter};
    --warningLightest: #{$warningLightest};
    --warningDark: #{$warningDark};
    --warningDarker: #{$warningDarker};

    --grey: #{$grey};
    --greyLight: #{$greyLight};
    --greyLighter: #{$greyLighter};
    --greyLightest: #{$greyLightest};
    --greyDark: #{$greyDark};
    --greyDarker: #{$greyDarker};

    --info: #{$info};
    --infoLight: #{$infoLight};
    --infoLighter: #{$infoLighter};
    --infoLightest: #{$infoLightest};
    --infoDark: #{$infoDark};
    --infoDarker: #{$infoDarker};

    --grey95: #F1F2F4;
    --grey90: #E2E6E9;
    --grey80: #C5CCD3;
    --grey70: #A9B3BC;
    --grey60: #8C99A6;
    --grey50: #6F8090;
    --grey40: #596673;
    --grey30: #434D56;
    --grey20: #2C333A;

    --shadowColour: #14171A99;

    --numberPlate: #{$numberPlate};
    --numberPlateFrom: #{$numberPlateFrom};
    --numberPlateTo: #{$numberPlateTo};
    --infoBlue: #{$infoBlue};
    --postItNote: #{$postItNote};

    --border-radius: 7px;

    // Font sizes for large screens.  Our screens
    --body-font-size: 14px;
    --grid-font-size: 12px;
    --grid-row-height: 22px;
    --h1-font-size: 27px;  //big numbers
    --h2-font-size: 20px;
    --h3-font-size: 17px;
    --h4-font-size: 15px;  //tile headers
    --h5-font-size: 13px;
    --popover-font-size: 13px;
    --button-font-size: 14px;
    --icon-button-font-size: 18px;
    --spinnerScale: 1.2;

    //settings for medium screens.   e.g. full HD at 1:1
    --body-font-size_mediumScreen: 13px;
    --grid-font-size_mediumScreen: 11px;
    --grid-row-height_mediumScreen: 16px;
    --h1-font-size_mediumScreen: 24px;
    --h2-font-size_mediumScreen: 19px;
    --h3-font-size_mediumScreen: 16px;
    --h4-font-size_mediumScreen: 14px;
    --h5-font-size_mediumScreen: 11px;
    --popover-font-size_mediumScreen: 12px;
    --button-font-size_mediumScreen: 13px;
    --icon-button-font-size_mediumScreen: 15px;
    --spinnerScale_mediumScreen: 1.0;
    
    //settings for small screens.   tested for full HD at 150% zoom
    --body-font-size_smallScreen: 8px;
    --grid-font-size_smallScreen: 7px;
    --grid-row-height_smallScreen: 11px;
    --h1-font-size_smallScreen: 16px;
    --h2-font-size_smallScreen: 11px;
    --h3-font-size_smallScreen: 9px;
    --h4-font-size_smallScreen: 9px;
    --h5-font-size_smallScreen: 7px;
    --popover-font-size_smallScreen: 8px;
    --button-font-size_smallScreen: 8px;
    --icon-button-font-size_smallScreen: 12px;
    --spinnerScale_smallScreen: 0.8;
}

.regular-theme {
    --primary: #{$primary};
    --primaryLight: #{$primaryLight};
    --primaryLighter: #{$primaryLighter};
    --primaryLightest: #{$primaryLightest};
    --primaryDark: #{$primaryDark};
    --primaryDarker: #{$primaryDarker};

    --secondary: #{$secondary};
    --secondaryLight: #{$secondaryLight};
    --secondaryLighter: #{$secondaryLighter};
    --secondaryLightest: #{$secondaryLightest};
    --secondaryDark: #{$secondaryDark};
    --secondaryDarker: #{$secondaryDarker};

    --bodyColour: #FFFFFF;
}

.light-theme {
    --primary: #{$primaryLightTheme};
    --primaryLight: #{$primaryLightThemeLight};
    --primaryLighter: #{$primaryLightThemeLighter};
    --primaryLightest: #{$primaryLightThemeLightest};
    --primaryDark: #{$primaryLightThemeDark};
    --primaryDarker: #{$primaryLightThemeDarker};

    --secondary: #{$secondaryLightTheme};
    --secondaryLight: #{$secondaryLightThemeLight};
    --secondaryLighter: #{$secondaryLightThemeLighter};
    --secondaryLightest: #{$secondaryLightThemeLightest};
    --secondaryDark: #{$secondaryLightThemeDark};
    --secondaryDarker: #{$secondaryLightThemeDarker};

    --bodyColour: #000000;
}