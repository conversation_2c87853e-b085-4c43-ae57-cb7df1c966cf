import { Injectable, } from '@angular/core';
import { BarNew } from "../../model/BarNew";
import { SheetToExtractOld } from "../../model/SheetToExtractOld";
import * as excelJS from 'exceljs';
import * as fs from 'file-saver';
import { ReconcileService } from './reconcile.service';
import { WaterfallBarSet } from 'src/app/model/WaterfallBarSet';
import { WaterfallBarDetailItem } from 'src/app/model/WaterfallBarDetailItem';
import { ReconciliationState } from 'src/app/model/ReconciliationState';
import { BehaviorSubject } from 'rxjs';
import { ConstantsService } from "src/app/services/constants.service";


@Injectable({
  providedIn: 'root'
})
export class ReconcileExcelExportService {
  tables: { value: string; name: string; colour: string; type: string; count: number; onlyProblems: boolean; }[];
  //declarations


  constructor(

    private service: ReconcileService,
    public constants: ConstantsService
  ) {

  }


  // Creates the Excel for only the current grid being displayed
  createGridExcel(items: WaterfallBarDetailItem[], bar: BarNew): void {

    if (bar.reconciliationState === ReconciliationState.MatchedToStockOrScan) {
      //is the central bar, stock matched to scans
      this.exportSheetsToExcel([this.makeInStockAndScanned(items, bar.description)])
    }

    else if (bar.isStock) {
      //is a stockItem based bar

      if (bar.reconciliationState === ReconciliationState.AllItems) {
        this.exportSheetsToExcel([this.makeDmsStockSheet(items, bar.description)]);
      }
      else if (bar.reconciliationState === ReconciliationState.Duplicate) {
        this.exportSheetsToExcel([this.makeDuplicateStockRecordSheet(items, bar.description)]);
      }
      else if (bar.reconciliationState === ReconciliationState.MatchedToReport) {
        this.exportSheetsToExcel([this.makeMissingMatchedToRecItemSheet(items, bar.description)]);
      }
      else if (bar.reconciliationState === ReconciliationState.Resolved) {
        this.exportSheetsToExcel([this.makeMissingResolvedSheet(items, bar.description)]);
      }
      else if (bar.reconciliationState === ReconciliationState.OutstandingIssue) {
        this.exportSheetsToExcel([this.makeMissingResolvedSheet(items, bar.description)]);
      }
      else if (bar.reconciliationState === ReconciliationState.MatchedToOtherSite) {
        this.exportSheetsToExcel([this.makeStockItemIsScannedAtAnotherSite(items, bar.description)]);
      }

    } else {
      //is a scan based bar

      if (bar.reconciliationState === ReconciliationState.AllItems) {
        this.exportSheetsToExcel([this.makeScansSheet(items, bar.description)]);
      }
      else if (bar.reconciliationState === ReconciliationState.Duplicate) {
        this.exportSheetsToExcel([this.makeDuplicateScansSheet(items, bar.description)]);
      }
      else if (bar.reconciliationState === ReconciliationState.MatchedToReport) {
        this.exportSheetsToExcel([this.makeUnknownsMatchedToReport(items, bar.description)]);
      }
      else if (bar.reconciliationState === ReconciliationState.Resolved) {
        this.exportSheetsToExcel([this.makeUnknownResolvedSheet(items, bar.description)]);
      }
      else if (bar.reconciliationState === ReconciliationState.OutstandingIssue) {
        this.exportSheetsToExcel([this.makeUnknownResolvedSheet(items, bar.description)]);
      }
      else if (bar.reconciliationState === ReconciliationState.MatchedToOtherSite) {
        this.exportSheetsToExcel([this.makeScanIsOtherSitesStockItem(items, bar.description)])
      }
    }


  }

  // Create a big excel download with a sheet for each bucket
  createWaterfallExcel(bars: BarNew[]): void {

    const toastRef = this.service.toastService.loadingToast("Generating Excel file...");
    this.service.getDataService.getAllWaterfallBarDetail(this.service.selections.stockCheck.id).subscribe((bars: WaterfallBarSet[]) => {

      let sheets: SheetToExtractOld[] = [];

      let stockBars = bars.filter(x => !x.isScan);

      // DMS Stock - one sheet
      sheets.push(this.makeDmsStockSheet(stockBars.find(x => x.state === ReconciliationState.AllItems).items, 'DMS Stock'));

      // Duplicate stock items
      let duplicateStockRec = stockBars.find(x => x.state === ReconciliationState.Duplicate && !x.isScan)

      if (duplicateStockRec.items.length > 0) {
        sheets.push(this.makeDuplicateStockRecordSheet(duplicateStockRec.items, 'Duplicate stock record'));
      }


      let matchedToRecItems = stockBars.filter(x => x.state === ReconciliationState.MatchedToReport);


      matchedToRecItems.forEach(bar => {
        if (bar.items.length > 0) {
          sheets.push(this.makeMissingMatchedToRecItemSheet(bar.items, bar.reconcilingItemTypeDescription));
        }
      });

      // Scanned at another site
      let scannedAtAnotherSite = stockBars.find(x => x.state === ReconciliationState.MatchedToOtherSite);
      if (scannedAtAnotherSite.items.length > 0) {
        sheets.push(this.makeStockItemIsScannedAtAnotherSite(scannedAtAnotherSite.items, 'Scanned at another site'))
      }

      let missingResolved = stockBars.find(x => x.state === ReconciliationState.Resolved);

      if (missingResolved.items.length > 0) {
        sheets.push(this.makeMissingResolvedSheet(missingResolved.items, 'Missing Resolved'));
      }

      let missingUnresolved = stockBars.find(x => x.state === ReconciliationState.OutstandingIssue);
      if (missingUnresolved.items.length > 0) {
        sheets.push(this.makeMissingResolvedSheet(missingUnresolved.items, 'Missing Unresolved'));
      }

      // In Stock And Scanned
      let inStockAndScanned = bars.find(x => x.state === ReconciliationState.MatchedToStockOrScan);

      if (inStockAndScanned.items.length > 0) {
        sheets.push(this.makeInStockAndScanned(inStockAndScanned.items, 'In Stock and Scanned'));
      }

      const scanBars = bars.filter(x => x.isScan)

      let scansMatchedToRecItems = scanBars.filter(x => x.state === ReconciliationState.MatchedToReport);

      scansMatchedToRecItems.forEach(bar => {
        if (bar.items.length > 0) {
          sheets.push(this.makeUnknownsMatchedToReport(bar.items, bar.reconcilingItemTypeDescription));
        }
      });

      // In stock at another site
      let inStockAtAnotherSite = scanBars.find(x => x.state === ReconciliationState.MatchedToOtherSite);
      if (inStockAtAnotherSite.items.length > 0) {
        sheets.push(this.makeScanIsOtherSitesStockItem(inStockAtAnotherSite.items, 'In stock at another site'));
      }

      // Duplicates - one sheet
      let dupScan = scanBars.find(x => x.state === ReconciliationState.Duplicate)

      if (dupScan.items.length > 0) {
        sheets.push(this.makeDuplicateScansSheet(dupScan.items, 'Duplicate scan'));
      }

      // Unknown Resolved
      let unknownResolved = scanBars.find(x => x.state === ReconciliationState.Resolved)

      if (unknownResolved.items.length > 0) {
        sheets.push(this.makeUnknownResolvedSheet(unknownResolved.items, 'Unknown resolved'));
      }

      let unknownUnresolved = scanBars.find(x => x.state === ReconciliationState.OutstandingIssue)
      if (unknownUnresolved.items.length > 0) {
        sheets.push(this.makeUnknownResolvedSheet(unknownUnresolved.items, 'Unknown unresolved'));
      }

      let totalScans = scanBars.find(x => x.state === ReconciliationState.AllItems)
      sheets.push(this.makeScansSheet(totalScans.items, 'Scanned'));


      this.exportSheetsToExcel(sheets);
    }, e => {

    }, () => {
      toastRef.close();
    })

  }


  private makeScansSheet(scans: WaterfallBarDetailItem[], groupName: string) {

    let tableData = [];

    scans.forEach((s, i) => {

      const regConf = s.regConfidence / 100;
      const vinConf = s.vinConfidence / 100;

      tableData.push({
        Count: i + 1,
        Reg: s.reg,
        RegEdited: s.regEditStatus,
        Vin: s.vin,
        VinEdited: s.vinEditStatus, 
        Site: s.siteName,
        Description: s.description,
        Notes: s.comment ? s.comment.trim() : '',
        ScanId: s.scanId,
        RegConfidence: regConf,
        VinConfidence: vinConf,
        ScannedBy: s.scannedBy,
        When: this.service.cphPipe.transform(s.scannedDate, 'excelDate', 0) + ' ' + this.service.cphPipe.transform(s.scannedDate, 'excelTime', 0),
        Location: s.scanLocation,
        Distance: s.latitude === 0 ? 'Unknown' : this.service.cphPipe.transform(s.distance, 'miles', 3, false),
        Longitude: s.longitude === 0 ? 'Unknown' : parseFloat(this.service.cphPipe.transform(s.longitude, 'number', 4)),
        Latitude: s.latitude === 0 ? 'Unknown' : parseFloat(this.service.cphPipe.transform(s.latitude, 'number', 4))
      });
    });

    return { tableData: tableData, tableName: groupName,columnWidths:[]};
  }

  private makeUnknownsMatchedToReport(scansWithResolution: WaterfallBarDetailItem[], groupName: string) {

    let tableData = [];

    scansWithResolution.forEach((s, i) => {

      const regConf = s.regConfidence / 100;
      const vinConf = s.vinConfidence / 100;

      tableData.push({
        Count: i + 1,
        Reg: s.reg,
        RegEdited: s.regEditStatus,
        Vin: s.vin,
        VinEdited: s.vinEditStatus,
        Site: s.siteName,
        Description: s.description,
        Notes: s.comment ? s.comment.trim() : '',
        ScanId: s.scanId,
        RegConfidence: regConf,
        VinConfidence: vinConf,
        ScannedBy: s.scannedBy,
        When: this.service.cphPipe.transform(s.scannedDate, 'excelDate', 0) + ' ' + this.service.cphPipe.transform(s.scannedDate, 'excelTime', 0),
        Location: s.scanLocation,
        Distance: s.latitude === 0 ? 'Unknown' : this.service.cphPipe.transform(s.distance, 'miles', 3, false),
        Longitude: s.longitude === 0 ? 'Unknown' : parseFloat(this.service.cphPipe.transform(s.longitude, 'number', 4)),
        Latitude: s.latitude === 0 ? 'Unknown' : parseFloat(this.service.cphPipe.transform(s.latitude, 'number', 4)),
        MatchingType: s.matchingItemTypeDesc,
        MatchingItem: s.matchingItemDesc,
        MatchingRef: s.matchingItemRef ? s.matchingItemRef : '',
        MatchingComment: s.matchingItemComment ? s.matchingItemComment : '',
      });

    });

    return { tableData: tableData, tableName: groupName,columnWidths:[]};
  }

  private makeUnknownResolvedSheet(scansMatchedToRecItem: WaterfallBarDetailItem[], groupName: string) {

    let tableData = [];

    
    scansMatchedToRecItem.forEach((s, i) => {

      const regConf = s.regConfidence / 100;
      const vinConf = s.vinConfidence / 100;
      
      tableData.push({
        Count: (i + 1),
        Reg: s.reg,
        RegEdited: s.regEditStatus,
        Vin: s.vin,
        VinEdited: s.vinEditStatus,
        Site: s.siteName,
        Description: s.description,
        Notes: s.comment ? s.comment.trim() : '',
        ScanId: s.scanId,
        RegConfidence: regConf,
        VinConfidence: vinConf,
        ScannedBy: s.scannedBy,
        When: this.service.cphPipe.transform(s.scannedDate, 'excelDate', 0) + ' ' + this.service.cphPipe.transform(s.scannedDate, 'excelTime', 0),
        Location: s.scanLocation,
        Distance: s.latitude === 0 ? 'Unknown' : this.service.cphPipe.transform(s.distance, 'miles', 3, false),
        Longitude: s.longitude === 0 ? 'Unknown' : parseFloat(this.service.cphPipe.transform(s.longitude, 'number', 4)),
        Latitude: s.latitude === 0 ? 'Unknown' : parseFloat(this.service.cphPipe.transform(s.latitude, 'number', 4)),
        ResolvedBy: s.resolvedBy,
        ResolutionType: s.resolutionTypeDesc,
        ResolutionDetail: s.resolutionNotes,
        Backup: s.resolutionImages?.length > 0 ? 'Yes' : 'No'
      });
    });

    return { tableData: tableData, tableName: groupName, columnWidths:[]};
  }

  

  private makeScanIsOtherSitesStockItem(scansMatchedToRecItem: WaterfallBarDetailItem[], groupName: string) {

    let tableData = [];

    scansMatchedToRecItem.forEach((scan, i) => {

      const regConf = scan.regConfidence / 100;
      const vinConf = scan.vinConfidence / 100;

      tableData.push({
        Count: i + 1,
        Reg: scan.reg,
        RegEdited: scan.regEditStatus,
        Vin: scan.vin,
        VinEdited: scan.vinEditStatus,
        Site: scan.siteName,
        Description: scan.description,
        Notes: scan.comment,
        ScanId: scan.scanId,
        RegConfidence: regConf,
        VinConfidence: vinConf,
        ScannedBy: scan.scannedBy,
        When: this.service.cphPipe.transform(scan.scannedDate, 'excelDate', 0) + ' ' + this.service.cphPipe.transform(scan.scannedDate, 'excelTime', 0),
        Location: scan.scanLocation,
        Distance: scan.latitude === 0 ? 'Unknown' : this.service.cphPipe.transform(scan.distance, 'miles', 3, false),
        Longitude: scan.longitude === 0 ? 'Unknown' : parseFloat(this.service.cphPipe.transform(scan.longitude, 'number', 4)),
        Latitude: scan.latitude === 0 ? 'Unknown' : parseFloat(this.service.cphPipe.transform(scan.latitude, 'number', 4)),
        OtherSite: scan.otherSiteName,
        StockItemId:scan.stockItemId,
        OtherSiteDescription: scan.matchingItemDesc,
        OtherSiteReference: scan.matchingItemRef,


      });
    });

    return { tableData: tableData, tableName: groupName ,columnWidths:[]};
  }

  private makeInStockAndScanned(stockItemsWithScan: WaterfallBarDetailItem[], groupName: string) {

    let tableData = [];

    if(this.constants.currencySymbol == '$')
    {
      stockItemsWithScan.forEach((s, i) => {

        const regConf = s.regConfidence / 100;
        const vinConf = s.vinConfidence / 100;
  
        tableData.push({
          Count: i + 1,
          StockItemId:s.stockItemId,
          Reg: s.reg,
          RegEdited: s.regEditStatus,
          Vin: s.vin,
          VinEdited: s.vinEditStatus,
          Site: s.siteName,
          Branch: s.branchName,
          Description: s.description ? s.description.trim() : '',
          StockValue: s.stockValue,
          Flooring: s.flooring,
          Dis: s.dis,
          GroupDis: s.groupDIS,
          StockType: s.stockType,
          Notes: s.comment ? s.comment.trim() : '',
          Reference: s.reference ? s.reference.trim(): '',
          ScanId: s.scanId,
          // RegConfidence: regConf,
          // VinConfidence: vinConf,
          ScannedBy: s.scannedBy,
          When: this.service.cphPipe.transform(s.scannedDate, 'excelDate', 0) + ' ' + this.service.cphPipe.transform(s.scannedDate, 'excelTime', 0),
          Location: s.scanLocation,
          Distance: s.latitude === 0 ? 'Unknown' : this.service.cphPipe.transform(s.distance, 'miles', 3, false),
          Longitude: s.longitude === 0 ? 'Unknown' : parseFloat(this.service.cphPipe.transform(s.longitude, 'number', 4)),
          Latitude: s.latitude === 0 ? 'Unknown' : parseFloat(this.service.cphPipe.transform(s.latitude, 'number', 4))
        });
      });

      return { tableData: tableData, tableName: groupName,columnWidths:[] }
    }
    else
    {

      stockItemsWithScan.forEach((s, i) => {

        const regConf = s.regConfidence / 100;
        const vinConf = s.vinConfidence / 100;
  
        tableData.push({
          Count: i + 1,
          StockItemId:s.stockItemId,
          Reg: s.reg,
          RegEdited: s.regEditStatus,
          Vin: s.vin,
          VinEdited: s.vinEditStatus,
          Site: s.siteName,
          Branch: s.branchName,
          Description: s.description ? s.description.trim() : '',
          Dis: s.dis,
          GroupDis: s.groupDIS,
          StockType: s.stockType,
          Notes: s.comment ? s.comment.trim() : '',
          Reference: s.reference ? s.reference.trim(): '',
          StockValue: s.stockValue,
          ScanId: s.scanId,
          // RegConfidence: regConf,
          // VinConfidence: vinConf,
          ScannedBy: s.scannedBy,
          When: this.service.cphPipe.transform(s.scannedDate, 'excelDate', 0) + ' ' + this.service.cphPipe.transform(s.scannedDate, 'excelTime', 0),
          Location: s.scanLocation,
          Distance: s.latitude === 0 ? 'Unknown' : this.service.cphPipe.transform(s.distance, 'miles', 3, false),
          Longitude: s.longitude === 0 ? 'Unknown' : parseFloat(this.service.cphPipe.transform(s.longitude, 'number', 4)),
          Latitude: s.latitude === 0 ? 'Unknown' : parseFloat(this.service.cphPipe.transform(s.latitude, 'number', 4))
        });
      });

      return { tableData: tableData, tableName: groupName,columnWidths:[] }
    }
    

  }

  private makeMissingResolvedSheet(missingsWithResolution: WaterfallBarDetailItem[], groupName: string) {

    let tableData = [];

    if(this.constants.currencySymbol == '$')
    {
      missingsWithResolution.forEach((s, i) => {
        tableData.push({
          Count: i + 1,
          StockItemId:s.stockItemId,
          Reg: s.reg,
          Vin: s.vin,
          Site: s.siteName,
          Branch: s.branchName,
          Description: s.description ? s.description.trim() : '',
          Dis: s.dis,
          GroupDis: s.groupDIS,
          StockType: s.stockType,
          Notes: s.comment ? s.comment.trim() : '',
          Reference: s.reference ? s.reference.trim(): '',
          StockValue: s.stockValue,
          Flooring: s.flooring,
          ResolvedBy: s.resolvedBy,
          When: s.resolutionDate != null ? this.service.cphPipe.transform(s.resolutionDate, 'excelDate', 0) + ' ' + this.service.cphPipe.transform(s.resolutionDate, 'excelTime', 0) : '',
          Resolution: s.resolutionTypeDesc,
          ResolutionDetail: s.resolutionNotes,
          Backup: s.resolutionImages?.length > 0 ? 'Yes' : 'No'
        });
      });
    }
    else
    {
      missingsWithResolution.forEach((s, i) => {
        tableData.push({
          Count: i + 1,
          StockItemId:s.stockItemId,
          Reg: s.reg,
          Vin: s.vin,
          Site: s.siteName,
          Branch: s.branchName,
          Description: s.description ? s.description.trim() : '',
          Dis: s.dis,
          GroupDis: s.groupDIS,
          StockType: s.stockType,
          Notes: s.comment ? s.comment.trim() : '',
          Reference: s.reference ? s.reference.trim(): '',
          StockValue: s.stockValue,
          ResolvedBy: s.resolvedBy,
          When: s.resolutionDate != null ? this.service.cphPipe.transform(s.resolutionDate, 'excelDate', 0) + ' ' + this.service.cphPipe.transform(s.resolutionDate, 'excelTime', 0) : '',
          Resolution: s.resolutionTypeDesc,
          ResolutionDetail: s.resolutionNotes,
          Backup: s.resolutionImages?.length > 0 ? 'Yes' : 'No'
        });
      });
    }


    return { tableData: tableData, tableName: groupName,columnWidths:[]};
  }

  private makeStockItemIsScannedAtAnotherSite(items: WaterfallBarDetailItem[], groupName: string) {

    let tableData = [];

    items.forEach((s, i) => {
      tableData.push({
        Count: i + 1,
        StockItemId:s.stockItemId,
        Reg: s.reg,
        Vin: s.vin,
        Site: s.siteName,
        Branch: s.branchName,
        Description: s.description ? s.description.trim() : '',
        Dis: s.dis,
        GroupDis: s.groupDIS,
        StockType: s.stockType,
        Notes: s.comment ? s.comment.trim() : '',
        Reference: s.reference ? s.reference.trim(): '',
        StockValue: s.stockValue,
        OtherSiteName: s.otherSiteName,
        MatchingScanId: s.scanId,
        MatchingLocation: s.scanLocation,
        MatchingScannedBy: s.scannedBy
      });
    });

    return { tableData: tableData, tableName: groupName,columnWidths:[]};
  }

  private makeMissingMatchedToRecItemSheet(missingMatchedToRec: WaterfallBarDetailItem[], groupName: string) {

    let tableData = [];

    if(this.constants.currencySymbol == '$')
    {
      missingMatchedToRec.forEach((s, i) => {
        tableData.push({
          Count: i + 1,
          StockItemId:s.stockItemId,
          Reg: s.reg,
          Vin: s.vin,
          Site: s.siteName,
          Branch: s.branchName,
          Description: s.description ? s.description.trim() : '',
          Dis: s.dis,
          GroupDis: s.groupDIS,
          StockType: s.stockType,
          Notes: s.comment ? s.comment.trim() : '',
          StockValue: s.stockValue,
          Flooring: s.flooring,
          Reference: s.reference ? s.reference.trim(): '',
          MatchingType: s.matchingItemTypeDesc,
          MatchingItem: s.matchingItemDesc,
          MatchingReference: s.matchingItemRef,
          MatchingComment: s.matchingItemComment,
        });
      });
    }
    else
    {
      missingMatchedToRec.forEach((s, i) => {
        tableData.push({
          Count: i + 1,
          StockItemId:s.stockItemId,
          Reg: s.reg,
          Vin: s.vin,
          Site: s.siteName,
          Branch: s.branchName,
          Description: s.description ? s.description.trim() : '',
          Dis: s.dis,
          GroupDis: s.groupDIS,
          StockType: s.stockType,
          Notes: s.comment ? s.comment.trim() : '',
          Reference: s.reference ? s.reference.trim(): '',
          StockValue: s.stockValue,
          MatchingType: s.matchingItemTypeDesc,
          MatchingItem: s.matchingItemDesc,
          MatchingReference: s.matchingItemRef,
          MatchingComment: s.matchingItemComment,
        });
      });
    }


    return { tableData: tableData, tableName: groupName,columnWidths:[]};
  }

  private makeDuplicateStockRecordSheet(duplicates: WaterfallBarDetailItem[], groupName: string) {

    let tableData = [];

    if(this.constants.currencySymbol == '$')
    {
      duplicates.forEach((s, i) => {
        tableData.push({
          Count: i + 1,
          StockItemid:s.stockItemId,
          Reg: s.reg,
          Vin: s.vin,
          Site: s.siteName,
          Branch: s.branchName,
          Description: s.description ? s.description.trim() : '',
          Dis: s.dis,
          GroupDis: s.groupDIS,
          StockType: s.stockType,
          Notes: s.comment ? s.comment.trim() : '',
          StockValue: s.stockValue,
          Flooring: s.flooring,
          Reference: s.reference ? s.reference.trim(): '',
          OriginalId: s.originalId,
          OriginalReference: s.originalReference,
          OriginalComment: s.originalComment
        });
      });
    }
    else
    {
      duplicates.forEach((s, i) => {
        tableData.push({
          Count: i + 1,
          StockItemid:s.stockItemId,
          Reg: s.reg,
          Vin: s.vin,
          Site: s.siteName,
          Branch: s.branchName,
          Description: s.description ? s.description.trim() : '',
          Dis: s.dis,
          GroupDis: s.groupDIS,
          StockType: s.stockType,
          Notes: s.comment ? s.comment.trim() : '',
          Reference: s.reference ? s.reference.trim(): '',
          StockValue: s.stockValue,
          OriginalId: s.originalId,
          OriginalReference: s.originalReference,
          OriginalComment: s.originalComment
        });
      });
    }


    return { tableData: tableData, tableName: groupName,columnWidths:[]};
  }

  private makeDuplicateScansSheet(duplicates: WaterfallBarDetailItem[], groupName: string) {

    let tableData = [];

    duplicates.forEach((s, i) => {

      const regConf = s.regConfidence / 100;
      const vinConf = s.vinConfidence / 100;
      
      tableData.push({
        Count: i + 1,
        Reg: s.reg,
        RegEdited: s.regEditStatus,
        Vin: s.vin,
        VinEdited: s.vinEditStatus,
        Site: s.siteName,
        Description: s.description ? s.description.trim() : '',
        Notes: s.comment ? s.comment.trim() : '',
        ScanId: s.scanId,
        RegConfidence: regConf,
        VinConfidence: vinConf,
        ScannedBy: s.scannedBy,
        When: this.service.cphPipe.transform(s.scannedDate, 'excelDate', 0) + ' ' + this.service.cphPipe.transform(s.scannedDate, 'excelTime', 0),
        Location: s.scanLocation,
        Distance: s.latitude === 0 ? 'Unknown' : this.service.cphPipe.transform(s.distance, 'miles', 3, false),
        Longitude: s.longitude === 0 ? 'Unknown' : parseFloat(this.service.cphPipe.transform(s.longitude, 'number', 4)),
        Latitude: s.latitude === 0 ? 'Unknown' : parseFloat(this.service.cphPipe.transform(s.latitude, 'number', 4)),
        OriginalScannedBy: s.originalScannedBy,
        OriginalLocation: s.originalLocationDescription,
        OriginalScanId: s.originalId,
        OriginalScanned: s.originalScannedDate
      });
    });

    return { tableData: tableData, tableName: groupName,columnWidths:[]};
  }

  private makeDmsStockSheet(dmsStock: WaterfallBarDetailItem[], groupName: string) {

    let tableData = [];

    if(this.constants.currencySymbol == '$')
    {
      dmsStock.forEach((s, i) => {

        tableData.push({
          Count: i + 1,
          StockItemId: s.stockItemId,
          Reg: s.reg,
          Vin: s.vin,
          Site: s.siteName,
          Branch: s.branchName,
          Description: s.description ? s.description.trim() : '',
          Dis: s.dis,
          GroupDis: s.groupDIS,
          StockType: s.stockType,
          Notes: s.comment ? s.comment.trim() : '',
          Reference: s.reference ? s.reference.trim(): '',
          StockValue: s.stockValue,
          Flooring: s.flooring
        });
      });

    }
    else
    {
      dmsStock.forEach((s, i) => {

        tableData.push({
          Count: i + 1,
          StockItemId: s.stockItemId,
          Reg: s.reg,
          Vin: s.vin,
          Site: s.siteName,
          Branch: s.branchName,
          Description: s.description ? s.description.trim() : '',
          Dis: s.dis,
          GroupDis: s.groupDIS,
          StockType: s.stockType,
          Notes: s.comment ? s.comment.trim() : '',
          Reference: s.reference ? s.reference.trim(): '',
          StockValue: s.stockValue
        });
      });

    }


    return { tableData: tableData, tableName: groupName,columnWidths:[]};
  }

  private exportSheetsToExcel(sheetDataToExtract: SheetToExtractOld[]): void {

    let workbook = new excelJS.Workbook();

    var imageId = workbook.addImage({
      base64: this.service.logo.provideStockPulseLogo(),
      extension: 'png'
    });


    sheetDataToExtract.forEach(ws => {

      try {
        //define worksheet
        let worksheet = workbook.addWorksheet(ws.tableName)

        //generic stuff for worksheet
        worksheet.views = [
          { state: 'frozen', xSplit: 1, ySplit: 6, zoomScale: 85 }
        ];

        //columns things
        let columns = []

        const calculatedWidths = this.service.excelExportService.workoutColWidths(ws.tableData)

        calculatedWidths.forEach(w => {
          columns.push({ width: w })
        })
        worksheet.columns = columns;

        var splitted = this.service.selections.stockCheckLongName.split("-", 2);
        let siteNameRow = worksheet.addRow([splitted[0]]);//blank
        siteNameRow.font = { name: 'Calibri', family: 4, size: 16, bold: true, color: 'white', fill: { bgColor: { rgb: '220,230,241' } } };

        let stockCheckDate = 'Stockcheck ' + splitted[1] + ' Current status: ' + this.service.selections.stockCheck.status;
        let stockCheckDateRow = worksheet.addRow([stockCheckDate]);
        stockCheckDateRow.font = { name: 'Calibri', family: 4, size: 11, bold: false, color: 'white', fill: { bgColor: { rgb: '220,230,241' } } };

        let extractedBy = 'Extracted ' + this.service.cphPipe.transform(new Date(), 'dateMed', 0) + ' ' + this.service.cphPipe.transform(new Date(), 'time', 0) + ' ' + this.service.selections.usersName;
        let extractedByRow = worksheet.addRow([extractedBy]);
        extractedByRow.font = { name: 'Calibri', family: 4, size: 11, bold: false, italic: true, color: 'white', fill: { bgColor: { rgb: '220,230,241' } } };


        worksheet.addRow([]);

        //rows
        let titleRow = worksheet.addRow([ws.tableName]); //title
        titleRow.font = { name: 'Calibri', family: 4, size: 16, bold: true, color: 'white', fill: { bgColor: { rgb: '220,230,241' } } };

        let headerNames = Object.keys(ws.tableData[0]);
        let headerNamesWithSpaces = [];

        if (headerNames[0] == 'Count') {
          headerNames[0] = '';
        }

        headerNames.forEach(element => {
          let temp = element.replace(/([A-Z])/g, ' $1').trim();

          switch (temp) {
            case 'Vin':
              temp = 'VIN';
              break;
            case 'Dis':
              temp = 'DIS';
              break;
            case 'Group Dis':
              temp = 'Group DIS';
              break;
            case 'Vin Edited':
              temp = 'VIN Edited';
              break;
          }

          headerNamesWithSpaces.push(temp);
        });


        //the table headerRow      
        let tableHeaderRow = worksheet.addRow(headerNamesWithSpaces)
        let colCount = Object.keys(ws.tableData[0]).length

        //loop through each column in active range and colour cells
        for (let i = 0; i < colCount; i++) {
          let colLetter = String.fromCharCode(65 + i)
          worksheet.getCell(colLetter + '6').font = { name: 'Calibri', family: 4, size: 11, color: { argb: 'FFFFFFFF' } }
          worksheet.getCell(colLetter + '6').fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF252424' } }
        }

        //the data rows
        ws.tableData.forEach(x => {
          let temp: any[] = Object.values(x);

          temp.forEach(element => {

            if (typeof element === 'string' || element instanceof String) {
              element = element.replace(/\s\s+/g, ' ');
            }

          });
          let newRow = worksheet.addRow(Object.values(x))

          // Special case to format "Stock Value" colum as currency
          if (worksheet.name === 'Stock' || worksheet.name === 'DMS Stock') {
            let valueCell = newRow._cells[12];
            valueCell.numFmt = `\\${this.constants.currencySymbol}#,##0.00;-\\${this.constants.currencySymbol}#,##0.00;-`;
            valueCell.alignment = { horizontal: 'right' };

            if(this.constants.currencySymbol == '$')
            {
              let valueCell1 = newRow._cells[13];
              valueCell1.numFmt = `\\${this.constants.currencySymbol}#,##0.00;-\\${this.constants.currencySymbol}#,##0.00;-`;
              valueCell1.alignment = { horizontal: 'right' };
            }
          }

          // Same for Stock and Scanned 
          if (worksheet.name === 'In Stock and Scanned' && this.constants.currencySymbol == '$') {
            let valueCell1 = newRow._cells[9];
            valueCell1.numFmt = `\\${this.constants.currencySymbol}#,##0.00;-\\${this.constants.currencySymbol}#,##0.00;-`;
            valueCell1.alignment = { horizontal: 'right' };

            let valueCell2 = newRow._cells[10];
            valueCell2.numFmt = `\\${this.constants.currencySymbol}#,##0.00;-\\${this.constants.currencySymbol}#,##0.00;-`;
            valueCell2.alignment = { horizontal: 'right' };
          }

          // Same for Stock and Scanned 
          if (worksheet.name === 'In Stock and Scanned' && this.constants.currencySymbol != '$') {
            let valueCell1 = newRow._cells[14];
            valueCell1.numFmt = `\\${this.constants.currencySymbol}#,##0.00;-\\${this.constants.currencySymbol}#,##0.00;-`;
            valueCell1.alignment = { horizontal: 'right' };
          }

          if((worksheet.name === 'Duplicate stock record' || worksheet.name === 'Booked and Pending' || worksheet.name === 'In Transit' || worksheet.name === 'At Auction'
            || worksheet.name === 'On Loan' || worksheet.name === 'Scanned at another site') 
            && this.constants.currencySymbol == '$') 
          {
            let valueCell3 = newRow._cells[11];
            valueCell3.numFmt = `\\${this.constants.currencySymbol}#,##0.00;-\\${this.constants.currencySymbol}#,##0.00;-`;
            valueCell3.alignment = { horizontal: 'right' };

            let valueCell4 = newRow._cells[12];
            valueCell4.numFmt = `\\${this.constants.currencySymbol}#,##0.00;-\\${this.constants.currencySymbol}#,##0.00;-`;
            valueCell4.alignment = { horizontal: 'right' };
          }

          if((worksheet.name === 'Duplicate stock record' || worksheet.name === 'Booked and Pending' || worksheet.name === 'In Transit' || worksheet.name === 'At Auction'
            || worksheet.name === 'On Loan' || worksheet.name === 'Scanned at another site') 
            && this.constants.currencySymbol != '$') 
          {
            let valueCell4 = newRow._cells[12];
            valueCell4.numFmt = `\\${this.constants.currencySymbol}#,##0.00;-\\${this.constants.currencySymbol}#,##0.00;-`;
            valueCell4.alignment = { horizontal: 'right' };
          }

          if((worksheet.name === 'Missing unresolved' || worksheet.name === 'Missing resolved') 
            && this.constants.currencySymbol == '$') 
          {
            let valueCell3 = newRow._cells[12];
            valueCell3.numFmt = `\\${this.constants.currencySymbol}#,##0.00;-\\${this.constants.currencySymbol}#,##0.00;-`;
            valueCell3.alignment = { horizontal: 'right' };

            let valueCell4 = newRow._cells[13];
            valueCell4.numFmt = `\\${this.constants.currencySymbol}#,##0.00;-\\${this.constants.currencySymbol}#,##0.00;-`;
            valueCell4.alignment = { horizontal: 'right' };
          }

          if((worksheet.name === 'Missing unresolved' || worksheet.name === 'Missing resolved') 
            && this.constants.currencySymbol != '$') 
          {
            let valueCell3 = newRow._cells[12];
            valueCell3.numFmt = `\\${this.constants.currencySymbol}#,##0.00;-\\${this.constants.currencySymbol}#,##0.00;-`;
            valueCell3.alignment = { horizontal: 'right' };
          }

          if((worksheet.name === 'In stock, at BCA auction' || worksheet.name === 'In stock, staff demo' || worksheet.name === 'In stock, courtesy loan')
            && this.constants.currencySymbol != '$') 
          {
            let valueCell4 = newRow._cells[12];
            valueCell4.numFmt = `\\${this.constants.currencySymbol}#,##0.00;-\\${this.constants.currencySymbol}#,##0.00;-`;
            valueCell4.alignment = { horizontal: 'right' };
          }

          const sheetsWithRegAndVin: string[] = ['Unknown resolved', 'Unknown unresolved', 'Duplicate scan', 'In stock at another site'];

          if ((worksheet.name.includes('Scan') || sheetsWithRegAndVin.includes(worksheet.name)) && worksheet.name !== 'In Stock and Scanned' && worksheet.name !== 'Scanned at another site') {
            let regConf = newRow._cells[9];
            regConf.numFmt = '0.00%';
            regConf.alignment = { horizontal: 'right' };
            let vinConf = newRow._cells[10];
            vinConf.numFmt = '0.00%';
            vinConf.alignment = { horizontal: 'right' };
          }
        })

        //loop through the first cell of each row and colour
        let rowCount = worksheet.rowCount + 1;

        for (let i = 6; i < rowCount; i++) {
          worksheet.getCell('A' + i.toString()).font = { name: 'Calibri', family: 4, size: 11, color: { argb: 'FFFFFFFF' } }
          worksheet.getCell('A' + i.toString()).fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF252424' } }
        }

        //images
        let columnCount = worksheet.columns.length
        worksheet.addImage(imageId, {

          tl: { col: columnCount - 1, row: 0 },
          //br: {col: 13, row: 2},
          ext: { width: 168, height: 36 },
          editAs: 'absolute'
        });
      }
      catch (e) {
        //carry on
      }


    })


    let workbookName = 'StockPulse Extract ' + new Date().getDate() + new Date().toLocaleString('en-gb', { month: 'short' }) + new Date().getFullYear();
    workbook.xlsx.writeBuffer().then((data) => {
      let blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      fs.saveAs(blob, workbookName + '.xlsx');
    });



    //XLSX.writeFile(workbook, 'my_file.xls', { bookType: 'xls', type: 'buffer' });
  }










}
