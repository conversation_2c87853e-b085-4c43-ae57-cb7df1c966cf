.overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 2;
}

nav#navbar-new {
    position: fixed;
    display: flex;
    justify-content: space-between;
    top: 0;
    left: 0;
    right: 0;
    height: 35px;
    background-color: var(--primaryDark);
    z-index: 1;
    padding-left: 40px;

    @media (max-width: 1680px) {
        height: 30px;
    }

    @media (max-width: 1280px) {
        height: 25px;
    }

    #logo-container {
        display: flex;
        align-items: center;
        width: 180px;
        height: 100%;
        padding-left: 0.5em;

        #navbarLogo {
            height: 24px;
            
            @media (max-width: 1680px) {
                height: 19px;
            }
        
            @media (max-width: 1280px) {
                height: 14px;
            }
        }
    }

    #top-right-buttons-container {
        display: flex;
        justify-content: flex-end;
        width: 150px;

        @media (max-width: 1680px) {
            width: 130px;
        }
        
        @media (max-width: 1280px) {
            width: 110px;
        }

        button,
        a {
            background-color: transparent;
            border: none;
            color: var(--bodyColour);
            width: 35px;
            transition: 0.5s;
            display: flex;
            justify-content: center;
            align-items: center;

            @media (max-width: 1680px) {
                width: 30px;
            }
            
            @media (max-width: 1280px) {
                width: 25px;
            }

            &:hover {
                color: var(--secondary);
            }

            @keyframes spin {
                from {
                    transform: rotate(0deg);
                }

                to {
                    transform: rotate(360deg);
                }
            }

            &.animate {
                color: var(--secondary);
                animation: spin 2s linear infinite;
            }
        }
    }
}

#settingsPopover button {
    width: 100%;
}

#navbar-new.fixSideMenu {
    margin-left: 180px;

    @media (max-width: 1680px) {
        margin-left: 160px;
    }
    @media (max-width: 1280px) {
        margin-left: 140px;
    }
}