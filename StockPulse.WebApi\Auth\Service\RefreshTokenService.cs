﻿using Microsoft.EntityFrameworkCore;
using StockPulse.WebApi.Auth.Model;
using System;
using System.Linq;
using System.Security.Cryptography;
using System.Threading.Tasks;

namespace StockPulse.WebApi.Auth.Service
{
    public interface IRefreshTokenService
    {
        Task AddToken(UserRefreshToken userRefreshToken);
        Task<UserRefreshToken> RenewRefreshTokenAsync(string refreshToken);
        UserRefreshToken GenerateRefreshToken(ApplicationUser user);

        Task DeleteTokens(ApplicationUser user);

    }

    public class RefreshTokenService: IRefreshTokenService
    {
        private readonly ApplicationDbContext applicationDbContext;

        public RefreshTokenService(ApplicationDbContext applicationDbContext)
        {
            this.applicationDbContext = applicationDbContext;
        }

        public async Task AddToken(UserRefreshToken userRefreshToken)
        {
            await applicationDbContext.UserRefreshToken.AddAsync(userRefreshToken);
            await applicationDbContext.SaveChangesAsync();
        }

        public async Task<UserRefreshToken> RenewRefreshTokenAsync(string refreshToken)
        {
            var userRefreshToken = applicationDbContext.UserRefreshToken.Where(u => u.RefreshToken == refreshToken).SingleOrDefault();

            // return null if no user found with token
            if (userRefreshToken == null) return null;

            //Check if the token is still valid
            if (userRefreshToken.ExpireAt < DateTime.UtcNow)
            {
                return null;
            }

            // replace old refresh token with a new one and save
            var newRefreshToken = GenerateRefreshToken();
            userRefreshToken.IssuedAt = DateTime.UtcNow;
            userRefreshToken.ExpireAt = DateTime.UtcNow.AddDays(30); // Verify - Make this configurable
            userRefreshToken.RefreshToken = newRefreshToken;

            applicationDbContext.UserRefreshToken.Update(userRefreshToken);
            await applicationDbContext.SaveChangesAsync();

            return userRefreshToken;
        }

        public UserRefreshToken GenerateRefreshToken(ApplicationUser user)
        {
            string rt = GenerateRefreshToken();

            var userRefreshToken = new UserRefreshToken()
            {
                ExpireAt = DateTime.UtcNow.AddDays(30), // Verify - Make this configurable
                IssuedAt = DateTime.UtcNow,
                RefreshToken = rt,
                UserId = user.Id,
                LinkedPersonId = user.LinkedPersonId?.ToString()
            };

            return userRefreshToken;
        }

        private string GenerateRefreshToken()
        {
            var randomNumber = new byte[32];
            using (var rng = RandomNumberGenerator.Create())
            {
                rng.GetBytes(randomNumber);
                return Convert.ToBase64String(randomNumber);
            }
        }

        public async Task DeleteTokens(ApplicationUser user)
        {
            await applicationDbContext.UserRefreshToken.Where(u => u.UserId == user.Id).ExecuteDeleteAsync();
        }
    }
}
