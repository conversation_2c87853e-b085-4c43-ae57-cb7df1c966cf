﻿using System;

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace StockPulse.Model
{
    public class UnknownResolution
    {
        [Key]
        public int Id { get; set; }

        public int? ResolutionTypeId { get; set; }
        [ForeignKey("ResolutionTypeId")]
        public virtual ResolutionType ResolutionTypes { get; set; }


        [Column(TypeName = "datetime")]
        public DateTime ResolutionDateTime { get; set; }

        public int UserId { get; set; }
        [ForeignKey("UserId")]
        public virtual User User { get; set; }

        public string Notes { get; set; }

        public bool IsResolved { get; set; }

    }

}