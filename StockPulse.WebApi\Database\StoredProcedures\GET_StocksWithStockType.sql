﻿/****** Object:  StoredProcedure [dbo].[StocksWithStockType]    Script Date: 31/3/21 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].GET_StocksWithStockType
(
    @StockCheckId INT = NULL,
    @UserId INT = NULL
)
AS
BEGIN

SET NOCOUNT ON;

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END


DECLARE @isRegional INT;
SET @isRegional = (SELECT IsRegional FROM StockChecks
                    WHERE StockChecks.Id = @StockCheckId)

DECLARE @isTotal INT;
SET @isTotal = (SELECT IsTotal FROM StockChecks
                    WHERE StockChecks.Id = @StockCheckId)

IF @isRegional = 0 AND @isTotal = 0

    BEGIN
	
    WITH temporaryTable AS 
    (
    SELECT StockItems.Id AS StockItemId, 
    StockItems.[Description] AS Description,
    StockItems.[GroupDIS],
    StockItems.[DIS],
    StockItems.[StockType],
        CASE WHEN (StockItems.ScanId IS NOT NULL) THEN 'Scanned, InStock' END AS Status1,
        CASE WHEN (StockItems.ReconcilingItemId IS NOT NULL) THEN 'Not scanned, but matched to report' END AS Status2,
        CASE WHEN (StockItems.MissingResolutionId IS NOT NULL) THEN 'Missing vehicles, resolved' END AS Status3,
        CASE WHEN (StockItems.MissingResolutionId IS NULL AND StockItems.ReconcilingItemId IS NULL AND StockItems.ScanId IS NULL) THEN 'Missing vehicles' END AS Status4
    FROM [dbo].[StockItems]
    LEFT JOIN ReconcilingItems ON StockItems.ReconcilingItemId = ReconcilingItems.Id
    WHERE [StockItems].[StockCheckId] = @StockCheckId
    )
    SELECT StockItemId AS Id, Description, GroupDIS, DIS, [StockType], CONCAT(Status1,Status2,Status3,Status4) AS Status FROM temporaryTable;	

    END

IF @isRegional = 1

    BEGIN

    DECLARE @DivisionId INT;

    SET @DivisionId = (SELECT DivisionId FROM StockChecks
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id
                        WHERE StockChecks.Id = @StockCheckId AND StockChecks.IsRegional = 1)

	
    ;WITH temporaryTable AS 
    (
    SELECT StockItems.Id AS StockItemId, 
    StockItems.[Description] AS Description,
    StockItems.[GroupDIS],
    StockItems.[DIS],
    StockItems.[StockType],
        CASE WHEN (StockItems.ScanId IS NOT NULL) THEN 'Scanned, InStock' END AS Status1,
        CASE WHEN (StockItems.ReconcilingItemId IS NOT NULL) THEN 'Not scanned, but matched to report' END AS Status2,
        CASE WHEN (StockItems.MissingResolutionId IS NOT NULL) THEN 'Missing vehicles, resolved' END AS Status3,
        CASE WHEN (StockItems.MissingResolutionId IS NULL AND StockItems.ReconcilingItemId IS NULL AND StockItems.ScanId IS NULL) THEN 'Missing vehicles' END AS Status4
    FROM [dbo].[StockItems]
    LEFT JOIN ReconcilingItems ON StockItems.ReconcilingItemId = ReconcilingItems.Id
    INNER JOIN StockChecks ON StockChecks.Id=StockItems.StockCheckId
    INNER JOIN Sites ON Sites.Id=StockChecks.SiteId
    INNER JOIN Divisions ON Divisions.Id=Sites.DivisionId
    WHERE Divisions.Id = @DivisionId
    )
    SELECT StockItemId AS Id, Description, GroupDIS, DIS, [StockType], CONCAT(Status1,Status2,Status3,Status4) AS Status FROM temporaryTable;	

    END

IF @isTotal = 1

    BEGIN

    DECLARE @DealerGroupId INT;
    SET @DealerGroupId = (SELECT DealerGroupId FROM StockChecks
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id
                        WHERE StockChecks.Id = @StockCheckId)

	
    ;WITH temporaryTable AS 
    (
    SELECT StockItems.Id AS StockItemId, 
    StockItems.[Description] AS Description,
    StockItems.[GroupDIS],
    StockItems.[DIS],
    StockItems.[StockType],
        CASE WHEN (StockItems.ScanId IS NOT NULL) THEN 'Scanned, InStock' END AS Status1,
        CASE WHEN (StockItems.ReconcilingItemId IS NOT NULL) THEN 'Not scanned, but matched to report' END AS Status2,
        CASE WHEN (StockItems.MissingResolutionId IS NOT NULL) THEN 'Missing vehicles, resolved' END AS Status3,
        CASE WHEN (StockItems.MissingResolutionId IS NULL AND StockItems.ReconcilingItemId IS NULL AND StockItems.ScanId IS NULL) THEN 'Missing vehicles' END AS Status4
    FROM [dbo].[StockItems]
    LEFT JOIN ReconcilingItems ON StockItems.ReconcilingItemId = ReconcilingItems.Id
    INNER JOIN StockChecks ON StockChecks.Id=StockItems.StockCheckId
    INNER JOIN Sites ON Sites.Id=StockChecks.SiteId
    INNER JOIN Divisions ON Divisions.Id=Sites.DivisionId
    INNER JOIN DealerGroup ON Divisions.DealerGroupId=DealerGroup.Id
    WHERE DealerGroup.Id = @DealerGroupId
    )
    SELECT StockItemId AS Id, Description, GroupDIS, DIS, [StockType], CONCAT(Status1,Status2,Status3,Status4) AS Status FROM temporaryTable;	

    END


END

GO



--To use this run 
--exec [GET_StocksWithStockType] @StockCheckId = 1, @UserId = 1