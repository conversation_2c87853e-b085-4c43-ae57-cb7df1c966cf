﻿using System;
using System.Collections.Generic;

namespace StockPulse.WebApi.ViewModel
{
    //stockcheck signoff
    public class FinancialLineVM
    {
        public int? id { get; set; }
        public string accountCode { get; set; }
        public string description { get; set; }
        public decimal balance { get; set; }
        public string notes { get; set; }
        public bool isReconcilingAdj { get; set; }
        public string? site {  get; set; }
        public string? FileName { get; set; }
        public DateTime? FileDate { get; set; }
        public DateTime? LoadDate { get; set; }
        public string? UserName { get; set; }

    }


    public class PostFinancialLines
    {
        public List<FinancialLineVM> financialLines { get; set; }
        public int stockCheckId { get; set; }
        public int fileImportId { get; set; }
    }





}
