﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace StockPulse.WebApi.Migrations
{
    /// <inheritdoc />
    public partial class addFKtofileimportv2 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "FileImportId",
                schema: "dbo",
                table: "StockItems",
                type: "int",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_StockItems_FileImportId",
                schema: "dbo",
                table: "StockItems",
                column: "FileImportId");

            migrationBuilder.AddForeignKey(
                name: "FK_StockItems_FileImports_FileImportId",
                schema: "dbo",
                table: "StockItems",
                column: "FileImportId",
                principalSchema: "import",
                principalTable: "FileImports",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_StockItems_FileImports_FileImportId",
                schema: "dbo",
                table: "StockItems");

            migrationBuilder.DropIndex(
                name: "IX_StockItems_FileImportId",
                schema: "dbo",
                table: "StockItems");

            migrationBuilder.DropColumn(
                name: "FileImportId",
                schema: "dbo",
                table: "StockItems");
        }
    }
}
