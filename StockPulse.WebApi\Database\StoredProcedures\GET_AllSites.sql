﻿
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE OR ALTER PROCEDURE [dbo].[GET_AllSites]
(
    @UserId INT = NULL
)
AS
BEGIN

SET NOCOUNT ON

SELECT S.[Id]
      ,S.[Description]
      ,[IsActive]
      ,[Longitude]
      ,[Latitude]
      ,[DivisionId]
      ,D.[Description] as Division
  FROM [dbo].[Sites] As S
  INNER JOIN [dbo].[Divisions] AS D ON S.DivisionId = D.Id
  INNER JOIN [dbo].[DealerGroup] AS DG ON D.DealerGroupId = DG.Id
  INNER JOIN [dbo].[Users] AS U ON U.DealerGroupId = DG.Id
  WHERE U.Id = @UserId

	

END

GO
