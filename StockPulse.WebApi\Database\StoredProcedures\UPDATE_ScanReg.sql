﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].[UPDATE_ScanReg]
(
    @ScanId INT = NULL	,
    @NewReg nvarchar(10) = NULL	,
	@IsMobileApp INT,
	@UserId INT = NULL

)
AS
BEGIN

SET NOCOUNT ON

DECLARE @StockCheckId INT = null
SET @StockCheckId = (SELECT StockCheckId FROM Scans WHERE Id = @ScanId)

IF (dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0 OR dbo.[CanSaveScans](@StockCheckId) = 0)
BEGIN 
	RETURN
END
	
UPDATE Scans SET 
	Reg = @NewReg, 
	IsRegEditedOnDevice = IIF(@IsMobileApp = 1, 1, IsRegEditedOnDevice)  ,
	IsRegEditedOnWeb = IIF(@IsMobileApp = 0, 1, IsRegEditedOnWeb)  
	WHERE Id = @ScanId

EXEC [dbo].[UPDATE_StockCheckLastUpdate] @StockCheckId, @UserId

SELECT @StockCheckId

	

END

GO


