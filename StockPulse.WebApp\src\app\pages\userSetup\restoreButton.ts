import { Component, ViewChild } from "@angular/core";

import { ICellRendererAngularComp } from "ag-grid-angular";
import { UserAndLogin } from "src/app/model/UserAndLogin";
import { ApiAccessService } from "src/app/services/apiAccess.service";
import { ToastService } from "src/app/services/newToast.service";
import { ConstantsService } from '../../services/constants.service';
import { IconService } from '../../services/icon.service';
import { SaveDataService } from '../../services/saveData.service';
import { SelectionsService } from '../../services/selections.service';




// both this and the parent component could be folded into one component as they're both simple, but it illustrates how
// a fuller example could work
@Component({
    selector: 'restoreButtonTarget-cell',
    template: `
   
        <button *ngIf="params.data && (this.params.data.isDeleted == true || this.params.data.isLocked == true)" class="btn btn-danger" (click)="maybeRestore()">
            <fa-icon [icon]="icon.faUnlock"> Restore </fa-icon>
            </button>    

    

    `
    ,
    styles: [
        `
        button{
            height: 1.6em;
    margin: 0px;
    margin-top: -5px;
    padding: 0px 10px;

        }

        .btn{opacity:0.2}
        .btn:hover{opacity:1}
       
      `
    ]
})
export class RestoreButtonUserComponent implements ICellRendererAngularComp {
   
params:any;


    constructor(
        public selections: SelectionsService,
        public icon: IconService,
        public constants: ConstantsService,
        public apiAccess: ApiAccessService,
        public toastService: ToastService
    ) { }


    agInit(params: any): void {

    this.params = params

    }
    refresh(): boolean {
        return false;
    }


    maybeRestore(){
        this.constants.confirmModal.confirmModalHeader = 'Really restore this user?';
        this.constants.confirmModal.isDestructive=true;
        let mySubscription = this.selections.confirmModalEmitter.subscribe(res=>{
            if(res){
                this.restore()
            }
            mySubscription.unsubscribe();
        })

        this.constants.confirmModal.showModal();

    }

    restore(){
        this.apiAccess.restoreUser('User','UserAndLogin/Restore',this.params.data.appUserId).subscribe(res => {
            this.toastService.successToast('Restored user');
            //to do let matchingIndex = this.params.context.
            let parentRowData = this.params.context.componentParent.selections.userSetup.rowData;
            const index: number = parentRowData.findIndex(item => item.appUserId === this.params.data.appUserId);
            parentRowData[index].isDeleted = false;
            parentRowData[index].isLocked = false;
            
            this.params.context.componentParent.showSelectedStatusUsers();

        },e=>{
            this.toastService.errorToast('Failed to restore user');
        })
    }


     


}


