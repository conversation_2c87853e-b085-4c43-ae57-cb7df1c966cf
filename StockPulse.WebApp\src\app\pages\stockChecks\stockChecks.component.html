<nav class="page-specific-navbar">
  <div class="buttonGroup">
    <button class="btn btn-primary" [ngClass]="{ 'active': service.showActive }"
      (click)="toggleStockchecks(true)">Current</button>
    <button class="btn btn-primary" [ngClass]="{ 'active': !service.showActive }"
      (click)="toggleStockchecks(false)">Archived</button>
  </div>

  <!-- From / to date pickers for archived stock checks -->
  <div *ngIf="!service.showActive" class="datePickers">
    <div class="datePicker me-4">
      <span class="me-2">From date</span>
      <input type="date" name="fromDate" [(ngModel)]="service.fromDate">
    </div>

    <div class="datePicker me-2">
      <span class="me-2">To date</span>
      <input type="date" name="toDate" [(ngModel)]="service.toDate">
    </div>

    <button class="btn btn-success" (click)="reloadArchivedStockChecks()">
      Go
    </button>
  </div>

  <ng-container *ngIf="!selections.userIsGeneralManager && !selections.userIsReadOnly">
    <div class="buttonGroup">
      <!-- Buttons to set to archived or not -->

      <!-- Archive -->
      <button *ngIf="service.showActive && selectedStockCheckIds.length !== 0" class="btn btn-success"
        (click)="archive()" [disabled]="false">
        <fa-icon [icon]="icon.faArchive"></fa-icon>
        Set Selected Stockchecks to Archived
      </button>

      <!-- Unarchive -->
      <button *ngIf="!service.showActive && selectedStockCheckIds.length !== 0" class="btn btn-success "
        (click)="unArchive()">
        <fa-icon [icon]="icon.faArchive"></fa-icon>
        Set Selected Stockchecks to Current
      </button>
    </div>

    <!-- Buttons to make new stockchecks -->
    <!-- <div class="buttonGroup"> -->

      <button *ngIf="showNewStockCheckButton()" class="btn btn-success" (click)="showNewStockCheckModal()">
        <fa-icon [icon]="icon.faFile"></fa-icon>
        New Stock Check
      </button>

    <button *ngIf="constants.AutomatedDataImport && service.showActive" 
    id="importButton" 
    class="btn btn-success" 
    (click)="showImportLatestDataModal()" 
    [disabled]="disableImportLatestDataButton"
    >
    <fa-icon [icon]="icon.faFile"></fa-icon>
    Import Latest Data
    </button>

    <!-- STK-1352 - Kill this button -->
    <!-- <button *ngIf="showNewAllSiteStockCheckButton()"
      id="newStockCheckAllSiteButton" [disabled]="disableNewAllSiteStockCheckButton()"
      [ngbPopover]="disableNewAllSiteStockCheckButton ? 'Cannot create stock checks for all sites while there are still active stock checks.' : null"
      triggers="mouseenter:mouseleave"
      class="btn btn-primary"
      (click)="showNewStockCheckModalAllSites()">
      <fa-icon [icon]="icon.faFile"></fa-icon>
      New Stock Check - All Sites
    </button> -->

    

    <button *ngIf="selectedStockCheckIds.length && !isDeleteStockCheckButtonDisabled()"
    [ngClass]="isDeleteStockCheckButtonDisabled() ? 'btn-primary' : 'btn-danger'" class="btn"
    (click)="delete()">
      <fa-icon [icon]="icon.faFile"></fa-icon>
      Delete Stock Check<span *ngIf="selectedStockCheckIds.length > 1">s</span>
    </button>


    <!-- </div> -->
  </ng-container>

  <!-- <instructionRow *ngIf="disableNewAllSiteStockCheckButton()" id="warningMessage" [message]=""></instructionRow> -->
</nav>



<div class="content-new">

  <instructionRow [message]="'Double-click a stockcheck below to load.'"></instructionRow>

  <!-- Sites -->
  <ng-container *ngIf="constants.whoAmILoaded">
    <stockChecksTable (clickedRow)="service.loadStockCheck($event,false)"
      (selectedStockChecks)="setSelectedStockChecks($event)" [tableType]="'sites'" [viewHeight]="85"
      [rowData]="service.stockCheckVMs"></stockChecksTable>
  </ng-container>
</div>






<ng-template #newStockCheckModal let-modal>

  <div class="modal-header">
    <h4 *ngIf="allSiteModal" class="modal-title" id="modal-basic-title">
      Create New Stock Check - All Sites
    </h4>
    <h4 *ngIf="!allSiteModal" class="modal-title" id="modal-basic-title">
      Create New Stock Check
    </h4>
    <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body ">

    <div class="cardCph">

      <table class="newStockCheck">
        <tbody>
          <!-- Site -->
          <tr *ngIf="!allSiteModal">
            <td>Site</td>
            <td>

              <!-- Site dropdown -->
              <sitePicker *ngIf="Sites" [sitesFromParent]="Sites" (updateSites)="onChosenSites($event)"></sitePicker>

            </td>
          </tr>


          <tr>
            <td>Date</td>
            <td>
              <input id="newStockCheckDate" id="newStockCheckDate" type="date" (change)="changeDate($event)"
                name="newDate" [ngModel]="newStockCheckDate" />

            </td>
          </tr>

        </tbody>
      </table>



    </div>


  </div>
  <div class="modal-footer">

    <button type="button" class="btn btn-success" (click)="modal.close('Ok')" [disabled]="!allSiteModal && !chosenSites">OK</button>
    <button type="button" class="btn btn-primary" (click)="modal.dismiss('Cancelled')">Cancel</button>
  </div>


  <ng-template #accountPopover>
    <div id="accountPopover">
      <div class="popover-header">
        User Details
      </div>
      <div class="popover-body-inner">
        <table>
          <tr>
            <td>Name</td>
            <td>Test</td>
          </tr>
          <tr>
            <td>Role</td>
            <td>Test</td>
          </tr>
          <tr></tr>
        </table>
      </div>
      <div class="popover-footer">
        <button class="btn btn-primary">
          Log out
        </button>
      </div>
    </div>

  </ng-template>

</ng-template>

<ng-template #newImportLatestDataModal let-modal>

  <div class="modal-header">
    <h4 class="modal-title" id="modal-basic-title">
      Import Latest Data
    </h4>
    <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>

  <div class="modal-body ">

    <div class="cardCph">

      <table class="newStockCheck">
        <h3>{{importMessage}}</h3>
        <h3>Latest data recieved date: {{service.latestDataRecievedDate}}</h3>
        <!-- <h3>Are you sure you want to import the latest data for {{selectedStockCheckIds.length}} Stock Checks?</h3> -->
      </table>

    </div>


  </div>
  <div class="modal-footer">

    <button type="button" class="btn btn-success" (click)="modal.close('Ok')">OK</button>
    <button type="button" class="btn btn-primary" (click)="modal.dismiss('Cancelled')">Cancel</button>
  </div>

</ng-template>