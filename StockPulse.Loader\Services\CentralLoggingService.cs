﻿using System;
using log4net;
using StockPulse.Model;
using StockPulse.Repository.Database;


namespace StockPulse.Loader.Services
{


    public static class CentralLoggingService 
    {

        private static readonly ILog Logger = LogManager.GetLogger(typeof(CentralLoggingService));

        public static void ReportError(LogMessage logMessage, bool isUS = false, bool supressErrorIncrement = false)
        {
            using (var db = new StockpulseContext(isUS))
            {

                try
                {
                    Logger.Error($"FAIL.  Logs: {logMessage.FailNotes}");
                    if(!supressErrorIncrement) logMessage.ErrorCount++; //add one in here as are dealing with error.
                    string convertedFailNotes = logMessage.FailNotes.Replace("\r\n", "<br>");
                    EmailerService.SendErrorMail($"Problem loading {logMessage.Job} 😟", $"{ convertedFailNotes}");
                    if (logMessage.FinishDate == null) logMessage.FinishDate = DateTime.UtcNow;
                    db.LogMessages.Add(logMessage);
                    db.SaveChanges();
                }
                catch (Exception err)
                {
                    Logger.Error($@"[{DateTime.UtcNow}] {logMessage.Job}: Failed to save log messages to db {err.ToString()}");
                }
            }

        }

        public static void MakeNote(string note)
        {
            Logger.Info($@"[{DateTime.UtcNow}] {note})");
        }

       


    }
}
