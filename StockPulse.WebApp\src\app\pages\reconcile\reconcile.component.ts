import { Component, ElementRef, HostListener, OnInit, Renderer2, ViewChild } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Subscription } from 'rxjs';
import { BulkResolveComponent } from 'src/app/components/bulkResolve/bulkResolve.component';
import { ItemToOpen } from 'src/app/model/ItemToOpen';
import { ReconciliationState } from 'src/app/model/ReconciliationState';
import { SelectionsService } from 'src/app/services/selections.service';
import { BarNew } from "../../model/BarNew";
import { CustomHeaderComponent } from '../../_cellRenderers/customHeader.component';
import { StockChecksService } from '../stockChecks/stockChecks.service';
import { ReconcileService } from './reconcile.service';
import { ReconcileExcelExportService } from './reconcileExcelExport.service';
import { ConstantsService } from 'src/app/services/constants.service';

@Component({
  selector: 'app-reconcile',
  templateUrl: './reconcile.component.html',
  styleUrls: ['./reconcile.component.scss']
})
export class ReconcileComponent implements OnInit {
  @HostListener('window:resize', [])
  private onresize(event) {
    if (this.service.gridApi) this.service.gridApi.sizeColumnsToFit();
    this.createAndRevealWaterfall(this.bars);
    this.recalculateBarWidths();
  }

  allIdsInList: ItemToOpen[];
  waterfall: {
    xAxisHeight: number;
    bars: Array<BarNew>;
    barWidth: number;
    canvasRangeY: number;
  };
  hoveredBar: BarNew;

  


  //other things 
  bars: BarNew[];
  @ViewChild('newVehicle', { static: true }) addVehicleModal: ElementRef;
  subscription: Subscription;
  
  pageRefreshSubscription: Subscription;
  //gridLoaded: boolean = false;
  //waterfallLoaded: boolean = false;
  frameworkComponents: { agColumnHeader: any; };
  reloadBarSubscription: Subscription;
  sidenavToggledSubscription: Subscription;

  constructor(
   
    public service: ReconcileService,
    private renderer: Renderer2,
    private excelExportService: ReconcileExcelExportService,
    public selectionsService: SelectionsService,
    public constants: ConstantsService,
    private modalService: NgbModal,
    public stockChecksService: StockChecksService
  ) {
    this.frameworkComponents = { agColumnHeader: CustomHeaderComponent };
  }

  ngOnDestroy() {
    if (this.subscription) this.subscription.unsubscribe()
    if (this.pageRefreshSubscription) this.pageRefreshSubscription.unsubscribe();
    if (this.reloadBarSubscription) { this.reloadBarSubscription.unsubscribe(); }
    if (this.sidenavToggledSubscription) { this.sidenavToggledSubscription.unsubscribe(); }
    this.service.chosenBar = null;
    this.service.gridApi = null;
   // this.gridLoaded = false;
    //this.waterfallLoaded = false;
  }



  ngOnInit() {

    this.service.drawBarsFromZero = true;

    this.subscription = this.service.selections.stockCheckItemChanged.subscribe(change => {
      if (this.service.gridApi) {this.service.gridApi.refreshCells();} //update grid
      this.getDataAndInitialiseComponent(this.service.chosenBar.description);
    })

    this.reconcileAndRefresh();

    this.pageRefreshSubscription = this.service.constants.refreshPage.subscribe((res) => {
      if (res) {
        this.service.toastService.loadingToast();
        this.reconcileAndRefresh();
      }
    })

    this.reloadBarSubscription = this.service.reloadBar.subscribe(() => {
      this.selectBar(this.service.chosenBar);
    })

    this.sidenavToggledSubscription = this.service.constants.sidenavToggledEmitter.subscribe(() => {
      if (this.service.gridApi) {
        this.service.gridApi.sizeColumnsToFit();
        this.createAndRevealWaterfall(this.bars);
        this.recalculateBarWidths();
      }
    })
  }

  getDataAndInitialiseComponent(bar: string) {
    const toastRef = this.service.toastService.loadingToast('Loading waterfall...')
    this.service.apiAccess.get('stockchecks', 'ReconciliationBuckets', [{ key: 'stockcheckId', value: this.service.selections.stockCheck.id }]).subscribe((data: BarNew[]) => {
      
      //adjust the stock steps to be negative
      data.map(x=>{
        if(x.isStock && !x.isFullHeight){x.vehicleCount = x.vehicleCount * -1}
        
        //set the other labels
        x.label = Math.abs(x.vehicleCount)
        x.popoverLabel = x.label + ' items'
      })
      
      //set the bars
      this.bars = data;

      const chosenBar = this.service.chosenBar || this.bars.find(x=>x.description==='In Stock and Scanned')
      this.selectBar(chosenBar);

      this.createAndRevealWaterfall(this.bars);
    },e=>{

    },()=>{
      toastRef.close();
    });
  }



  reconcileAndRefresh() {
    //reconcile
    this.service.apiAccess.get('stockchecks', 'ReconcileStockCheck', [{ key: 'stockCheckId', value: this.service.selections.stockCheck.id }]).subscribe((res: any) => {
      this.stockChecksService.loadStockCheck(this.service.selections.stockCheck, true, false);
      let bar: string = 'In Stock and Scanned';
      this.getDataAndInitialiseComponent(bar);
    })
  }

 

 

  createAndRevealWaterfall(bars: Array<BarNew>) {
    const barAreaElement: HTMLElement = this.renderer.selectRootElement('#barAreaInsideArea');
    const shouldAnimate = this.service.drawBarsFromZero;
  
    let maxValue = 0;
    let minValue = 0;
    let cumulative = 0;
  
    const outsideBarsMax = Math.max(...bars.filter(x => x.reconciliationState === ReconciliationState.AllItems).map(x => x.vehicleCount));
    const middleBarVal = bars.find(x => x.reconciliationState === ReconciliationState.MatchedToStockOrScan)?.vehicleCount || 0;
  
    bars.forEach(bar => {
      bar.vehicleCountAdjusted = bar.vehicleCount; // Apply custom adjustment here if needed
    });
  
    bars.forEach(bar => {
      if (bar.isFullHeight) {
        cumulative = bar.vehicleCountAdjusted;
      } else {
        cumulative += bar.vehicleCountAdjusted;
      }
      maxValue = Math.max(maxValue, cumulative);
      minValue = Math.min(minValue, cumulative);
    });
  
    const range = maxValue - minValue;
    const barAreaHeight = barAreaElement.offsetHeight - 4;
    const barAreaWidth = barAreaElement.offsetWidth;
    const barWidth = barAreaWidth / bars.length;
  
    bars.forEach(bar => {
      bar.heightUltimate = Math.abs(bar.vehicleCountAdjusted) / range * barAreaHeight;
      bar.isGood = bar.vehicleCountAdjusted >= 0;
      bar.height = shouldAnimate ? 20 : bar.heightUltimate; // Start short only if animating
    });
  
    const xAxisHeight = -minValue / range * barAreaHeight;
    let cumulativeHeight = 0;
  
    bars.forEach(bar => {
      if (bar.isFullHeight) {
        bar.bottomEdgeUltimate = xAxisHeight;
        cumulativeHeight = xAxisHeight + bar.heightUltimate;
      } else {
        if (bar.isGood) {
          bar.bottomEdgeUltimate = cumulativeHeight;
          cumulativeHeight += bar.heightUltimate;
        } else {
          bar.bottomEdgeUltimate = cumulativeHeight - bar.heightUltimate;
          cumulativeHeight -= bar.heightUltimate;
        }
      }
      bar.bottomEdge = shouldAnimate ? 20 : bar.bottomEdgeUltimate; // Start baseline if animating
    });
  
    this.waterfall = {
      xAxisHeight: xAxisHeight,
      bars: bars,
      barWidth: barWidth,
      canvasRangeY: 0,
    };
  
    if (shouldAnimate) {
      setTimeout(() => {
        this.waterfall.bars.forEach(bar => {
          bar.bottomEdge = bar.bottomEdgeUltimate;
          bar.height = bar.heightUltimate;
        });
      }, 500);
    }
  }
  


  selectBarOnClick(bar: BarNew) {
    this.selectBar(bar);
  }


  selectBar(bar: BarNew) {

    this.service.selectedRows = null;
    this.service.chosenBar = bar;
    
    if (this.service.gridApi) {
      this.service.filterModel = this.service.gridApi.getFilterModel();
    }

    this.service.getDetailItemData(bar)
  }

  emitRefresh() {
    this.service.drawBarsFromZero = false;
    this.constants.refreshPage.emit(true);
  }

  

  rowTrackByFunction(index, item) {
    return item.id
  }
  
 


  unknownLabelLeft(): number {
    if (!this.waterfall) return 0
    let multiplier = this.waterfall.barWidth;
    return this.bars.filter(x => x.isStock).length * multiplier
  }

  unknownLabelWidth(): number {
    if (!this.waterfall) return 0
    let multiplier = this.waterfall.barWidth;
    return (this.bars.filter(x => x.isScan).length - 2) * multiplier
  }
  missingLabelWidth(): number {
    if (!this.waterfall) return 0
    let multiplier = this.waterfall.barWidth;
    return (this.bars.filter(x => x.isStock).length - 2) * multiplier
  }


  downloadGrid(): void {
    this.excelExportService.createGridExcel(this.service.detailItems, this.service.chosenBar)
  }

  downloadWaterfall(): void {
    this.excelExportService.createWaterfallExcel(this.bars);
  }

  provideInstructionMessage(){
    if(this.service.chosenBar){
      return `Click a bar or bar label to see vehicle detail.   Double click a vehicle row to see more detail.`
    }else{
      return 'Loading...';
    }
  }

  recalculateBarWidths() {
    let barAreaElement: HTMLElement = this.renderer.selectRootElement('#barAreaInsideArea');
    let barAreaWidth = barAreaElement.offsetWidth;
    this.waterfall.barWidth = barAreaWidth / this.bars.length;
  }

  maybeBulkResolve() {
    const modalRef = this.modalService.open(BulkResolveComponent, { size: 'sm' });
    modalRef.componentInstance.itemsToResolve = this.service.selectedRows;
    modalRef.componentInstance.isStock = this.service.chosenBar.isStock;
    modalRef.result.then(res => {})
  }
}
