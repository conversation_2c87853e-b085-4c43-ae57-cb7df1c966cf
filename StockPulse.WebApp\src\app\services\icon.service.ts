


import { Injectable } from '@angular/core';
import {
  faFilePlus,
  faArrowAltDown,
  faCheckCircle,
  faTimesCircle,
  faFileUpload,
  faSave,
  faCircleNotch,
  faEdit,
  faFileExcel,
  faCars,
  faCar,
  faFileMedical,
  faFilesMedical,
  faFileDownload,
  faMapMarkerQuestion,
  faMapMarkerExclamation,
  faThLarge,
  faFileImage,
  faGraduationCap,
  faChevronRight,
  faChevronLeft,
  faPencil,
  faLightbulb,
  faTimesSquare,
  faCircleInfo,
  faAnglesRight,
  faUnlock,
  
  
  
} from '@fortawesome/pro-solid-svg-icons'


import {
  faSearch,
   faFileSignature, faListUl, faClipboardCheck, faLocationArrow, faListAlt, faExclamation,faQuestion,faSignOutAlt,
  faMinusCircle,
  faPlusCircle,
  faQuestionCircle,
  faClone,
  faCarAlt,
  faLongArrowAltUp,
  faFilter,
  faLongArrowAltDown,
  faBars,
  faFileCircleCheck,
  faCalendarDays
  

} from '@fortawesome/free-solid-svg-icons'

import{
  faCheckSquare,
  faSquare,
  faInfoSquare,
  faMemoCircleInfo,
  faCog,
  faMessageQuestion
} from '@fortawesome/pro-regular-svg-icons'

import {
  faFilePowerpoint,
  faSyncAlt,
  faUser,
  faHouse,
  faList,
  faFileImport,
  faPlay,
  faCheck,
  faTable,
  faTriangleExclamation,
  faRepeat,
  faMagnifyingGlass,
  faUsers,
  faTrash,
  faFile,
  faCamera,
  faCircleExclamation,
  faLock,
  faEnvelope,
  faSunBright,
  faMoon,
  faHome,
  faFolderOpen,
  faArchive,
  faPrint,
  faChartWaterfall,
  faBadgeCheck,
  faTerminal
} from '@fortawesome/pro-light-svg-icons';

@Injectable({
  providedIn: 'root'
})


export class IconService{
  faTerminal = faTerminal;
  faFolderOpen=faFolderOpen;
  faMemoCircleInfo=faMemoCircleInfo;
  faFileCircleCheck=faFileCircleCheck;
  faCircleExclamation=faCircleExclamation;
  faFilePlus=faFilePlus;
  faSearch=faSearch;
  faTrash=faTrash;
  faQuestionCircle=faQuestionCircle;
  faHome=faHome;
  faFileSignature=faFileSignature;
  faListUl=faListUl;
  faClipboardCheck=faClipboardCheck;
  faCamera=faCamera;
  faPlay=faPlay;
  faChevronRight=faChevronRight;
  faChevronLeft=faChevronLeft;
  faLocationArrow=faLocationArrow;
  faListAlt=faListAlt;
  faExclamation=faExclamation;
  faQuestion=faQuestion;
  faSignOutAlt=faSignOutAlt;
  faMinusCircle=faMinusCircle;
  faPlusCircle=faPlusCircle;
  faClone=faClone;
  faFile=faFile;
  faSyncAlt=faSyncAlt;
  faArrowAltDown=faArrowAltDown;
  faCheckCircle=faCheckCircle;
  faTimesCircle=faTimesCircle;
  faFileUpload=faFileUpload;
  faSave=faSave;
  faCircleNotch=faCircleNotch;
  faEdit=faEdit;
  faFileExcel=faFileExcel;
  faCars=faCars;
  faCar=faCar;
  faCarAlt=faCarAlt;
  faRepeat=faRepeat;
  faFileMedical=faFileMedical;
  faFilesMedical=faFilesMedical;
  faFileDownload=faFileDownload;
  faMapMarkerQuestion=faMapMarkerQuestion;
  faMapMarkerExclamation=faMapMarkerExclamation;
  faThLarge=faThLarge;
  faLongArrowAltUp=faLongArrowAltUp;
  faLongArrowAltDown=faLongArrowAltDown;
  faFilter=faFilter;
  faBars=faBars;
  faFileImage=faFileImage;
  faGraduationCap=faGraduationCap;
  faCheckSquare=faCheckSquare;
  faTimesSquare=faTimesSquare;
  faSquare=faSquare;
  faUser=faUser;
  faLock=faLock;
  faEnvelope=faEnvelope;
  faInfoSquare=faInfoSquare;

  faUpload=faFileUpload;
  faPencil=faPencil;
  faFilePowerpoint=faFilePowerpoint;
  faHouse=faHouse;
  faList=faList;
  faFileImport=faFileImport;
  faCheck=faCheck;
  faTable=faTable;
  faTriangleExclamation=faTriangleExclamation;
  faMagnifyingGlass=faMagnifyingGlass;
  faUsers=faUsers;
  faSunBright = faSunBright;
  faMoon = faMoon;
  faArchive = faArchive;
  faLightBulb = faLightbulb;
  faCalendarDays = faCalendarDays;
  faCog = faCog;
  faPrint = faPrint;
  faCircleInfo = faCircleInfo;
  faChartWaterfall = faChartWaterfall;
  faBadgeCheck = faBadgeCheck;
  faAnglesRight = faAnglesRight;
  faMessageQuestion = faMessageQuestion;
  faUnlock = faUnlock;

  constructor() {
    
   

    
  }

  
}