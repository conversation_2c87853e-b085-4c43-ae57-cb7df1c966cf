﻿using System.ComponentModel.DataAnnotations;

namespace StockPulse.Model
{
    public class MaintenanceTable
    {
        [Key]
        public int Id { get; set; }

        public string Name { get; set; }
        
        public bool IsEnabled { get; set; }
    }


    public class MaintenanceTableSaveVM
    {
        public string TableName { get; set; }

        public string RowData { get; set; }

    }

    public class MaintenanceTableDeleteVM
    {
        public string TableName { get; set; }

        public int RowId { get; set; }

    }

}
