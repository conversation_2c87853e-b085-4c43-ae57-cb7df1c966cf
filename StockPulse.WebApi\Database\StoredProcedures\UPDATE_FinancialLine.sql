﻿
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].[UPDATE_FinancialLine]
(
    @StockCheckId INT = NULL,
    @UserId INT = NULL,
    @FinancialLineId INT = NULL,
    @Description NVarchar(50),
    @Notes nvarchar(250),
    @Balance decimal(15,3)
    
)
AS
BEGIN

SET NOCOUNT ON;


IF (dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0 OR dbo.[CanAmmendStockCheck](@StockCheckId) = 0 )
BEGIN 
    RETURN
END


UPDATE [FinancialLines] Set AccountDescription = @Description, Notes = @Notes, Balance = @Balance 
WHERE Id = @FinancialLineId

EXEC [dbo].[UPDATE_StockCheckLastUpdate] @StockCheckId, @UserId


	
END

GO


