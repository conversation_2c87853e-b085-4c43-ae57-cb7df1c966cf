﻿

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO




CREATE OR ALTER PROCEDURE [dbo].[DELETE_UnknownResolution]
(
    @StockCheckId INT,
	@UserId INT,
	@UnknownResolutionId INT
)
AS
BEGIN

SET NOCOUNT ON;

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END



UPDATE Scans 
SET UnknownResolutionId = NULL
WHERE UnknownResolutionId  = @UnknownResolutionId

DELETE [UnknownResolutionImages]
WHERE UnknownResolutionId = @UnknownResolutionId

DELETE [UnknownResolutions]
WHERE Id = @UnknownResolutionId

EXEC [dbo].[UPDATE_StockCheckLastUpdate] @StockCheckId, @UserId



END
	
  
	


GO


