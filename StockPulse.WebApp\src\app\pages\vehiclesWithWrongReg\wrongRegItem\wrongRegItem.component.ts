import { Component, Input } from "@angular/core";
import { ScanRegDifference } from "src/app/model/ScanRegDifference";
import { IconService } from '../../../services/icon.service';

@Component({
  selector: 'wrongRegItem',
  templateUrl: './wrongRegItem.component.html',
  styleUrls: ['./wrongRegItem.component.scss']
})
export class WrongRegItemComponent {
  @Input() item: ScanRegDifference;

  constructor(
    public icon: IconService
  ) { }
}
