﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

  CREATE OR ALTER PROCEDURE [dbo].[DataFactory_UpdateStocks]

AS
BEGIN
    -- SET NOCOUNT ON added to prevent extra result sets from
    -- interfering with SELECT statements.
    SET NOCOUNT ON

    -- Insert statements for procedure here
    BEGIN TRAN

	
	--------------------------------------------------------------------------------------------------
	----Backup the data---
	DECLARE @UniqueID UNIQUEIDENTIFIER
	SET @UniqueID  = NEWID()

	DECLARE @BackupDate DateTime
	SET @BackupDate = GETUTCDATE()

	INSERT INTO stockpulse_all_stock_backup (
			UniqueId
			,BackupDate
			,[Stockbook Number]
           ,[Registration Number]
           ,[Chassis]
           ,[Vehicle Description]
           ,[DIS]
           ,[DIG]
           ,[Branch]
           ,[Stock Type]
           ,[Comment]
           ,[Reference]
           ,[Stock Value]
           ,[Updated])
	SELECT @UniqueID, @BackupDate, [Stockbook Number]
           ,[Registration Number]
           ,[Chassis]
           ,[Vehicle Description]
           ,[DIS]
           ,[DIG]
           ,[Branch]
           ,[Stock Type]
           ,[Comment]
           ,[Reference]
           ,[Stock Value]
           ,[Updated]
	FROM [stockpulse_all_stock]

	--DELETE Older backups. Delete backups older than 15days.
	DELETE FROM stockpulse_all_stock_backup WHERE BackupDate < GETUTCDATE() - 15

	--------------------------------------------------------------------------------------------------
	




--	begin tran
--select top 0 *  into stockpulse_all_stock_20210406 from stockpulse_all_stock_20210405

--STEP 0.5 Now do the BCP thing

--STEP 1 FIRST FIX THEIR DATA QUALITY.  Pretty sure don't need this if input from their db




	UPDATE [stockpulse_all_stock]
	SET DIS = null
	WHERE DIS = 'NULL'

	UPDATE [stockpulse_all_stock]
	SET Chassis = null
	WHERE Chassis = 'NULL'

	UPDATE [stockpulse_all_stock]
	SET DIG = null
	WHERE DIG = 'NULL'

	UPDATE [stockpulse_all_stock]
	SET [Stock Value] = null
	WHERE [Stock Value] = 'NULL'

	UPDATE [stockpulse_all_stock]
	SET [Registration Number] = null
	WHERE [Registration Number] = 'NULL'

	UPDATE [stockpulse_all_stock]
	SET [Stock Value] = 0
	WHERE ISNUMERIC([Stock Value]) = 0

	UPDATE [stockpulse_all_stock]
	SET [Vehicle Description] = REPLACE([Vehicle Description], '  ', '')

	UPDATE [stockpulse_all_stock]
	SET  [Comment] = REPLACE([Comment], '  ', '')

	update [dbo].[stockpulse_all_stock] 
	SET Reference = '|'+Reference
	WHERE Reference not like '%|%'


--STEP 2 Now Import

COMMIT TRAN

BEGIN TRAN

 --drop table  #CurrentStockItems
 --drop table  #NewStockItems
 --drop table #SameItems

 DECLARE @TotalRecords INT;

 SET @TotalRecords = (SELECT Count(1) FROM [stockpulse_all_stock])

 IF (@TotalRecords > 15000)
 BEGIN 


	SELECT CONCAT(SI.StockCheckId,SUBSTRING(SI.Reference,0,IIF(CHARINDEX('|',SI.Reference,0) = 0,LEN(SI.Reference) +1,CHARINDEX('|',SI.Reference,0)))) As [UniqueId],
	SI.Id 
	INTO #CurrentStockItems 
	FROM StockItems AS SI
	INNER JOIN StockChecks AS SC ON SI.StockCheckId = SC.Id
	INNER JOIN sites as s on sc.SiteId = s.Id
	INNER JOIN Divisions as d on s.DivisionId = d.Id
	WHERE d.DealerGroupId = 1 AND SC.IsActive = 1

	SELECT
	CONCAT(ISNULL(SC.Id,SCForMap.Id),[Stockbook Number]) AS [UniqueId]
	INTO #NewStockItems
	FROM [dbo].[stockpulse_all_stock] As STK
	LEFT JOIN [dbo].[Sites] AS S ON S.Description = STK.Branch 
	LEFT JOIN [dbo].[stockchecks] AS SC ON S.Id = SC.SiteId AND SC.IsActive = 1
	LEFT JOIN [import].[SiteDescriptionDictionary] AS BN ON STK.Branch = BN.Description AND BN.IsPrimarySiteId = 1 AND BN.DealerGroupId = 1
	LEFT JOIN [dbo].[Sites] AS SForMap ON SForMap.Id = BN.SiteId
	LEFT JOIN [dbo].[stockchecks] AS SCForMap ON SForMap.Id = SCForMap.SiteId AND SCForMap.IsActive = 1
	WHERE ISNULL(SC.Id,SCForMap.Id) is NOT null
 
	--remove 
	--firstly remove any refs in scans to the stockitems we are about to remove
	UPDATE S
	SET S.stockitemid = null 
	FROM scans AS S
	INNER JOIN StockChecks AS SC ON S.StockCheckId = SC.Id
	INNER JOIN sites as si on sc.SiteId = si.Id
	INNER JOIN Divisions as d on si.DivisionId = d.Id
	WHERE d.DealerGroupId = 1
	AND SC.IsActive = 1
	AND stockitemid IN (SELECT Id FROM #CurrentStockItems WHERE UniqueId NOT IN (SELECT * FROM #NewStockItems))
	--now can remove the stockitems


	DELETE SI
	FROM StockItems AS SI
	INNER JOIN StockChecks AS SC ON SI.StockCheckId = SC.Id
	INNER JOIN sites as s on sc.SiteId = s.Id
	INNER JOIN Divisions as d on s.DivisionId = d.Id
	WHERE d.DealerGroupId = 1
	AND SC.IsActive = 1
	AND SI.IsAgencyStock = 0
	AND CONCAT(StockCheckId,SUBSTRING(Reference,0,IIF(CHARINDEX('|',Reference,0) = 0,LEN(Reference) +1,CHARINDEX('|',Reference,0)))) 
	IN (SELECT UniqueId FROM #CurrentStockItems WHERE UniqueId NOT IN (SELECT * FROM #NewStockItems))





	--insert new 

	--select * into StockitemsTEST from StockItems




	INSERT INTO Stockitems ([ScanId]
			   ,[ReconcilingItemId]
			   ,[MissingResolutionId]
			   ,[StockCheckId]
			   ,[SourceReportId]
			   ,[Reg]
			   ,[Vin]
			   ,[Description]
			   ,[DIS]
			   ,[GroupDIS]
			   ,[Branch]
			   ,[Comment]
			   ,[StockType]
			   ,[Reference]
			   ,[StockValue])
           


	--this gives us everything we need for the new items
	SELECT
	null,
	null,
	null,
	ISNULL(SC.Id,SCForMap.Id)
	, 1,[Registration Number] As Reg, Chassis As Vin
	, [Vehicle Description] As description,
	ISNULL(Dis,0),ISNULL(Dig,0),Branch,Comment, [Stock Type], 
	SUBSTRING(CONCAT([Stockbook Number],SUBSTRING(Reference,CHARINDEX('|',Reference,0),LEN(Reference) + 1)),0,50)  As reference -- limiting to 50 chars col limit
	, ISNULL([Stock Value],0)

	 FROM [dbo].[stockpulse_all_stock] As STK
	 LEFT JOIN [dbo].[Sites] AS S ON S.Description = STK.Branch AND S.IsActive = 1
	 LEFT JOIN [dbo].[stockchecks] AS SC ON S.Id = SC.SiteId  AND SC.IsActive = 1
	 LEFT JOIN [import].[SiteDescriptionDictionary] AS BN ON STK.Branch = BN.Description AND BN.IsPrimarySiteId = 1 AND BN.DealerGroupId = 1
	 LEFT JOIN [dbo].[Sites] AS SForMap ON SForMap.Id = BN.SiteId AND SForMap.IsActive = 1
	 LEFT JOIN [dbo].[stockchecks] AS SCForMap ON SForMap.Id = SCForMap.SiteId AND SCForMap.IsActive = 1
	 WHERE 
	 ISNULL(SC.Id,SCForMap.Id) is NOT null AND 
	 CONCAT(ISNULL(SC.Id,SCForMap.Id),[Stockbook Number]) IN 
	(SELECT UniqueId FROM #NewStockItems WHERE UniqueId NOT IN (SELECT UniqueId FROM #CurrentStockItems))
	--and [Stock Value] is null

	

	--Update

	--this gives us everything we need for the same items
	SELECT
	null As ScanId ,null AS ReconId ,null As Resid,
	ISNULL(SC.Id,SCForMap.Id) As UniqueId
	, 1 As ReportId,[Registration Number] As Reg, Chassis As Vin, [Vehicle Description] As description,
	ISNULL(Dis,0) AS DIS,ISNULL(Dig,0) AS DIG,Branch,Comment, [Stock Type], 
	CONCAT([Stockbook Number],SUBSTRING(Reference,CHARINDEX('|',Reference,0),LEN(Reference) +1))  As reference,
	[Stock Value]
	INTO #SameItems
	FROM [dbo].[stockpulse_all_stock] As STK
	LEFT JOIN [dbo].[Sites] AS S ON S.Description = STK.Branch 
	LEFT JOIN [dbo].[stockchecks] AS SC ON S.Id = SC.SiteId AND SC.IsActive = 1 
	LEFT JOIN [import].[SiteDescriptionDictionary] AS BN ON STK.Branch = BN.Description AND BN.IsPrimarySiteId = 1 AND BN.DealerGroupId = 1
	LEFT JOIN [dbo].[Sites] AS SForMap ON SForMap.Id = BN.SiteId
	LEFT JOIN [dbo].[stockchecks] AS SCForMap ON SForMap.Id = SCForMap.SiteId AND SCForMap.IsActive = 1
	WHERE ISNULL(SC.Id,SCForMap.Id) IS NOT null
 


	--Update the extisting items
	UPDATE SI
	SET SI.Reg = STk.Reg,
	SI.Vin = STK.Vin,
	SI.Description = STK.Description,
	SI.DIS = STK.DIS,
	SI.GroupDIS = STk.DIG,
	SI.Branch = STK.Branch,
	SI.Comment = STK.Comment,
	SI.StockType = STK.[Stock Type],
	SI.Reference =SUBSTRING(STK.reference,0,50),
	SI.StockValue = STK.[Stock Value]
	FROM StockItems As SI
	INNER JOIN #SameItems As STK ON CONCAT(SI.StockCheckId,SUBSTRING(SI.Reference,0,IIF(CHARINDEX('|',SI.Reference,0) = 0,LEN(SI.Reference) +1,CHARINDEX('|',SI.Reference,0)))) = CONCAT(UniqueId,SUBSTRING(STK.Reference,0,IIF(CHARINDEX('|',STK.Reference,0) = 0,LEN(STK.Reference) +1,CHARINDEX('|',STK.Reference,0))))
	INNER JOIN StockChecks AS SC ON SI.StockCheckId = SC.Id
	INNER JOIN sites as s on sc.SiteId = s.Id
	INNER JOIN Divisions as d on s.DivisionId = d.Id
	WHERE d.DealerGroupId = 1
	AND SC.IsActive = 1


END

COMMIT TRAN



END

GO