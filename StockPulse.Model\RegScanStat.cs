﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace StockPulse
{
    public class RegScanStat
    {
        public RegScanStat() { }
        public RegScanStat(string priority , decimal waitTime, bool didSucceed , int userId) 
        {
            UserId = userId;
            ScanDate = DateTime.Now;
            Priority = int.Parse(priority);
            WaitTime = waitTime;
            DidSucceed = didSucceed;
            FromLocallySaved = priority == "1";
        }
        [Key]
        public int Id { get; set; }
        public int UserId { get; set; }

        [Column(TypeName = "datetime")]
        public DateTime ScanDate { get; set; }
        public int Priority { get; set; }
        public decimal WaitTime { get; set; }
        public bool DidSucceed { get; set; }
        public bool FromLocallySaved { get; set; }
    }


}