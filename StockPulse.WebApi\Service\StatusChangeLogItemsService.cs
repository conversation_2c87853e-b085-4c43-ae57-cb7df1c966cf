﻿using StockPulse.Model;
using StockPulse.WebApi.DataAccess;
using StockPulse.WebApi.ViewModel;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace StockPulse.WebApi.Service
{
    public interface IStatusChangeLogItemsService
    {
        Task<IEnumerable<StatusChangeLogItemVM>> GetStatusChangeLogItems(int stockCheckId);
        Task AddStatusChangeLogItem(StatusChangeLogItemParams parms);
    }

    public class StatusChangeLogItemsService : IStatusChangeLogItemsService
    {
        //properties of the service
        private readonly IStatusChangeLogItemsDataAccess StatusChangeLogItemsDataAccess;

        //constructor
        public StatusChangeLogItemsService(IStatusChangeLogItemsDataAccess StatusChangeLogItemsDataAccess)
        {
            this.StatusChangeLogItemsDataAccess = StatusChangeLogItemsDataAccess;
        }

        public async Task<IEnumerable<StatusChangeLogItemVM>> GetStatusChangeLogItems(int stockCheckId)
        {
            return await StatusChangeLogItemsDataAccess.GetStatusChangeLogItems(stockCheckId);
        }

        public async Task AddStatusChangeLogItem(StatusChangeLogItemParams parms)
        {
            await StatusChangeLogItemsDataAccess.AddStatusChangeLogItem(parms);
        }
    }
}
