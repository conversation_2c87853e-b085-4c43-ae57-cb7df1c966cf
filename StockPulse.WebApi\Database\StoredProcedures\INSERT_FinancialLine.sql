﻿
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].[INSERT_FinancialLine]
(
    @StockCheckId INT = NULL,
    @UserId INT = NULL,
    @FinancialLine FinancialLineType READONLY
)
AS
BEGIN

SET NOCOUNT ON;

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END



INSERT into [FinancialLines] (Code,AccountDescription,Notes,IsExplanation,Balance,StockCheckId,FileImportId) (select * from @FinancialLine)


EXEC [dbo].[UPDATE_StockCheckLastUpdate] @StockCheckId, @UserId


END
	


GO


