﻿using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore.Query.Internal;
using Newtonsoft.Json;
using PlateRecognizer;
using RestSharp;
using StockPulse.WebApi.DataAccess;
using StockPulse.WebApi.ViewModel;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.Serialization.Json;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
//using System.Text.Json;

namespace StockPulse.WebApi.Service
{
    public interface IRecogniseService
    {
        Task<RegResult> RecogniseImage(IFormFile fileInput, string priority, int userId, string plateType, bool returnDummyIfNoneFound);
        Task<RegResult> RecogniseImageAndCheckIfDuplicate(IFormFile fileInput, string priority, int stockCheckId, int userId, string plateType);
        Task<VinResult> RecogniseVin(VinToInterpret payload, bool returnDummyItemIfNoneFound);
        Task<VinResult> RecogniseVinAndCheckIfSeenBefore(VinToInterpret payload, int stockCheckId, int userId);
    }

    public class RecogniseService : IRecogniseService
    {
        private IRecogniseDataAccess recogniseDataAccess;
        private IPlateService plateService;
        private IScanService scanService;
        private IScanDataAccess scanDataAccess;

        public RecogniseService(IRecogniseDataAccess recogniseDataAccess, IPlateService plateService, IScanService scanService, IScanDataAccess scanDataAccess)
        {
            this.recogniseDataAccess = recogniseDataAccess;
            this.plateService = plateService;
            this.scanService = scanService;
            this.scanDataAccess = scanDataAccess;
        }


        public async Task<RegResult> RecogniseImageAndCheckIfDuplicate(IFormFile fileInput, string priority, int stockCheckId, int userId, string plateType)
        {
            RegResult result = await RecogniseImage(fileInput, priority, userId, plateType, false);
            if (result.ScoreAndPlateItems.Count > 0)
            {
                //now check if most likely plate is a dupe
                try
                {
                    var firstResult = result.ScoreAndPlateItems.FirstOrDefault();
                    if (firstResult != null)
                    {

                        result.SeenBeforeMessage = await scanService.CheckIfRegSeenBefore(firstResult.Plate.Replace(" ", ""), stockCheckId, userId);
                    }
                }
                catch (Exception ex)
                {
                    //throw new Exception("Nothing found");
                }

            }
            // result.ScoreAndPlateItems.Add(new ScoreAndPlateItem() { Plate = "TEST", Score = 70 });
            return result;
        }



        public async Task<RegResult> RecogniseImage(IFormFile fileInput, string priority, int userId, string plateType, bool returnDummyIfNoneFound)
        {
            //string jsonResult = string.Empty;

            byte[] bytes = await fileInput.GetBytes();


            Random rnd = new Random();
            int randInt = rnd.Next(1, 3);
            bool isEven = randInt == 1;

            //Make Api Call
            // cancel source after 5 seconds

            int timeout = (priority == "1") ? 30000 : 5000;  //30s if from locally saved else 5s

            var cancellationTokenSource = new CancellationTokenSource(timeout);
            var cancellationToken = cancellationTokenSource.Token;

            try
            {
                DateTime scanDate = DateTime.Now;
                var task = Task.Run(() => plateService.ProcessScanAsync(bytes, isEven, plateType, cancellationToken), cancellationToken);
                PlateReaderResult processResult = await task;

                try
                {
                    TimeSpan waitTime = DateTime.Now - scanDate;
                    RegScanStat stat = new RegScanStat(priority, (int)waitTime.TotalMilliseconds, true, userId);
                    FireAndForgetSaveRegScanStat(stat);
                }
                catch (Exception ex)
                {

                }
                

                if (!processResult.DidSucceeed)
                {
                    throw new Exception("Nothing found");
                }


                //loop through the results

                if (plateType == "GB")
                {
                    ConstantsService.TidyUpResponses(processResult); //TODO if plateType equals GB
                }

                var toReturn = new RegResult(processResult);

                //add dummy item to prevent locallySaved interpretation routine choking out 
                if (toReturn.ScoreAndPlateItems.Count == 0 && returnDummyIfNoneFound)
                {
                        toReturn.ScoreAndPlateItems.Add(new ScoreAndPlateItem() { Plate = string.Empty, Score = 0 });
                }

                toReturn.ScoreAndPlateItems = toReturn.ScoreAndPlateItems.DistinctBy(x => x.Plate).ToList();
                return toReturn;
            }
            catch (OperationCanceledException)
            {
                throw new Exception("Failed to retrieve in time");
            }
        }



        public static class JsonSerializer<TType> where TType : class
        {
            /// <summary>
            /// DeSerializes an object from JSON
            /// </summary>
            public static TType DeSerialize(string json)
            {
                using (var stream = new MemoryStream(Encoding.Default.GetBytes(json)))
                {
                    var serializer = new DataContractJsonSerializer(typeof(TType));
                    return serializer.ReadObject(stream) as TType;
                }
            }
        }


        public async Task<VinResult> RecogniseVinAndCheckIfSeenBefore(VinToInterpret payload, int stockCheckId, int userId)

        {
            VinResult result = await RecogniseVin(payload,false);
            ScanSeenBefore seenBeforeResult = new ScanSeenBefore() { };

            string vin = result.ScoreAndPlateItems.FirstOrDefault()?.Plate;

            if (vin == "")
            {
                return new VinResult() { ScoreAndPlateItems = new List<ScoreAndPlateItem>() };
            }
            else
            {
                try
                {

                    seenBeforeResult = await scanDataAccess.CheckIfVinSeenBefore(vin, stockCheckId, userId);
                }
                catch (Exception ex)
                {

                }
                return new VinResult() { ScoreAndPlateItems = result.ScoreAndPlateItems, SeenBeforeMessage = seenBeforeResult };
            }
        }


        private async void FireAndForgetSaveRegScanStat(RegScanStat stat)
        {
            try
            {
                await recogniseDataAccess.SaveRegScanStat(stat);
            }
            catch (Exception ex)
            {
            }
        }

        public class VisionRequest
        {
            public List<ImageRequest> requests { get; set; }
        }

        public class ImageRequest
        {
            public ImageContent image { get; set; }
            public List<Feature> features { get; set; }
            public ImageContext imageContext { get; set; }
        }

        public class ImageContent
        {
            public string content { get; set; }
        }

        public class Feature
        {
            public string type { get; set; }
        }

        public class ImageContext
        {
            //  public string orientationHint { get; set; }
            public List<string> languageHints { get; set; }
        }



        public async Task<VinResult> RecogniseVin(VinToInterpret payload, bool returnDummyItemIfNoneFound)
        {
            string imageString = payload.imageString;
            var client = new RestClient("https://vision.googleapis.com/");
            var request = new RestRequest("v1/images:annotate?key=AIzaSyCt_RbuE35Bc-p1sITTRRMvr_vaaeNWDR8");
            request.AddHeader("Content-Type", "application/json");
            //\"languageHints\":[],\"

            var visionRequest = new VisionRequest
            {
                requests = new List<ImageRequest>
                    {
                        new ImageRequest
                        {
                            image = new ImageContent { content = imageString },
                            features = new List<Feature>
                            {
                                new Feature { type = "TEXT_DETECTION" }
                            },
                            imageContext = new ImageContext { languageHints = new List<string>(){"en" } }
                        }
                    }
            };


            string bodyString = Newtonsoft.Json.JsonConvert.SerializeObject(visionRequest);
            //string bodyString = "{\"requests\":[{\"image\":{\"content\":\"" + imageString + "\"},\"features\":[{\"type\":\"TEXT_DETECTION\"}],\"imageContext\":{\"orientationHint\":\"UP\" }]}";
            request.AddParameter("text/json", bodyString, ParameterType.RequestBody);
            var response = await client.ExecutePostAsync(request);
            VinReadResult vinResult = JsonSerializer<VinReadResult>.DeSerialize(response.Content);

            if (vinResult.Responses == null || vinResult.Responses.Count == 0 || vinResult.Responses[0].TextAnnotations == null || vinResult.Responses[0].TextAnnotations.Count == 0)
            {
                var emptyResult = new VinResult() { ScoreAndPlateItems = new List<ScoreAndPlateItem>() };

                //add dummy item to prevent locallySaved interpretation routine choking out 
                if (returnDummyItemIfNoneFound)
                {
                    emptyResult.ScoreAndPlateItems.Add( new ScoreAndPlateItem() { Plate = string.Empty, Score = 0, ScoreDebug = string.Empty });
                }

                return emptyResult;
            }
            else
            {
                // Assuming textAnnotation is a class with a Description property
                string textDescription = vinResult.Responses[0].TextAnnotations[0].Description; //Full test found on the Image

                List<ScoreAndPlateItem> scoreAndPlateItems = new List<ScoreAndPlateItem>();
                scoreAndPlateItems = textDescription.Split("\n").Select(s => new ScoreAndPlateItem { Score = 0, Plate = s }).ToList();

                foreach (var candidate in scoreAndPlateItems)
                {
                    CalculateConfidenceAndCleanVin(candidate);
                }

                scoreAndPlateItems = scoreAndPlateItems.Where(v => v.Score > 0).DistinctBy(x => x.Plate).OrderByDescending(x => x.Score).Take(4).ToList();

                //new bug, we must not ever end up with a fully empty list
                if(scoreAndPlateItems.Count == 0) { scoreAndPlateItems.Add(new ScoreAndPlateItem() { Plate = string.Empty, Score = 0, ScoreDebug = string.Empty }); }

                return new VinResult() { ScoreAndPlateItems = scoreAndPlateItems };
            }
        }

        public static void CalculateConfidenceAndCleanVin(ScoreAndPlateItem candidate)
        {
            string origString = candidate.Plate;
            candidate.Score = 0;
            if (candidate.Plate.Length < 8) return;  //can't be the vin we want.  And a result less than 8 is not worth having, easier to give no match and let user snap again.

            //free pass - remove GM from end of VIN
            //candidate.Plate = candidate.Plate.ToUpper().EndsWith("GM") ? candidate.Plate.ToUpper().TrimEnd('G','M') : candidate.Plate; 

            //safer approach
            if (candidate.Plate.ToUpper().EndsWith("GM"))
            {
                candidate.Plate = candidate.Plate.Substring(0, candidate.Plate.Length - 2);
            }
            
            //firstly give a free pass for dashes as some legit vins hold these.  
            candidate.Plate = candidate.Plate.Replace("-", "");


            //note down the presence of non alpha-numerics so can penalise later
            int fwdSlashCount = candidate.Plate.Split('/').Length - 1;
            candidate.Plate = candidate.Plate.Replace('/', '7');

            int spaceCount = candidate.Plate.Split(' ').Length - 1;
            candidate.Plate = candidate.Plate.Replace(" ", string.Empty);

            int vinTextCount = candidate.Plate.ToLower().Split("vin:").Length - 1;
            candidate.Plate = candidate.Plate.Replace("vin:", string.Empty, StringComparison.OrdinalIgnoreCase);

            char[] specialCharsInVin = candidate.Plate.ToCharArray().Where(c => !char.IsLetterOrDigit(c)).ToArray();
            int specialCharCount = candidate.Plate.Split(specialCharsInVin).Length - 1;
            candidate.Plate = Regex.Replace(candidate.Plate, @"[^a-zA-Z0-9]", string.Empty);


            //replace known characters
            char[] knownCharsToReplace = new[] { 'O', 'I', 'Q' };
            int knownCharsToReplaceCount = candidate.Plate.Split(knownCharsToReplace).Length - 1;
            candidate.Plate = candidate.Plate.Replace("O", "0"); /// Globally replace these things
            candidate.Plate = candidate.Plate.Replace("I", "1"); /// A vin plate never can have an O, I or Q
            candidate.Plate = candidate.Plate.Replace("Q", "9");


            //return if after cleanup the length is less than 8 
            if (candidate.Plate.Length < 8) return;


            int lengthOf17Bonus = 0;
            if (candidate.Plate.Length == 17) lengthOf17Bonus = 5;//bonus score if exactly 17 characters, very likely to be the VIN

            //set baseline score for length.  Designed for a 17 char vin to score 93, then + its bonus would be 98.
            int charIndex = 0;
            int baseLineScore = 43;
            foreach (char c in candidate.Plate.ToCharArray())
            {
                if (charIndex >= 8 && charIndex < 17)
                {
                    bool isLowerCase = char.IsLower(c);
                    baseLineScore += isLowerCase ? 2 : 5;
                }
                charIndex++;
            }

            //Convert to Upper
            candidate.Plate = candidate.Plate.ToUpper();
            candidate.Plate = candidate.Plate.Length > 8 ? candidate.Plate.Substring(candidate.Plate.Length - 8) : candidate.Plate;


            int totalErrorCount = fwdSlashCount + spaceCount + vinTextCount + specialCharCount + knownCharsToReplaceCount;// - lengthOf17;



            //If result has some numbers in, more likely to be a VIN
            int hasNumberBonus = 0;
            if (ContainsNumber(candidate.Plate))
            {
                hasNumberBonus = 5;
            }
            candidate.Score = baseLineScore - (totalErrorCount * 5) + lengthOf17Bonus + hasNumberBonus;

            string vinReadNote = $"Original was {origString} ({origString.Count()}).  Baseline was {baseLineScore}.   LengthOf17 bonus was: {lengthOf17Bonus}.  HasNumberBonus: {hasNumberBonus} ";
            if (fwdSlashCount > 0) { vinReadNote += $"{fwdSlashCount} fwdSlashCount error(s)"; }
            if (spaceCount > 0) { vinReadNote += $"{spaceCount} spaceCount error(s)"; }
            if (vinTextCount > 0) { vinReadNote += $"{vinTextCount} vinTextCount error(s)"; }
            if (specialCharCount > 0) { vinReadNote += $"{specialCharCount} specialCharCount error(s)"; }
            if (knownCharsToReplaceCount > 0) { vinReadNote += $"{knownCharsToReplaceCount} knownCharsToReplaceCount error(s)"; }

            candidate.ScoreDebug = vinReadNote;
            Console.WriteLine(vinReadNote);
            System.Diagnostics.Trace.WriteLine(vinReadNote);
            Console.Out.Flush();//should write to the log file immediately 


            if (candidate.Score < 40) candidate.Score = 40;
            if (candidate.Score > 98) candidate.Score = 98;
        }


        private static bool ContainsNumber(string input)
        {
            foreach (char c in input)
            {
                if (char.IsDigit(c))
                {
                    return true;
                }
            }
            return false;
        }



        private class UnknownResponse
        {
            public List<TextAnnotation> textAnnotations { get; set; }
        }

        private class TextAnnotation
        {
            public string description { get; set; }
        }





    }



}
