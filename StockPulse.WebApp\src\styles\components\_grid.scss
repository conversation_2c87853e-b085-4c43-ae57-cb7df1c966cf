ag-grid-angular {
    customheader {
        width: 100%;
    }

    .customSortDownLabel {
        color: var(--secondary) !important;
        transform: scale(1.2);
    }

    .customSortUpLabel {
        color: var(--secondary) !important;
        transform: scale(1.2);
    }

    .customFilter {
        color: var(--secondary) !important;
        transform: scale(1.2);
    }

    input.ag-floating-filter-input:focus {
        box-shadow: none !important;
    }

    .ag-cell-value {
        display: flex;
        align-items: center;
        word-break: keep-all;
        word-wrap: break-word;
    }

    .ag-cell .wrap {
        width: 100%;
        white-space: break-spaces;
        text-align: center;
        line-height: 1em;
    }
}

.agAlignCentre {
    display: flex;
    align-items: center;
    justify-content: center;
}

.indexCell {
    border: 0px !important;
}

.agAlignRight {
    display: flex;
    justify-content: flex-end;
}

importheader {
    width: 100% !important;
}

.ng-resizable {
    position: relative
}

.ng-resizable-handle {
    position: absolute;
    display: block;
    -ms-touch-action: none;
    touch-action: none
}

.ng-resizable-handle.ng-resizable-e {
    cursor: e-resize;
    width: 7px;
    right: -5px;
    height: 100%;
    top: 0
}

.ng-resizable-handle.ng-resizable-w {
    cursor: w-resize;
    width: 7px;
    left: -5px;
    height: 100%;
    top: 0
}

.ng-resizable-handle.ng-resizable-s {
    cursor: s-resize;
    height: 7px;
    bottom: -5px;
    width: 100%;
    left: 0
}

.ng-resizable-handle.ng-resizable-n {
    cursor: n-resize;
    height: 7px;
    top: -5px;
    width: 100%;
    left: 0
}

.ng-resizable-handle.ng-resizable-se {
    cursor: se-resize;
    width: 12px;
    height: 12px;
    right: 1px;
    bottom: 1px
}

.ng-resizable-handle.ng-resizable-sw {
    cursor: sw-resize;
    width: 12px;
    height: 12px;
    left: 1px;
    bottom: 1px
}

.ng-resizable-handle.ng-resizable-ne {
    cursor: ne-resize;
    width: 12px;
    height: 12px;
    right: 1px;
    top: 1px
}

.ng-resizable-handle.ng-resizable-nw {
    cursor: nw-resize;
    width: 12px;
    height: 12px;
    left: 1px;
    top: 1px
}

.ng-resizable-diagonal {
    box-sizing: border-box;
    width: 0;
    height: 0;
    border-bottom: 12px solid #aaa;
    border-left: 12px solid transparent
}

.ag-theme-balham .ag-row.regionalRow {
    background-color: var(--grey95);
    font-weight: 500;
}

.ag-theme-balham .ag-row.totalRow {
    background-color: var(--grey80);
    font-weight: 700;
}

.ag-theme-balham .ag-row-hover .ag-cell {
    background-color: var(--secondaryLightest);
}

.ag-theme-balham .ag-row-hover .ag-cell.noCellBackground,
.ag-theme-balham .ag-row.activeStockcheck .ag-cell.noCellBackground {
    background-color: var(--ag-background-color);
}

.ag-theme-balham .ag-row.activeStockcheck .ag-cell {
    background-color: var(--secondary);
}

bar-cell {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    align-items: center;
}

#gridHolder {
    width: 100%;
    height: calc(100vh - 35px - 2em - 4em); // Full height - nav height - content div padding - inst row
    position: relative;

    #grid {
        height: 100%;
        width: 100%;
    }
}

.ag-layout-normal>.ag-body-viewport.ag-layout-normal {
    overflow-y: overlay;
}

.ag-ltr .ag-cell-focus:not(.ag-cell-range-selected):focus-within,
.ag-ltr .ag-context-menu-open .ag-cell-focus:not(.ag-cell-range-selected),
.ag-ltr .ag-full-width-row.ag-row-focus:focus .ag-cell-wrapper.ag-row-group,
.ag-ltr .ag-cell-range-single-cell,
.ag-ltr .ag-cell-range-single-cell.ag-cell-range-handle,
.ag-rtl .ag-cell-focus:not(.ag-cell-range-selected):focus-within,
.ag-rtl .ag-context-menu-open .ag-cell-focus:not(.ag-cell-range-selected),
.ag-rtl .ag-full-width-row.ag-row-focus:focus .ag-cell-wrapper.ag-row-group,
.ag-rtl .ag-cell-range-single-cell,
.ag-rtl .ag-cell-range-single-cell.ag-cell-range-handle {
    border-color: transparent !important;
}

.ag-ltr .ag-floating-filter-button {
    margin-left: 0.25em;
}

@media (max-width: 1440px) {
    [class*=ag-theme-] {
    }
}

.ag-cell {
    overflow: hidden;
}