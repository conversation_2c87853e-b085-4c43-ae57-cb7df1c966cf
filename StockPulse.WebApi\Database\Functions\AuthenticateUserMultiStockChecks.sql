﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE FUNCTION [dbo].[AuthenticateUserMultiStockChecks] (@UserId INT, @StockCheckIds nvarchar(max))
RETURNS BIT
BEGIN

-- If user has access to the site linked to the stockcheck, return true
 
	  DECLARE @validStockCheckCount int
	  SELECT @validStockCheckCount = Count(1)
	  FROM [dbo].[Users]
	  INNER JOIN UserSites ON UserSites.UserId=Users.Id
	  INNER JOIN StockChecks ON StockChecks.SiteId=UserSites.SiteId
	  WHERE Users.Id = @UserId AND StockChecks.Id IN (SELECT * FROM STRING_SPLIT(@StockCheckIds, ','))

	  IF (@validStockCheckCount = (SELECT COUNT(*) FROM STRING_SPLIT(@StockCheckIds, ',')))
	  BEGIN 
		RETURN 1
	  END


-- Else, return false.
RETURN 0;
END;
