	
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].GET_AlreadyScannedCheck
(
	@UserId INT,
    @StockCheckId INT,
	@<PERSON> varchar(10)
)
AS
BEGIN


IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END

SELECT TOP 1 
1 AS IsSeenBefore,
CONCAT(Reg, ' scanned by ',us.Name,' at ', loc.Description) AS SeenBeforeDetail,
sc.ScanDateTime AS SeenBeforeTime
FROM Scans sc
INNER JOIN Users us on us.id = sc.UserId
INNER JOIN Locations loc on loc.id = sc.LocationId
WHERE StockCheckId = @StockCheckId
AND Reg = @Reg

END