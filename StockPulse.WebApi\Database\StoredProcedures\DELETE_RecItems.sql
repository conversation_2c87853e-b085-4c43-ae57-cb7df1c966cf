﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].[DELETE_RecItems]
(
    @StockCheckId INT = NULL,
    @UserId INT = NULL,
    @ItemId INT = NULL
)
AS
BEGIN

SET NOCOUNT ON;

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END

	
DELETE FROM ReconcilingItems 
WHERE StockCheckId =  @StockCheckId
AND Id = @ItemId

EXEC [dbo].[UPDATE_StockCheckLastUpdate] @StockCheckId, @UserId


END

GO


