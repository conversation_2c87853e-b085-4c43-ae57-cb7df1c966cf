import { Injectable } from '@angular/core';
import { Router, ActivatedRoute, CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { SelectionsService } from './services/selections.service';


@Injectable({
  providedIn: 'root'
})
export class AuthGuard implements CanActivate {


  constructor(
    private router: Router,
    public selections: SelectionsService,
    private routeIs: ActivatedRoute,

  ) { }



  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) {

    //no page, ok
    if (route.url.length === 0) { return true; }

    //always allowed to go to these
    if (['', 'home', 'vehicleSearch', 'stockChecks', 'labelPrinter', 'userMaintenance'].includes(route.url[0].path)) { return true; }


    //d user maintenance
    // if (['userMaintenance'].includes(route.url[0].path)) {
    //   return this.selections.userRole && this.selections.userRole === 'SysAdministrator';// true// !this.selections.userRole
    // }

    if (['tableMaintenance'].includes(route.url[0].path)) {
      return this.selections.userRole && this.selections.userRole === 'SysAdministrator' && (this.selections.userUsername.endsWith('@cphi.co.uk') || this.selections.userUsername.endsWith('@cphinsight.com'));// true// !this.selections.userRole
    }

    //otherwise, depends if you have loaded a stockcheck
    return !!this.selections.stockCheck








  }

}
