import { Component, OnInit } from '@angular/core';
import { forkJoin, Subscription } from 'rxjs';
import { RepeatOffendersStage } from "src/app/model/RepeatOffendersStage";
import { StockCheck } from 'src/app/model/StockCheck';
import { RepeatOffendersService } from 'src/app/pages/repeatOffenders/repeatOffenders.service';
import { IconService } from 'src/app/services/icon.service';

@Component({
  selector: 'app-repeatOffenders',
  templateUrl: './repeatOffenders.component.html',
  styleUrls: ['./repeatOffenders.component.scss']
})
export class RepeatOffendersComponent implements OnInit {
  stages = RepeatOffendersStage
  pageRefreshSubscription: Subscription;
  //newlyChosenStockCheckSub: Subscription;

  constructor(

    public service: RepeatOffendersService,
    public icon: IconService
  ) { }


  ngOnInit() {
    this.initParams();
    this.setupSubs();
  }



  private setupSubs() {
    // this.newlyChosenStockCheckSub = this.service.selection.newStockCheckSelectedEmitter.subscribe(res => {
    //   this.service.reset();
    // });

    this.pageRefreshSubscription = this.service.constants.refreshPage.subscribe((res) => {
      if (res) { this.refreshData(); }
    });
  }



  ngOnDestroy() {
    if (this.pageRefreshSubscription) { this.pageRefreshSubscription.unsubscribe(); }
   // if (this.newlyChosenStockCheckSub) { this.newlyChosenStockCheckSub.unsubscribe(); }

  }

  initParams() {


    //if don't exist, get them
    if (!this.service.stockChecks) {
      this.service.stage = RepeatOffendersStage.chooseStockChecks;
      //this.service.stockChecks = [];
      // this.service.missingVehicles = null;
      // this.service.unknownVehicles = [];
      this.getData();
    }



  }

  getData() {
    this.service.apiService.get('StockChecks', 'GetStockChecksForSite', [{ key: 'stockCheckId', value: this.service.selection.stockCheck.id }]).subscribe((res) => {
      this.service.stockChecks = res.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
      //this.service.stockChecksThisSite = res;
      this.service.toastService.destroyToast();
    });


  }


  refreshData() {
    // this.service.toastService.loadingToast('Loading...');
    this.getData();
  }



  selectStage(stage: RepeatOffendersStage) {
    this.service.stage = stage;

    //if (stage === RepeatOffendersStage.chooseStockCheck){ this.service.stockChecks = null;}
  }


  disableStockChecksButton() {
    if (!this.service.stockChecks) return true;
    return this.service.stockChecks.filter(x => x.isChosen).length < 2
  }



  showCalculateButton() {
    if (!this.service.stockChecks) return false;
    return this.service.stockChecks.filter(s => s.isChosen).length > 1
  }

  toggleStockCheck(stockCheck: StockCheck) {
    stockCheck.isChosen = !stockCheck.isChosen;
    // event.stopPropagation();
  }

  calculateRepeatIssues() {
    let toastRef = this.service.toastService.loadingToast();

    let stockcheckIds = this.service.stockChecks.filter(s => s.isChosen === true).sort((a, b) => b.id - a.id).map(i => i.id);

    let requests = [];
    requests.push(this.service.apiService.get('Scans', `GetRepeatUnknowns?stockcheckIds=${stockcheckIds.toString()}`, null)) //0
    requests.push(this.service.apiService.get('StockItems', `GetRepeatMissings?stockcheckIds=${stockcheckIds.toString()}`, null)) //1

    forkJoin(requests).subscribe((results: any[]) => {
      this.service.unknownVehicles = results[0];
      this.service.unknownVehicles.map(scan => {
        const strings = this.service.constants.buildImageStrings(scan.scanId, false)
        scan.regImageLargeUrl = strings.regImageLargeUrl;
        scan.regImageThumbnailUrl = strings.regImageThumbnailUrl;
      })

      this.service.missingVehicles = results[1];

      toastRef.close();
      this.selectStage(RepeatOffendersStage.unknown);
      this.service.toastService.successToast('Recurring issues calculated')
    })

  }

  shouldShowStage(stage: RepeatOffendersStage) {
    if (stage === RepeatOffendersStage.chooseStockChecks) { return true; }
    return this.service.stockChecks;
  }


  repeatUnknownsLabel() {
    let unknownsCount = this.service.unknownVehicles ? this.service.unknownVehicles.length : 0;
    return `Repeat Unknown (${this.service.constants.pluralise(unknownsCount, 'vehicle', 'vehicles')})`
  }
  repeatMissingsLabel() {
    let missingsCount = this.service.missingVehicles ? this.service.missingVehicles.length : 0;
    return `Repeat Missing (${this.service.constants.pluralise(missingsCount, 'vehicle', 'vehicles')})`
  }

  instructionRowMessage() {

    if (this.service.stage === this.stages.chooseStockChecks) {
      if (!this.showCalculateButton()) {
        //not yet chosen stock checks
        return 'Select stock checks to analyse. '
      } else if (!this.service.unknownVehicles) {
        //chosen stock checks but not loaded items
        return 'Click the calculate button to see any unknown and missing vehicles that are consistent across all stock checks.'
      }
      else {
        //loaded items
        return 'Choose whether to show recurring Unknown or Missing vehicles.';
      }
    }

    //workout the message part which describes which stockchecks we have chosen
    const chosenStockCheckDates = this.service.stockChecks.filter(s => s.isChosen).map(x => x.date)
    const stockChecksMessage = chosenStockCheckDates.map(x => this.service.cphPipe.transform(x, 'date', 0)).join(',');

    if (this.service.stage === this.stages.missing) { return `Showing vehicles that were missing in ${stockChecksMessage} stock checks.` }
    if (this.service.stage === this.stages.unknown) { return `Showing vehicles that were unknown in ${stockChecksMessage} stock checks.` }


  }
}
