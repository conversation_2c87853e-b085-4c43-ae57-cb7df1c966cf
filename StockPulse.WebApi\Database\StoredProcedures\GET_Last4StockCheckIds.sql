/****** Object:  StoredProcedure [dbo].[GET_Last4StockCheckIds]    Script Date: 22/3/21 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].GET_Last4StockCheckIds
(
    @StockCheckId INT = NULL,
    @UserId INT = NULL
)	
AS
BEGIN

SET NOCOUNT ON;

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END

SELECT TOP 4 Id 
FROM StockChecks 
where SiteId =  (SELECT siteId FROM StockChecks WHERE Id = @StockCheckId) 
AND Id < @StockCheckId
ORDER BY Id Desc


END

GO



--To use this run 
-- exec [GET_Last4StockCheckIds] @StockCheckId = 99, UserId = 104