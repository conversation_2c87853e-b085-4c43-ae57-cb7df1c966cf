﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace StockPulse.WebApi.Migrations
{
    /// <inheritdoc />
    public partial class addFKtofileimportforreconandfl : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "FileImportId",
                schema: "dbo",
                table: "ReconcilingItems",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "FileImportId",
                schema: "dbo",
                table: "FinancialLines",
                type: "int",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_ReconcilingItems_FileImportId",
                schema: "dbo",
                table: "ReconcilingItems",
                column: "FileImportId");

            migrationBuilder.CreateIndex(
                name: "IX_FinancialLines_FileImportId",
                schema: "dbo",
                table: "FinancialLines",
                column: "FileImportId");

            migrationBuilder.AddForeignKey(
                name: "FK_FinancialLines_FileImports_FileImportId",
                schema: "dbo",
                table: "FinancialLines",
                column: "FileImportId",
                principalSchema: "import",
                principalTable: "FileImports",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_ReconcilingItems_FileImports_FileImportId",
                schema: "dbo",
                table: "ReconcilingItems",
                column: "FileImportId",
                principalSchema: "import",
                principalTable: "FileImports",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_FinancialLines_FileImports_FileImportId",
                schema: "dbo",
                table: "FinancialLines");

            migrationBuilder.DropForeignKey(
                name: "FK_ReconcilingItems_FileImports_FileImportId",
                schema: "dbo",
                table: "ReconcilingItems");

            migrationBuilder.DropIndex(
                name: "IX_ReconcilingItems_FileImportId",
                schema: "dbo",
                table: "ReconcilingItems");

            migrationBuilder.DropIndex(
                name: "IX_FinancialLines_FileImportId",
                schema: "dbo",
                table: "FinancialLines");

            migrationBuilder.DropColumn(
                name: "FileImportId",
                schema: "dbo",
                table: "ReconcilingItems");

            migrationBuilder.DropColumn(
                name: "FileImportId",
                schema: "dbo",
                table: "FinancialLines");
        }
    }
}
