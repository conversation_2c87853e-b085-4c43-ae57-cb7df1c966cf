import { Component, ViewChild, ElementRef } from "@angular/core";
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { SelectionsService } from '../../services/selections.service';
import { ConstantsService } from "src/app/services/constants.service";
import { SiteVMWithSelected } from "src/app/model/SiteVMWithSelected";
import { ApiAccessService } from "src/app/services/apiAccess.service";
import { IconService } from "src/app/services/icon.service";
import { KeyValuePair } from "src/app/model/KeyValuePair";
import { ToastService } from "src/app/services/newToast.service";
import { Location } from "src/app/model/Location";

@Component({
  selector: 'scanLocationsModal',
  templateUrl: './scanLocationsModal.component.html',
  styleUrls: ['./scanLocationsModal.component.scss']
})
export class ScanLocationsModalComponent {
  @ViewChild('modalRef', { static: true }) modalRef: ElementRef;

  selectedSite: SiteVMWithSelected;
  sitesCopy: SiteVMWithSelected[];
  searchString: string = '';
  scanLocationsForSite: Location[];
  newLocation: string;

  constructor(
    public modalService: NgbModal,
    public constantsService: ConstantsService,
    public selectionsService: SelectionsService,
    public apiAccessService: ApiAccessService,
    public iconService: IconService,
    public toastService: ToastService
  ) { }

  showModal() {
    this.sitesCopy = JSON.parse(JSON.stringify(this.constantsService.Sites));
    this.modalService.open(this.modalRef, { size: 'xs', keyboard: true, ariaLabelledBy: 'modal-basic-title' }).result.then((result) => {

    }, (reason) => {

    });
  }

  searchList() {
    let sitesCopy: SiteVMWithSelected[] = JSON.parse(JSON.stringify(this.constantsService.Sites));
    this.sitesCopy = sitesCopy.filter(site => site.description.toLocaleLowerCase().includes(this.searchString.toLocaleLowerCase()));
  }

  selectSite(site: SiteVMWithSelected) {
    this.selectedSite = site;
    this.getScanLocationsBySiteId();
  }

  getScanLocationsBySiteId() {
    this.apiAccessService.get('ScanLocations', 'GetScanLocationsForSiteId', [{ key: 'siteId', value: this.selectedSite.id }]).subscribe((data: Location[]) => {
      this.scanLocationsForSite = data.sort((a, b) => a.description.localeCompare(b.description));

      if (this.selectedSite.description === this.selectionsService.stockCheck.site) {
        this.constantsService.Locations = this.scanLocationsForSite;
      }
    });
  }

  saveScanLocationForSiteId() {
    const params: any = {
      siteId: this.selectedSite.id,
      newLocation: this.newLocation
    }

    this.apiAccessService.post('ScanLocations', 'SaveScanLocationForSiteId', params).subscribe(() => {
      this.newLocation = null;
      this.toastService.successToast('Scan location saved');
      this.getScanLocationsBySiteId();
    }, error => {
      this.toastService.errorToast('Failed to save scan location');
      console.error('Failed to save scan location', error);
    });
  }

  deleteScanLocationForSiteId(locationId: number) {
    const params: any = {
      siteId: this.selectedSite.id,
      locationId: locationId
    }

    this.apiAccessService.post('ScanLocations', 'DeleteScanLocationForSiteId', params).subscribe(() => {
      this.toastService.successToast('Scan location deleted');
      this.getScanLocationsBySiteId();
    }, error => {
      this.toastService.errorToast('Failed to delete scan location');
      console.error('Failed to delete scan location', error);
    });
  }
}
