//tables
table.cph {
    position: relative;

    thead {
        th {
            font-weight: 700;
            text-align: right;
            line-height: 1.5em;
            height: 6em;
        }

        th.centre {
            text-align: center;
        }

        th:first-of-type {
            text-align: left;
            padding-left: 1em;
        }

        th:first-of-type.centre {
            text-align: center;
            padding-left: 0em;
        }

        th:last-of-type {
            padding-right: 1em;
        }
    }

    tbody {
        tr:nth-of-type(even) td {
            background-color: var(--grey95);
        }

        tr:nth-of-type(odd) td {
            background-color: white;
        }

        td {
            line-height: 3em;
            padding: 0em 0.5em;
            text-align: right;

            input {
                text-align: right;
                width: 100%;
            }
        }

        td.centre {
            text-align: center;

            input {
                text-align: center
            }
        }

        tr.input {
            td {
                padding: 0px;
            }
        }

        td:first-of-type {
            text-align: left;
            padding-left: 1em;

            input {
                text-align: left;
            }
        }

        td:first-of-type.centre {
            text-align: center;
            padding-left: 0em;

            input {
                text-align: center;
            }
        }

        td:last-of-type {
            padding-right: 1em;
        }

        tr.total td,
        td.total {
            font-weight: 700;
        }

        tr.bad td {
            background-color: var(--bad);
        }

        tr.highlighted td {
            background-color: var(--secondaryLightest);

            svg {
                opacity: 1 !important
            }
        }
    }
}

table.cph.noStripes {
    tbody tr:nth-of-type(even) td {
        background-color: white;
    }
}

table.glowRows {
    tr:hover td {
        background-color: var(--secondaryLightest) !important;
    }
}

table.borderAroundRow {
    tbody tr {
        border: 1px solid var(--grey80);
    }
}

table.fullWidth {
    width: 100%;
}

table.width90 {
    width: 90%;
    margin-left: auto;
    margin-right: auto;
}

table.open {
    width: 80%;
    margin: 1em auto;

    thead th {
        background-color: unset !important;
    }

    tr:nth-of-type(even) td {
        background-color: white;
    }

    tr:hover td {
        background-color: var(--secondaryLighter);
    }

    tbody td:first-of-type {
        padding-left: 0.5em !important
    }

    ;

    tbody tr td {
        line-height: 2.5em !important;
    }
}