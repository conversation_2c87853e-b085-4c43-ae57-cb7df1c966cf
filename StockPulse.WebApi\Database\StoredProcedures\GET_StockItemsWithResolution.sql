

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].[GET_StockItemsWithResolution]  
(  
    @StockCheckId INT = NULL,  
    @UserId INT = NULL  
)  
AS  
BEGIN  
  
SET NOCOUNT ON;  
   
DECLARE @isRegional INT;    
DECLARE @isTotal INT;    
DECLARE @StockCheckDate DateTime;  
DECLARE @DivisionId INT = NULL;  
DECLARE @DealerGroupId INT = NULL;  
DECLARE @SCId INT = NULL;  
  
  
IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END


  
SELECT   
@isRegional = IsRegional,   
@isTotal = IsTotal,   
@StockCheckDate = Date   
FROM StockChecks    
WHERE StockChecks.Id = @StockCheckId  
  
  

  
  
IF @isRegional = 0 AND @isTotal = 0  
  
    BEGIN  
  
  SET @SCId = @StockCheckId;  
  
  
    END  
  
ELSE IF @isRegional = 1 AND @isTotal = 0  
  
    BEGIN  
  
  
    SET @DivisionId = (SELECT DivisionId FROM StockChecks  
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId  
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id  
                        WHERE StockChecks.Id = @StockCheckId AND StockChecks.IsRegional = 1)  
  
  
    END  
  
ELSE IF  @isRegional = 0 AND @isTotal = 1  
  
    BEGIN  
  
      
      
    SET @DealerGroupId = (SELECT DealerGroupId FROM StockChecks  
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId  
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id  
                        WHERE StockChecks.Id = @StockCheckId)  
  
  
    END  
  
   
 select   
 s.Id as StockItemId,
 Reg,
 Vin,
 s.Description,
 DIS,
 GroupDIS,
 Branch,
 StockType,
 Flooring,
 Comment,
 Reference,
 StockValue,  
 rt.Id as ResolutionTypeId,  
 rt.Description as ResolutionTypeDescription,  
 mr.Id as ResolutionId,  

 mr.IsResolved,  
 usrs.Name as ResolvedBy,  
 mr.Notes as ResolutionNotes,  
    Sites.Description AS SiteName,
 (select String_Agg(CONCAT(mri.Id,'|',ISNULL(mri.FileName,'FileName')),'::')) AS ResolutionImageIds, --this seems slow TODO
 mr.ResolutionDate as ResolutionDate,
 SC.Date as StockCheckDate
 from stockitems s  
 LEFT JOIN MissingResolutions mr on mr.Id = s.MissingResolutionId  
 LEFT JOIN MissingResolutionImages mri on mri.MissingResolutionId = mr.Id
 LEFT JOIN ResolutionTypes rt on rt.Id = mr.ResolutionTypeId  
 LEFT JOIN Users usrs on usrs.Id = mr.UserId  
 INNER JOIN StockChecks AS SC ON SC.Id=s.StockCheckId  
    INNER JOIN Sites ON Sites.Id=SC.SiteId  
 INNER JOIN Divisions AS D ON D.Id=Sites.DivisionId  
 INNER JOIN DealerGroup AS DG ON D.DealerGroupId=DG.Id  
 where scanId is null  
 and ReconcilingItemId is null  
 --and StockCheckId = @StockCheckId   
 AND s.isDuplicate = 0  
 AND SC.Id = ISNULL(@SCId, SC.Id)  
 AND SC.Date = @StockCheckDate  
 AND D.Id = ISNULL(@DivisionId, D.Id)  
 AND DG.Id = ISNULL(@DealerGroupId, DG.Id)  
 GROUP BY 
 s.Id,
 Reg,
 Vin,
 s.Description,
 DIS,
 GroupDIS,
 Branch,
 StockType,
 Comment,
 Reference,
 Flooring,
 StockValue,  
 rt.Id,  
 rt.Description,  
 mr.Id,  
 mr.IsResolved,  
 usrs.Name,  
 mr.Notes,  
 Sites.Description,
 mr.ResolutionDate,
 SC.Date
  
  
  
END  
  

GO