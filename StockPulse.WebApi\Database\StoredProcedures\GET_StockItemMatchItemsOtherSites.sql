
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].GET_StockItemMatchItemsOtherSites
(
    @StockCheckId INT,
	@UserId INT
)
AS
BEGIN

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END


DECLARE @stockcheckDate Date = (SELECT CONVERT(date,Date) FROM Stockchecks where Id = @StockCheckId);

----------------------------------------------------------
--Find out the dealerGroupId
----------------------------------------------------------
DECLARE @dealerGroupId int = 
(
	SELECT div.DealerGroupId
	FROM Stockchecks sc 
	INNER JOIN Sites s on s.Id = sc.SiteId
	INNER JOIN Divisions div on div.Id = s.DivisionId
	WHERE sc.Id = @StockCheckId
);

DECLARE @IntraGroupMatchingRange int = (SELECT numberValue FROM GlobalParams WHERE DealerGroupId = @dealerGroupId AND Name = 'IntraGroupMatchingRange')
DECLARE @DateFrom date = DATEADD(DAY, -@IntraGroupMatchingRange, @stockcheckDate)
DECLARE @DateTo date = DATEADD(DAY, @IntraGroupMatchingRange, @stockcheckDate)

----------------------------------------------------------
--Firstly work out which relevant stockchecks are
----------------------------------------------------------
SELECT
sc.Id
INTO #relevantStockCheckIds
FROM StockChecks sc
INNER JOIN Sites si on si.id = sc.SiteId
INNER JOIN Divisions div on div.id = si.DivisionId
AND sc.Id <> @StockCheckId
AND div.DealerGroupId = @dealerGroupId
AND
(
	(@IntraGroupMatchingRange > 0 AND CONVERT(date, sc.Date) BETWEEN @DateFrom AND @DateTo)
    OR
    (@IntraGroupMatchingRange = 0 AND CONVERT(date, sc.Date) = @stockcheckDate)
)


----------------------------------------
--Build up final result
----------------------------------------
--Find out which stockItems at other sites do not match to a scan at all
SELECT
s.Id as ItemId,
s.Reg,
s.Vin
FROM StockItems s
INNER JOIN #relevantStockCheckIds rel on rel.Id = s.StockCheckId

WHERE s.ScanId IS NULL

UNION ALL

--Also add on stockitems at other sites that already are matched to my site (is likely will still be the case)
SELECT
s.Id as ItemId,
s.Reg,
s.Vin
FROM StockItems s
INNER JOIN #relevantStockCheckIds rel on rel.Id = s.StockCheckId
LEFT JOIN Scans alreadyMatchedScan on alreadyMatchedScan.Id = s.scanId  --FK to PK.   Assume fast

WHERE alreadyMatchedScan.StockCheckId = @StockCheckId

ORDER BY s.Id



END

GO


