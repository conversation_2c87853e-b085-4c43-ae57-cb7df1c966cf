SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].ADD_FileImport
(
    @FileName TEXT = NULL,
    @FileDate DATETIME = NULL,
    @LoadDate DATETIME = NULL,
    @LoadedByUserId INT = NULL
)
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @InsertedId INT;

    INSERT INTO import.FileImports (FileName, FileDate, LoadDate, LoadedByUserId)
    VALUES (@FileName, @FileDate, @LoadDate, @LoadedByUserId);

    SET @InsertedId = SCOPE_IDENTITY();

    SELECT @InsertedId AS InsertedId;
END
GO

--############################## Next SP #############################################﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].[ADD_MissingResolution]
(
    @Stock<PERSON>heckId INT,
	@UserId INT,
	@ResolutionTypeId INT,
	@ResolutionDate datetime,
	@Notes nvarchar(250),
	@IsResolved bit,
	@StockItemId INT
)
AS
BEGIN

SET NOCOUNT ON;

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END


INSERT INTO [dbo].[MissingResolutions]
		([ResolutionTypeId]
		,[ResolutionDate]
		,[Notes]
		,[IsResolved]
		,[UserId]
		,[StockcheckIdAndReference])
	VALUES
		(@ResolutionTypeId
		,@ResolutionDate
		,@Notes
		,@IsResolved
		,@UserId
		,(SELECT LEFT(CONCAT('SCID:',@StockCheckId,'|VIN:',Vin,'|REG:',Reg,'|REF:',Reference), 250) FROM StockItems WHERE Id = @StockItemId )
		)

		DECLARE @Id INT;
		SET @Id = SCOPE_IDENTITY();

		EXEC [dbo].[UPDATE_StockCheckLastUpdate] @StockCheckId, @UserId

		SELECT @Id


END
	
  
	


GO




--############################## Next SP #############################################﻿
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].[ADD_MissingResolutionImage]
(
	@FileName nvarchar(255),
    @StockCheckId INT,
	@UserId INT,
	@MissingResolutionId INT
)
AS
BEGIN

SET NOCOUNT ON;

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END

INSERT INTO [dbo].[MissingResolutionImages]
    ([MissingResolutionId],[FileName])
VALUES
    (@MissingResolutionId, @FileName)

	DECLARE @Id INT
	SET @Id = SCOPE_IDENTITY();

	EXEC [dbo].[UPDATE_StockCheckLastUpdate] @StockCheckId, @UserId

	SELECT @Id


END
	


GO




--############################## Next SP #############################################

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].[ADD_ReconcilingItemBackup]
(
	@FileName nvarchar(200),
    @StockCheckId INT,
	@ReconcilingItemTypeId INT,
	@UserId INT
)
AS
BEGIN

SET NOCOUNT ON;

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END

INSERT INTO [dbo].[ReconcilingItemBackups]
    ([StockCheckId],[ReconcilingItemTypeId],[FileName])
VALUES
    (@StockCheckId, @ReconcilingItemTypeId, @FileName)

	DECLARE @Id INT
	SET @Id = SCOPE_IDENTITY();

	EXEC [dbo].[UPDATE_StockCheckLastUpdate] @StockCheckId, @UserId

	SELECT @Id


END

GO


--############################## Next SP #############################################﻿

CREATE OR ALTER PROCEDURE [dbo].[ADD_Scan]
(
    @StockCheckId INT,
	@UserId INT,
	@LocationId INT,
	@RegConfidence INT,
	@VinConfidence INT,
	@Longitude decimal(9,4),
	@Latitude decimal(9,4),
	@ScanDateTime datetime,
	@HasVimImage bit,
    @Reg nvarchar(20),
    @Vin nvarchar(20),
    @InterpretedReg nvarchar(20),
    @InterpretedVin nvarchar(20),
    @IsRegEditedOnDevice INT,
    @IsVinEditedOnDevice INT,
    @Comment nvarchar(500),
    @Description nvarchar(250)
)
AS
BEGIN

SET NOCOUNT ON;


IF (dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0 OR dbo.[CanSaveScans](@StockCheckId) = 0)
BEGIN 
	RETURN
END

DECLARE @Id INT

BEGIN TRY
INSERT INTO [dbo].[Scans]
    (
    [StockCheckId]
    ,[UserId]
    ,[LastEditedById]
    ,[LocationId]
    ,[RegConfidence]
    ,[VinConfidence]
    ,[Longitude]
    ,[Latitude]
    ,[ScanDateTime]
    ,[HasVinImage]
    ,[IsDuplicate]
    ,[Reg]
    ,[Vin]
    ,[InterpretedReg]
    ,[InterpretedVin]
    ,[IsRegEditedOnWeb]
    ,[IsVinEditedOnWeb]
    ,[IsRegEditedOnDevice]
    ,[IsVinEditedOnDevice]
    ,[Comment]
    ,[Description]
    ,[SaveDate]
    )

VALUES
    (
    @StockCheckId
    ,@UserId
    ,@UserId
    ,@LocationId
    ,@RegConfidence
    ,@VinConfidence
    ,@Longitude
    ,@Latitude
    ,@ScanDateTime
    ,@HasVimImage
    ,0 --IsDuplicate
    ,@Reg 
    ,@Vin 
    ,@InterpretedReg
    ,@InterpretedVin
    ,0 --IsRegEditedOnWeb
    ,0 --IsVinEditedOnWeb
    ,@IsRegEditedOnDevice
    ,@IsVinEditedOnDevice
    ,@Comment 
    ,@Description
    ,GETUTCDATE()
    
    )

		SET @Id = SCOPE_IDENTITY();

		EXEC [dbo].[UPDATE_StockCheckLastUpdate] @StockCheckId, @UserId

		SELECT @Id AS ScanId, 0 AS IsExistingScan

END TRY
BEGIN CATCH
IF ERROR_NUMBER() = 2601 OR ERROR_NUMBER() = 2627 -- error number for UNIQUE KEY violation
        BEGIN
            DECLARE @ExistingId INT;

            -- Fetch the existing record's primary key
            SELECT @ExistingId = Id -- Replace 'Id' with the actual PK column name
            FROM dbo.Scans
            WHERE UserId = @UserId AND Reg = @Reg AND Vin = @Vin AND ScanDateTime = @ScanDateTime AND StockCheckId = @StockCheckId;

            -- Raise a custom error including the existing record's PK
            --RAISERROR ('UNIQUE KEY violation. The existing record''s primary key is %d.', 16, 1, @ExistingId);
			SELECT @ExistingId AS ScanId, 1 AS IsExistingScan
        END
        ELSE
        BEGIN
            -- Re-throw the original error if it's not a UNIQUE KEY violation
            THROW
        END
END CATCH;

    
    


END
	
  
	


GO




--############################## Next SP #############################################﻿

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].ADD_StatusChangeLogItem
(
	@StockCheckId int = NULL,
	@UserId int = NULL,
	@StatusId int = NULL
)
AS
BEGIN

SET NOCOUNT ON

INSERT INTO StatusChangeLogItems (StockCheckId, UserId, StatusId, Date)
VALUES (@StockCheckId, @UserId, @StatusId, GETDATE())

END

GO


--############################## Next SP #############################################﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].[ADD_UnknownResolution]
(
    @StockCheckId INT,
	@UserId INT,
	@ResolutionTypeId INT,
	@ResolutionDate datetime,
	@Notes nvarchar(250),
	@IsResolved bit
)
AS
BEGIN

SET NOCOUNT ON;

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END

INSERT INTO [dbo].[UnknownResolutions]
    ([ResolutionTypeId]
    ,[ResolutionDateTime]
    ,[Notes]
    ,[IsResolved]
    ,[UserId])
VALUES
    (@ResolutionTypeId
    ,@ResolutionDate
    ,@Notes
    ,@IsResolved
    ,@UserId)


DECLARE @Id INT
SET @Id = SCOPE_IDENTITY();

EXEC [dbo].[UPDATE_StockCheckLastUpdate] @StockCheckId, @UserId

SELECT @Id



END
	


GO



--############################## Next SP #############################################﻿
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].[ADD_UnknownResolutionImage]
(
	@fileName nvarchar(255),
    @StockCheckId INT,
	@UserId INT,
	@UnknownResolutionId INT
)
AS
BEGIN

SET NOCOUNT ON;

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END

INSERT INTO [dbo].[UnknownResolutionImages]
([UnknownResolutionId], [FileName])
VALUES
(@UnknownResolutionId, @fileName)


DECLARE @Id INT
SET @Id = SCOPE_IDENTITY();

EXEC [dbo].[UPDATE_StockCheckLastUpdate] @StockCheckId, @UserId

SELECT @Id



END
	
  
	

GO




--############################## Next SP #############################################﻿
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE OR ALTER PROCEDURE [admin].[GET_Last5DayStats] 
AS  
BEGIN  
  
SET NOCOUNT ON;  
  

CREATE TABLE #last5days (DayDate varchar(12))
DECLARE @todayDate Date = DATEADD(DAY,-4,getDate())

WHILE (@todayDate < getDate())

BEGIN
	INSERT INTO #last5days (DayDate) SELECT REPLACE(CONCAT('_',CONVERT(varchar(11), @todayDate, 113)),' ','')
	SET @todayDate = DATEADD(day,1,@todayDate)
END

DECLARE @cols as nvarchar(max);
set @cols =   (select STRING_AGG(DayDate,',') FROM #last5days);
DROP TABLE #last5days

--SELECT @cols

DECLARE @sqlstat nvarchar(max);
SET @sqlstat = N'
SELECT * FROM
(
	SELECT
	REPLACE(CONCAT(''_'',CONVERT(varchar(12), sca.ScanDateTime, 113)),'' '','''') as DayDate,
	dg.Description,
	ISNULL(COUNT(sca.Id),0) as Scans
	FROM Scans sca
	INNER JOIN StockChecks sc on sc.id = sca.StockCheckId
	INNER JOIN Sites si on si.id = sc.SiteId
	INNER JOIN Divisions div on div.id = si.DivisionId
	INNER JOIN DealerGroup dg on dg.id = div.DealerGroupId
	WHERE 
	sca.ScanDateTime >= DATEADD(DAY,-5,getDate())
	GROUP BY dg.Description,REPLACE(CONCAT(''_'',CONVERT(varchar(12), sca.ScanDateTime, 113)),'' '','''')

	UNION ALL

	SELECT
	''Total'' DayDate,
	dg.Description,
	ISNULL(COUNT(sca.Id),0) as Scans
	FROM Scans sca
	INNER JOIN StockChecks sc on sc.id = sca.StockCheckId
	INNER JOIN Sites si on si.id = sc.SiteId
	INNER JOIN Divisions div on div.id = si.DivisionId
	INNER JOIN DealerGroup dg on dg.id = div.DealerGroupId
	WHERE 
	sca.ScanDateTime >= DATEADD(DAY,-5,getDate())
	GROUP BY dg.Description

) as SourceTable
PIVOT --create the pivot
(
	SUM(Scans) --The measure you want as the value
	FOR [DayDate] in ('+@cols+',Total'+')
	
) as PivotTable;


';

--3. Run pivot
EXEC(@sqlstat)
   
END  
  
GO

--############################## Next SP #############################################﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE OR ALTER PROCEDURE [admin].[GET_LastHourActivity] 
AS  
BEGIN  
  
SET NOCOUNT ON;  

DECLARE @MissingResolutions int = 
(
	SELECT
	COUNT(Id)
	FROM MissingResolutions
	WHERE ResolutionDate >= DATEADD(hour,-1,getDate())
)

DECLARE @UnknownResolutions int = 
(
	SELECT
	COUNT(Id)
	FROM UnknownResolutions
	WHERE ResolutionDateTime >= DATEADD(hour,-1,getDate())
)

DECLARE @Scans int = 
(
	SELECT
	COUNT(Id)
	FROM Scans
	WHERE ScanDateTime >= DATEADD(hour,-1,getDate())
)

SELECT
@Scans as ScansTaken,
@MissingResolutions as MissingsResolved,
@UnknownResolutions as UnknownsResolved

END  
  
GO


--############################## Next SP #############################################﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE OR ALTER PROCEDURE [admin].[GET_StockCheckScanRecognitionReport] 
(
	@StockCheckId INT
)
AS  
BEGIN  
  
SET NOCOUNT ON;  

--This is the normal query bit

DECLARE @TotalScans int = (SELECT COUNT(Id) FROM Scans WHERE StockCheckId = @StockCheckId)

SELECT
CASE
	WHEN sca.IsRegEditedOnDevice = 1 THEN 'Changed on device'
	WHEN sca.IsRegEditedOnWeb =1 THEN 'Changed on web'
	ELSE 'OK'
END as Reg,

CASE
	WHEN sca.IsVinEditedOnDevice = 1 THEN 'Changed on device'
	WHEN sca.IsVinEditedOnWeb =1 THEN 'Changed on web'
	ELSE 'OK'
END as Vin,

COUNT(Id) as Count,
CAST(ROUND((CAST(COUNT(Id) AS FLOAT) / @TotalScans) * 100, 0) AS VARCHAR) + '%' as Percentage

FROM Scans sca
WHERE sca.StockCheckId = @StockCheckId
GROUP BY 
CASE
	WHEN sca.IsRegEditedOnDevice = 1 THEN 'Changed on device'
	WHEN sca.IsRegEditedOnWeb =1 THEN 'Changed on web'
	ELSE 'OK'
END ,

CASE
	WHEN sca.IsVinEditedOnDevice = 1 THEN 'Changed on device'
	WHEN sca.IsVinEditedOnWeb =1 THEN 'Changed on web'
	ELSE 'OK'
END 

UNION ALL

SELECT 'Total','',@TotalScans,''




--This bit outputs a load of messages giving us detail on the ones that it could not find


DECLARE @Id int, @Type nvarchar(20), @InterpretedString nvarchar(255), @FinalString nvarchar(255)
DECLARE @Message nvarchar(max)

-- Cursor to loop through the rows
DECLARE diagnostic_cursor CURSOR FOR 

SELECT Id, 'RegEditedOnDev', InterpretedReg, Reg FROM Scans WHERE StockCheckId = @StockCheckId AND IsRegEditedOnDevice = 1
UNION ALL
SELECT Id, 'RegEditedOnWeb', InterpretedReg, Reg FROM Scans WHERE StockCheckId = @StockCheckId AND IsRegEditedOnWeb = 1
UNION ALL
SELECT Id, 'VinEditedOnDev', InterpretedVin, Vin FROM Scans WHERE StockCheckId = @StockCheckId AND IsVinEditedOnDevice = 1
UNION ALL
SELECT Id, 'VinEditedOnWeb', InterpretedVin, Vin FROM Scans WHERE StockCheckId = @StockCheckId AND IsVinEditedOnWeb = 1

-- Open the cursor
OPEN diagnostic_cursor

-- Fetch the first row from the cursor
FETCH NEXT FROM diagnostic_cursor INTO @Id, @Type, @InterpretedString, @FinalString

-- Loop through the rows
WHILE @@FETCH_STATUS = 0
BEGIN
    -- Construct and print the message for each row
    SET @Message = @Type + ': ' + 'Id #' + CAST(@Id AS nvarchar) 
            + ', Interpreted: ' + RIGHT('          ' + CONVERT(nvarchar(8), @InterpretedString), 10)
            + ', Final: ' + RIGHT('          ' + CONVERT(varchar(8), @FinalString), 10)

    PRINT @Message

    -- Fetch the next row from the cursor
    FETCH NEXT FROM diagnostic_cursor INTO @Id, @Type,@InterpretedString, @FinalString
END

-- Close and deallocate the cursor
CLOSE diagnostic_cursor
DEALLOCATE diagnostic_cursor


END  
  
GO





--############################## Next SP #############################################/****** Object:  StoredProcedure [dbo].[BULKADD_StockItems]    Script Date: 17/04/2023 18:19:37 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO



CREATE  OR ALTER PROCEDURE [dbo].[BULKADD_StockItems]
(
    @StockCheckId INT = NULL,
	@UserId INT = NULL,
    @StockItems StockItemType READONLY
)
AS
BEGIN

SET NOCOUNT ON;


IF (dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0 OR dbo.[CanSaveScans](@StockCheckId) = 0)
BEGIN 
	RETURN
END
	
INSERT INTO StockItems (ScanId,ReconcilingItemId,MissingResolutionId,StockCheckId,SourceReportId,Reg,Vin,Description,DIS,GroupDIS,Branch,Comment,StockType,Reference,StockValue,Flooring,IsAgencyStock,FileImportId)
(SELECT ScanId,ReconcilingItemId,MissingResolutionId,StockCheckId,SourceReportId,Reg,Vin,Description,DIS,GroupDIS,Branch,Comment,StockType,Reference,StockValue,Flooring,IsAgencyStock,FileImportId FROM @StockItems)



	
END

GO




--############################## Next SP #############################################﻿/****** Object:  StoredProcedure [dbo].[CREATE_ImportMask]    Script Date: 9/3/21 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].CREATE_ImportMask
(
    @Name varchar(30) = NULL	,
	@UserId int = NULL,
	@TopRowsToSkip int = NULL	,
	@ColumnValueEqualsesJSON varchar(500) = NULL	,
	@ColumnValueDifferentFromsJSON varchar(500) = NULL	,
	@ColumnValueNotNullsJSON varchar(500) = NULL	,
	@ColumnsWeWantJSON varchar(500) = NULL	,
	@IsStandard bit = 0,
	@IsMultiSite bit = 0,
	@IgnoreZeroValues bit = 0
)
AS
BEGIN

SET NOCOUNT ON

INSERT 
INTO ImportMasks (Name,UserId,TopRowsToSkip,ColumnValueEqualsesJSON,ColumnValueDifferentFromsJSON,ColumnValueNotNullsJSON,ColumnsWeWantJSON,IsStandard,IsMultiSite,IgnoreZeroValues)
 values (@Name,@UserId,@TopRowsToSkip,@ColumnValueEqualsesJSON,@ColumnValueDifferentFromsJSON,@ColumnValueNotNullsJSON,@ColumnsWeWantJSON,@IsStandard,@IsMultiSite,@IgnoreZeroValues);

 SELECT SCOPE_IDENTITY();
END

GO



--To use this run 
--exec [CREATE_ImportMask] @Name = 'Bob', @DealerGroup = 1

--############################## Next SP #############################################SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].CREATE_LabelPrintLog
(
    @Vin varchar = NULL,
	@UserId int = NULL
)
AS
BEGIN

SET NOCOUNT ON

INSERT 
INTO PrintLogs (UserId, Vin, Date)
VALUES (@UserId, @Vin, GETDATE());

SELECT SCOPE_IDENTITY();
END

GO


--############################## Next SP #############################################﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].[CREATE_ReconcilingItems]
(
    @Items  as ReconcilingItemSaveType readonly,
	@UserId int
)
AS
BEGIN

SET NOCOUNT ON

DECLARE @StockCheckId INT 

SET @StockCheckId = (SELECT TOP 1 StockCheckId FROM @Items)

IF (dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0 OR dbo.[CanSaveScans](@StockCheckId) = 0)
BEGIN 
    RETURN
END

INSERT 
	INTO ReconcilingItems (ReconcilingItemTypeId,Reg,Vin,Description,Comment,Reference,SourceReportId,StockCheckId,FileImportId)
	(select ReconcilingItemTypeId,Reg,Vin,Description,Comment,Reference,SourceReportId,StockCheckId,FileImportId from @Items)
	

	EXEC [dbo].[UPDATE_StockCheckLastUpdate] @StockCheckId, @UserId
 
END

GO




--############################## Next SP #############################################﻿/****** Object:  StoredProcedure [dbo].[CREATE_User]    Script Date: 9/3/21 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].CREATE_User
(
	@UserId INT,
    @Name varchar(30) = NULL,
	@DealerGroup int,
	@EmployeeNumber varchar(50) = NULL
)
AS
BEGIN

--declare @NewIdOutput table (Id int);

SET NOCOUNT ON

IF ((SELECT DealerGroupId FROM dbo.Users WHERE Id = @UserId) != @DealerGroup)
BEGIN 
    RETURN
END

INSERT 
INTO Users (Name,DealerGroupId,EmployeeNumber)
	--OUTPUT inserted.Id into @NewIdOutput
 values (@Name,@DealerGroup,@EmployeeNumber);

 --select Id from @NewIdOutput

 SELECT SCOPE_IDENTITY();
END

GO




--To use this run 
--exec [CREATE_User] @Name = 'Bob', @DealerGroup = 1

--############################## Next SP #############################################﻿

/****** Object:  StoredProcedure [dbo].[CREATE_UserSites]    Script Date: 30/04/2021 19:07:42 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

  
  
CREATE OR ALTER PROCEDURE [dbo].[CREATE_UserSites]  
(  
    @UserId int,  
    @NewUserId int,  
 @SiteId int,  
 @SiteString varchar(max)  
)  
AS  
BEGIN  
  
  
SET NOCOUNT ON  
  
DECLARE @UserDealerGroupId INT;
DECLARE @NewUserDealerGroupId INT;
SELECT @UserDealerGroupId = DealerGroupId FROM Users WHERE Id = @UserId
SELECT @NewUserDealerGroupId = DealerGroupId FROM Users WHERE Id = @NewUserId
IF (@NewUserDealerGroupId != @UserDealerGroupId)
BEGIN 
    RETURN
END
  

DELETE US  
FROM UserSites AS US   
WHERE US.UserId = @NewUserId   
AND Us.SiteId NOT IN (SELECT * FROM STRING_SPLIT(@SiteString, ','))  
  
  
INSERT INTO UserSites (UserId,SiteId,IsDefault)  

SELECT @NewUserId As UserId, NewSites.value As SiteId, 0 As IsDefault
FROM STRING_SPLIT(@SiteString, ',') AS NewSites 
WHERE NewSites.value NOT IN (SELECT SiteId FROM UserSites WHERE UserId = @NewUserId )




UPDATE UserSites  
SET IsDefault = 0  
WHERE UserId = @NewUserId 
  
UPDATE UserSites  
SET IsDefault = 1  
WHERE UserId = @NewUserId AND SiteId = @SiteId  
  
  
  
  
   
END  
  
GO




--############################## Next SP #############################################﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].[DataFactory_InsertWIP]
AS
BEGIN


	--------------------------------------------------------------------------------------------------
	----Backup the data---
	DECLARE @UniqueID UNIQUEIDENTIFIER
	SET @UniqueID  = NEWID()

	DECLARE @BackupDate DateTime
	SET @BackupDate = GETUTCDATE()

	INSERT INTO [dbo].[stockpulse_wip_backup]
           ([UniqueId]
           ,[BackupDate]
           ,[Reg]
           ,[WIP Number]
           ,[Chassis]
           ,[Description]
           ,[Comment]
           ,[Reference]
           ,[Location Description]
           ,[Delete Flag]
           ,[Fingerprint]
           ,[Updated])
	SELECT 
           @UniqueId
           ,@BackupDate
           ,[Reg]
           ,[WIP Number]
           ,[Chassis]
           ,[Description]
           ,[Comment]
           ,[Reference]
           ,[Location Description]
           ,[Delete Flag]
           ,[Fingerprint]
           ,[Updated]
	FROM [dbo].stockpulse_wip

	--DELETE Older backups. Delete backups older than 15days.
	DELETE FROM [stockpulse_wip_backup] WHERE BackupDate < GETUTCDATE() - 15



UPDATE [dbo].stockpulse_wip
SET [Description] = REPLACE([Description], '  ', '')

UPDATE [dbo].stockpulse_wip
SET [Comment] = REPLACE([Comment], '  ', '')


INSERT INTO [dbo].[ReconcilingItems]
           ([ReconcilingItemTypeId]
           ,[Reg]
           ,[Vin]
           ,[Description]
           ,[Comment]
           ,[Reference]
           ,[SourceReportId]
           ,[StockCheckId])
     

SELECT 7, WIP.Reg, WIP.Chassis, WIP.Description, WIP.Comment, WIP.Reference, 1, ISNULL(SC.Id,SCForMap.Id)
FROM [dbo].stockpulse_wip AS WIP
LEFT JOIN [dbo].[Sites] AS S ON S.Description = WIP.[Location Description]
LEFT JOIN [dbo].[stockchecks] AS SC ON S.Id = SC.SiteId AND SC.IsActive = 1
LEFT JOIN [import].[SiteDescriptionDictionary] AS BN ON WIP.[Location Description] = BN.Description AND BN.IsPrimarySiteId = 1 AND BN.DealerGroupId = 1
LEFT JOIN [dbo].[Sites] AS SForMap ON SForMap.Id = BN.SiteId 
LEFT JOIN [dbo].[stockchecks] AS SCForMap ON SForMap.Id = SCForMap.SiteId AND SCForMap.IsActive = 1
WHERE ISNULL(S.Id,SForMap.Id) IS NOT NULL AND ISNULL(SC.Id,SCForMap.Id) IS NOT NULL

END


 

--############################## Next SP #############################################﻿
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
  
CREATE OR ALTER   PROC [dbo].[Datafactory_ReconciliationBucketsReport]  
@StockCheckId int  
AS  
BEGIN  
  
SET NOCOUNT ON  
  
DECLARE @UserId INT  
DECLARE @DMSStock INT  
DECLARE @OtherStocks INT  
DECLARE @StocksMatched INT  
DECLARE @OtherScanned INT  
DECLARE @TotalScanned INT  
DECLARE @WaterfallMatched bit  
  
--Get a user for this stockstock  
SET @UserId = (select top 1 U.Id from StockChecks sc inner join Sites as s on sc.SiteId = s.Id  
inner join Divisions as d on s.DivisionId = d.Id inner join Users as U on d.DealerGroupId = u.DealerGroupId  
where sc.IsActive = 1  
and sc.id = @StockCheckId )  
  
  
  
CREATE TABLE #ReconciliationBucketsData(  
Description nvarchar (250) not null  
,VehicleCount int not null
,InStockValue int null
,IsFullHeight int not null  
,[Order] int not null  
,IsProblem int not null  
,IsStock int not null  
,IsScan int not null  
,ReconciliationTypeId int null  
,ReconciliationState nvarchar (250) not null  
)   
   
   
--set identity_insert #ReconciliationBucketsData on  
insert into #ReconciliationBucketsData  
  (  
Description  
,VehicleCount
,InStockValue
,IsFullHeight   
,[Order]   
,IsProblem  
,IsStock  
,IsScan  
,ReconciliationtypeId
,ReconciliationState
  )  
exec [dbo].[GET_ReconciliationBuckets] @StockCheckId,@UserId  
--select * from #ReconciliationBucketsData  
  
SET @DMSStock = (SELECT ISNULL(VehicleCount,0) from #ReconciliationBucketsData WHERE Description = 'Stock')  
SET @OtherStocks = (SELECT SUM(ISNULL(VehicleCount,0)) from #ReconciliationBucketsData WHERE Description NOT IN ('Stock', 'In Stock and Scanned') AND IsStock = 1)  
SET @StocksMatched = (SELECT ISNULL(VehicleCount,0) from #ReconciliationBucketsData WHERE Description = 'In Stock and Scanned')  
SET @OtherScanned = (SELECT SUM(ISNULL(VehicleCount,0)) from #ReconciliationBucketsData WHERE Description NOT IN ('Scanned') AND IsStock = 0)  
SET @TotalScanned = (SELECT ISNULL(VehicleCount,0) from #ReconciliationBucketsData WHERE Description = 'Scanned')  
  
SET @DMSStock = ISNULL(@DMSStock,0)  
SET @OtherStocks = ISNULL(@OtherStocks, 0)  
SET @StocksMatched = ISNULL(@StocksMatched,0)  
SET @OtherScanned = ISNULL(@OtherScanned,0)  
SET @TotalScanned = ISNULL(@TotalScanned,0)  
  
  
  
SET @WaterfallMatched = 0  
  
IF ((@DMSStock - @OtherStocks) = @StocksMatched) AND ((@StocksMatched + @OtherScanned) = @TotalScanned)  
BEGIN   
 SET @WaterfallMatched = 1  
END  
   
  
  
  
insert into ReconciliationBucketsReport(  
 StockCheckId   
,DMSStock   
,OtherStocks   
,StocksMatched   
,OtherScanned  
,TotalScanned  
,WaterfallMatched  
,ReportDate   
)  
select @StockCheckId, @DMSStock, @OtherStocks, @StocksMatched, @OtherScanned, @TotalScanned, @WaterfallMatched, Convert(date,GetDATE())  
  
--select * from #ReconciliationBucketsReport  
DROP TABLE #ReconciliationBucketsData  
END  
  



--############################## Next SP #############################################﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].[Datafactory_RunReconciliationBucketsReport]  
  
AS  
BEGIN  
  
DECLARE @StockCheckId INT  
  
--Clear todays report data  
DELETE FROM [ReconciliationBucketsReport] WHERE ReportDate = Convert(date, GETDATE())  
  
   
DECLARE load_cursor CURSOR FOR   
    SELECT SC.Id FROM StockChecks AS SC   
 WHERE SC.IsActive = 1  
  
   
OPEN load_cursor   
FETCH NEXT FROM load_cursor INTO @StockCheckId  
   
WHILE @@FETCH_STATUS = 0   
BEGIN   
  
 EXEC [dbo].[Datafactory_ReconciliationBucketsReport] @StockCheckId  
  
   
    FETCH NEXT FROM load_cursor INTO @StockCheckId  
END   
   
CLOSE load_cursor   
DEALLOCATE load_cursor   
  
  
  
  
END

--############################## Next SP #############################################﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].[DataFactory_UpdateFinancialLines]

AS
BEGIN
    -- SET NOCOUNT ON added to prevent extra result sets from
    -- interfering with SELECT statements.
    SET NOCOUNT ON

    -- Insert statements for procedure here
    BEGIN TRAN



	--------------------------------------------------------------------------------------------------
	----Backup the data---

	DECLARE @UniqueID UNIQUEIDENTIFIER
	SET @UniqueID  = NEWID()

	DECLARE @BackupDate DateTime
	SET @BackupDate = GETUTCDATE()

	INSERT INTO [dbo].[stockpulse_dealership_totals_backup]
           ([UniqueId]
           ,[BackupDate]
           ,[location_name]
           ,[expense_code]
           ,[nl_balance]
           ,[fingerprint]
           ,[updated])
	SELECT  @UniqueId
           ,@BackupDate
           ,[location_name]
           ,[expense_code]
           ,[nl_balance]
           ,[fingerprint]
           ,[updated]
	FROM [stockpulse_dealership_totals]

	--DELETE Older backups. Delete backups older than 15days.
	DELETE FROM [stockpulse_dealership_totals_backup] WHERE BackupDate < GETUTCDATE() - 15
	--------------------------------------------------------------------------------------------------

	






MERGE FinancialLines AS TARGET
USING 
(SELECT DT.expense_code As Code, CONCAT(S.Description, ' ',DT.expense_code)  as AccountDescription, NULL As Notes, 0 As IsExplanation,DT.nl_balance As Balance, ISNULL(SC.Id,SCForMap.Id) AS StockCheckId
 FROM [dbo].[stockpulse_dealership_totals] As DT
 LEFT JOIN [dbo].[Sites] AS S ON S.Description = DT.location_name AND S.IsActive = 1
 LEFT JOIN [dbo].[stockchecks] AS SC ON S.Id = SC.SiteId AND SC.IsActive = 1
 LEFT JOIN [import].[SiteDescriptionDictionary] AS BN ON DT.location_name = BN.Description AND BN.IsPrimarySiteId = 1 AND BN.DealerGroupId = 1
 LEFT JOIN [dbo].[Sites] AS SForMap ON SForMap.Id = BN.SiteId 
 LEFT JOIN [dbo].[stockchecks] AS SCForMap ON SForMap.Id = SCForMap.SiteId AND SCForMap.IsActive = 1
 WHERE ISNULL(SC.Id,SCForMap.Id) IS NOT NULL) 
  
 AS SOURCE
 ON TARGET.Code = SOURCE.CODE AND TARGET.StockCheckId = SOURCE.StockCheckId 

 WHEN MATCHED AND TARGET.IsExplanation = 0
 THEN UPDATE SET TARGET.Balance = SOURCE.Balance

 WHEN NOT MATCHED BY TARGET 
 THEN INSERT ([Code]
           ,[AccountDescription]
           ,[Notes]
           ,[IsExplanation]
           ,[Balance]
           ,[StockCheckId]) VALUES 
		   (SOURCE.[Code]
           ,SOURCE.[AccountDescription]
           ,SOURCE.[Notes]
           ,SOURCE.[IsExplanation]
           ,SOURCE.[Balance]
           ,SOURCE.[StockCheckId])

WHEN NOT MATCHED BY SOURCE AND TARGET.StockCheckId IN (
SELECT SC.Id FROM StockChecks AS SC 
INNER JOIN sites as s on sc.SiteId = s.Id and s.IsActive = 1
INNER JOIN Divisions as d on s.DivisionId = d.Id
WHERE d.DealerGroupId = 1 AND SC.IsActive = 1
)
THEN DELETE


OUTPUT $action,
INSERTED.*,
DELETED.*;
--DELETED.Code AS Code, 
--DELETED.AccountDescription AS AccountDescription, 
--DELETED.IsExplanation AS IsExplanation, 
--DELETED.Balance AS Balance, 
--DELETED.StockCheckId AS StockCheckId, 
--INSERTED.Code AS Code, 
--INSERTED.AccountDescription AS AccountDescription, 
--INSERTED.IsExplanation AS IsExplanation, 
--INSERTED.Balance AS Balance, 
--INSERTED.StockCheckId AS StockCheckId;
--INTO @Outputs;



--INSERT INTO [stockpulse_dealership_totals_logs]()
--SELECT * FROM @outputs

SELECT @@ROWCOUNT;


--SELECT * FROM [stockpulse_dealership_totals_logs]

COMMIT TRAN


END
GO


--############################## Next SP #############################################﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

  CREATE OR ALTER PROCEDURE [dbo].[DataFactory_UpdateStocks]

AS
BEGIN
    -- SET NOCOUNT ON added to prevent extra result sets from
    -- interfering with SELECT statements.
    SET NOCOUNT ON

    -- Insert statements for procedure here
    BEGIN TRAN

	
	--------------------------------------------------------------------------------------------------
	----Backup the data---
	DECLARE @UniqueID UNIQUEIDENTIFIER
	SET @UniqueID  = NEWID()

	DECLARE @BackupDate DateTime
	SET @BackupDate = GETUTCDATE()

	INSERT INTO stockpulse_all_stock_backup (
			UniqueId
			,BackupDate
			,[Stockbook Number]
           ,[Registration Number]
           ,[Chassis]
           ,[Vehicle Description]
           ,[DIS]
           ,[DIG]
           ,[Branch]
           ,[Stock Type]
           ,[Comment]
           ,[Reference]
           ,[Stock Value]
           ,[Updated])
	SELECT @UniqueID, @BackupDate, [Stockbook Number]
           ,[Registration Number]
           ,[Chassis]
           ,[Vehicle Description]
           ,[DIS]
           ,[DIG]
           ,[Branch]
           ,[Stock Type]
           ,[Comment]
           ,[Reference]
           ,[Stock Value]
           ,[Updated]
	FROM [stockpulse_all_stock]

	--DELETE Older backups. Delete backups older than 15days.
	DELETE FROM stockpulse_all_stock_backup WHERE BackupDate < GETUTCDATE() - 15

	--------------------------------------------------------------------------------------------------
	




--	begin tran
--select top 0 *  into stockpulse_all_stock_20210406 from stockpulse_all_stock_20210405

--STEP 0.5 Now do the BCP thing

--STEP 1 FIRST FIX THEIR DATA QUALITY.  Pretty sure don't need this if input from their db




	UPDATE [stockpulse_all_stock]
	SET DIS = null
	WHERE DIS = 'NULL'

	UPDATE [stockpulse_all_stock]
	SET Chassis = null
	WHERE Chassis = 'NULL'

	UPDATE [stockpulse_all_stock]
	SET DIG = null
	WHERE DIG = 'NULL'

	UPDATE [stockpulse_all_stock]
	SET [Stock Value] = null
	WHERE [Stock Value] = 'NULL'

	UPDATE [stockpulse_all_stock]
	SET [Registration Number] = null
	WHERE [Registration Number] = 'NULL'

	UPDATE [stockpulse_all_stock]
	SET [Stock Value] = 0
	WHERE ISNUMERIC([Stock Value]) = 0

	UPDATE [stockpulse_all_stock]
	SET [Vehicle Description] = REPLACE([Vehicle Description], '  ', '')

	UPDATE [stockpulse_all_stock]
	SET  [Comment] = REPLACE([Comment], '  ', '')

	update [dbo].[stockpulse_all_stock] 
	SET Reference = '|'+Reference
	WHERE Reference not like '%|%'


--STEP 2 Now Import

COMMIT TRAN

BEGIN TRAN

 --drop table  #CurrentStockItems
 --drop table  #NewStockItems
 --drop table #SameItems

 DECLARE @TotalRecords INT;

 SET @TotalRecords = (SELECT Count(1) FROM [stockpulse_all_stock])

 IF (@TotalRecords > 15000)
 BEGIN 


	SELECT CONCAT(SI.StockCheckId,SUBSTRING(SI.Reference,0,IIF(CHARINDEX('|',SI.Reference,0) = 0,LEN(SI.Reference) +1,CHARINDEX('|',SI.Reference,0)))) As [UniqueId],
	SI.Id 
	INTO #CurrentStockItems 
	FROM StockItems AS SI
	INNER JOIN StockChecks AS SC ON SI.StockCheckId = SC.Id
	INNER JOIN sites as s on sc.SiteId = s.Id
	INNER JOIN Divisions as d on s.DivisionId = d.Id
	WHERE d.DealerGroupId = 1 AND SC.IsActive = 1

	SELECT
	CONCAT(ISNULL(SC.Id,SCForMap.Id),[Stockbook Number]) AS [UniqueId]
	INTO #NewStockItems
	FROM [dbo].[stockpulse_all_stock] As STK
	LEFT JOIN [dbo].[Sites] AS S ON S.Description = STK.Branch 
	LEFT JOIN [dbo].[stockchecks] AS SC ON S.Id = SC.SiteId AND SC.IsActive = 1
	LEFT JOIN [import].[SiteDescriptionDictionary] AS BN ON STK.Branch = BN.Description AND BN.IsPrimarySiteId = 1 AND BN.DealerGroupId = 1
	LEFT JOIN [dbo].[Sites] AS SForMap ON SForMap.Id = BN.SiteId
	LEFT JOIN [dbo].[stockchecks] AS SCForMap ON SForMap.Id = SCForMap.SiteId AND SCForMap.IsActive = 1
	WHERE ISNULL(SC.Id,SCForMap.Id) is NOT null
 
	--remove 
	--firstly remove any refs in scans to the stockitems we are about to remove
	UPDATE S
	SET S.stockitemid = null 
	FROM scans AS S
	INNER JOIN StockChecks AS SC ON S.StockCheckId = SC.Id
	INNER JOIN sites as si on sc.SiteId = si.Id
	INNER JOIN Divisions as d on si.DivisionId = d.Id
	WHERE d.DealerGroupId = 1
	AND SC.IsActive = 1
	AND stockitemid IN (SELECT Id FROM #CurrentStockItems WHERE UniqueId NOT IN (SELECT * FROM #NewStockItems))
	--now can remove the stockitems


	DELETE SI
	FROM StockItems AS SI
	INNER JOIN StockChecks AS SC ON SI.StockCheckId = SC.Id
	INNER JOIN sites as s on sc.SiteId = s.Id
	INNER JOIN Divisions as d on s.DivisionId = d.Id
	WHERE d.DealerGroupId = 1
	AND SC.IsActive = 1
	AND SI.IsAgencyStock = 0
	AND CONCAT(StockCheckId,SUBSTRING(Reference,0,IIF(CHARINDEX('|',Reference,0) = 0,LEN(Reference) +1,CHARINDEX('|',Reference,0)))) 
	IN (SELECT UniqueId FROM #CurrentStockItems WHERE UniqueId NOT IN (SELECT * FROM #NewStockItems))





	--insert new 

	--select * into StockitemsTEST from StockItems




	INSERT INTO Stockitems ([ScanId]
			   ,[ReconcilingItemId]
			   ,[MissingResolutionId]
			   ,[StockCheckId]
			   ,[SourceReportId]
			   ,[Reg]
			   ,[Vin]
			   ,[Description]
			   ,[DIS]
			   ,[GroupDIS]
			   ,[Branch]
			   ,[Comment]
			   ,[StockType]
			   ,[Reference]
			   ,[StockValue])
           


	--this gives us everything we need for the new items
	SELECT
	null,
	null,
	null,
	ISNULL(SC.Id,SCForMap.Id)
	, 1,[Registration Number] As Reg, Chassis As Vin
	, [Vehicle Description] As description,
	ISNULL(Dis,0),ISNULL(Dig,0),Branch,Comment, [Stock Type], 
	SUBSTRING(CONCAT([Stockbook Number],SUBSTRING(Reference,CHARINDEX('|',Reference,0),LEN(Reference) + 1)),0,50)  As reference -- limiting to 50 chars col limit
	, ISNULL([Stock Value],0)

	 FROM [dbo].[stockpulse_all_stock] As STK
	 LEFT JOIN [dbo].[Sites] AS S ON S.Description = STK.Branch AND S.IsActive = 1
	 LEFT JOIN [dbo].[stockchecks] AS SC ON S.Id = SC.SiteId  AND SC.IsActive = 1
	 LEFT JOIN [import].[SiteDescriptionDictionary] AS BN ON STK.Branch = BN.Description AND BN.IsPrimarySiteId = 1 AND BN.DealerGroupId = 1
	 LEFT JOIN [dbo].[Sites] AS SForMap ON SForMap.Id = BN.SiteId AND SForMap.IsActive = 1
	 LEFT JOIN [dbo].[stockchecks] AS SCForMap ON SForMap.Id = SCForMap.SiteId AND SCForMap.IsActive = 1
	 WHERE 
	 ISNULL(SC.Id,SCForMap.Id) is NOT null AND 
	 CONCAT(ISNULL(SC.Id,SCForMap.Id),[Stockbook Number]) IN 
	(SELECT UniqueId FROM #NewStockItems WHERE UniqueId NOT IN (SELECT UniqueId FROM #CurrentStockItems))
	--and [Stock Value] is null

	

	--Update

	--this gives us everything we need for the same items
	SELECT
	null As ScanId ,null AS ReconId ,null As Resid,
	ISNULL(SC.Id,SCForMap.Id) As UniqueId
	, 1 As ReportId,[Registration Number] As Reg, Chassis As Vin, [Vehicle Description] As description,
	ISNULL(Dis,0) AS DIS,ISNULL(Dig,0) AS DIG,Branch,Comment, [Stock Type], 
	CONCAT([Stockbook Number],SUBSTRING(Reference,CHARINDEX('|',Reference,0),LEN(Reference) +1))  As reference,
	[Stock Value]
	INTO #SameItems
	FROM [dbo].[stockpulse_all_stock] As STK
	LEFT JOIN [dbo].[Sites] AS S ON S.Description = STK.Branch 
	LEFT JOIN [dbo].[stockchecks] AS SC ON S.Id = SC.SiteId AND SC.IsActive = 1 
	LEFT JOIN [import].[SiteDescriptionDictionary] AS BN ON STK.Branch = BN.Description AND BN.IsPrimarySiteId = 1 AND BN.DealerGroupId = 1
	LEFT JOIN [dbo].[Sites] AS SForMap ON SForMap.Id = BN.SiteId
	LEFT JOIN [dbo].[stockchecks] AS SCForMap ON SForMap.Id = SCForMap.SiteId AND SCForMap.IsActive = 1
	WHERE ISNULL(SC.Id,SCForMap.Id) IS NOT null
 


	--Update the extisting items
	UPDATE SI
	SET SI.Reg = STk.Reg,
	SI.Vin = STK.Vin,
	SI.Description = STK.Description,
	SI.DIS = STK.DIS,
	SI.GroupDIS = STk.DIG,
	SI.Branch = STK.Branch,
	SI.Comment = STK.Comment,
	SI.StockType = STK.[Stock Type],
	SI.Reference =SUBSTRING(STK.reference,0,50),
	SI.StockValue = STK.[Stock Value]
	FROM StockItems As SI
	INNER JOIN #SameItems As STK ON CONCAT(SI.StockCheckId,SUBSTRING(SI.Reference,0,IIF(CHARINDEX('|',SI.Reference,0) = 0,LEN(SI.Reference) +1,CHARINDEX('|',SI.Reference,0)))) = CONCAT(UniqueId,SUBSTRING(STK.Reference,0,IIF(CHARINDEX('|',STK.Reference,0) = 0,LEN(STK.Reference) +1,CHARINDEX('|',STK.Reference,0))))
	INNER JOIN StockChecks AS SC ON SI.StockCheckId = SC.Id
	INNER JOIN sites as s on sc.SiteId = s.Id
	INNER JOIN Divisions as d on s.DivisionId = d.Id
	WHERE d.DealerGroupId = 1
	AND SC.IsActive = 1


END

COMMIT TRAN



END

GO

--############################## Next SP #############################################
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].[GET_ScansFromLatestStockchecksForSpark]
(
    @DealerGroupId INT
)
AS
BEGIN

SET NOCOUNT ON


;WITH LatestStockChecks AS (SELECT SC.SiteId, MAX(SC.[Date]) As [Date] FROM StockChecks AS SC WITH (NOLOCK)
INNER JOIN Sites AS SI WITH (NOLOCK) ON SI.Id=SC.SiteId
INNER JOIN Divisions AS D WITH (NOLOCK) ON SI.DivisionId=D.Id
INNER JOIN DealerGroup AS DG WITH (NOLOCK) ON D.DealerGroupId = DG.Id
WHERE DG.Id = @DealerGroupId AND StatusId > 1
GROUP BY SC.SiteId)


SELECT S.Id, S.Reg, S.Vin,loc.Description as Location,u.Name as ScannerName,S.ScanDateTime
FROM Scans AS S WITH (NOLOCK) 
INNER JOIN StockChecks AS SC WITH (NOLOCK) ON S.StockCheckId = SC.Id
INNER JOIN LatestStockChecks AS LSC ON SC.SiteId = LSC.SiteId AND SC.Date = LSC.Date
INNER JOIN Locations loc on loc.Id = S.LocationId
INNER JOIN Users u on u.Id = S.UserId

	

END

GO

--############################## Next SP #############################################﻿﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].[DELETE_AllAgencyStockItems]  
(  
    @StockCheckId INT = NULL,  
    @UserId INT = NULL
)  
AS  
BEGIN  
  
SET NOCOUNT ON;  

IF dbo.[AuthenticateUser](@UserId, @StockCheckId)  = 0
BEGIN
	RETURN SELECT 0
END

   
UPDATE Scans
SET StockItemId = NULL
WHERE StockItemId IN (
	SELECT Id 
	FROM StockItems   
	WHERE StockCheckId =  @StockCheckId  
	AND IsAgencyStock = 1
)


DELETE FROM StockItems   
WHERE StockCheckId =  @StockCheckId  
AND IsAgencyStock = 1
	
  
EXEC [dbo].[UPDATE_StockCheckLastUpdate] @StockCheckId, @UserId  
  
 
   
  
END  
GO
  

--############################## Next SP #############################################﻿﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].[DELETE_AllDMSStockItems]  
(  
    @StockCheckId INT = NULL,  
    @UserId INT = NULL
)  
AS  
BEGIN  
  
SET NOCOUNT ON;  

IF dbo.[AuthenticateUser](@UserId, @StockCheckId)  = 0
BEGIN
	RETURN SELECT 0
END

   
UPDATE Scans
SET StockItemId = NULL
WHERE StockItemId IN (
	SELECT Id 
	FROM StockItems   
	WHERE StockCheckId =  @StockCheckId
	AND IsAgencyStock = 0
)


DELETE FROM StockItems   
WHERE StockCheckId =  @StockCheckId  
AND IsAgencyStock = 0
	
  
EXEC [dbo].[UPDATE_StockCheckLastUpdate] @StockCheckId, @UserId  
  
 
   
  
END  
GO
  

--############################## Next SP #############################################﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].[DELETE_AllReconcilingItems]
(
    @StockCheckId INT = NULL,
    @UserId INT = NULL,
    @ReconcilingItemTypeId INT = NULL
)
AS
BEGIN

SET NOCOUNT ON;

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END

	
--Firstly have to remove any references to these rec items in stockItems and scans
UPDATE StockItems set ReconcilingItemId = null 
where ReconcilingItemId in 
	(select Id from ReconcilingItems where StockCheckId =  @StockCheckId AND ReconcilingItemTypeId = @ReconcilingItemTypeId)

UPDATE Scans set ReconcilingItemId = null 
where ReconcilingItemId in 
	(select Id from ReconcilingItems where StockCheckId =  @StockCheckId AND ReconcilingItemTypeId = @ReconcilingItemTypeId)

--Now can remove
DELETE FROM ReconcilingItems 
WHERE StockCheckId =  @StockCheckId
AND ReconcilingItemTypeId = @ReconcilingItemTypeId

EXEC [dbo].[UPDATE_StockCheckLastUpdate] @StockCheckId, @UserId


END

GO




--############################## Next SP #############################################﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


  
CREATE OR ALTER PROCEDURE [dbo].[DELETE_FinancialAllLines]
(  
    @StockCheckId INT = NULL,  
    @UserId INT = NULL
)  
AS  
BEGIN  
  
SET NOCOUNT ON;  
  
IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END

   
DELETE FROM [dbo].[FinancialLines]
WHERE StockCheckId = @StockCheckId


EXEC [dbo].[UPDATE_StockCheckLastUpdate] @StockCheckId, @UserId  
  
  
END  
  
GO




--############################## Next SP #############################################﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


  
CREATE OR ALTER PROCEDURE [dbo].[DELETE_FinancialLine]  
(  
    @StockCheckId INT = NULL,  
    @UserId INT = NULL,
	@FinancialLineId INT = NULL
    
)  
AS  
BEGIN  
  
SET NOCOUNT ON;  
  
IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END

   
DELETE FROM [dbo].[FinancialLines]
WHERE Id = @FinancialLineId


EXEC [dbo].[UPDATE_StockCheckLastUpdate] @StockCheckId, @UserId  
  
  
END  
  
GO




--############################## Next SP #############################################

CREATE OR ALTER PROCEDURE [dbo].[DELETE_ImportMask]
(
	@Id int
)
  
AS  
BEGIN  

SET NOCOUNT ON;  
  
DELETE FROM ImportMasks
WHERE Id = @Id

END
GO


--############################## Next SP #############################################﻿
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].[DELETE_MissingResolution]
(
    @StockCheckId INT,
	@UserId INT,
	@MissingResolutionId INT
)
AS
BEGIN

SET NOCOUNT ON;

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END
	
UPDATE StockItems 
SET MissingResolutionId = NULL
WHERE MissingResolutionId = @MissingResolutionId

DELETE [MissingResolutionImages]
WHERE MissingResolutionId = @MissingResolutionId

DELETE [MissingResolutions]
WHERE Id = @MissingResolutionId


EXEC [dbo].[UPDATE_StockCheckLastUpdate] @StockCheckId, @UserId


END
	
  
	


GO




--############################## Next SP #############################################﻿
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].[DELETE_MissingResolutionImage]
(
    @StockCheckId INT,
	@UserId INT,
	@MissingResolutionId INT,
	@ImageId INT
)
AS
BEGIN

SET NOCOUNT ON;

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END

DELETE [dbo].[MissingResolutionImages]
WHERE Id = @ImageId AND MissingResolutionId = @MissingResolutionId


EXEC [dbo].[UPDATE_StockCheckLastUpdate] @StockCheckId, @UserId


END


GO




--############################## Next SP #############################################﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].[DELETE_RecItems]
(
    @StockCheckId INT = NULL,
    @UserId INT = NULL,
    @ItemId INT = NULL
)
AS
BEGIN

SET NOCOUNT ON;

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END

	
DELETE FROM ReconcilingItems 
WHERE StockCheckId =  @StockCheckId
AND Id = @ItemId

EXEC [dbo].[UPDATE_StockCheckLastUpdate] @StockCheckId, @UserId


END

GO




--############################## Next SP #############################################SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].[DELETE_ReconcilingItemBackup]
(
    @StockCheckId INT,
	@UserId INT,
	@ReconcilingItemTypeId INT,
	@FileId INT
)
AS
BEGIN

SET NOCOUNT ON;

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END

DELETE [dbo].[ReconcilingItemBackups]
WHERE Id = @FileId AND ReconcilingItemTypeId = @ReconcilingItemTypeId

EXEC [dbo].[UPDATE_StockCheckLastUpdate] @StockCheckId, @UserId

END

GO


--############################## Next SP #############################################﻿
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO





CREATE OR ALTER PROCEDURE [dbo].[DELETE_Scan]
(
	@UserId INT,
	@ScanId INT
)
AS
BEGIN

SET NOCOUNT ON;

DECLARE @StockcheckId int;

set @StockCheckId = (SELECT StockCheckId FROM Scans WHERE Id = @ScanId)


IF (dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0 OR dbo.[CanSaveScans](@StockCheckId) = 0)
BEGIN 
	RETURN
END

DELETE FROM scans WHERE Id = @ScanId

EXEC [dbo].[UPDATE_StockCheckLastUpdate] @StockCheckId, @UserId



END
	
  
	


GO




--############################## Next SP #############################################

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE OR ALTER PROCEDURE [dbo].[DELETE_ScanLocationForSiteId]
(
    @SiteId INT = NULL,
	@LocationId INT = NULL
)
AS
BEGIN

SET NOCOUNT ON

DELETE FROM SiteLocations
WHERE
SiteId = @SiteId AND
LocationId = @LocationId

END

GO


--############################## Next SP #############################################

CREATE OR ALTER PROCEDURE [dbo].[DELETE_SiteNameLookup]
(
	@Id int,
	@UserId int
)
  
AS  
BEGIN  

SET NOCOUNT ON;

DECLARE @userDealerGroupId INT;

SET @userDealerGroupId = (SELECT DealerGroupId FROM Users WHERE Id = @UserId)
  
DELETE FROM import.SiteDescriptionDictionary
WHERE
Id = @Id AND
DealerGroupId = @userDealerGroupId

END
GO


--############################## Next SP #############################################﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO



CREATE OR ALTER PROCEDURE [dbo].[DELETE_StockCheck]
(
    @StockCheckIds varchar(max),
    @UserId INT
)
AS
BEGIN

SET NOCOUNT ON;


IF (dbo.[AuthenticateUserMultiStockChecks](@UserId, @StockCheckIds) = 0)
BEGIN 
    RETURN
END


DELETE StockChecks
WHERE Id IN (SELECT * FROM STRING_SPLIT(@StockCheckIds,','))

	
END

GO




--############################## Next SP #############################################  
  
GO  
CREATE OR ALTER PROCEDURE [dbo].[DELETE_StockItem]  
(  
    @StockCheckId INT = NULL,  
    @UserId INT = NULL,  
    @ItemId INT = NULL  
)  
AS  
BEGIN  
  
SET NOCOUNT ON;  
  
IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0  
BEGIN   
    RETURN  
END  
  
UPDATE Scans  
SET StockItemId = NULL  
WHERE  
StockItemId = @ItemId  


DELETE FROM StockItems   
WHERE StockCheckId =  @StockCheckId  
AND Id = @ItemId  
  
  
EXEC [dbo].[UPDATE_StockCheckLastUpdate] @StockCheckId, @UserId  
  
  
   
  
END  
  
SET ANSI_NULLS ON  
GO




--############################## Next SP #############################################﻿

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO




CREATE OR ALTER PROCEDURE [dbo].[DELETE_UnknownResolution]
(
    @StockCheckId INT,
	@UserId INT,
	@UnknownResolutionId INT
)
AS
BEGIN

SET NOCOUNT ON;

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END



UPDATE Scans 
SET UnknownResolutionId = NULL
WHERE UnknownResolutionId  = @UnknownResolutionId

DELETE [UnknownResolutionImages]
WHERE UnknownResolutionId = @UnknownResolutionId

DELETE [UnknownResolutions]
WHERE Id = @UnknownResolutionId

EXEC [dbo].[UPDATE_StockCheckLastUpdate] @StockCheckId, @UserId



END
	
  
	


GO




--############################## Next SP #############################################﻿
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO




CREATE OR ALTER PROCEDURE [dbo].[DELETE_UnknownResolutionImage]
(
    @StockCheckId INT,
	@UserId INT,
	@UnknownResolutionId INT,
	@ImageId INT
)
AS
BEGIN

SET NOCOUNT ON;

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END


DELETE [dbo].[UnknownResolutionImages]
WHERE Id = @ImageId AND UnknownResolutionId = @UnknownResolutionId

EXEC [dbo].[UPDATE_StockCheckLastUpdate] @StockCheckId, @UserId



END
	
  
	


GO




--############################## Next SP #############################################﻿/****** Object:  StoredProcedure [dbo].[GET_AgencyStock]    Script Date: 01/09/23 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO



CREATE OR ALTER PROCEDURE [dbo].[GET_AgencyStock]
(
    @StockCheckId INT = NULL,
	@UserId INT = NULL
)
AS
BEGIN

SET NOCOUNT ON;

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
	RETURN
END

	SELECT COUNT(Id)
	FROM [dbo].[StockItems] 
	WHERE StockCheckId = @StockCheckId 	
	AND IsAgencyStock = 1


END

GO





--############################## Next SP #############################################﻿
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE OR ALTER PROCEDURE [dbo].[GET_AllSites]
(
    @UserId INT = NULL
)
AS
BEGIN

SET NOCOUNT ON

SELECT S.[Id]
      ,S.[Description]
      ,[IsActive]
      ,[Longitude]
      ,[Latitude]
      ,[DivisionId]
      ,D.[Description] as Division
  FROM [dbo].[Sites] As S
  INNER JOIN [dbo].[Divisions] AS D ON S.DivisionId = D.Id
  INNER JOIN [dbo].[DealerGroup] AS DG ON D.DealerGroupId = DG.Id
  INNER JOIN [dbo].[Users] AS U ON U.DealerGroupId = DG.Id
  WHERE U.Id = @UserId

	

END

GO


--############################## Next SP #############################################﻿


CREATE OR ALTER PROCEDURE [dbo].[GET_AllSitesFromDictionary]
(
    @UserId INT = NULL
)
AS
BEGIN

SET NOCOUNT ON

SELECT UPPER(Description) AS Description, S.DealerGroupId, SiteId, S.Id, IsPrimarySiteId FROM [import].[SiteDescriptionDictionary] As S
INNER JOIN [dbo].[Users] AS U ON U.Id = @UserId
WHERE S.DealerGroupId = U.DealerGroupId AND S.IsPrimarySiteId = 1

END


GO

--############################## Next SP #############################################﻿


CREATE OR ALTER PROCEDURE [dbo].[GET_AllUsersAndLogins] (
	@UserId int
)
AS
BEGIN

SELECT
anu.Id as AppUserId,
anu.LinkedPersonId as Code,
u.Name as Name,
u.Name as NameShort,
anu.Username as UserName,
anr.Name as RoleName,
anu.Email as Email,
(SELECT STRING_AGG(SiteId, ',') FROM UserSites WHERE UserId = anu.LinkedPersonId) as Sites,
(SELECT TOP 1(SiteId) FROM UserSites WHERE UserId = anu.LinkedPersonId AND IsDefault = 1) as SiteCode,
u.EmployeeNumber,
CASE 
	WHEN DATEDIFF(DAY, GETUTCDATE(), anu.LockoutEnd) > 999 THEN 1
	ELSE 0
END as IsDeleted,
CASE 
	WHEN DATEDIFF(DAY, GETUTCDATE(), anu.LockoutEnd) <= 999 THEN 1
	ELSE 0
END as IsLocked

FROM AspNetUsers anu
INNER JOIN Users u on u.Id = anu.LinkedPersonId
INNER JOIN AspNetUserRoles anur on anur.UserId = anu.Id
INNER JOIN AspNetRoles anr on anr.Id = anur.RoleId
INNER JOIN Users cu on cu.Id = @UserId
WHERE
u.DealerGroupId = cu.DealerGroupId 

END
GO


--############################## Next SP #############################################﻿/****** Object:  StoredProcedure [dbo].[GET_AllUsersWithSites]    Script Date: 26/03/2021 14:02:29 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].[GET_AllUsersWithSites]
(
	@UserId INT = NULL
)
AS
BEGIN

SET NOCOUNT ON;


SELECT US.*, U.[Name] FROM UserSites AS US
INNER JOIN Users AS U ON US.UserId = U.Id
INNER JOIN Users AS CurrentUser ON U.DealerGroupId = CurrentUser.DealerGroupId
WHERE CurrentUser.Id = @UserId


END
GO




--############################## Next SP #############################################	
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].GET_AlreadyScannedCheck
(
	@UserId INT,
    @StockCheckId INT,
	@Reg varchar(10)
)
AS
BEGIN


IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END

SELECT TOP 1 
1 AS IsSeenBefore,
CONCAT(Reg, ' scanned by ',us.Name,' at ', loc.Description) AS SeenBeforeDetail,
sc.ScanDateTime AS SeenBeforeTime
FROM Scans sc
INNER JOIN Users us on us.id = sc.UserId
INNER JOIN Locations loc on loc.id = sc.LocationId
WHERE StockCheckId = @StockCheckId
AND Reg = @Reg

END

--############################## Next SP #############################################
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].GET_AlreadyScannedVinCheck
(
	@UserId INT,
    @StockCheckId INT,
	@Vin varchar(10)
)
AS
BEGIN


IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END

SELECT TOP 1 
1 AS IsSeenBefore,
CONCAT(Vin, ' scanned by ',us.Name,' at ', loc.Description) AS SeenBeforeDetail,
sc.ScanDateTime AS SeenBeforeTime
FROM Scans sc
INNER JOIN Users us on us.id = sc.UserId
INNER JOIN Locations loc on loc.id = sc.LocationId
WHERE StockCheckId = @StockCheckId
AND Vin = @Vin

END

--############################## Next SP #############################################/****** Object:  StoredProcedure [dbo].[DMSStocks]    Script Date: 22/3/21 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].GET_DMSStocks
(
    @StockCheckId INT = NULL,
    @UserId INT = NULL
)
AS
BEGIN

SET NOCOUNT ON;

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END


SELECT 
[Id]
    ,[ReconcilingItemId]
    ,[MissingResolutionId]
    ,StockItems.[Description]
    ,[DIS]
    ,[GroupDIS]
    ,[Branch]
    ,[StockType]
    ,[StockValue]
FROM [dbo].[StockItems] 
WHERE StockItems.StockCheckId = @StockCheckId 	

	

END

GO


--To use this run 
--exec [GET_DMSStocks] @StockCheckId = 1, @UserId = 104

--############################## Next SP #############################################

  
CREATE OR ALTER PROCEDURE [dbo].[GET_DuplicateScans]  
(  
    @StockCheckId INT = NULL,  
    @UserId INT = NULL  
)  
AS  
BEGIN  
  
SET NOCOUNT ON;  

DECLARE @accessVar INT;    
DECLARE @isRegional INT;    
DECLARE @isTotal INT;    
DECLARE @StockCheckDate DateTime;  
DECLARE @DivisionId INT = NULL;  
DECLARE @DealerGroupId INT = NULL;  
DECLARE @SCId INT = NULL;  
  
  
IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END
 
  
SELECT   
@isRegional = IsRegional,   
@isTotal = IsTotal,   
@StockCheckDate = Date   
FROM StockChecks    
WHERE StockChecks.Id = @StockCheckId  
    

  
    
IF @isRegional = 0 AND @isTotal = 0    
    
    BEGIN    
  
 SET @SCId = @StockCheckId;  
    
  
    END    
    
ELSE IF @isRegional = 1 AND @isTotal = 0    
    
    BEGIN    
    
    
    SET @DivisionId = (SELECT DivisionId FROM StockChecks    
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId    
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id    
                        WHERE StockChecks.Id = @StockCheckId AND StockChecks.IsRegional = 1)    
    
    END    
    
ELSE IF @isRegional = 0 AND @isTotal = 1    
    
    BEGIN    
    
        
    SET @DealerGroupId = (SELECT DealerGroupId FROM StockChecks    
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId    
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id    
                        WHERE StockChecks.Id = @StockCheckId)    
    END   
  

  --work out first instance of each scan
  SELECT
  sca.Reg,sca.Vin,
  MIN(sca.Id) as Id
  INTO #UniqueIds
  FROM Scans sca
  INNER JOIN StockChecks  SC ON SC.Id=Sca.StockCheckId 
  INNER JOIN Sites sit ON sit.Id=SC.SiteId  
  INNER JOIN Divisions  D ON D.Id=sit.DivisionId  
  INNER JOIN DealerGroup  DG ON D.DealerGroupId=DG.Id  
  WHERE SC.Id = ISNULL(@SCId, SC.Id)   
  AND SC.Date = @StockCheckDate  
  AND D.Id = ISNULL(@DivisionId, D.Id)  
  AND DG.Id = ISNULL(@DealerGroupId, DG.Id)  
  AND sca.IsDuplicate = 0
  GROUP BY sca.Reg,sca.Vin

  --find out everything about that first instance
  SELECT
  sca.Id, 
  sca.StockCheckId, 
  sca.Reg,
  sca.Vin,
  loc.Description as Location,
  usrs.Name as ScannedName,
  sca.ScanDateTime
  INTO #firstItems
  FROM Scans sca
  INNER JOIN Locations loc on loc.Id = sca.LocationId
  INNER JOIN Users usrs ON usrs.Id=sca.UserId   
  INNER JOIN #uniqueIds u on u.id = sca.id
  DROP TABLE #uniqueIds
  
  ;WITH allRecords AS (
        SELECT Scans.Id as ScanId ,
        Scans.[UserId]  ,
        scans.[Reg] as ScanReg ,
        scans.[IsDuplicate]  ,
        scans.[StockCheckId]  ,
        scans.[StockItemId]  ,
        scans.[ReconcilingItemId]  ,
        scans.[RegConfidence]  ,
        scans.[VinConfidence],
        scans.[ScanDateTime]  ,
        scans.[Vin]   as ScanVin,
        usrs.Name AS ScannerName  ,
        scans.[LocationId]  ,
        l.Description AS 'LocationDescription'  ,
        scans.[Comment] as ScanComment ,
        scans.[CoordinatesJSON]  ,
        Scans.[Description] as ScanDescription ,
        scans.[HasVinImage]  ,
        --scans.[IsEdited]  ,
        scans.[LastEditedById]  ,
        scans.[LastEditedDateTime]  ,
        Scans.[Latitude]  ,
        Scans.[Longitude]  ,
        Sites.Description AS SiteName  ,
		f.Id as OriginalScanId,
		f.Location as OriginalLocationDescription,
		f.ScannedName as OriginalScannedBy,
		f.ScanDateTime as OriginalScannedDate,
		ROW_NUMBER() OVER (Partition By Scans.Id Order By f.Id) AS RowNumber,
        Sites.Longitude as StockCheckLongitude,
        Sites.Latitude as StockCheckLatitude,
        CASE
            WHEN scans.IsRegEditedOnWeb = 1 THEN 'Web app'
            WHEN scans.IsRegEditedOnDevice = 1 THEN 'Mobile app'
            ELSE NULL
        END AS RegEditStatus,
        CASE
            WHEN scans.IsVinEditedOnWeb = 1 THEN 'Web app'
            WHEN scans.IsVinEditedOnDevice = 1 THEN 'Mobile app'
            ELSE NULL
        END AS VinEditStatus


        FROM [dbo].[Scans] scans  
        INNER JOIN Users usrs ON usrs.Id=Scans.UserId  
        INNER JOIN Locations l ON l.Id=Scans.LocationId  
        INNER JOIN StockChecks AS SC ON SC.Id=Scans.StockCheckId  
        INNER JOIN Sites ON Sites.Id=SC.SiteId  
		INNER JOIN Divisions AS D ON D.Id=Sites.DivisionId  
		INNER JOIN DealerGroup AS DG ON D.DealerGroupId=DG.Id  
		LEFT JOIN #firstItems f on (
		(f.Reg <> '' AND f.Reg = scans.Reg) OR 
		(f.Vin <> '' AND f.Vin = scans.Vin)
		) AND f.StockCheckId = scans.StockCheckId
        WHERE   
        Scans.IsDuplicate = 1  
		AND SC.Id = ISNULL(@SCId, SC.Id)  
		AND SC.Date = @StockCheckDate  
		AND D.Id = ISNULL(@DivisionId, D.Id)  
		AND DG.Id = ISNULL(@DealerGroupId, DG.Id)  
	)

	SELECT * FROM allRecords where RowNumber = 1
     
DROP TABLE #firstItems
  
END  


GO




--############################## Next SP #############################################﻿
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].[GET_FinancialLines]  
(  
    @StockCheckId INT = NULL,  
    @UserId INT = NULL  
)  
AS  
BEGIN  
  
SET NOCOUNT ON;  
  
IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END

  
DECLARE @isRegional INT;  
SET @isRegional = (SELECT IsRegional FROM StockChecks  
                    WHERE StockChecks.Id = @StockCheckId)  
  
DECLARE @isTotal INT;  
SET @isTotal = (SELECT IsTotal FROM StockChecks  
                    WHERE StockChecks.Id = @StockCheckId)  

 DECLARE @StockCheckDate Date;
  SET @StockCheckDate = (SELECT Date FROM StockChecks WHERE StockChecks.Id = @StockCheckId)
  
IF @isRegional = 0 AND @isTotal = 0  
  
    BEGIN  
   
    SELECT fl.Id,
		fl.[Code] AS AccountCode  
        ,fl.[AccountDescription] AS Description  
        ,fl.[Notes]  
        ,fl.[IsExplanation] AS IsReconcilingAdj  
        ,fl.[Balance] AS Balance
        ,fi.FileName
        ,fi.FileDate
        ,fi.LoadDate
	    ,u.Name As UserName
    FROM [dbo].[FinancialLines] fl
    LEFT JOIN [import].[FileImports] fi ON fi.Id = fl.FileImportId
    LEFT JOIN Users u ON u.Id = fi.LoadedByUserId
    WHERE fl.StockCheckId = @StockCheckId    
  
    END  
  
IF @isRegional = 1 AND @isTotal = 0  
  
    BEGIN  
  
    DECLARE @DivisionId INT;  
  
    SET @DivisionId = (SELECT DivisionId FROM StockChecks
					    INNER JOIN Sites ON Sites.Id=StockChecks.SiteId  
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id  
                        WHERE StockChecks.Id = @StockCheckId AND StockChecks.IsRegional = 1)  
  
    SELECT [Code] AS AccountCode  
    ,[AccountDescription] AS Description  
    ,[Notes]  
    ,[IsExplanation] AS IsReconcilingAdj  
    ,sum([Balance]) AS Balance 
    ,fi.FileName
    ,fi.FileDate
    ,fi.LoadDate
	,u.Name As UserName
    FROM [dbo].[FinancialLines]  
    INNER JOIN StockChecks ON StockChecks.Id=FinancialLines.StockCheckId  
    INNER JOIN Sites ON Sites.Id=StockChecks.SiteId  
    INNER JOIN Divisions ON Divisions.Id=Sites.DivisionId  
    LEFT JOIN [import].[FileImports] fi ON fi.Id = FinancialLines.FileImportId
    LEFT JOIN Users u ON u.Id = fi.LoadedByUserId
    WHERE Divisions.Id = @DivisionId AND StockChecks.Date = @StockCheckDate
    GROUP BY [Code],[AccountDescription],[Notes],[IsExplanation],fi.FileName,fi.FileDate,fi.LoadDate,u.Name
  
    END  
  
IF @isRegional = 0 AND @isTotal = 1  
  
    DECLARE @DealerGroupId INT;  
  
    SET @DealerGroupId = (SELECT DealerGroupId FROM StockChecks  
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId  
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id  
                        WHERE StockChecks.Id = @StockCheckId)  
  
    SELECT [Code] AS AccountCode  
    ,[AccountDescription] AS Description  
    ,[Notes]  
    ,[IsExplanation] AS IsReconcilingAdj  
    ,sum([Balance]) AS Balance 
    ,fi.FileName
    ,fi.FileDate
    ,fi.LoadDate
	,u.Name As UserName
    FROM [dbo].[FinancialLines]  
    INNER JOIN StockChecks ON StockChecks.Id=FinancialLines.StockCheckId  
    INNER JOIN Sites ON Sites.Id=StockChecks.SiteId  
    INNER JOIN Divisions ON Divisions.Id=Sites.DivisionId  
    INNER JOIN DealerGroup ON Divisions.DealerGroupId=DealerGroup.Id  
    LEFT JOIN [import].[FileImports] fi ON fi.Id = FinancialLines.FileImportId
    LEFT JOIN Users u ON u.Id = fi.LoadedByUserId
    WHERE DealerGroup.Id = @DealerGroupId  AND StockChecks.Date = @StockCheckDate
	GROUP BY [Code],[AccountDescription],[Notes],[IsExplanation],fi.FileName,fi.FileDate,fi.LoadDate,u.Name
   
END  
  
Go




--############################## Next SP #############################################﻿/****** Object:  StoredProcedure [dbo].[GET_GlobalParams]    Script Date: 10/3/21 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].GET_GlobalParams
(
    @UserId INT = NULL
)
AS
BEGIN

SET NOCOUNT ON

SELECT Id,DealerGroupId,Name,StringValue,BoolValue,DateValue,NumberValue 
FROM GlobalParams 
WHERE DealerGroupId = (select DealergroupId from Users where Id = @UserId)
	
	

END

GO




--To use this run 
--exec [GET_GlobalParams] @UserId = 1

--############################## Next SP #############################################﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO




CREATE OR ALTER PROCEDURE [dbo].[GET_GlobalSearchResults]
(
--@StockCheckId INT = NULL,
    @UserId INT = NULL,
	@Reg nvarchar(10) ,
	@Vin nvarchar(10) ,
	@RequireAndMatch bit
)
AS
BEGIN


DECLARE @DealerGroupId INT;
SET @DealerGroupId = (SELECT DealerGroupId FROM Users WHERE Id = @UserId)

IF @Reg = '' AND @Vin = '' 
BEGIN 
	SELECT NULL
END

ELSE

	BEGIN

	--Find Scans matching
	SELECT 
	sca.Id As ScanId, 
	sca.StockItemId, 
	si.Description As StockCheckSiteName, 
	SC.Date As StockCheckDate
	FROM Scans AS sca
	INNER JOIN StockChecks AS SC ON sca.StockCheckId = SC.Id
	INNER JOIN Sites AS si ON si.Id = SC.SiteId
	INNER JOIN Users AS U ON sca.UserId = U.Id
	INNER JOIN Locations AS L ON sca.LocationId = L.Id
	WHERE 
	(
		(
			@RequireAndMatch = 1 AND
			(sca.Reg <> '' AND sca.Reg IS NOT NULL AND sca.Reg = @Reg ) AND
				(sca.Vin <> '' AND sca.Vin IS NOT NULL AND sca.Vin = @Vin )
		)
		OR
		(
			@RequireAndMatch = 0 AND
			(
				(sca.Reg <> '' AND sca.Reg IS NOT NULL AND sca.Reg = @Reg ) OR
				(sca.Vin <> '' AND sca.Vin IS NOT NULL AND sca.Vin = @Vin )
			)
		)
	)

	AND sca.StockItemId IS NULL --to prevent us finding same item twice, as the query below will find this
	AND U.DealerGroupId = @DealerGroupId


	--Find StockItems matching
	SELECT 
	SI.Id As StockItemId, 
	SI.ScanId, 
	ST.Description As StockCheckSiteName, 
	SC.Date As StockCheckDate
	FROM StockItems AS SI
	INNER JOIN StockChecks AS SC ON SI.StockCheckId = SC.Id
	INNER JOIN Sites AS ST ON ST.Id = SC.SiteId
	INNER JOIN Divisions AS D ON ST.DivisionId = D.Id
	INNER JOIN DealerGroup AS DG ON d.DealerGroupId = DG.Id
	WHERE 
	(
		(
			@RequireAndMatch = 1 AND
			(si.Reg <> '' AND si.Reg IS NOT NULL AND si.Reg = @Reg ) AND
				(si.Vin <> '' AND si.Vin IS NOT NULL AND si.Vin = @Vin )
		)
		OR
		(
			@RequireAndMatch = 0 AND
			(
				(si.Reg <> '' AND si.Reg IS NOT NULL AND si.Reg = @Reg ) OR
				(si.Vin <> '' AND si.Vin IS NOT NULL AND si.Vin = @Vin )
			)
		)
	)
	
	AND DG.Id = @DealerGroupId

	END

END

GO




--############################## Next SP #############################################﻿
/****** Object:  StoredProcedure [dbo].[GET_ImportMasks]    Script Date: 15/06/2021 12:32:44 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].[GET_ImportMasks]
(
    @UserId INT = NULL
)
AS
BEGIN

SET NOCOUNT ON

DECLARE @DealGroupId INT;
SET @DealGroupId = (select DealerGroupId from Users where Id = @UserId)

	
SELECT IM.[Id]
      ,IM.[Name]
      ,IM.[TopRowsToSkip]
      ,IM.[ColumnValueEqualsesJSON]
      ,IM.[ColumnValueDifferentFromsJSON]
      ,IM.[ColumnValueNotNullsJSON]
      ,IM.[ColumnsWeWantJSON]
      ,IM.[UserId]
      ,U.Name AS CreatedBy
      ,IM.[IsStandard]
	  ,IM.[IsMultiSite]
      ,IM.[IgnoreZeroValues]
  FROM [dbo].[ImportMasks] AS IM
  INNER JOIN Users AS U ON IM.UserId = U.Id
  WHERE U.DealerGroupId = @DealGroupId

	

END

GO




--############################## Next SP #############################################/****** Object:  StoredProcedure [dbo].[GET_Last4StockCheckIds]    Script Date: 22/3/21 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].GET_Last4StockCheckIds
(
    @StockCheckId INT = NULL,
    @UserId INT = NULL
)	
AS
BEGIN

SET NOCOUNT ON;

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END

SELECT TOP 4 Id 
FROM StockChecks 
where SiteId =  (SELECT siteId FROM StockChecks WHERE Id = @StockCheckId) 
AND Id < @StockCheckId
ORDER BY Id Desc


END

GO



--To use this run 
-- exec [GET_Last4StockCheckIds] @StockCheckId = 99, UserId = 104

--############################## Next SP #############################################﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].[GET_LatestDataRecievedDate]
(
    @dealerGroupId INT
)
AS
BEGIN

SET NOCOUNT ON

    SELECT 
        MIN(fi.FileDate)
    FROM 
        input.StockItems si
    INNER JOIN 
        import.FileImports fi 
    ON 
        fi.Id = si.FileImportId
    WHERE 
        si.DealerGroupId = @dealerGroupId;

END

GO

--To use this run 
--EXEC [GET_LatestDataRecievedDate] @dealerGroupId = 11

--############################## Next SP #############################################﻿/****** Object:  StoredProcedure [dbo].[GET_LocationsForStockCheck]    Script Date: 11/3/21 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].GET_LocationsForStockCheck
(
    @StockCheckId int null,
	@UserId int null
)
AS
BEGIN

SET NOCOUNT ON

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END

	
SELECT L.[Id]      ,L.[Description]
FROM [dbo].Locations L
inner join SiteLocations SL on SL.LocationId = L.Id
inner join Sites S on S.Id = SL.SiteId
WHERE SiteId = (select SiteId from StockChecks where Id =  @StockCheckId)

END
	



GO



--To use this run 
--exec [GET_LocationsForStockCheck] @StockCheckId = 99, @UserId =1

--############################## Next SP #############################################﻿/****** Object:  StoredProcedure [dbo].[GET_LongAndLat]    Script Date: 24/03/21 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].GET_LongAndLat
(
    @StockCheckId INT = NULL,
    @UserId INT = NULL
)
AS
BEGIN

SET NOCOUNT ON;

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END


SELECT Longitude, Latitude 
FROM StockChecks
INNER JOIN Sites ON StockChecks.SiteId=Sites.Id
WHERE StockChecks.Id = @StockCheckId



END

GO




--To use this run 
--exec [GET_LongAndLat] @StockCheckId = 99, @UserId=104

--############################## Next SP #############################################﻿/****** Object:  StoredProcedure [dbo].[MissingMatched]    Script Date: 01/04/21 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].GET_MissingMatched
(
   	@UserId INT = NULL,
    @StockCheckId INT = NULL	
)
AS
BEGIN

SET NOCOUNT ON;

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END


DECLARE @isRegional INT;
SET @isRegional = (SELECT IsRegional FROM StockChecks
                    WHERE StockChecks.Id = @StockCheckId)

DECLARE @isTotal INT;
SET @isTotal = (SELECT IsTotal FROM StockChecks
                    WHERE StockChecks.Id = @StockCheckId)


IF @isRegional = 0 AND @isTotal = 0
   
    BEGIN
	
    SELECT [Id]
          ,[ReconcilingItemId]
          ,[MissingResolutionId]
          ,StockItems.[Description]
          ,[DIS]
          ,[GroupDIS]
          ,[Branch]
          ,[StockType]
          ,[StockValue]
    FROM [dbo].[StockItems] 
    WHERE StockItems.StockCheckId = @StockCheckId 	

    END
	
IF @isRegional = 1 AND @isTotal = 0

    BEGIN

    DECLARE @DivisionId INT;

    SET @DivisionId = (SELECT DivisionId FROM StockChecks
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id
                        WHERE StockChecks.Id = @StockCheckId AND StockChecks.IsRegional = 1)
	
    SELECT StockItems.[Id]
          ,[ReconcilingItemId]
          ,[MissingResolutionId]
          ,StockItems.[Description]
          ,[DIS]
          ,[GroupDIS]
          ,[Branch]
          ,[StockType]
          ,[StockValue]
    FROM [dbo].[StockItems]
    INNER JOIN StockChecks ON StockChecks.Id=StockItems.StockCheckId
    INNER JOIN Sites ON Sites.Id=StockChecks.SiteId
    INNER JOIN Divisions ON Divisions.Id=Sites.DivisionId
    WHERE Divisions.Id = @DivisionId	

    END

IF @isRegional = 0 AND @isTotal = 1

    BEGIN 

    DECLARE @DealerGroupId INT;
    
    SET @DealerGroupId = (SELECT DealerGroupId FROM StockChecks
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id
                        WHERE StockChecks.Id = @StockCheckId)

    SELECT StockItems.[Id]
          ,[ReconcilingItemId]
          ,[MissingResolutionId]
          ,StockItems.[Description]
          ,[DIS]
          ,[GroupDIS]
          ,[Branch]
          ,[StockType]
          ,[StockValue]
    FROM [dbo].[StockItems]
    INNER JOIN StockChecks ON StockChecks.Id=StockItems.StockCheckId
    INNER JOIN Sites ON Sites.Id=StockChecks.SiteId
    INNER JOIN Divisions ON Divisions.Id=Sites.DivisionId
    INNER JOIN DealerGroup ON Divisions.DealerGroupId=DealerGroup.Id
    WHERE DealerGroup.Id = @DealerGroupId

    END

END

GO



-- To use this run 
--exec [GET_MissingMatched] @StockCheckId = 1, @UserId = 104

--############################## Next SP #############################################SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].GET_MissingResolutionImageIds
(
    @ResolutionId INT = NULL
)
AS
BEGIN

SET NOCOUNT ON;

SELECT
Id
FROM MissingResolutionImages
WHERE MissingResolutionId = @ResolutionId


END

GO



--To use this run 
--exec [GET_MissingVehicles] @StockCheckId = 1, @UserId = 104

--############################## Next SP #############################################/****** Object:  StoredProcedure [dbo].[MissingVehicles]    Script Date: 22/3/21 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].GET_MissingVehicles
(
    @StockCheckId INT = NULL,
    @UserId INT = NULL
)
AS
BEGIN

SET NOCOUNT ON;

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END


SELECT [Id]
    ,[ReconcilingItemId]
    ,[MissingResolutionId]
    ,StockItems.[Description]
    ,[DIS]
    ,[GroupDIS]
    ,[Branch]
    ,[StockType]
    ,[StockValue]
FROM [dbo].[StockItems] 
WHERE StockItems.StockCheckId = @StockCheckId 	


END

GO



--To use this run 
--exec [GET_MissingVehicles] @StockCheckId = 1, @UserId = 104

--############################## Next SP #############################################﻿
  
CREATE OR ALTER PROCEDURE [admin].[GET_MorningEndToEndUK]  
AS   
BEGIN  
SET NOCOUNT ON  
 
    CREATE TABLE #ExpectedJobs(  
    OrderBy decimal(10,5),  
    WhenShouldRun varchar(10),   
    [Job] [nvarchar](50) NULL,
    DealerGroupId int
    )


    CREATE TABLE #TempJobsHolder(  
    OrderBy decimal(10,5),  
    WhenShouldRun varchar(10),   
    [Job] [nvarchar](50) NULL  
    )


    DECLARE @mmg_Jobs varchar(100);
    SET @mmg_Jobs = '11';  -- MMG 11

    DECLARE @lithiaUK_Jobs varchar(100);
    SET @lithiaUK_Jobs = '7';  -- LithiaUK 7

    -- MMG
    INSERT INTO #TempJobsHolder (OrderBy,WhenShouldRun,Job) 
    VALUES
    (1,DATEADD(HOUR, -24, GETDATE()),'MMGStockOnLoans'), 
    (2,DATEADD(HOUR, -24, GETDATE()),'MMGWIP'),
    (3,DATEADD(HOUR, -24, GETDATE()),'MMGStockAtAuction'),  
    (4,DATEADD(HOUR, -24, GETDATE()),'MMGStock'),  
    (5,DATEADD(HOUR, -24, GETDATE()),'MMGTB');


    INSERT INTO #ExpectedJobs (OrderBy, WhenShouldRun, Job, DealerGroupId)
    SELECT 
        t.OrderBy + ROW_NUMBER() OVER (ORDER BY dg.Id) AS OrderBy,
        t.WhenShouldRun AS WhenShouldRun, 
        t.Job AS Job,
        dg.Id AS DealerGroupId
    FROM DealerGroup dg
    LEFT JOIN #TempJobsHolder t ON 1 = 1
    WHERE dg.Id IN (SELECT value FROM STRING_SPLIT(@mmg_Jobs, ','));

    --- Clear for Lithia
    DELETE FROM #TempJobsHolder WHERE 1=1;

    -- Lithia UK (Jardine)
    INSERT INTO #TempJobsHolder (OrderBy,WhenShouldRun,Job) 
    VALUES
    (6,DATEADD(HOUR, -24, GETDATE()),'JardineWIPs'),  
    (7,DATEADD(HOUR, -24, GETDATE()),'JardineStocks'),
    (8,DATEADD(HOUR, -24, GETDATE()),'JardineTB');

    INSERT INTO #ExpectedJobs (OrderBy,WhenShouldRun,Job,DealerGroupId) 
    (SELECT 
        t.OrderBy + ROW_NUMBER() Over(order by dg.Id) as OrderBy,
        t.WhenShouldRun as WhenShouldRun, 
        t.Job as JobName,
        dg.Id AS DealerGroupId
        FROM DealerGroup dg
        LEFT JOIN #TempJobsHolder t on 1=1
        WHERE dg.Id IN ( SELECT value from STRING_SPLIT(@lithiaUK_Jobs,','))
    );

    --- Get the latest log messages
    WITH RecentJobs AS  
    (  
        SELECT LM.*,  
                ROW_NUMBER() OVER (PARTITION BY LM.Job ORDER BY LM.SourceDate DESC) AS rn  
        FROM LogMessages LM LEFT JOIN DealerGroup DG ON LM.DealerGroup_Id = DG.Id
        WHERE finishDate > dateadd(hour,16,DATEDIFF(d,0,GETDATE()-1))   
    ),
    
	-- Rank them to prevent duplicate entries in email
	RankedResults AS (
		SELECT 
			CONCAT(dg.Description, ':', e.Job) as Job,  
			IIF(lm.Job IS NULL, 'Not Found', IIF(lm.ErrorCount > 0, 'Errors', 'Ok')) as Status,
			ROW_NUMBER() OVER (PARTITION BY CONCAT(dg.Description, ':', e.Job) ORDER BY e.OrderBy ASC) as RowNum
		FROM #ExpectedJobs e  
		LEFT JOIN RecentJobs lm ON lm.Job = e.Job
		LEFT JOIN DealerGroup dg ON e.DealerGroupId = dg.Id
	)

	SELECT 
		Job,
		Status
	INTO #Results
	FROM RankedResults
	WHERE RowNum = 1
	ORDER BY Job;

    SELECT STRING_AGG(Job,', ') as FailedList, 'Stockpulse UK - Morning end to end summary' as emailSubject FROM #Results where Status <> 'Ok' 

    DROP TABLE #ExpectedJobs  
    DROP TABLE #TempJobsHolder
	DROP TABLE #Results

END  
  
GO

--SELECT TOP 10 * FROM LogMessages
--SELECT * FROM DealerGroup

--############################## Next SP #############################################﻿
  
CREATE OR ALTER PROCEDURE [admin].[GET_MorningEndToEndUS]  
AS   
BEGIN  
SET NOCOUNT ON  
 
    CREATE TABLE #ExpectedJobs(  
    OrderBy decimal(10,5),  
    WhenShouldRun varchar(10),   
    [Job] [nvarchar](50) NULL,
    DealerGroupId int
    )


    CREATE TABLE #TempJobsHolder(  
    OrderBy decimal(10,5),  
    WhenShouldRun varchar(10),   
    [Job] [nvarchar](50) NULL  
    )


    DECLARE @lithia_Jobs varchar(100);
    SET @lithia_Jobs = '10';  -- LithiaUK/MMG 7/11

    --LocalBargain_Job
    INSERT INTO #TempJobsHolder (OrderBy,WhenShouldRun,Job) 
    VALUES
    (1,DATEADD(HOUR, -24, GETDATE()),'LithiaBookedAndPendingJob'), 
    (2,DATEADD(HOUR, -24, GETDATE()),'LithiaTB'),  
    (3,DATEADD(HOUR, -24, GETDATE()),'LithiaWIP'),
    (4,DATEADD(HOUR, -24, GETDATE()),'LithiaStockItems - New'),
    (5,DATEADD(HOUR, -24, GETDATE()),'LithiaStockItems - Used'),
    (6,DATEADD(HOUR, -24, GETDATE()),'LithiaStockItems - Program'),
    (7,DATEADD(HOUR, -24, GETDATE()),'LithiaStockItems - Rental'),
    (8,DATEADD(HOUR, -24, GETDATE()),'LithiaStockItems - Loaner');

    INSERT INTO #ExpectedJobs (OrderBy, WhenShouldRun, Job, DealerGroupId)
    SELECT 
        t.OrderBy + ROW_NUMBER() OVER (ORDER BY dg.Id) AS OrderBy,
        t.WhenShouldRun AS WhenShouldRun, 
        t.Job AS Job,
        dg.Id AS DealerGroupId
    FROM DealerGroup dg
    LEFT JOIN #TempJobsHolder t ON 1 = 1
    WHERE dg.Id IN (SELECT value FROM STRING_SPLIT(@lithia_Jobs, ','));

    WITH RecentJobs AS  
    (  
        SELECT LM.*,  
                ROW_NUMBER() OVER (PARTITION BY LM.Job ORDER BY LM.SourceDate DESC) AS rn  
        FROM LogMessages LM
        WHERE finishDate > dateadd(hour,9,DATEDIFF(d,0,getdate()-1))   
    ),
    
	-- Rank them to prevent duplicate entries in email
	RankedResults AS (
		SELECT 
			CONCAT(dg.Description, ':', e.Job) as Job,  
			IIF(lm.Job IS NULL, 'Not Found', IIF(lm.ErrorCount > 0, 'Errors', 'Ok')) as Status,
			ROW_NUMBER() OVER (PARTITION BY CONCAT(dg.Description, ':', e.Job) ORDER BY e.OrderBy ASC) as RowNum
		FROM #ExpectedJobs e  
		LEFT JOIN RecentJobs lm ON lm.Job = e.Job
		LEFT JOIN DealerGroup dg ON e.DealerGroupId = dg.Id
	)

    SELECT 
    Job,
    Status
	INTO #Results
	FROM RankedResults
	WHERE RowNum = 1
	ORDER BY Job;

    SELECT STRING_AGG(Job,', ') AS FailedList, 'Stockpulse US - Morning end to end summary' AS emailSubject FROM #Results where Status <> 'Ok' 

    DROP TABLE #ExpectedJobs  
    DROP TABLE #Results  
    DROP TABLE #TempJobsHolder

END  
  
GO

--SELECT TOP 10 * FROM LogMessages
--SELECT * FROM DealerGroup

--############################## Next SP #############################################﻿
  
CREATE OR ALTER PROCEDURE [dbo].[GET_ReconciliationBuckets]    
(    
    @StockCheckId INT = NULL,    
    @UserId INT = NULL    
)    
AS    
BEGIN    
    
SET NOCOUNT ON;    
    
IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0  
BEGIN   
    RETURN  
END  

DECLARE @DealerGroupId INT;  
    
DECLARE @isRegional INT;    
SET @isRegional = (SELECT IsRegional FROM StockChecks    
                    WHERE StockChecks.Id = @StockCheckId)    
    
DECLARE @isTotal INT;    
SET @isTotal = (SELECT IsTotal FROM StockChecks    
                    WHERE StockChecks.Id = @StockCheckId)    
    
DECLARE @StockCheckDate DateTime;    
SET @StockCheckDate = (SELECT Date FROM StockChecks    
                    WHERE StockChecks.Id = @StockCheckId)    
   
   
---------------------  
 --IF SINGLE SITE  
 ---------------------  
IF @isRegional = 0 AND @isTotal = 0    
     
    BEGIN     
    
       SELECT 'Stock' as 'Description',     
       (  
            SELECT COUNT(Id)   
            FROM StockItems   
            WHERE StockCheckId = @StockCheckId  
       )  as 'VehicleCount',   
       (  
            SELECT SUM(StockValue)   
            FROM StockItems   
            WHERE StockCheckId = @StockCheckId  
       )  as 'InStockValue',   
	  (  
            SELECT SUM(Flooring)   
            FROM StockItems   
            WHERE StockCheckId = @StockCheckId  
       )  as 'Flooring', 
       1 as 'IsFullHeight',   
       0 as 'Order',  
       0 as 'IsProblem',  
       1 as 'IsStock',  
       0 as 'IsScan',  
       null as 'ReconciliationTypeId'    ,
       'AllItems' as ReconciliationState
    
 UNION ALL    
  
    SELECT 'Duplicate stock record' as 'Description',     
    (  
        SELECT COUNT(si.Id)   
        FROM StockItems AS si    
        WHERE si.StockCheckId = @StockCheckId   
        AND IsDuplicate = 1  
    )  as 'VehicleCount',   
     (  
        SELECT SUM(si.StockValue)   
        FROM StockItems AS si    
        WHERE si.StockCheckId = @StockCheckId   
        AND IsDuplicate = 1  
    )  as 'InStockValue',  
	(  
        SELECT SUM(Flooring)   
        FROM StockItems   
        WHERE StockCheckId = @StockCheckId  
    )  as 'Flooring', 
    0 as 'IsFullHeight',   
    0 as 'Order',  
    0 as 'IsProblem',  
    1 as 'IsStock',  
    0 as 'IsScan',  
    null as 'ReconciliationTypeId'  ,
    'Duplicate' as ReconciliationState
    
    
 UNION ALL  
   
    SELECT * from   
    (  
      SELECT RecTypes.Description as 'Description',     
      COUNT(si.Id) as 'VehicleCount',   
      SUM(si.StockValue) as 'InStockValue',  
	  SUM(si.Flooring) as 'Flooring',  
      0 as 'IsFullHeight',   
      1 as 'Order',  
      0 as 'IsProblem',  
      1 as 'IsStock',  
      0 as 'IsScan',  
      Min(RecTypes.Id) as 'ReconciliationTypeId',  --don't know why we have to use the MIN    
      'MatchedToReport' as ReconciliationState
      FROM ReconcilingItemTypes RecTypes    
      FULL JOIN ReconcilingItems ri on ri.ReconcilingItemTypeId = RecTypes.Id  AND ri.StockCheckId = @StockCheckId
      FULL JOIN StockItems si on si.ReconcilingItemId = ri.Id    
      INNER JOIN Users AS U ON U.DealerGroupId = RecTypes.DealerGroupId AND U.Id = @UserId    
      WHERE (si.StockCheckId = @StockCheckId OR si.StockCheckId IS NULL)    
      AND RecTypes.ExplainsMissingVehicle = 1   
      AND si.ScanId IS NULL --Added si.ScanId IS NULL to fix (vindis - skoda vw cambridge) issue    
      GROUP BY RecTypes.Description, RecTypes.SortOrder, RecTypes.IsActive
	  HAVING RecTypes.IsActive = 1 OR COUNT(si.Id) > 0  
      ORDER BY RecTypes.SortOrder OFFSET 0 ROWS  
    ) x  
     
 UNION ALL   
   
    SELECT     
    'Scanned at another site'  as 'Description' ,    
    (  
        SELECT COUNT(st.Id)   
        FROM StockItems st     
        INNER JOIN Scans sc on sc.Id = st.ScanId    
        WHERE sc.StockCheckId <> @StockCheckId     
        AND ScanId is not null    
        AND sc.IsDuplicate = 0    
        AND st.IsDuplicate = 0    
        AND st.StockCheckId = @StockCheckId  
    ) as 'VehicleCount',   
    (  
        SELECT SUM(st.StockValue)   
        FROM StockItems st     
        INNER JOIN Scans sc on sc.Id = st.ScanId    
        WHERE sc.StockCheckId <> @StockCheckId     
        AND ScanId is not null    
        AND sc.IsDuplicate = 0    
        AND st.IsDuplicate = 0    
        AND st.StockCheckId = @StockCheckId  
    ) as 'InStockValue',  
	(  
        SELECT SUM(st.Flooring)   
        FROM StockItems st     
        INNER JOIN Scans sc on sc.Id = st.ScanId    
        WHERE sc.StockCheckId <> @StockCheckId     
        AND ScanId is not null    
        AND sc.IsDuplicate = 0    
        AND st.IsDuplicate = 0    
        AND st.StockCheckId = @StockCheckId  
    ) as 'Flooring', 
    0 as 'IsFullHeight',   
    2 as 'Order',  
    0 as 'IsProblem',  
    1 as 'IsStock',  
    0 as 'IsScan',  
    null as 'ReconciliationTypeId'   ,
    'MatchedToOtherSite' as ReconciliationState
    
 UNION ALL   
   
    SELECT     
    'Missing resolved'  as 'Description',      
    (  
        SELECT COUNT(st.Id)   
        FROM StockItems st     
        INNER JOIN MissingResolutions mr on mr.Id = st.MissingResolutionId     
        WHERE st.ScanId is null AND mr.IsResolved = 1 AND st.ReconcilingItemId is null      
        AND st.StockCheckId = @StockCheckId  
    )   AS 'VehicleCount',  
    (  
        SELECT SUM(st.StockValue)   
        FROM StockItems st     
        INNER JOIN MissingResolutions mr on mr.Id = st.MissingResolutionId     
        WHERE st.ScanId is null AND mr.IsResolved = 1 AND st.ReconcilingItemId is null      
        AND st.StockCheckId = @StockCheckId  
    )   AS 'InStockValue',  

	(  
        SELECT SUM(st.Flooring)   
        FROM StockItems st     
        INNER JOIN MissingResolutions mr on mr.Id = st.MissingResolutionId     
        WHERE st.ScanId is null AND mr.IsResolved = 1 AND st.ReconcilingItemId is null      
        AND st.StockCheckId = @StockCheckId  
    )   AS 'Flooring', 

    0 as 'IsFullHeight',  
    3 as 'Order',  
    0 as 'IsProblem',  
    1 as 'IsStock',  
    0 as 'IsScan',  
    null as 'ReconciliationTypeId'    ,
    'Resolved' as ReconciliationState
    
 UNION ALL   
   
    SELECT     
    'Missing unresolved' as 'Description',     
    (  
        SELECT COUNT(Id)   
        FROM stockitems     
        WHERE ScanId is null   
        AND ReconcilingItemId is null     
        AND MissingResolutionId is null   
        AND IsDuplicate = 0   
        AND StockItems.StockCheckId = @StockCheckId  
    )    
        +     
    (  
        SELECT COUNT(st.Id)   
        FROM StockItems st     
        INNER JOIN MissingResolutions mr on mr.Id = st.MissingResolutionId     
        WHERE mr.IsResolved = 0   
        AND st.ScanId is null   
        AND st.ReconcilingItemId is null   
        AND st.IsDuplicate = 0     
        AND st.StockCheckId = @StockCheckId  
    )  as 'VehicleCount',  
    (  
        SELECT COALESCE(SUM(st.StockValue), 0)   
        FROM stockitems   st  
        WHERE ScanId is null   
        AND ReconcilingItemId is null     
        AND MissingResolutionId is null   
        AND IsDuplicate = 0   
        AND st.StockCheckId = @StockCheckId  
    )    
        +     
    (  
        SELECT COALESCE(SUM(st.StockValue), 0)   
        FROM StockItems st     
        INNER JOIN MissingResolutions mr on mr.Id = st.MissingResolutionId     
        WHERE mr.IsResolved = 0   
        AND st.ScanId is null   
        AND st.ReconcilingItemId is null   
        AND st.IsDuplicate = 0     
        AND st.StockCheckId = @StockCheckId  
    )  as 'InStockValue', 

	(  
        SELECT COALESCE(SUM(st.Flooring), 0)   
        FROM stockitems   st  
        WHERE ScanId is null   
        AND ReconcilingItemId is null     
        AND MissingResolutionId is null   
        AND IsDuplicate = 0   
        AND st.StockCheckId = @StockCheckId  
    )    
        +     
    (  
        SELECT COALESCE(SUM(st.Flooring), 0)   
        FROM StockItems st     
        INNER JOIN MissingResolutions mr on mr.Id = st.MissingResolutionId     
        WHERE mr.IsResolved = 0   
        AND st.ScanId is null   
        AND st.ReconcilingItemId is null   
        AND st.IsDuplicate = 0     
        AND st.StockCheckId = @StockCheckId  
    )  as 'Flooring', 

    0 as 'IsFullHeight',  
    4 as 'Order',  
    1 as 'IsProblem',  
    1 as 'IsStock',  
    0 as 'IsScan',null as 'ReconciliationTypeId'    ,
    'OutstandingIssue' as ReconciliationState
    
 UNION ALL   
   
    SELECT     
    'In Stock and Scanned' as 'Description',     
    (  
        SELECT COUNT(st.Id)   
        FROM StockItems st     
        INNER JOIN Scans sc on sc.Id = st.ScanId     
        WHERE sc.StockCheckId = @StockCheckId   
        AND  ScanId is not null     
        AND st.StockCheckId = @StockCheckId  
    ) as 'VehicleCount',  
    (  
        SELECT SUM(st.StockValue)  
        FROM StockItems st     
        INNER JOIN Scans sc on sc.Id = st.ScanId     
        WHERE sc.StockCheckId = @StockCheckId   
        AND  ScanId is not null     
        AND st.StockCheckId = @StockCheckId  
    ) as 'InStockValue',  
	(  
        SELECT SUM(st.Flooring)  
        FROM StockItems st     
        INNER JOIN Scans sc on sc.Id = st.ScanId     
        WHERE sc.StockCheckId = @StockCheckId   
        AND  ScanId is not null     
        AND st.StockCheckId = @StockCheckId  
    ) as 'Flooring',
    1 as 'IsFullHeight',  
    5 as 'Order',  
    0 as 'IsProblem',  
    1 as 'IsStock',  
    1 as 'IsScan',  
    null as 'ReconciliationTypeId'    ,
    'MatchedToStockOrScan' as ReconciliationState
    
UNION ALL  
  
    SELECT * FROM   
        (   
            SELECT RecTypes.Description as 'Description',     
            COUNT(scns.Id)  as 'VehicleCount',  
            0 as 'InStockValue',  
			0 as 'Flooring', 
            0 as 'IsFullHeight',  
            6 as 'Order',  
            0 as 'IsProblem',  
            0 as 'IsStock',  
            1 as 'IsScan',  
            MIN(RecTypes.Id) as 'ReconciliationTypeId', --don't know why we have to use the MIN    
            'MatchedToReport' as ReconciliationState
            FROM ReconcilingItemTypes RecTypes    
            FULL JOIN ReconcilingItems ri on ri.ReconcilingItemTypeId = RecTypes.Id AND ri.StockCheckId = @StockCheckId   
            FULL JOIN Scans scns on scns.ReconcilingItemId = ri.Id    
            INNER JOIN Users AS U ON U.DealerGroupId = RecTypes.DealerGroupId AND U.Id = @UserId    
            WHERE   
            (  
                scns.StockCheckId = @StockCheckId OR   
                scns.StockCheckId IS NULL  
            )    
            AND scns.StockItemId IS NULL    
            AND RecTypes.ExplainsMissingVehicle = 0    
            GROUP BY RecTypes.Description, RecTypes.SortOrder, RecTypes.IsActive
			HAVING RecTypes.IsActive = 1 OR COUNT(scns.Id) > 0
            ORDER BY RecTypes.SortOrder OFFSET 0 ROWS  
        ) x  
     
UNION ALL   
  
        SELECT 'In stock at another site' as 'Description',     
        (  
            SELECT Count(scns.Id)   
            FROM scans scns     
            INNER JOIN StockItems stockitem on stockitem.Id = scns.StockItemId    
            WHERE scns.StockCheckId = @StockCheckId     
            AND stockItem.StockCheckId <> @StockCheckId  
        ) as 'VehicleCount',  
        0 as 'InStockValue', 
		0 as 'Flooring',
        0 as 'IsFullHeight',  
        7 as 'Order',  
        0 as 'IsProblem',  
        0 as 'IsStock',  
        1 as 'IsScan',  
        null as 'ReconciliationTypeId'   ,
        'MatchedToOtherSite' as ReconciliationState
    
UNION ALL   
  
        SELECT 'Duplicate scan' as 'Description',     
        (  
            SELECT COUNT(Id)   
            FROM Scans   
            WHERE IsDuplicate = 1     
            AND Scans.StockCheckId = @StockCheckId  
        ) as 'VehicleCount',  
        0 as 'InStockValue',  
		0 as 'Flooring',  
        0 as 'IsFullHeight',  
        8 as 'Order',  
        0 as 'IsProblem',  
        0 as 'IsStock',  
        1 as 'IsScan',  
        null as 'ReconciliationTypeId'  ,
        'Duplicate' as ReconciliationState
    
UNION ALL   
  
        SELECT 'Unknown resolved' as 'Description',      
        (  
            SELECT COUNT(sc.Id) from Scans sc     
            INNER JOIN UnknownResolutions mr on mr.Id = sc.UnknownResolutionId     
            WHERE mr.IsResolved = 1   
            AND sc.IsDuplicate = 0   
            AND sc.StockItemId IS NULL   
            AND sc.ReconcilingItemId is null   
            AND sc.StockCheckId = @StockCheckId  
        ) as 'VehicleCount',  
        0 as 'InStockValue',  
		0 as 'Flooring',
        0 as 'IsFullHeight',  
        9 as 'Order',  
        0 as 'IsProblem',  
        0 as 'IsStock',  
        1 as 'IsScan',  
        null as 'ReconciliationTypeId'    ,
        'Resolved' as ReconciliationState
    
UNION ALL   
  
        SELECT 'Unknown unresolved' as 'Description',     
        (  
            SELECT COUNT(Id)   
            FROM Scans   
            WHERE StockItemId is null   
            AND ReconcilingItemId is null   
            AND UnknownResolutionId is null   
            AND IsDuplicate = 0   
            AND Scans.StockCheckId = @StockCheckId  
        )     
    +     
        (  
            SELECT COUNT(sc.Id) from Scans sc     
            INNER JOIN UnknownResolutions mr on mr.Id = sc.UnknownResolutionId     
            WHERE mr.IsResolved = 0   
            AND StockItemId IS NULL   
            AND ReconcilingItemId IS NULL   
            AND sc.IsDuplicate = 0   
            AND sc.StockCheckId = @StockCheckId  
        ) as 'VehicleCount',  
        0 as 'InStockValue',  
		0 as 'Flooring',
        0 as 'IsFullHeight',  
        10 as 'Order',  
        1 as 'IsProblem',  
        0 as 'IsStock',  
        1 as 'IsScan',  
        null as 'ReconciliationTypeId'    ,
        'OutstandingIssue' as ReconciliationState
    
UNION ALL   
  
        SELECT 'Scanned' as 'Description',   
        (  
            SELECT COUNT(Id)   
            FROM Scans   
            WHERE StockCheckId = @StockCheckId  
        ) as 'VehicleCount' ,  
        0 as 'InStockValue',  
		0 as 'Flooring', 
        1 as 'IsFullHeight',  
        11 as 'Order',  
        0 as 'IsProblem',  
        0 as 'IsStock',  
        1 as 'IsScan',  
        null as 'ReconciliationTypeId' ,
        'AllItems' as ReconciliationState
    
 END    
    
  
  
  
  
  
 ---------------------  
 --IF REGIONAL  
 ---------------------  
IF @isRegional = 1 AND @isTotal = 0    
    
    BEGIN     
    
    DECLARE @DivisionId INT;    
    
    SET @DivisionId = (SELECT DivisionId FROM StockChecks    
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId    
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id    
                        WHERE StockChecks.Id = @StockCheckId AND StockChecks.IsRegional = 1)


	    
        
    SET @DealerGroupId = (SELECT DealerGroupId FROM StockChecks    
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId    
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id    
                        WHERE StockChecks.Id = @StockCheckId)    
						
    
	SELECT  'Stock' as 'Description',
	COUNT(StockItems.Id) as 'VehicleCount',
	SUM(StockItems.StockValue) as 'InStockValue',
	SUM(StockItems.Flooring) AS 'Flooring',
	1 as 'IsFullHeight',
	0 as 'Order',
	0 as 'IsProblem',
	1 as 'IsStock',
	0 as 'IsScan',
	null as 'ReconciliationTypeId',
    'AllItems' as ReconciliationState
	FROM StockItems   
	INNER JOIN Stockchecks ON Stockchecks.Id=StockItems.StockCheckId AND Stockchecks.Date = @StockCheckDate  
	INNER JOIN Sites ON Sites.Id=StockChecks.SiteId  
	INNER JOIN Divisions ON Divisions.Id=Sites.DivisionId  
	WHERE Divisions.Id = @DivisionId
  
 UNION ALL  

	SELECT 'Duplicate stock record' as 'Description',   
	COUNT(StockItems.Id) as 'VehicleCount',
	SUM(StockItems.StockValue)  as 'InStockValue',
	SUM(StockItems.Flooring) AS 'Flooring',
		0 as 'IsFullHeight',
	0 as 'Order',
	0 as 'IsProblem',
	1 as 'IsStock',
	0 as 'IsScan',
	null as 'ReconciliationTypeId'  ,
    'Duplicate' as ReconciliationState
	FROM StockItems   
	INNER JOIN Stockchecks ON Stockchecks.Id=StockItems.StockCheckId AND Stockchecks.Date = @StockCheckDate  
	INNER JOIN Sites ON Sites.Id=StockChecks.SiteId  
	INNER JOIN Divisions ON Divisions.Id=Sites.DivisionId  
	WHERE Divisions.Id = @DivisionId AND IsDuplicate = 1
      
    
    
 UNION ALL   
   
    SELECT   RecTypes.Description as 'Description',     
    COUNT(si.Id) as 'VehicleCount',  
    SUM(si.StockValue) as 'InStockValue',  
	SUM(si.Flooring) AS 'Flooring',
    0 as 'IsFullHeight',  
    1 as 'Order',  
    0 as 'IsProblem',  
    1 as 'IsStock',  
    0 as 'IsScan',  
    Min(RecTypes.Id) as 'ReconciliationTypeId' , --don't know why we have to use the MIN    
    'MatchedToReport' as ReconciliationState
    FROM ReconcilingItemTypes RecTypes    
    FULL JOIN ReconcilingItems ri on ri.ReconcilingItemTypeId = RecTypes.Id    
    FULL JOIN StockItems si on si.ReconcilingItemId = ri.Id    
    FULL JOIN Stockchecks sc ON sc.Id=si.StockCheckId     
    FULL JOIN Sites st ON st.Id=sc.SiteId    
    FULL JOIN Divisions di ON di.Id=st.DivisionId    
    INNER JOIN Users AS U ON U.DealerGroupId = RecTypes.DealerGroupId AND U.Id = @UserId    
    WHERE (di.Id = @DivisionId OR di.Id IS NULL)    
    AND RecTypes.ExplainsMissingVehicle = 1 AND si.ScanId IS NULL AND sc.Date = @StockCheckDate    
    GROUP BY RecTypes.Description    
     
        
 UNION ALL   
   
	SELECT   'Scanned at another site'  as 'Description' ,  
	COUNT(st.Id) as 'VehicleCount',
	SUM(st.StockValue) AS 'InStockValue',
	SUM(st.Flooring) AS 'Flooring',
	0 as 'IsFullHeight',
	2 as 'Order',
	0 as 'IsProblem',
	1 as 'IsStock',
	0 as 'IsScan',
	null as 'ReconciliationTypeId'  ,
    'MatchedToOtherSite' as ReconciliationState
	FROM StockItems st   
	INNER JOIN Scans sc on sc.Id = st.ScanId  
	INNER JOIN Stockchecks stkck ON stkck.Id=st.StockCheckId AND stkck.Date = @StockCheckDate  
	INNER JOIN Sites si ON si.Id = stkck.SiteId  
	INNER JOIN Divisions di ON di.Id=si.DivisionId  
	WHERE sc.StockCheckId <> st.StockCheckId  
	AND di.Id = @DivisionId  
	AND sc.IsDuplicate = 0  
	AND st.IsDuplicate = 0  
	AND  ScanId is not null
    
    
 UNION ALL   
   
    SELECT  'Missing resolved' AS 'Description',    
    COUNT(st.Id) AS 'VehicleCount',
	SUM(st.StockValue)  AS 'InStockValue',
	SUM(st.Flooring) AS 'Flooring',
	0 as 'IsFullHeight',
    3 as 'Order',
    0 as 'IsProblem',
    1 as 'IsStock',
    0 as 'IsScan',
    null as 'ReconciliationTypeId'  ,
    'Resolved' as ReconciliationState
    FROM StockItems st   
    INNER JOIN MissingResolutions mr on mr.Id = st.MissingResolutionId   
    INNER JOIN Stockchecks stkck ON stkck.Id=st.StockCheckId AND stkck.Date = @StockCheckDate  
    INNER JOIN Sites si ON si.Id = stkck.SiteId  
    INNER JOIN Divisions di ON di.Id=si.DivisionId  
    WHERE st.ScanId is null 
    AND mr.IsResolved = 1 
    AND st.ReconcilingItemId is null    
    AND di.Id = @DivisionId
     
    
        
 UNION ALL   
   
    SELECT   'Missing unresolved' as 'Description',     
    (  
        SELECT COUNT(stockitems.Id)   
        FROM stockitems    
        INNER JOIN Stockchecks stkck ON stkck.Id=stockitems.StockCheckId AND stkck.Date = @StockCheckDate    
        INNER JOIN Sites si ON si.Id = stkck.SiteId    
        INNER JOIN Divisions di ON di.Id=si.DivisionId    
        WHERE ScanId is null   
        AND ReconcilingItemId is null     
        AND MissingResolutionId is null   
        AND IsDuplicate = 0   
        AND di.Id = @DivisionId  
    )    
      
    +     
      
    (  
        SELECT COUNT(st.Id)   
        FROM StockItems st     
        INNER JOIN MissingResolutions mr on mr.Id = st.MissingResolutionId    
        INNER JOIN Stockchecks stkck ON stkck.Id=st.StockCheckId AND stkck.Date = @StockCheckDate    
        INNER JOIN Sites si ON si.Id = stkck.SiteId    
        INNER JOIN Divisions di ON di.Id=si.DivisionId    
        WHERE mr.IsResolved = 0   
        AND st.ScanId is null   
        AND st.ReconcilingItemId is null   
        AND st.IsDuplicate = 0 AND di.Id = @DivisionId  
    )  as 'VehicleCount',  
     (  
        SELECT SUM(stockitems.StockValue)   
        FROM stockitems    
        INNER JOIN Stockchecks stkck ON stkck.Id=stockitems.StockCheckId AND stkck.Date = @StockCheckDate    
        INNER JOIN Sites si ON si.Id = stkck.SiteId    
        INNER JOIN Divisions di ON di.Id=si.DivisionId    
        WHERE ScanId is null   
        AND ReconcilingItemId is null     
        AND MissingResolutionId is null   
        AND IsDuplicate = 0   
        AND di.Id = @DivisionId  
    )    
      
    +     
      
    (  
        SELECT SUM(st.StockValue)   
        FROM StockItems st     
        INNER JOIN MissingResolutions mr on mr.Id = st.MissingResolutionId    
        INNER JOIN Stockchecks stkck ON stkck.Id=st.StockCheckId AND stkck.Date = @StockCheckDate    
        INNER JOIN Sites si ON si.Id = stkck.SiteId    
        INNER JOIN Divisions di ON di.Id=si.DivisionId    
        WHERE mr.IsResolved = 0   
        AND st.ScanId is null   
        AND st.ReconcilingItemId is null   
        AND st.IsDuplicate = 0 AND di.Id = @DivisionId  
    )  as 'InStockValue',  

	(  
        SELECT SUM(stockitems.Flooring)   
        FROM stockitems    
        INNER JOIN Stockchecks stkck ON stkck.Id=stockitems.StockCheckId AND stkck.Date = @StockCheckDate    
        INNER JOIN Sites si ON si.Id = stkck.SiteId    
        INNER JOIN Divisions di ON di.Id=si.DivisionId    
        WHERE ScanId is null   
        AND ReconcilingItemId is null     
        AND MissingResolutionId is null   
        AND IsDuplicate = 0   
        AND di.Id = @DivisionId  
    )    
      
    +     
      
    (  
        SELECT SUM(st.Flooring)   
        FROM StockItems st     
        INNER JOIN MissingResolutions mr on mr.Id = st.MissingResolutionId    
        INNER JOIN Stockchecks stkck ON stkck.Id=st.StockCheckId AND stkck.Date = @StockCheckDate    
        INNER JOIN Sites si ON si.Id = stkck.SiteId    
        INNER JOIN Divisions di ON di.Id=si.DivisionId    
        WHERE mr.IsResolved = 0   
        AND st.ScanId is null   
        AND st.ReconcilingItemId is null   
        AND st.IsDuplicate = 0 AND di.Id = @DivisionId  
    )  as 'Flooring',  

    0 as 'IsFullHeight',  
    4 as 'Order',  
    1 as 'IsProblem',  
    1 as 'IsStock',  
    0 as 'IsScan',  
    null as 'ReconciliationTypeId'    ,
    'OutstandingIssue' as ReconciliationState
    
    
 UNION ALL   
   
   
	SELECT   'In Stock and Scanned' as 'Description',   
	COUNT(st.Id) as 'VehicleCount',
	SUM(st.StockValue)  as 'InStockValue',
	SUM(st.Flooring)  as 'Flooring',
	1 as 'IsFullHeight',
	5 as 'Order',
	0 as 'IsProblem',
	1 as 'IsStock',
	1 as 'IsScan',
	null as 'ReconciliationTypeId'  ,
    'MatchedToStockOrScan' as ReconciliationState
	FROM StockItems st   
	INNER JOIN Scans sc on sc.Id = st.ScanId  
	INNER JOIN Stockchecks stkck ON stkck.Id=st.StockCheckId AND stkck.Date = @StockCheckDate  
	INNER JOIN Sites si ON si.Id = stkck.SiteId  
	INNER JOIN Divisions di ON di.Id=si.DivisionId  
	WHERE sc.StockCheckId = st.StockCheckId AND  ScanId is not null   
	AND di.Id = @DivisionId
            
    
UNION ALL 

			SELECT 
			RecTypes.Description as 'Description', 
			COALESCE(x.VehicleCount,0) as 'VehicleCount',
			0 as 'InStockValue',
			0 as 'Flooring', 
			0 as 'IsFullHeight',    
			6 as 'Order',    
			0 as 'IsProblem',    
			0 as 'IsStock',    
			1 as 'IsScan',    
			RecTypes.Id as 'ReconciliationTypeId', 
			'MatchedToReport' as 'ReconciliationState'  

			FROM  
			ReconcilingItemTypes RecTypes
			LEFT JOIN 
				(     
				  SELECT 
				  COUNT(scns.Id)  as 'VehicleCount',    
				  MIN(RecTypes.Id) as 'ReconciliationTypeId' --don't know why we have to use the MIN      
				  FROM ReconcilingItemTypes RecTypes      
				  FULL JOIN ReconcilingItems ri on ri.ReconcilingItemTypeId = RecTypes.Id      
				  FULL JOIN Scans scns on scns.ReconcilingItemId = ri.Id      
				  INNER JOIN Stockchecks stkck ON stkck.Id=ri.StockCheckId      
				  INNER JOIN Sites si ON si.Id = stkck.SiteId      
				  INNER JOIN Divisions di ON di.Id=si.DivisionId      
				  WHERE (di.Id = @DivisionId OR scns.StockCheckId IS NULL)     
				  AND scns.StockItemId IS NULL      
				  AND RecTypes.ExplainsMissingVehicle = 0      
				  AND stkck.Date = @StockCheckDate
				  AND RecTypes.DealerGroupId = @DealerGroupId
				  GROUP BY RecTypes.Description, RecTypes.SortOrder    
				  ORDER BY RecTypes.SortOrder OFFSET 0 ROWS    
				) x  
			ON RecTypes.Id = x.ReconciliationTypeId
			WHERE RecTypes.ExplainsMissingVehicle = 0
			AND RecTypes.DealerGroupId = @DealerGroupId 
    
    
	UNION ALL   
   
    SELECT 'In stock at another site' as 'Description',     
    (  
        SELECT COUNT(scns.Id) from scans scns     
        INNER JOIN StockItems stockitem on stockitem.Id = scns.StockItemId    
        INNER JOIN Stockchecks stkck ON stkck.Id=scns.StockCheckId AND stkck.Date = @StockCheckDate    
        INNER JOIN Sites si ON si.Id = stkck.SiteId    
        INNER JOIN Divisions di ON di.Id=si.DivisionId    
        WHERE di.Id = @DivisionId    
        AND stockItem.StockCheckId <> scns.StockCheckId  
    ) as 'VehicleCount',  
    0 as 'InStockValue',  
	0 as 'Flooring',
    0 as 'IsFullHeight',  
    7 as 'Order',  
    0 as 'IsProblem',  
    0 as 'IsStock',  
    1 as 'IsScan',  
    null as 'ReconciliationTypeId'    ,
    'MatchedToOtherSite' as ReconciliationState
    
    
 UNION ALL   
   
    SELECT 'Duplicate scan' as 'Description',     
    (  
        SELECT count(Scans.Id) from Scans     
        INNER JOIN Stockchecks stkck ON stkck.Id=Scans.StockCheckId AND stkck.Date = @StockCheckDate    
        INNER JOIN Sites si ON si.Id = stkck.SiteId    
        INNER JOIN Divisions di ON di.Id=si.DivisionId      
        WHERE Scans.IsDuplicate = 1     
        AND di.Id = @DivisionId  
    ) as 'VehicleCount',  
    0 as 'InStockValue',  
	0 as 'Flooring',
    0 as 'IsFullHeight',  
    8 as 'Order',  
    0 as 'IsProblem',  
    0 as 'IsStock',  
    1 as 'IsScan',  
    null as 'ReconciliationTypeId'    ,
    'Duplicate' as ReconciliationState
    
    
 UNION ALL   
   
    SELECT 'Unknown resolved' as 'Description',      
    (  
        SELECT count(sc.Id) from Scans sc     
        INNER JOIN UnknownResolutions mr on mr.Id = sc.UnknownResolutionId     
        INNER JOIN Stockchecks stkck ON stkck.Id=sc.StockCheckId AND stkck.Date = @StockCheckDate    
        INNER JOIN Sites si ON si.Id = stkck.SiteId    
        INNER JOIN Divisions di ON di.Id=si.DivisionId      
        WHERE mr.IsResolved = 1   
        AND sc.IsDuplicate = 0   
        AND sc.StockItemId IS NULL   
        AND sc.ReconcilingItemId is null   
        AND di.Id = @DivisionId  
    ) as 'VehicleCount',  
    0 as 'InStockValue',  
	0 as 'Flooring', 
    0 as 'IsFullHeight',  
    9 as 'Order',  
    0 as 'IsProblem',  
    0 as 'IsStock',  
    1 as 'IsScan',  
    null as 'ReconciliationTypeId'    ,
    'Resolved' as ReconciliationState
    
 UNION ALL   
   
    SELECT 'Unknown unresolved' as 'Description',     
    (  
        SELECT count(Scans.Id) from Scans     
        INNER JOIN Stockchecks ON Stockchecks.Id=Scans.StockCheckId AND StockChecks.Date = @StockCheckDate    
        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId    
        INNER JOIN Divisions ON Divisions.Id=Sites.DivisionId    
        WHERE StockItemId is null   
        AND ReconcilingItemId is null   
        AND UnknownResolutionId is null   
        AND Scans.IsDuplicate = 0   
        AND Divisions.Id = @DivisionId  
    )     
    
    +     
      
    (  
        SELECT count(sc.Id)   
        FROM Scans sc     
        INNER JOIN UnknownResolutions mr on mr.Id = sc.UnknownResolutionId    
        INNER JOIN Stockchecks stkck ON stkck.Id=sc.StockCheckId AND stkck.Date = @StockCheckDate    
        INNER JOIN Sites si ON si.Id = stkck.SiteId    
        INNER JOIN Divisions di ON di.Id=si.DivisionId      
        WHERE mr.IsResolved = 0   
        AND sc.StockItemId IS NULL   
        AND sc.ReconcilingItemId IS NULL   
        AND sc.IsDuplicate = 0    
        AND di.Id = @DivisionId  
    ) as 'VehicleCount',  
    0 as 'InStockValue',  
	0 as 'Flooring',  
    0 as 'IsFullHeight',  
    10 as 'Order',  
    1 as 'IsProblem',  
    0 as 'IsStock',  
    1 as 'IsScan',  
    null as 'ReconciliationTypeId'    ,
    'OutstandingIssue' as ReconciliationState
    
 UNION ALL   
   
    SELECT 'Scanned' as 'Description',   
    (  
        SELECT count(Scans.Id) from Scans     
        INNER JOIN Stockchecks stkck ON stkck.Id=Scans.StockCheckId  AND stkck.Date = @StockCheckDate    
        INNER JOIN Sites si ON si.Id = stkck.SiteId    
        INNER JOIN Divisions di ON di.Id=si.DivisionId     
        WHERE di.Id = @DivisionId  
    ) as 'VehicleCount',  
    0 as 'InStockValue',  
	0 as 'Flooring',
    1 as 'IsFullHeight',  
    11 as 'Order',  
    0 as 'IsProblem',  
    0 as 'IsStock',  
    1 as 'IsScan',  
    null as 'ReconciliationTypeId'   ,
    'AllItems' as ReconciliationState
  
 END    
  
  
  
 ---------------------  
 --IF TOTAL  
 ---------------------  
IF @isRegional = 0 AND @isTotal = 1    
    
    BEGIN     
    
    SET @DealerGroupId = (SELECT DealerGroupId FROM StockChecks    
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId    
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id    
                        WHERE StockChecks.Id = @StockCheckId)    
    
	SELECT 'Stock' as 'Description',     
	count(StockItems.Id)  as 'VehicleCount',
	SUM(StockItems.StockValue) as 'InStockValue',  
	SUM(StockItems.Flooring) as 'Flooring', 
	1 as 'IsFullHeight',  
	0 as 'Order',  
	0 as 'IsProblem',  
	1 as 'IsStock',  
	0 as 'IsScan',  
	null as 'ReconciliationTypeId'   ,
    'AllItems' as ReconciliationState
	from StockItems
	INNER JOIN Stockchecks ON Stockchecks.Id=StockItems.StockCheckId  AND StockChecks.Date = @StockCheckDate    
	INNER JOIN Sites ON Sites.Id=StockChecks.SiteId    
	INNER JOIN Divisions ON Divisions.Id=Sites.DivisionId    
	INNER JOIN DealerGroup ON Divisions.DealerGroupId=DealerGroup.Id    
	WHERE DealerGroup.Id = @DealerGroupId    
   
    
 UNION ALL    
  
	SELECT 'Duplicate stock record' as 'Description',     
	count(StockItems.Id) as 'VehicleCount',
	SUM(StockItems.StockValue) as 'InStockValue',
	SUM(StockItems.Flooring) as 'Flooring',
	0 as 'IsFullHeight',  
	0 as 'Order',  
	0 as 'IsProblem',  
	1 as 'IsStock',  
	0 as 'IsScan',  
	null as 'ReconciliationTypeId'    ,
    'Duplicate' as ReconciliationState
	from StockItems     
	INNER JOIN Stockchecks ON Stockchecks.Id=StockItems.StockCheckId  AND StockChecks.Date = @StockCheckDate    
	INNER JOIN Sites ON Sites.Id=StockChecks.SiteId    
	INNER JOIN Divisions ON Divisions.Id=Sites.DivisionId    
	INNER JOIN DealerGroup ON Divisions.DealerGroupId=DealerGroup.Id    
	WHERE DealerGroup.Id = @DealerGroupId AND IsDuplicate = 1    
    
    
 UNION ALL   
   
    SELECT   RecTypes.Description as 'Description',     
    COUNT(si.Id) as 'VehicleCount',  
    SUM(si.StockValue) as 'InStockValue',  
	SUM(si.Flooring) as 'Flooring',  
    0 as 'IsFullHeight',   
    1 as 'Order',  
    0 as 'IsProblem',  
    1 as 'IsStock',  
    0 as 'IsScan',  
    Min(RecTypes.Id) as 'ReconciliationTypeId'   ,
    'MatchedToReport' as ReconciliationState
    FROM ReconcilingItemTypes RecTypes  --don't know why we have to use the MIN    
    FULL JOIN ReconcilingItems ri on ri.ReconcilingItemTypeId = RecTypes.Id    
    FULL JOIN StockItems si on si.ReconcilingItemId = ri.Id    
    FULL JOIN Stockchecks sc ON sc.Id=si.StockCheckId      
    FULL JOIN Sites st ON st.Id=sc.SiteId    
    FULL JOIN Divisions di ON di.Id=st.DivisionId    
    FULL JOIN DealerGroup ON di.DealerGroupId=DealerGroup.Id    
    WHERE (DealerGroup.Id = @DealerGroupId OR si.StockCheckId IS NULL)    
    AND RecTypes.ExplainsMissingVehicle = 1 AND si.ScanId IS NULL AND sc.Date = @StockCheckDate    
    GROUP BY RecTypes.Description    
     
        
 UNION ALL   
   
	SELECT   'Scanned at another site'  as 'Description' ,    
	count(st.Id)  as 'VehicleCount' ,
	SUM(st.StockValue)  as 'InStockValue', 
	SUM(st.Flooring)  as 'Flooring',
	0 as 'IsFullHeight',  
	2 as 'Order',  
	0 as 'IsProblem',  
	1 as 'IsStock',  
	0 as 'IsScan',  
	null as 'ReconciliationTypeId'    ,
    'MatchedToOtherSite' as ReconciliationState
	FROM StockItems st     
	INNER JOIN Scans sc on sc.Id = st.ScanId    
	INNER JOIN Stockchecks stkck ON stkck.Id=st.StockCheckId  AND stkck.Date = @StockCheckDate    
	INNER JOIN Sites si ON si.Id = stkck.SiteId    
	INNER JOIN Divisions di ON di.Id=si.DivisionId    
	INNER JOIN DealerGroup ON di.DealerGroupId=DealerGroup.Id    
	WHERE sc.StockCheckId <> st.StockCheckId    
	AND DealerGroup.Id = @DealerGroupId    
	AND sc.IsDuplicate = 0    
	AND st.IsDuplicate = 0    
	AND  ScanId is not null  
    
    
 UNION ALL   
   
	SELECT   'Missing resolved'  as 'Description',      
	count(st.Id)   as 'VehicleCount', 
	SUM(st.StockValue) as 'InStockValue',
	SUM(st.Flooring) as 'Flooring',
	0 as 'IsFullHeight',  
	3 as 'Order',  
	0 as 'IsProblem',  
	1 as 'IsStock',  
	0 as 'IsScan',  
	null as 'ReconciliationTypeId'    ,
    'Resolved' as ReconciliationState
	FROM StockItems st     
	INNER JOIN MissingResolutions mr on mr.Id = st.MissingResolutionId     
	INNER JOIN Stockchecks stkck ON stkck.Id=st.StockCheckId  AND stkck.Date = @StockCheckDate    
	INNER JOIN Sites si ON si.Id = stkck.SiteId    
	INNER JOIN Divisions di ON di.Id=si.DivisionId    
	INNER JOIN DealerGroup ON di.DealerGroupId=DealerGroup.Id    
	WHERE st.ScanId is null   
	AND mr.IsResolved = 1   
	AND st.ReconcilingItemId is null      
	AND DealerGroup.Id = @DealerGroupId    
        
    
        
 UNION ALL   
   
        SELECT     
		'Missing unresolved' as 'Description',     
        (  
            SELECT count(stockitems.Id)   
            FROM stockitems    
            INNER JOIN Stockchecks stkck ON stkck.Id=stockitems.StockCheckId    
            INNER JOIN Sites si ON si.Id = stkck.SiteId  AND stkck.Date = @StockCheckDate    
            INNER JOIN Divisions di ON di.Id=si.DivisionId    
            INNER JOIN DealerGroup ON di.DealerGroupId=DealerGroup.Id    
            WHERE ScanId is null AND ReconcilingItemId is null     
            AND MissingResolutionId is null AND IsDuplicate = 0 AND DealerGroup.Id = @DealerGroupId  
        )    
          
        +     
    
        (  
            SELECT count(st.Id)   
            FROM StockItems st     
            INNER JOIN MissingResolutions mr on mr.Id = st.MissingResolutionId    
            INNER JOIN Stockchecks stkck ON stkck.Id=st.StockCheckId  AND stkck.Date = @StockCheckDate    
            INNER JOIN Sites si ON si.Id = stkck.SiteId    
            INNER JOIN Divisions di ON di.Id=si.DivisionId    
            INNER JOIN DealerGroup ON di.DealerGroupId=DealerGroup.Id    
            WHERE mr.IsResolved = 0   
            AND st.ScanId is null   
            AND st.ReconcilingItemId is null   
            AND st.IsDuplicate = 0    
            AND DealerGroup.Id = @DealerGroupId  
        )  as 'VehicleCount',  

         (  
            SELECT SUM(stockitems.StockValue)   
            FROM stockitems    
            INNER JOIN Stockchecks stkck ON stkck.Id=stockitems.StockCheckId    
            INNER JOIN Sites si ON si.Id = stkck.SiteId  AND stkck.Date = @StockCheckDate    
            INNER JOIN Divisions di ON di.Id=si.DivisionId    
            INNER JOIN DealerGroup ON di.DealerGroupId=DealerGroup.Id    
            WHERE ScanId is null AND ReconcilingItemId is null     
            AND MissingResolutionId is null AND IsDuplicate = 0 AND DealerGroup.Id = @DealerGroupId  
        )    
          
        +     
    
        (  
            SELECT SUM(st.StockValue)   
            FROM StockItems st     
            INNER JOIN MissingResolutions mr on mr.Id = st.MissingResolutionId    
            INNER JOIN Stockchecks stkck ON stkck.Id=st.StockCheckId  AND stkck.Date = @StockCheckDate    
            INNER JOIN Sites si ON si.Id = stkck.SiteId    
            INNER JOIN Divisions di ON di.Id=si.DivisionId    
            INNER JOIN DealerGroup ON di.DealerGroupId=DealerGroup.Id    
            WHERE mr.IsResolved = 0   
            AND st.ScanId is null   
            AND st.ReconcilingItemId is null   
            AND st.IsDuplicate = 0    
            AND DealerGroup.Id = @DealerGroupId  
        )  as 'InStockValue',  

         (  
            SELECT SUM(stockitems.Flooring)   
            FROM stockitems    
            INNER JOIN Stockchecks stkck ON stkck.Id=stockitems.StockCheckId    
            INNER JOIN Sites si ON si.Id = stkck.SiteId  AND stkck.Date = @StockCheckDate    
            INNER JOIN Divisions di ON di.Id=si.DivisionId    
            INNER JOIN DealerGroup ON di.DealerGroupId=DealerGroup.Id    
            WHERE ScanId is null AND ReconcilingItemId is null     
            AND MissingResolutionId is null AND IsDuplicate = 0 AND DealerGroup.Id = @DealerGroupId  
        )    
          
        +     
    
        (  
            SELECT SUM(st.Flooring)   
            FROM StockItems st     
            INNER JOIN MissingResolutions mr on mr.Id = st.MissingResolutionId    
            INNER JOIN Stockchecks stkck ON stkck.Id=st.StockCheckId  AND stkck.Date = @StockCheckDate    
            INNER JOIN Sites si ON si.Id = stkck.SiteId    
            INNER JOIN Divisions di ON di.Id=si.DivisionId    
            INNER JOIN DealerGroup ON di.DealerGroupId=DealerGroup.Id    
            WHERE mr.IsResolved = 0   
            AND st.ScanId is null   
            AND st.ReconcilingItemId is null   
            AND st.IsDuplicate = 0    
            AND DealerGroup.Id = @DealerGroupId  
        )  as 'Flooring', 

        0 as 'IsFullHeight',  
        4 as 'Order',  
        1 as 'IsProblem',  
        1 as 'IsStock',  
        0 as 'IsScan',  
        null as 'ReconciliationTypeId'    ,
        'OutstandingIssue' as ReconciliationState
    
    
 UNION ALL   
   
	SELECT  'In Stock and Scanned' as 'Description',     
	count(st.Id)   as 'VehicleCount', 
	SUM(st.StockValue) as 'InStockValue', 
	SUM(st.Flooring) as 'Flooring', 
	1 as 'IsFullHeight',  
	5 as 'Order',  
	0 as 'IsProblem',  
	1 as 'IsStock',  
	1 as 'IsScan',  
	null as 'ReconciliationTypeId'   ,
    'MatchedToStockOrScan' as ReconciliationState
	FROM StockItems st     
	INNER JOIN Scans sc on sc.Id = st.ScanId    
	INNER JOIN Stockchecks stkck ON stkck.Id=st.StockCheckId  AND stkck.Date = @StockCheckDate    
	INNER JOIN Sites si ON si.Id = stkck.SiteId    
	INNER JOIN Divisions di ON di.Id=si.DivisionId    
	INNER JOIN DealerGroup ON di.DealerGroupId=DealerGroup.Id    
	WHERE sc.StockCheckId = st.StockCheckId   
	AND  ScanId is not null     
	AND DealerGroup.Id = @DealerGroupId  
            
    
 UNION ALL     
  

	SELECT 
	RecTypes.Description as 'Description', 
	COALESCE(x.VehicleCount,0) as 'VehicleCount',
	0 as 'InStockValue',
	0 as 'Flooring',
	0 as 'IsFullHeight',    
	6 as 'Order',    
	0 as 'IsProblem',    
	0 as 'IsStock',    
	1 as 'IsScan',    
	RecTypes.Id as 'ReconciliationTypeId', 
	'MatchedToReport' as 'ReconciliationState'  

	FROM  
	ReconcilingItemTypes RecTypes
	LEFT JOIN 
		(     
			SELECT RecTypes.Description as 'Description',       
			COUNT(scns.Id)  as 'VehicleCount',    
			MIN(RecTypes.Id) as 'ReconciliationTypeId' --don't know why we have to use the MIN      
			FROM ReconcilingItemTypes RecTypes      
			FULL JOIN ReconcilingItems ri on ri.ReconcilingItemTypeId = RecTypes.Id      
			FULL JOIN Scans scns on scns.ReconcilingItemId = ri.Id      
			INNER JOIN Stockchecks stkck ON stkck.Id=ri.StockCheckId  AND stkck.Date = @StockCheckDate      
			INNER JOIN Sites si ON si.Id = stkck.SiteId      
			INNER JOIN Divisions di ON di.Id=si.DivisionId      
			INNER JOIN DealerGroup ON di.DealerGroupId=DealerGroup.Id      
			WHERE (DealerGroup.Id = @DealerGroupId) AND scns.StockItemId IS NULL      
			AND RecTypes.ExplainsMissingVehicle = 0      
			GROUP BY RecTypes.Description, RecTypes.SortOrder    
			ORDER BY RecTypes.SortOrder OFFSET 0 ROWS     
		) x  
		ON RecTypes.Id = x.ReconciliationTypeId
		WHERE RecTypes.ExplainsMissingVehicle = 0
		AND RecTypes.DealerGroupId = @DealerGroupId    
    
    
 UNION ALL   
   
        SELECT 'In stock at another site' as 'Description',     
        (  
            SELECT Count(scns.Id)   
            FROM scans scns     
            INNER JOIN StockItems stockitem on stockitem.Id = scns.StockItemId    
            INNER JOIN Stockchecks stkck ON stkck.Id=scns.StockCheckId  AND stkck.Date = @StockCheckDate    
            INNER JOIN Sites si ON si.Id = stkck.SiteId    
            INNER JOIN Divisions di ON di.Id=si.DivisionId    
            INNER JOIN DealerGroup ON di.DealerGroupId=DealerGroup.Id    
            WHERE DealerGroup.Id = @DealerGroupId    
            AND stockItem.StockCheckId <> scns.StockCheckId   
        ) as 'VehicleCount',  
        0 as 'InStockValue',
		0 as 'Flooring',
        0 as 'IsFullHeight',  
        7 as 'Order',  
        0 as 'IsProblem',  
        0 as 'IsStock',  
        1 as 'IsScan',  
        null as 'ReconciliationTypeId'    ,
        'MatchedToOtherSite' as ReconciliationState
    
    
 UNION ALL   
   
        SELECT 'Duplicate scan' as 'Description',     
        (  
            SELECT count(Scans.Id)   
            FROM Scans     
            INNER JOIN Stockchecks stkck ON stkck.Id=Scans.StockCheckId  AND stkck.Date = @StockCheckDate    
            INNER JOIN Sites si ON si.Id = stkck.SiteId    
            INNER JOIN Divisions di ON di.Id=si.DivisionId      
            INNER JOIN DealerGroup ON di.DealerGroupId=DealerGroup.Id    
            WHERE Scans.IsDuplicate = 1     
            AND DealerGroup.Id = @DealerGroupId  
        ) as 'VehicleCount',  
        0 as 'InStockValue',
		0 as 'Flooring',
        0 as 'IsFullHeight',  
        8 as 'Order',  
        0 as 'IsProblem',  
        0 as 'IsStock',  
        1 as 'IsScan',  
        null as 'ReconciliationTypeId'    ,
        'Duplicate' as ReconciliationState
    
    
 UNION ALL   
   
        SELECT 'Unknown resolved' as 'Description',      
        (  
            SELECT count(sc.Id)   
            FROM Scans sc     
            INNER JOIN UnknownResolutions mr on mr.Id = sc.UnknownResolutionId     
            INNER JOIN Stockchecks stkck ON stkck.Id=sc.StockCheckId  AND stkck.Date = @StockCheckDate    
            INNER JOIN Sites si ON si.Id = stkck.SiteId    
            INNER JOIN Divisions di ON di.Id=si.DivisionId     
            INNER JOIN DealerGroup ON di.DealerGroupId=DealerGroup.Id    
            WHERE mr.IsResolved = 1   
            AND sc.IsDuplicate = 0   
            AND sc.StockItemId IS NULL   
            AND sc.ReconcilingItemId is null   
            AND DealerGroup.Id = @DealerGroupId  
        ) as 'VehicleCount',  
        0 as 'InStockValue',
		0 as 'Flooring',
        0 as 'IsFullHeight',  
        9 as 'Order',  
        0 as 'IsProblem',  
        0 as 'IsStock',  
        1 as 'IsScan',  
        null as 'ReconciliationTypeId'    ,
        'Resolved' as ReconciliationState
    
 UNION ALL   
   
        SELECT 'Unknown unresolved' as 'Description',     
        (  
            SELECT count(Scans.Id)   
            FROM Scans     
            INNER JOIN Stockchecks stkck ON stkck.Id=Scans.StockCheckId  AND stkck.Date = @StockCheckDate    
            INNER JOIN Sites si ON si.Id = stkck.SiteId    
            INNER JOIN Divisions di ON di.Id=si.DivisionId      
            INNER JOIN DealerGroup ON di.DealerGroupId=DealerGroup.Id    
            WHERE StockItemId is null   
            AND Scans.ReconcilingItemId is null   
            AND UnknownResolutionId is null   
            AND Scans.IsDuplicate = 0   
            AND DealerGroup.Id = @DealerGroupId  
        )     
          
        +     
      
        (   
            SELECT COUNT(sc.Id)   
            FROM Scans sc     
            INNER JOIN UnknownResolutions mr on mr.Id = sc.UnknownResolutionId    
            INNER JOIN Stockchecks stkck ON stkck.Id=sc.StockCheckId  AND stkck.Date = @StockCheckDate    
            INNER JOIN Sites si ON si.Id = stkck.SiteId    
            INNER JOIN Divisions di ON di.Id=si.DivisionId      
            INNER JOIN DealerGroup ON di.DealerGroupId=DealerGroup.Id    
            WHERE mr.IsResolved = 0   
            AND StockItemId IS NULL   
            AND ReconcilingItemId IS NULL   
            AND sc.IsDuplicate = 0   
            AND DealerGroup.Id = @DealerGroupId  
        ) as 'VehicleCount',  
        0 as 'InStockValue',
		0 as 'Flooring',
        0 as 'IsFullHeight',  
        10 as 'Order',  
        1 as 'IsProblem',  
        0 as 'IsStock',  
        1 as 'IsScan',  
        null as 'ReconciliationTypeId'    ,
        'OutstandingIssue' as ReconciliationState
    
 UNION ALL   
   
        SELECT 'Scanned' as 'Description',   
        (  
            SELECT count(Scans.Id)   
            FROM Scans     
            INNER JOIN Stockchecks stkck ON stkck.Id=Scans.StockCheckId  AND stkck.Date = @StockCheckDate    
            INNER JOIN Sites si ON si.Id = stkck.SiteId    
            INNER JOIN Divisions di ON di.Id=si.DivisionId     
            INNER JOIN DealerGroup ON di.DealerGroupId=DealerGroup.Id    
            WHERE DealerGroup.Id = @DealerGroupId  
        ) as 'VehicleCount' ,  
        0 as 'InStockValue',  
		0 as 'Flooring', 
        1 as 'IsFullHeight',  
        11 as 'Order',  
        0 as 'IsProblem',  
        0 as 'IsStock',  
        1 as 'IsScan',  
        null as 'ReconciliationTypeId'    ,
        'AllItems' as ReconciliationState
    
 END    
    
END    
    
    
GO
  

--############################## Next SP #############################################﻿

CREATE OR ALTER PROCEDURE [dbo].[GET_ReconcilingItemArray]
(
    @StockCheckId INT = NULL,
    @UserId INT = NULL,
    @ReconcilingItemTypeId INT = NULL
)
AS
BEGIN

SET NOCOUNT ON;

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END


SELECT ri.[Id]
    ,ri.[ReconcilingItemTypeId]
    ,ri.[Reg]
    ,ri.[Vin]
    ,ri.[Description]
    ,ri.[Comment]
    ,ri.[Reference]
    ,ri.[SourceReportId]
    ,ri.[StockCheckId]
	,IIF(s.Id IS NOT NULL, 1, 0) AS IsScanned
	,s.Id AS ScanId
    ,fi.FileName
    ,fi.FileDate
    ,fi.LoadDate
	,u.Name As UserName
FROM [dbo].[ReconcilingItems] ri
LEFT JOIN Scans s ON s.ReconcilingItemId = ri.Id
LEFT JOIN [import].[FileImports] fi ON fi.Id = ri.FileImportId
LEFT JOIN Users u ON u.Id = fi.LoadedByUserId
WHERE ri.StockCheckId = @StockCheckId
AND ri.ReconcilingItemTypeId = @ReconcilingItemTypeId

END


--############################## Next SP #############################################SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].[GET_ReconcilingItemBackups]
(
    @StockCheckId INT,
	@ReconcilingItemTypeId INT,
	@UserId INT
)
AS
BEGIN

SET NOCOUNT ON;

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END

SELECT * FROM [dbo].[ReconcilingItemBackups]
WHERE StockCheckId = @StockCheckId AND ReconcilingItemTypeId = @ReconcilingItemTypeId

END

GO


--############################## Next SP #############################################SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].GET_ReconcilingItemMissingsMatchItems
(
    @StockCheckId INT,
	@UserId INT
)
AS
BEGIN

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END

SELECT
r.Id as ItemId,
r.Reg,
r.Vin
FROM ReconcilingItems r
INNER JOIN StockChecks sc on sc.Id = r.StockCheckId AND sc.Id = @StockCheckId
INNER JOIN ReconcilingItemTypes rt on rt.Id = r.ReconcilingItemTypeId
WHERE rt.ExplainsMissingVehicle=1
ORDER BY r.Id


END


GO





--############################## Next SP #############################################/****** Object:  StoredProcedure [dbo].[GET_ReconcilingItems]    Script Date: 31/03/21 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].GET_ReconcilingItems
(
    @StockCheckId INT = NULL,
    @UserId INT = NULL
)
AS
BEGIN

SET NOCOUNT ON;

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END


DECLARE @isRegional INT;
SET @isRegional = (SELECT IsRegional FROM StockChecks
                    WHERE StockChecks.Id = @StockCheckId)

DECLARE @isTotal INT;
SET @isTotal = (SELECT IsTotal FROM StockChecks
                    WHERE StockChecks.Id = @StockCheckId)

IF @isRegional = 0 AND @isTotal = 0

    BEGIN
	
    SELECT Id, Reg, Vin, Description, Comment FROM ReconcilingItems
    WHERE ReconcilingItems.StockCheckId = @StockCheckId;

    END

IF @isRegional = 1 AND @isTotal = 0

    BEGIN

    DECLARE @DivisionId INT;

    SET @DivisionId = (SELECT DivisionId FROM StockChecks
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id
                        WHERE StockChecks.Id = @StockCheckId AND StockChecks.IsRegional = 1)
	
    SELECT ReconcilingItems.Id, Reg, Vin, ReconcilingItems.Description, Comment FROM ReconcilingItems
    INNER JOIN StockChecks ON StockChecks.Id=ReconcilingItems.StockCheckId
    INNER JOIN Sites ON StockChecks.SiteId=Sites.Id
    INNER JOIN Divisions ON Divisions.Id=Sites.DivisionId
    WHERE Divisions.Id = @DivisionId

    END

IF @isRegional = 0 AND @isTotal = 1

    BEGIN

    DECLARE @DealerGroupId INT;
    
    SET @DealerGroupId = (SELECT DealerGroupId FROM StockChecks
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id
                        WHERE StockChecks.Id = @StockCheckId)
	
    SELECT ReconcilingItems.Id, Reg, Vin, ReconcilingItems.Description, Comment FROM ReconcilingItems
    INNER JOIN StockChecks ON StockChecks.Id=ReconcilingItems.StockCheckId
    INNER JOIN Sites ON StockChecks.SiteId=Sites.Id
    INNER JOIN Divisions ON Divisions.Id=Sites.DivisionId
    INNER JOIN DealerGroup ON Divisions.DealerGroupId=DealerGroup.Id
    WHERE DealerGroup.Id = @DealerGroupId

    END
	

END

GO




--To use this run 
--exec [GET_ReconcilingItems] @StockCheckId = 1, @UserId = 104

--############################## Next SP #############################################/****** Object:  StoredProcedure [dbo].[GET_ReconcilingItemsWithType]    Script Date: 22/03/21 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].GET_ReconcilingItemsWithType
(
    @StockCheckId INT = NULL,
    @UserId INT = NULL
)
AS
BEGIN

SET NOCOUNT ON;

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END


	
SELECT 
recitems.Id, 
Reg, 
Vin, 
recitems.Description, 
Comment, 
rectypes.Id as ReconcilingItemTypeId, 
rectypes.Description as ReconcilingItemTypeDescription
FROM ReconcilingItems recitems
INNER JOIN ReconcilingItemTypes rectypes on recitems.ReconcilingItemTypeId = rectypes.Id
WHERE recitems.StockCheckId = @StockCheckId;



END

GO



--To use this run 
--exec [GET_ReconcilingItemsWithType] @StockCheckId = 1, @UserId = 104

--############################## Next SP #############################################﻿/****** Object:  StoredProcedure [dbo].[GET_ReconcilingItemTypeStats]    Script Date: 18/03/21 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].GET_ReconcilingItemTypeStats
(
    @StockCheckId INT = NULL,
	@UserId INT = NULL
)
AS
BEGIN

SET NOCOUNT ON

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END

	

  
--with @recitemstotal table(id int, vehicleCount int) as
SELECT reconcilingItemTypeId, count(Id) AS 'vehicleCount'  
INTO #recitemstotal
FROM ReconcilingItems WHERE StockCheckId=@StockCheckId group by ReconcilingItemTypeId


SELECT 
recTypes.Id, 
recTypes.Description,
recTypes.ExplainsMissingVehicle as ExplainsMissing,
recTypes.SortOrder,
ISNULL(counts.vehicleCount,0) AS VehicleCount,
recTypes.IsActive
FROM ReconcilingItemTypes recTypes
left join #recitemstotal counts ON counts.reconcilingItemTypeId = rectypes.id
WHERE DealerGroupId = (SELECT DealerGroupId FROM Users WHERE Id = @UserId)
AND (recTypes.IsActive = 1 OR ISNULL(counts.vehicleCount,0) > 0)

DROP TABLE #recitemstotal

	

END

GO




--To use this run 
--exec [GET_ReconcilingItemTypeStats] @StockCheckId = 99, @UserId = 1

--############################## Next SP #############################################SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].GET_ReconcilingItemUnknownsMatchItems
(
    @StockCheckId INT,
	@UserId INT
)
AS
BEGIN

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END


SELECT
r.Id as ItemId,
r.Reg,
r.Vin
FROM ReconcilingItems r
INNER JOIN StockChecks sc on sc.Id = r.StockCheckId AND sc.Id = @StockCheckId
INNER JOIN ReconcilingItemTypes rt on rt.Id = r.ReconcilingItemTypeId
WHERE rt.ExplainsMissingVehicle=0
ORDER BY r.Id

END


GO



--############################## Next SP #############################################﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].GET_RecProcessMatchChoice
(
    @StockCheckId INT = NULL,
	@UserId INT = NULL
)
AS
BEGIN

SET NOCOUNT ON

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END

	
SELECT
gb.BoolValue
FROM StockChecks sc
INNER JOIN Sites si on si.Id = sc.SiteId
INNER JOIN Divisions div on div.id = si.DivisionId
LEFT JOIN GlobalParams gb on gb.DealerGroupId = div.DealerGroupId
WHERE sc.Id = @StockCheckId
AND gb.Name = 'recProcessMatchRequiresBothRegAndVin'

	

END
GO






--############################## Next SP #############################################﻿/****** Object:  StoredProcedure [dbo].[GET_RepeatUnknown]    Script Date: 22/3/21 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].GET_RepeatUnknown
(
    @StockCheckId INT = NULL,
    @UserId INT = NULL
)
AS
BEGIN

SET NOCOUNT ON;

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END


SELECT [Id]
        ,[ScanId]
        ,[ReconcilingItemId]
        ,[MissingResolutionId]
        ,[StockCheckId]
        ,[SourceReportId]
        ,[Reg]
        ,[Vin]
        ,[Description]
        ,[DIS]
        ,[GroupDIS]
        ,[Branch]
        ,[Comment]
        ,[StockType]
        ,[Reference]
        ,[StockValue]
FROM [dbo].[StockItems] 
WHERE StockCheckId = @StockCheckId 	


END

GO




--To use this run 
--exec [GET_RepeatUnknown] @StockCheckId = 1,  @UserId = 1

--############################## Next SP #############################################﻿/****** Object:  StoredProcedure [dbo].[GET_ResolutionBuckets]    Script Date: 11/08/2021 11:56:01 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].[GET_ResolutionBuckets]
(
    @StockCheckId INT = NULL,
	@UserId INT = NULL
)
AS
BEGIN

SET NOCOUNT ON;


DECLARE @isRegional INT;  
DECLARE @isTotal INT;  
DECLARE @StockCheckDate DateTime;
DECLARE @DivisionId INT = NULL;
DECLARE @DealerGroupId INT = NULL;
DECLARE @SCId INT = NULL;


	
IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END


 SET @DealerGroupId = (SELECT DealerGroupId FROM StockChecks  
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId  
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id  
                        WHERE StockChecks.Id = @StockCheckId)  

--SET VARIABLES BASED ON THE CHOSEN STOCKCHECK
SELECT 
@isRegional = IsRegional, 
@isTotal = IsTotal, 
@StockCheckDate = Date 
FROM StockChecks  
WHERE StockChecks.Id = @StockCheckId
 

--IF SITE
IF @isRegional = 0 AND @isTotal = 0  
  
    BEGIN  
	SET @SCId = @StockCheckId;
    END  

--REGIONAL  
ELSE IF @isRegional = 1 AND @isTotal = 0  
  
    BEGIN  
    SET @DivisionId = (SELECT DivisionId FROM StockChecks  
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId  
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id  
                        WHERE StockChecks.Id = @StockCheckId AND StockChecks.IsRegional = 1)  
    END  

--TOTAL  
ELSE IF @isRegional = 0 AND @isTotal = 1  
  
    BEGIN  
    SET @DealerGroupId = (SELECT DealerGroupId FROM StockChecks  
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId  
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id  
                        WHERE StockChecks.Id = @StockCheckId)  
    END 




	--find all the unknowns that have been resolved
	DECLARE @unknownsresolved table(resTypeId int, vehicleCount int)
	INSERT INTO @unknownsresolved (resTypeId,vehicleCount) 
	( 
		SELECT rt.Id, 
		Count(sc.Id) as vehicleCount
		FROM SCANS sc
		INNER JOIN UnknownResolutions ur on ur.id = sc.UnknownResolutionId
		INNER join ResolutionTypes rt on rt.Id = ur.ResolutionTypeId
		INNER JOIN Stockchecks ON Stockchecks.Id=sc.StockCheckId 
		INNER JOIN Sites ON Sites.Id=StockChecks.SiteId
		INNER JOIN Divisions AS D ON D.Id=Sites.DivisionId
		INNER JOIN DealerGroup AS DG ON D.DealerGroupId=DG.Id
	
		WHERE 	UnknownResolutionId is not null 
		AND ur.IsResolved = 1 
		AND ReconcilingItemId IS NULL 
		AND sc.IsDuplicate = 0 
		AND sc.StockItemId IS NULL
		AND Stockchecks.Id = ISNULL(@SCId, Stockchecks.Id)
		AND Stockchecks.Date = @StockCheckDate
		AND D.Id = ISNULL(@DivisionId, D.Id)
		AND DG.Id = ISNULL(@DealerGroupId, DG.Id)
		GROUP BY rt.Id
	)


	--find all the missings that have been resolved
	DECLARE @missingsresolved table(resTypeId int, vehicleCount int, inStockValue float)
	INSERT INTO @missingsresolved (resTypeId,vehicleCount,inStockValue) 
	( 
		SELECT rt.Id, Count(st.Id), ROUND(SUM(st.StockValue), 2) as inStockValue 
		FROM StockItems st
		INNER JOIN MissingResolutions mr on mr.id = st.MissingResolutionId
		INNER JOIN ResolutionTypes rt on rt.Id = mr.ResolutionTypeId
		INNER JOIN Stockchecks ON Stockchecks.Id=st.StockCheckId 
		INNER JOIN Sites ON Sites.Id=StockChecks.SiteId
		INNER JOIN Divisions AS D ON D.Id=Sites.DivisionId
		INNER JOIN DealerGroup AS DG ON D.DealerGroupId=DG.Id
		WHERE MissingResolutionId is not null AND ReconcilingItemId IS NULL and mr.IsResolved =1 and st.ScanId is null 
		AND Stockchecks.Id = ISNULL(@SCId, Stockchecks.Id)
		AND Stockchecks.Date = @StockCheckDate
		AND D.Id = ISNULL(@DivisionId, D.Id)
		AND DG.Id = ISNULL(@DealerGroupId, DG.Id)
		GROUP BY rt.Id
	)


	--find the total count of unknowns not resolved  
	 DECLARE @unknownsnotresolved table(vehicleCount int)
	 INSERT INTO @unknownsnotresolved (vehicleCount)  
	 SELECT
	 (
		SELECT count(S.Id) 
		FROM Scans as S
		INNER JOIN Stockchecks ON Stockchecks.Id=S.StockCheckId 
		INNER JOIN Sites ON Sites.Id=StockChecks.SiteId
		INNER JOIN Divisions AS D ON D.Id=Sites.DivisionId
		INNER JOIN DealerGroup AS DG ON D.DealerGroupId=DG.Id
		WHERE  Stockchecks.Id = ISNULL(@SCId, Stockchecks.Id)
		AND Stockchecks.Date = @StockCheckDate
		AND D.Id = ISNULL(@DivisionId, D.Id)
		AND DG.Id = ISNULL(@DealerGroupId, DG.Id)
		AND StockItemId is null 
		AND ReconcilingItemId is null 
		AND UnknownResolutionId is null 
		AND IsDuplicate = 0 
	 )  

	 +   

	 (
		SELECT count(sc.Id) 
		FROM Scans sc 
		INNER JOIN UnknownResolutions mr on mr.Id = sc.UnknownResolutionId 
		INNER JOIN Stockchecks ON Stockchecks.Id=sc.StockCheckId 
		INNER JOIN Sites ON Sites.Id=StockChecks.SiteId
		INNER JOIN Divisions AS D ON D.Id=Sites.DivisionId
		INNER JOIN DealerGroup AS DG ON D.DealerGroupId=DG.Id
		WHERE mr.IsResolved = 0 
		AND StockItemId IS NULL 
		AND ReconcilingItemId IS NULL 
		AND IsDuplicate = 0
		AND  Stockchecks.Id = ISNULL(@SCId, Stockchecks.Id)
		AND Stockchecks.Date = @StockCheckDate
		AND D.Id = ISNULL(@DivisionId, D.Id)
		AND DG.Id = ISNULL(@DealerGroupId, DG.Id)
	  )    
  
	 
	 
	 --find the total count of missings not resolved  
	 declare @missingsnotresolved table (vehicleCount int)
	 INSERT INTO @missingsnotresolved (vehicleCount)
	 SELECT
	 (
		SELECT count(SI.Id) 
		FROM stockitems AS SI
		INNER JOIN Stockchecks ON Stockchecks.Id=SI.StockCheckId 
		INNER JOIN Sites ON Sites.Id=StockChecks.SiteId
		INNER JOIN Divisions AS D ON D.Id=Sites.DivisionId
		INNER JOIN DealerGroup AS DG ON D.DealerGroupId=DG.Id
		WHERE ScanId is null 
		AND ReconcilingItemId is null 
		AND MissingResolutionId is null 
		AND IsDuplicate = 0 
		AND  Stockchecks.Id = ISNULL(@SCId, Stockchecks.Id)
		AND Stockchecks.Date = @StockCheckDate
		AND D.Id = ISNULL(@DivisionId, D.Id)
		AND DG.Id = ISNULL(@DealerGroupId, DG.Id)
	 ) 

	 +   

	 (
		SELECT count(SI.Id) 
		FROM StockItems SI 
		INNER JOIN MissingResolutions mr on mr.Id = SI.MissingResolutionId 
		INNER JOIN Stockchecks ON Stockchecks.Id=SI.StockCheckId 
		INNER JOIN Sites ON Sites.Id=StockChecks.SiteId
		INNER JOIN Divisions AS D ON D.Id=Sites.DivisionId
		INNER JOIN DealerGroup AS DG ON D.DealerGroupId=DG.Id
		WHERE 	 mr.IsResolved = 0 
		AND SI.ScanId is null 
		AND IsDuplicate = 0 
		AND SI.ReconcilingItemId is null
		AND  Stockchecks.Id = ISNULL(@SCId, Stockchecks.Id)
		AND Stockchecks.Date = @StockCheckDate
		AND D.Id = ISNULL(@DivisionId, D.Id)
		AND DG.Id = ISNULL(@DealerGroupId, DG.Id)
	 )  
  
  

	 SELECT 
	 resTypes.Description, 
	 ISNULL(ur.vehicleCount,0) as 'VehicleCount', 
	 0 as InStockValue,
	 0 as 'ExplainsMissing'  
	 FROM ResolutionTypes resTypes  
	 LEFT JOIN @unknownsresolved ur on ur.resTypeId = resTypes.Id 
	 WHERE resTypes.ExplainsMissingVehicle = 0 
	 AND resTypes.DealerGroupId = @DealerGroupId


	UNION ALL

	SELECT 'Unknowns not resolved' as Description, 
	vehicleCount as VehicleCount, 
	0 as InStockValue,
	0 as ExplainsMissing
	FROM @unknownsnotresolved

	UNION ALL

	SELECT resTypes.Description, 
	ISNULL(mr.vehicleCount,0) as 'VehicleCount', 
	ISNULL(mr.inStockValue,0) as 'InStockValue',
	1 as 'ExplainsMissing'
	FROM ResolutionTypes resTypes
	LEFT JOIN @missingsresolved mr on mr.resTypeId = resTypes.Id
	WHERE resTypes.ExplainsMissingVehicle = 1 and resTypes.DealerGroupId = @DealerGroupId

	UNION ALL

	SELECT 'Missings not resolved' as Description, 
	vehicleCount as VehicleCount, 
	0 as InStockValue,
	1 as ExplainsMissing
	FROM @missingsnotresolved




END



--############################## Next SP #############################################﻿/****** Object:  StoredProcedure [dbo].[GET_ResolutionTypes]    Script Date: 10/3/21 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].GET_ResolutionTypes
(
    @UserId INT = NULL
)
AS
BEGIN

SET NOCOUNT ON


select * from ResolutionTypes where DealerGroupId = (Select DealerGroupId from Users where Id= @UserId)


	
	

END

GO



--To use this run 
--exec [GET_Scans] @StockCheckId = 1

--############################## Next SP #############################################﻿  
CREATE OR ALTER PROCEDURE [dbo].[GET_Scans]  
(  
    @StockCheckId INT = NULL,  
    @UserId INT = NULL  
)  
AS  
BEGIN  
   
SET NOCOUNT ON;    
  
DECLARE @isRegional INT;    
DECLARE @isTotal INT;    
DECLARE @StockCheckDate DateTime;  
DECLARE @DivisionId INT = NULL;  
DECLARE @DealerGroupId INT = NULL;  
DECLARE @SCId INT = NULL;  
  


--Early return if no access
IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0 
BEGIN 
	RETURN
END

  
SELECT   
@isRegional = IsRegional,   
@isTotal = IsTotal,   
@StockCheckDate = Date   
FROM StockChecks    
WHERE StockChecks.Id = @StockCheckId  
    


--Still here so continue  
IF @isRegional = 0 AND @isTotal = 0    
    
    BEGIN    
  
 SET @SCId = @StockCheckId;  
    
  
    END    
    
ELSE IF @isRegional = 1 AND @isTotal = 0    
    
    BEGIN    
    
    
    SET @DivisionId = (SELECT DivisionId FROM StockChecks    
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId    
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id    
                        WHERE StockChecks.Id = @StockCheckId AND StockChecks.IsRegional = 1)    
    
    END    
    
ELSE IF @isRegional = 0 AND @isTotal = 1    
    
    BEGIN    
    
        
    SET @DealerGroupId = (SELECT DealerGroupId FROM StockChecks    
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId    
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id    
                        WHERE StockChecks.Id = @StockCheckId)    
    
    
    END   
  
SELECT Scans.Id  
,Locations.[Description] AS locationDescription  
,Users.Name AS scannerName  
,Scans.[UserId]  
,[StockCheckId]  
,[LastEditedById]  
,[LocationId]  
,[UnknownResolutionId]  
,[StockItemId]  
,[ReconcilingItemId]  
,[LastEditedDateTime]  
,[RegConfidence]  
,[VinConfidence]  
--,[IsEdited]  
,Scans.[Longitude]  
,Scans.[Latitude]  
,[ScanDateTime]  
,[Comment]  
,[Reg]  
,[Vin]  
,Scans.[Description]  
,[CoordinatesJSON]  
,[HasVinImage]  
,[IsDuplicate]  
,Sites.[Description] AS SiteName  
FROM [dbo].[Scans]   
INNER JOIN Users ON Users.Id=Scans.UserId  
INNER JOIN Locations ON Locations.Id=Scans.LocationId  
INNER JOIN StockChecks AS SC ON SC.Id=Scans.StockCheckId  
INNER JOIN Sites ON Sites.Id=SC.SiteId  
INNER JOIN Divisions AS D ON D.Id=Sites.DivisionId  
INNER JOIN DealerGroup AS DG ON D.DealerGroupId=DG.Id  
WHERE   
--StockCheckId = @StockCheckId  
SC.Id = ISNULL(@SCId, SC.Id)  
AND SC.Date = @StockCheckDate  
AND D.Id = ISNULL(@DivisionId, D.Id)  
AND DG.Id = ISNULL(@DealerGroupId, DG.Id)  



END  
  
  
  
  
  

--############################## Next SP #############################################﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


  
CREATE OR ALTER PROCEDURE [dbo].[GET_ScanFullDetail]  
(  
    @ScanId INT = NULL,  
    @UserId INT NULL,
	@SkipAuthCheck bit = 0
)  
AS  
BEGIN  
  
SET NOCOUNT ON  
  
IF (dbo.[AuthenticateUser](@UserId, (select StockCheckId from dbo.Scans where Id = @ScanId)) = 0 AND @SkipAuthCheck = 0)
BEGIN 
    RETURN
END

   
DECLARE @DealerGroupId INT;
SET @DealerGroupId = (SELECT DealerGroupId FROM Users WHERE Id = @UserId)

DECLARE @StockCheckId INT = (SELECT StockCheckId FROM Scans WHERE Id = @ScanId)

--work out first instance of same scans
  SELECT
  sc.Reg,sc.Vin,
  MIN(sc.Id) as Id
  INTO #UniqueIds
  FROM Scans sc
  INNER JOIN StockChecks  Sto ON Sto.Id=Sc.StockCheckId 
  WHERE Sto.Id = @StockCheckId
  AND sc.IsDuplicate = 0
  GROUP BY sc.Reg,sc.Vin

  --find out everything about that first instance
  SELECT
  sca.Id, sca.StockCheckId, sca.Reg,sca.Vin,loc.Description as LocationDescription,us.Name as ScannedBy,sca.ScanDateTime
  INTO #firstItems
  FROM Scans sca
  INNER JOIN #uniqueIds u on u.id = sca.id
  INNER JOIN Locations loc on loc.id = sca.LocationId
  INNER JOIN Users us on us.Id = sca.UserId
  DROP TABLE #uniqueIds



 SELECT   
        scns.Id as ScanId, 
      Locations.[Description] AS LocationDescription,
      Users.Name AS ScannerName  ,
      scns.[RegConfidence]  ,
      scns.[VinConfidence]  ,
      scns.Longitude,
      scns.Latitude,
      scns.ScanDateTime  ,
      scns.[Comment] as ScanComment ,
      scns.[Reg] as ScanReg ,
      scns.[Vin] as ScanVin ,
      scns.[Description] as ScanDescription ,
      scns.[HasVinImage]  ,
      CASE
	    WHEN scns.IsDuplicate = 1 THEN 'Duplicate'
	    WHEN sto.Id IS NOT NULL AND sto.StockCheckId <> scns.StockCheckId THEN 'MatchedToOtherSite'
	    WHEN sto.Id IS NOT NULL AND sto.StockCheckId = scns.StockCheckId THEN  'MatchedToStockOrScan'
	    WHEN scns.ReconcilingItemId IS NOT NULL THEN 'MatchedToReport'
	    WHEN res.Id IS NOT NULL AND res.IsResolved = 1 THEN 'Resolved'
      ELSE 'OutstandingIssue'
      END as ScanState,

      sites.Description as SiteName,

      --matching stock item detail
      sto.Id as StockItemId,
      sti.Reg,
      sti.Vin,
      sti.Description,
      sti.DIS,
      sti.GroupDIS,
      sti.Branch,
      sti.STockType,
      sti.Comment,
      sti.Reference,
      sti.StockValue,
      CASE
	    WHEN sto.Id IS NOT NULL AND sto.StockCheckId <> scns.StockCheckId THEN 'MatchedToOtherSite'
	        WHEN sto.Id IS NOT NULL AND sto.StockCheckId = scns.StockCheckId THEN  'MatchedToStockOrScan'
	    ELSE NULL
        END as State,

      
      --Reconciling item detail
      scns.[ReconcilingItemId]  ,
       rectypes.Id as ReconcilingItemTypeId,  
       recitems.[Reg] AS ReconcilingItemReg,
        recitems.[Vin] AS ReconcilingItemVin,
       rectypes.Description as ReconcilingItemTypeDescription,  
        recitems.Description as ReconcilingItemDesc,  
        recitems.Comment as ReconcilingItemComment,  
        recitems.Reference as ReconcilingItemReference  ,


        --Resolution Detail
      scns.[UnknownResolutionId] as ResolutionId ,
        restypes.Id as ResolutionTypeId,  
        restypes.Description as ResolutionTypeDescription,  
        restypes.BackupRequired as ResolutionTypeBackup, 
        res.IsResolved,  
        usrsRes.Name as ResolvedBy,  
        res.ResolutionDateTime as ResolutionDate,  
        res.Notes as ResolutionNotes,  
        (select String_Agg(CONCAT(Id,'|',ISNULL([FileName],'FileName')),'::') from UnknownResolutionImages where UnknownResolutionId= res.Id) as ResolutionImageIds,  

        --If matched to other site, the other site name
        IIF(sti.Id IS NOT NULL AND siScheck.id <> scns.StockCheckId, siSite.Description, '') as OtherSiteName,

        --original item stuff if it's a duplicate
        IIF(scns.IsDuplicate=1,f.Id,null) as OriginalId,
        IIF(scns.IsDuplicate=1,f.LocationDescription,null) as OriginalLocationDescription,
        IIF(scns.IsDuplicate=1,f.ScannedBy,null) as OriginalScannedBy,
        IIF(scns.IsDuplicate=1,f.ScanDateTime,null) as OriginalScannedDate,

	scns.[IsRegEditedOnDevice],
	scns.[IsRegEditedOnWeb],
	scns.[InterpretedReg],
	scns.[IsVinEditedOnDevice],
	scns.[IsVinEditedOnWeb],
	scns.[InterpretedVin],
    scns.[StockCheckId]
  
  
  FROM [dbo].[Scans] scns  
  INNER JOIN Users ON Users.Id=scns.UserId  
  INNER JOIN Locations ON Locations.Id=scns.LocationId  
  INNER JOIN StockChecks schek on schek.Id = scns.StockCheckId
  INNER JOIN Sites sites on sites.Id = schek.SiteId
  
  LEFT JOIN StockItems sto ON sto.Id = scns.StockItemId and (scns.IsDuplicate = 0)

  --Resolutions joins
  LEFT JOIN UnknownResolutions res on res.Id = scns.UnknownResolutionId   
  LEFT JOIN ResolutionTypes restypes on restypes.Id = res.ResolutionTypeId  
  LEFT JOIN Users usrsRes on usrsRes.Id = res.UserId  
  
  --Reconciling item joins
  LEFT JOIN ReconcilingItems recitems on  scns.ReconcilingItemId =recitems.Id   
  LEFT JOIN ReconcilingItemTypes rectypes on recitems.ReconcilingItemTypeId=rectypes.Id   

  --StockItem joins
  LEFT JOIN StockItems sti on sti.Id = scns.StockItemId
  LEFT JOIN StockChecks siScheck on siScheck.Id = sti.StockCheckId
  LEFT JOIN Sites siSite on siSite.Id = siScheck.SiteId

  --duplicate item joins
  LEFT JOIN #firstItems f on (
	(f.Reg <> '' AND f.Reg = scns.Reg) OR 
	(f.Vin <> '' AND f.Vin = scns.Vin)
	) AND f.StockCheckId = scns.StockCheckId

  WHERE scns.Id = @ScanId 
  AND Users.DealerGroupId = @DealerGroupId
  

   
   
  
END  
  
GO




--############################## Next SP #############################################

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE OR ALTER PROCEDURE [dbo].[GET_ScanLocationsForSiteId]
(
    @SiteId INT = NULL
)
AS
BEGIN

SET NOCOUNT ON

SELECT l.[Id]
      ,l.[Description]
  FROM [dbo].[SiteLocations] As sl
  INNER JOIN [dbo].[Locations] AS l ON l.Id = sl.LocationId
  WHERE sl.SiteId = @SiteId

END

GO

--############################## Next SP #############################################  
CREATE OR ALTER PROCEDURE [dbo].[GET_ScanMatchedToOtherSiteStockItem]  
(  
    @StockCheckId INT = NULL,  
    @UserId INT = NULL  
)  
AS  
BEGIN  
  
SET NOCOUNT ON;  
  
  
DECLARE @accessVar INT;    
DECLARE @isRegional INT;    
DECLARE @isTotal INT;    
DECLARE @StockCheckDate DateTime;  
DECLARE @DivisionId INT = NULL;  
DECLARE @DealerGroupId INT = NULL;  
DECLARE @SCId INT = NULL;  
  
  
IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END


  
SELECT   
@isRegional = IsRegional,   
@isTotal = IsTotal,   
@StockCheckDate = Date   
FROM StockChecks    
WHERE StockChecks.Id = @StockCheckId  
    

    
IF @isRegional = 0 AND @isTotal = 0    
    
    BEGIN    
  
 SET @SCId = @StockCheckId;  
    
  
    END    
    
ELSE IF @isRegional = 1 AND @isTotal = 0    
    
    BEGIN    
    
    
    SET @DivisionId = (SELECT DivisionId FROM StockChecks    
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId    
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id    
                        WHERE StockChecks.Id = @StockCheckId AND StockChecks.IsRegional = 1)    
    
    END    
    
ELSE IF @isRegional = 0 AND @isTotal = 1    
    
    BEGIN    
    
        
    SET @DealerGroupId = (SELECT DealerGroupId FROM StockChecks    
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId    
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id    
                        WHERE StockChecks.Id = @StockCheckId)    
    
    
    END   
  
  
  
        SELECT Scans.Id as ScanId 
            ,Locations.[Description] AS locationDescription  
            ,Users.Name AS scannerName  
            ,Scans.[UserId]  
            ,scans.[StockCheckId] AS StockcheckId  
            ,[LastEditedById]  
            ,[LocationId]  
            ,scans.[UnknownResolutionId]  
            ,[StockItemId]  
            ,scans.[ReconcilingItemId] AS ReconcilingItemId  
            ,[LastEditedDateTime]  
            ,[RegConfidence]  
            ,[VinConfidence]
            --,[IsEdited]  
            ,Scans.[Longitude]  
            ,Scans.[Latitude]  
            ,[ScanDateTime]  
            ,scans.[Comment]  as ScanComment 
            ,scans.[Reg]  as ScanReg
            ,scans.[Vin]  as ScanVin
            ,Scans.[Description]  as ScanDescription
            ,[CoordinatesJSON]  
            ,[HasVinImage]  
            ,scans.[IsDuplicate]  
   ,SI.[Description] AS otherSiteDescription  
   ,SI.[Reference] AS matchingStockItemReference  
   ,SI.[Comment] AS matchingStockItemComment  
   ,SI.[StockType] AS matchingStockItemType  
   ,SI.[Description] AS matchingStockItemDescription
   
   ,stockCheckSite.[Description] AS MatchingSiteDescription  
   ,stockCheckSite.[Id] AS MatchingSiteId  
            ,Sites.Description AS SiteName  
            ,Sites.Longitude as StockCheckLongitude
            ,Sites.Latitude as StockCheckLatitude,

            CASE
                WHEN scans.IsRegEditedOnWeb = 1 THEN 'Web app'
                WHEN scans.IsRegEditedOnDevice = 1 THEN 'Mobile app'
                ELSE NULL
            END AS RegEditStatus,
            CASE
                WHEN scans.IsVinEditedOnWeb = 1 THEN 'Web app'
                WHEN scans.IsVinEditedOnDevice = 1 THEN 'Mobile app'
                ELSE NULL
            END AS VinEditStatus
        FROM [dbo].[Scans]  scans 
        INNER JOIN Users ON Users.Id=Scans.UserId  
        INNER JOIN Locations ON Locations.Id=Scans.LocationId  
        INNER JOIN StockItems AS SI ON SI.Id = Scans.StockItemId  
        INNER JOIN StockChecks AS SC ON SC.Id=Scans.StockCheckId  
        INNER JOIN Sites ON Sites.Id=SC.SiteId  
        INNER JOIN StockChecks as stockItemStockCheck on stockItemStockCheck.Id = si.StockCheckId
        INNER JOIN Sites stockCheckSite on stockCheckSite.Id = stockItemStockCheck.SiteId
  INNER JOIN Divisions AS D ON D.Id=Sites.DivisionId  
  INNER JOIN DealerGroup AS DG ON D.DealerGroupId=DG.Id  
        WHERE   
  --Scans.StockCheckId = @StockCheckId  
        SI.StockCheckId <> Scans.StockCheckId  
  AND SC.Id = ISNULL(@SCId, SC.Id)  
 AND SC.Date = @StockCheckDate  
 AND D.Id = ISNULL(@DivisionId, D.Id)  
 AND DG.Id = ISNULL(@DealerGroupId, DG.Id)  
    

  
END  
  

--############################## Next SP #############################################SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].GET_ScanMatchItems
(
    @StockCheckId INT,
	@UserId INT
)
AS
BEGIN

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END


SELECT
s.Id as ItemId,
s.Reg,
s.Vin
FROM Scans s
INNER JOIN StockChecks sc on sc.Id = s.StockCheckId AND sc.Id = @StockCheckId
ORDER BY s.Id

END



--############################## Next SP #############################################
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].GET_ScanMatchItemsOtherSites
(
    @StockCheckId INT,
	@UserId INT
)
AS
BEGIN

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END

DECLARE @stockcheckDate Date = (SELECT CONVERT(date,Date) FROM Stockchecks where Id = @StockCheckId);
DECLARE @dealerGroupId int = 
(
SELECT div.DealerGroupId
FROM Stockchecks sc 
INNER JOIN Sites s on s.Id = sc.SiteId
INNER JOIN Divisions div on div.Id = s.DivisionId
WHERE sc.Id = @StockCheckId
);

DECLARE @IntraGroupMatchingRange int = (SELECT numberValue FROM GlobalParams WHERE DealerGroupId = @dealerGroupId AND Name = 'IntraGroupMatchingRange')
DECLARE @DateFrom date = DATEADD(DAY, -@IntraGroupMatchingRange, @stockcheckDate)
DECLARE @DateTo date = DATEADD(DAY, @IntraGroupMatchingRange, @stockcheckDate)

SELECT
s.Id as ItemId,
s.Reg,
s.Vin
FROM Scans s
INNER JOIN StockChecks sc on sc.Id = s.StockCheckId 
INNER JOIN Sites si on si.id = sc.SiteId
INNER JOIN Divisions div on div.id = si.DivisionId
LEFT JOIN StockItems alreadyMatchedStockItems on alreadyMatchedStockItems.Id = s.StockItemId
LEFT JOIN Stockchecks scStockItem on scStockItem.Id = alreadyMatchedStockItems.StockCheckId

WHERE sc.Id <> @StockCheckId
AND div.DealerGroupId = @dealerGroupId
AND (s.StockItemId IS NULL OR scStockItem.Id = @StockCheckId)
AND s.IsDuplicate = 0
AND
(
	(@IntraGroupMatchingRange > 0 AND CONVERT(date, sc.Date) BETWEEN @DateFrom AND @DateTo)
    OR
    (@IntraGroupMatchingRange = 0 AND CONVERT(date, sc.Date) = @stockcheckDate)
)

ORDER BY s.Id


END

GO



--############################## Next SP #############################################/****** Object:  StoredProcedure [dbo].[GET_Scans]    Script Date: 17/02/2023 12:51:36 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].[GET_Scans]
(
    @StockCheckId INT = NULL,
    @UserId INT = NULL,
    @LimitToIds varchar(max) = NULL
)
AS
BEGIN
 
SET NOCOUNT ON;  

DECLARE @isRegional INT;  
DECLARE @isTotal INT;  
DECLARE @StockCheckDate DateTime;
DECLARE @DivisionId INT = NULL;
DECLARE @DealerGroupId INT = NULL;
DECLARE @SCId INT = NULL;


--Early return if no access
IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0 
BEGIN 
	RETURN
END


SELECT Value as Id INTO #limitIds from STRING_SPLIT(@LimitToIds,',')

SELECT 
@isRegional = IsRegional, 
@isTotal = IsTotal, 
@StockCheckDate = Date 
FROM StockChecks  
WHERE StockChecks.Id = @StockCheckId
  



--Still here so continue  
IF @isRegional = 0 AND @isTotal = 0  

    BEGIN  
    SET @SCId = @StockCheckId;
    END  

ELSE IF @isRegional = 1 AND @isTotal = 0  

    BEGIN  
    SET @DivisionId = (SELECT DivisionId FROM StockChecks  
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId  
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id  
                        WHERE StockChecks.Id = @StockCheckId AND StockChecks.IsRegional = 1)  
    END  

ELSE IF @isRegional = 0 AND @isTotal = 1  

    BEGIN  
    SET @DealerGroupId = (SELECT DealerGroupId FROM StockChecks  
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId  
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id  
                        WHERE StockChecks.Id = @StockCheckId)  
    END 





SELECT 
sca.Id as ScanId,
Locations.[Description] AS LocationDescription,
u.Name AS ScannerName,
sca.[RegConfidence],
sca.[VinConfidence],
sca.[Longitude],
sca.[Latitude],
sca.[ScanDateTime],
sca.[Comment] as ScanComment,
sca.[Reg] as ScanReg,
sca.[Vin] as ScanVin,
sca.[HasVinImage],
sca.Description as ScanDescription,
CASE
	WHEN sca.IsDuplicate = 1 THEN 'Duplicate'
	WHEN sto.Id IS NOT NULL AND sto.StockCheckId <> sca.StockCheckId THEN 'MatchedToOtherSite'
	WHEN sto.Id IS NOT NULL AND sto.StockCheckId = sca.StockCheckId THEN  'MatchedToStockOrScan'
	WHEN sca.ReconcilingItemId IS NOT NULL THEN 'MatchedToReport'
	WHEN ur.Id IS NOT NULL THEN 'Resolved'
	ELSE 'OutstandingIssue'
END as ScanState,
rt.Description as ResolutionTypeDescription,
si.Longitude as StockCheckLongitude,
si.Latitude as StockCheckLatitude,

CASE
    WHEN sca.IsRegEditedOnWeb = 1 THEN 'Web app'
    WHEN sca.IsRegEditedOnDevice = 1 THEN 'Mobile app'
    ELSE NULL
END AS RegEditStatus,
CASE
    WHEN sca.IsVinEditedOnWeb = 1 THEN 'Web app'
    WHEN sca.IsVinEditedOnDevice = 1 THEN 'Mobile app'
    ELSE NULL
END AS VinEditStatus,
si.Description AS SiteName

FROM [dbo].[Scans] sca
INNER JOIN Users u ON u.Id=sca.UserId
INNER JOIN Locations ON Locations.Id=sca.LocationId
INNER JOIN StockChecks AS st ON st.Id=sca.StockCheckId
INNER JOIN Sites si ON si.Id=ST.SiteId
INNER JOIN Divisions div  ON div.Id= si.DivisionId
INNER JOIN DealerGroup dg ON dg.Id=div.DealerGroupId
LEFT JOIN StockItems sto ON sto.Id = sca.StockItemId
LEFT JOIN UnknownResolutions ur on ur.id = sca.UnknownResolutionId AND ur.IsResolved = 1
LEFT JOIN ResolutionTypes rt on rt.id = ur.ResolutionTypeId
LEFT JOIN #limitIds lids on lids.id = sca.id
WHERE 
--StockCheckId = @StockCheckId
st.Id = ISNULL(@SCId, st.Id)
AND st.Date = @StockCheckDate
AND div.Id = ISNULL(@DivisionId, div.Id)
AND DG.Id = ISNULL(@DealerGroupId, DG.Id)
AND (@LimitToIds IS NULL OR lids.id IS NOT NULL)
ORDER BY sca.Id desc



END
GO





--GO




--############################## Next SP #############################################﻿/****** Object:  StoredProcedure [dbo].[GET_ScansByUser]    Script Date: 10/3/21 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO



CREATE OR ALTER PROCEDURE [dbo].GET_ScansByUser
(
    @StockCheckId INT = NULL,
    @UserId INT = NULL	
)
AS
BEGIN

SET NOCOUNT ON

--Early return if no access
IF dbo.[AuthenticateUser](@UserId, @StockCheckId)   = 0 
BEGIN 
	RETURN SELECT 0
END


--Still here so continue  
	
SELECT [Id]
      ,[UserId]
      ,[StockCheckId]
      ,[LastEditedById]
      ,[LocationId]
      ,[LastEditedDateTime]
      ,[RegConfidence]
      ,[VinConfidence]
      --,[IsEdited]
      ,[Longitude]
      ,[Latitude]
      ,[ScanDateTime]
      ,[Comment]
      ,[Reg]
      ,[Vin]
      ,[Description]
      ,[CoordinatesJSON]
      ,[HasVinImage]
  FROM [dbo].[Scans] 
  WHERE StockCheckId = @StockCheckId
  AND UserId = @UserId

	
	

END
GO




--To use this run 
--exec [GET_Scans] @StockCheckId = 1

--############################## Next SP #############################################﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
  
  
    
CREATE OR ALTER PROCEDURE [dbo].[GET_ScansForSpark]    
(    
    @DealerGroupId INT,    
 @Reg NVARCHAR(50) = NULL,    
 @Vin NVARCHAR(50) = NULL    
    
)    
AS    
BEGIN    
    
SET NOCOUNT ON    

  
    
    
DECLARE @ImageKeyValue nvarchar(500)    
DECLARE @ConfigFilePathValue NVARCHAR(500)    
    
SET @ImageKeyValue = (SELECT StringValue FROM GlobalParams WHERE DealerGroupId = @DealerGroupId AND [Name] = 'ImageKey')    
SET @ConfigFilePathValue = (SELECT StringValue FROM GlobalParams WHERE DealerGroupId = @DealerGroupId AND [Name] = 'ConfigFilePath')    



  
IF (@Reg = '' OR @Reg = ' ' OR @Reg = 'INPUT' OR @Reg = 'TBC')  
BEGIN   
 SET @Reg = NULL  
END  
  
IF (@Vin = '' OR @Vin = ' ' OR @Vin = 'INPUT' OR @Vin = 'TBC')  
BEGIN   
 SET @Vin = NULL  
END  

PRINT @Reg
PRINT @Vin
  

;WITH LatestStockChecks AS (SELECT SC.SiteId, SC.[Date],
ROW_NUMBER() OVER (PARTITION BY SC.SiteId
                              ORDER BY SC.[Date] DESC
                             ) as RowNumber
FROM StockChecks AS SC WITH (NOLOCK)  
INNER JOIN Sites AS SI WITH (NOLOCK) ON SI.Id=SC.SiteId  
INNER JOIN Divisions AS D WITH (NOLOCK) ON SI.DivisionId=D.Id  
INNER JOIN DealerGroup AS DG WITH (NOLOCK) ON D.DealerGroupId = DG.Id  
WHERE DG.Id = @DealerGroupId AND StatusId > 1)    
    
SELECT TOP 3 S.Id, S.Reg, S.Vin, U.Name,S.Description,    
CONCAT(@ConfigFilePathValue, S.Id, 'T.jpg?',@ImageKeyValue) AS URL,    
S.ScanDateTime, L.Description AS 'Location'
FROM Scans AS S WITH (NOLOCK)     
INNER JOIN StockChecks AS SC WITH (NOLOCK) ON S.StockCheckId = SC.Id    
INNER JOIN LatestStockChecks AS LSC ON SC.SiteId = LSC.SiteId AND SC.Date = LSC.Date  
INNER JOIN Sites AS SI WITH (NOLOCK) ON SI.Id=SC.SiteId    
INNER JOIN Divisions AS D WITH (NOLOCK) ON SI.DivisionId=D.Id    
INNER JOIN DealerGroup AS DG WITH (NOLOCK) ON D.DealerGroupId = DG.Id    
INNER JOIN Locations AS L WITH (NOLOCK) ON S.LocationId = L.Id    
LEFT JOIN Users AS U WITH (NOLOCK) ON S.UserId = U.Id    
WHERE   
LSC.RowNumber <=3
AND DG.Id = @DealerGroupId     
AND SC.IsRegional = 0 and SC.IsTotal = 0
AND (ISNULL(@Reg, '') != ISNULL(@Vin, '')) --both can't be null 
AND 
(
(ISNULL(@Reg, '') != '' AND ISNULL(@Reg, '') = CASE WHEN S.Reg = '' OR S.Reg = ' ' OR S.Reg = 'INPUT' OR S.Reg = 'TBC'  THEN '' ELSE S.Reg END)
OR (ISNULL(@Vin, '') != '' AND ISNULL(@Vin, '') = CASE WHEN S.Vin = '' OR S.Vin= ' ' OR S.Vin= 'INPUT' OR S.Vin= 'TBC'  THEN '' ELSE S.Vin END)
)

    
ORDER BY SC.Id DESC    
    
    
     
    
END    
    
  


--############################## Next SP #############################################﻿/****** Object:  StoredProcedure [dbo].[GET_ScansFromLatestStockchecksForSpark]    Script Date: 08/01/2022 13:21:10 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].[GET_ScansFromLatestStockchecksForSpark]
(
    @DealerGroupId INT
)
AS
BEGIN

SET NOCOUNT ON


;WITH LatestStockChecks AS (SELECT SC.SiteId, MAX(SC.[Date]) As [Date] FROM StockChecks AS SC WITH (NOLOCK)
INNER JOIN Sites AS SI WITH (NOLOCK) ON SI.Id=SC.SiteId
INNER JOIN Divisions AS D WITH (NOLOCK) ON SI.DivisionId=D.Id
INNER JOIN DealerGroup AS DG WITH (NOLOCK) ON D.DealerGroupId = DG.Id
WHERE DG.Id = @DealerGroupId AND StatusId > 1
GROUP BY SC.SiteId)


SELECT S.Id, S.Reg, S.Vin,loc.Description as Location,u.Name as ScannerName,S.ScanDateTime
FROM Scans AS S WITH (NOLOCK) 
INNER JOIN StockChecks AS SC WITH (NOLOCK) ON S.StockCheckId = SC.Id
INNER JOIN LatestStockChecks AS LSC ON SC.SiteId = LSC.SiteId AND SC.Date = LSC.Date
INNER JOIN Locations loc on loc.Id = S.LocationId
INNER JOIN Users u on u.Id = S.UserId

	

END



GO


--############################## Next SP #############################################SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO  
  
CREATE OR ALTER PROCEDURE [dbo].[GET_ScansMatchedToRecItem]  
(  
    @StockCheckId INT = NULL,  
    @UserId INT = NULL  
)  
AS  
BEGIN  
  
SET NOCOUNT ON;  
    
DECLARE @isRegional INT;    
DECLARE @isTotal INT;    
DECLARE @StockCheckDate DateTime;  
DECLARE @DivisionId INT = NULL;  
DECLARE @DealerGroupId INT = NULL;  
DECLARE @SCId INT = NULL;  
  
  
IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END


  
SELECT   
@isRegional = IsRegional,   
@isTotal = IsTotal,   
@StockCheckDate = Date   
FROM StockChecks    
WHERE StockChecks.Id = @StockCheckId  
    

  
IF @isRegional = 0 AND @isTotal = 0  
  
    BEGIN  
   
 SET @SCId = @StockCheckId;  
     
    END  
  
ELSE IF @isRegional = 1 AND @isTotal = 0  
   
    BEGIN  
  
    SET @DivisionId = (SELECT DivisionId FROM StockChecks  
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId  
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id  
                        WHERE StockChecks.Id = @StockCheckId AND StockChecks.IsRegional = 1)  
  
    END  
  
ELSE IF @isRegional = 0 AND @isTotal = 1  
  
    BEGIN  
      
    SET @DealerGroupId = (SELECT DealerGroupId FROM StockChecks  
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId  
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id  
                        WHERE StockChecks.Id = @StockCheckId)  
  
    END  
  
  SELECT  
    scns.Id as ScanId,
    scns.UserId,
    scns.StockCheckId,
    LastEditedById,
    LocationId,
    scns.UnknownResolutionId,
    StockItemId,
    ReconcilingItemId,
    IsDuplicate,
    LastEditedDateTime,
    RegConfidence,
    VinConfidence,
    --IsEdited,
    scns.Longitude,
    scns.Latitude,
    ScanDateTime,
    scns.Comment as ScanComment,
    scns.Reg as ScanReg,
    scns.Vin as ScanVin,
    scns.Description as ScanDescription,
    CoordinatesJSON,
    HasVinImage,  
    locns.Description as 'LocationDescription',  
    usrs.Name as 'ScannerName',  
  
    rectypes.Id as 'ReconcilingItemTypeId',  
    rectypes.Description as 'ReconcilingItemTypeDescription',  
    recitems.Description as 'RecItemDescription',  
    recitems.Comment as 'RecItemComment',  
    recitems.Reference as 'RecItemReference',
  
    Sites.Description AS SiteName,
    Sites.Longitude as StockCheckLongitude,
    Sites.Latitude as StockCheckLatitude,

    CASE
        WHEN scns.IsRegEditedOnWeb = 1 THEN 'Web app'
        WHEN scns.IsRegEditedOnDevice = 1 THEN 'Mobile app'
        ELSE NULL
    END AS RegEditStatus,
    CASE
        WHEN scns.IsVinEditedOnWeb = 1 THEN 'Web app'
        WHEN scns.IsVinEditedOnDevice = 1 THEN 'Mobile app'
        ELSE NULL
    END AS VinEditStatus
    FROM dbo.Scans scns  
    INNER JOIN Locations locns on locns.Id = scns.LocationId  
    INNER JOIN Users usrs on usrs.Id = scns.UserId  
    INNER JOIN ReconcilingItems recitems on recitems.Id = scns.ReconcilingItemId  
    INNER JOIN ReconcilingItemTypes rectypes on rectypes.Id = recitems.ReconcilingItemTypeId  
    INNER JOIN StockChecks AS SC ON SC.Id=scns.StockCheckId  
    INNER JOIN Sites ON Sites.Id=SC.SiteId  
	INNER JOIN Divisions AS D ON D.Id=Sites.DivisionId  
	INNER JOIN DealerGroup AS DG ON D.DealerGroupId=DG.Id  
    WHERE ReconcilingItemId IS NOT NULL   
	AND scns.StockItemId IS NULL  
    --AND scns.StockCheckId = @StockCheckId  
	AND SC.Id = ISNULL(@SCId, SC.Id)  
	AND SC.Date = @StockCheckDate  
	AND D.Id = ISNULL(@DivisionId, D.Id)  
	AND DG.Id = ISNULL(@DealerGroupId, DG.Id)  

	UNION ALL

	SELECT  
    scns.Id,
	scns.UserId,
	scns.StockCheckId,
	LastEditedById,
	LocationId,
	scns.UnknownResolutionId,
    StockItemId,
	NULL AS ReconcilingItemId,
	0 AS IsDuplicate,
	LastEditedDateTime,
	RegConfidence,
    VinConfidence,
	--IsEdited,
    scns.Longitude,
	scns.Latitude,
	ScanDateTime,
	scns.Comment,
	scns.Reg,scns.
	Vin,
	scns.Description,
	CoordinatesJSON,
	HasVinImage,  
    locns.Description as 'LocationDescription',  
    usrs.Name as 'ScannerName',

	NULL AS 'ReconcilingItemTypeId',  
    'In stock at another site' AS 'ReconcilingItemTypeDescription',  
    'In stock at another site' AS 'RecItemDescription',  
    NULL as 'RecItemComment',  
    NULL as 'RecItemReference',  
  
    Sites.Description AS SiteName,
    Sites.Longitude as StockCheckLongitude,
    Sites.Latitude as StockCheckLatitude,
    CASE
        WHEN scns.IsRegEditedOnWeb = 1 THEN 'Web app'
        WHEN scns.IsRegEditedOnDevice = 1 THEN 'Mobile app'
        ELSE NULL
    END AS RegEditStatus,
    CASE
        WHEN scns.IsVinEditedOnWeb = 1 THEN 'Web app'
        WHEN scns.IsVinEditedOnDevice = 1 THEN 'Mobile app'
        ELSE NULL
    END AS VinEditStatus

    FROM scans scns     
    INNER JOIN StockItems stockitem on stockitem.Id = scns.StockItemId 
    INNER JOIN Locations locns on locns.Id = scns.LocationId  
    INNER JOIN Users usrs on usrs.Id = scns.UserId  
    --INNER JOIN ReconcilingItems recitems on recitems.Id = scns.ReconcilingItemId  
    --INNER JOIN ReconcilingItemTypes rectypes on rectypes.Id = recitems.ReconcilingItemTypeId  
    INNER JOIN StockChecks AS SC ON SC.Id=scns.StockCheckId  
    INNER JOIN Sites ON Sites.Id=SC.SiteId  
	INNER JOIN Divisions AS D ON D.Id=Sites.DivisionId  
	INNER JOIN DealerGroup AS DG ON D.DealerGroupId=DG.Id  
    WHERE scns.StockCheckId = @StockCheckId     
    AND stockItem.StockCheckId <> @StockCheckId  
  
  
END 

GO


--############################## Next SP #############################################SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].GET_ScansWithLocation
(
    @StockCheckId INT = NULL,
    @UserId INT = NULL
)
AS
BEGIN

SET NOCOUNT ON;

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END

DECLARE @isRegional INT;
SET @isRegional = (SELECT IsRegional FROM StockChecks
                    WHERE StockChecks.Id = @StockCheckId)

DECLARE @isTotal INT;
SET @isTotal = (SELECT IsTotal FROM StockChecks
                    WHERE StockChecks.Id = @StockCheckId)

IF @isRegional = 0 AND @isTotal = 0
	
    BEGIN 

    WITH temporaryTable AS 
    (
    SELECT Scans.Id, 
    Scans.[Description] AS Description, 
    Locations.[Description] AS Location,
    DIS,
    CASE
        WHEN (Scans.StockItemId IS NOT NULL AND Scans.IsDuplicate = 0) THEN 'Scanned, InStock'
        WHEN (Scans.ReconcilingItemId IS NOT NULL AND Scans.IsDuplicate = 0) THEN 'Not instock, matched to report'
        WHEN (Scans.UnknownResolutionId IS NOT NULL AND Scans.IsDuplicate = 0) THEN 'Unknown vehicle, resolved'
        WHEN (Scans.UnknownResolutionId IS NULL AND Scans.ReconcilingItemId IS NULL AND Scans.StockItemId IS NULL AND Scans.IsDuplicate = 0) THEN 'Unknown vehicle'
    END AS Status
    FROM [dbo].[Scans]
    LEFT JOIN StockItems ON StockItems.Id = Scans.StockItemId
    INNER JOIN Locations ON Locations.Id = Scans.LocationId
    WHERE [Scans].[StockCheckId] = @StockCheckId
    )
    SELECT Id, Description, Location, Status, DIS FROM temporaryTable;

	END

IF @isRegional = 1
	
    BEGIN

    DECLARE @DivisionId INT;

    SET @DivisionId = (SELECT DivisionId FROM StockChecks
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id
                        WHERE StockChecks.Id = @StockCheckId AND StockChecks.IsRegional = 1)

    ;WITH temporaryTable AS 
    (
    SELECT Scans.Id, 
    Scans.[Description] AS Description, 
    Locations.[Description] AS Location,
    DIS,
    CASE
        WHEN (Scans.StockItemId IS NOT NULL AND Scans.IsDuplicate = 0) THEN 'Scanned, InStock'
        WHEN (Scans.ReconcilingItemId IS NOT NULL AND Scans.IsDuplicate = 0) THEN 'Not instock, matched to report'
        WHEN (Scans.UnknownResolutionId IS NOT NULL AND Scans.IsDuplicate = 0) THEN 'Unknown vehicle, resolved'
        WHEN (Scans.UnknownResolutionId IS NULL AND Scans.ReconcilingItemId IS NULL AND Scans.StockItemId IS NULL AND Scans.IsDuplicate = 0) THEN 'Unknown vehicle'
    END AS Status
    FROM [dbo].[Scans]
    LEFT JOIN StockItems ON StockItems.Id = Scans.StockItemId
    INNER JOIN Locations ON Locations.Id = Scans.LocationId
    INNER JOIN StockChecks ON StockChecks.Id=StockItems.StockCheckId
    INNER JOIN Sites ON Sites.Id=StockChecks.SiteId
    INNER JOIN Divisions ON Divisions.Id=Sites.DivisionId
    WHERE Divisions.Id = @DivisionId
    )
    SELECT Id, Description, Location, Status, DIS FROM temporaryTable;

	END
    
IF @isTotal = 1

    BEGIN

    DECLARE @DealerGroupId INT;
    SET @DealerGroupId = (SELECT DealerGroupId FROM StockChecks
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id
                        WHERE StockChecks.Id = @StockCheckId)

    ;WITH temporaryTable AS 
    (
    SELECT Scans.Id, 
    Scans.[Description] AS Description, 
    Locations.[Description] AS Location,
    DIS,
    CASE
        WHEN (Scans.StockItemId IS NOT NULL AND Scans.IsDuplicate = 0) THEN 'Scanned, InStock'
        WHEN (Scans.ReconcilingItemId IS NOT NULL AND Scans.IsDuplicate = 0) THEN 'Not instock, matched to report'
        WHEN (Scans.UnknownResolutionId IS NOT NULL AND Scans.IsDuplicate = 0) THEN 'Unknown vehicle, resolved'
        WHEN (Scans.UnknownResolutionId IS NULL AND Scans.ReconcilingItemId IS NULL AND Scans.StockItemId IS NULL AND Scans.IsDuplicate = 0) THEN 'Unknown vehicle'
    END AS Status
    FROM [dbo].[Scans]
    LEFT JOIN StockItems ON StockItems.Id = Scans.StockItemId
    INNER JOIN Locations ON Locations.Id = Scans.LocationId
    INNER JOIN StockChecks ON StockChecks.Id=StockItems.StockCheckId
    INNER JOIN Sites ON Sites.Id=StockChecks.SiteId
    INNER JOIN Divisions ON Divisions.Id=Sites.DivisionId
    INNER JOIN DealerGroup ON Divisions.DealerGroupId=DealerGroup.Id
    WHERE DealerGroup.Id = @DealerGroupId
    )
    SELECT Id, Description, Location, Status, DIS FROM temporaryTable;

    END

END

GO




--To use this run 
--exec [GET_ScansWithLocation] @StockCheckId = 99, @UserId = 99

--############################## Next SP #############################################﻿/****** Object:  StoredProcedure [dbo].[GET_ScansWithRegDifference]    Script Date: 31/3/21 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].GET_ScansWithRegDifference
(
    @StockCheckId INT = NULL,
    @UserId INT = NULL
)
AS
BEGIN

SET NOCOUNT ON;

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END



DECLARE @isRegional INT;
SET @isRegional = (SELECT IsRegional FROM StockChecks
                    WHERE StockChecks.Id = @StockCheckId)

DECLARE @isTotal INT;
SET @isTotal = (SELECT IsTotal FROM StockChecks
                    WHERE StockChecks.Id = @StockCheckId)

IF @isRegional = 0 AND @isTotal = 0

    BEGIN

        SELECT 
        Scans.Id AS ScanId, 
        Scans.Reg AS RegPerScan, 
        StockItems.Reg AS RegPerStockList, 
        Scans.Vin AS VinPerScan, 
        StockItems.Vin AS VinPerStockList,
		u.Name as ScannerName,
		loc.Description as LocationDescription,
		scans.ScanDateTime,
        CASE WHEN Scans.Reg = StockItems.Reg THEN 1 ELSE 0 END AS RegMatches
        FROM StockItems
        INNER JOIN Scans ON Scans.Id = StockItems.ScanId
        INNER JOIN Users u on u.Id = scans.UserId
		INNER JOIN Locations loc on loc.Id = scans.LocationId
        WHERE StockItems.StockCheckId = @StockCheckId AND
        ((Scans.Reg != StockItems.Reg OR StockItems.Vin != Scans.Vin))
		AND
		Scans.Reg != '' AND StockItems.Reg != '' AND Scans.Vin != '' AND StockItems.Vin != '';

    END

IF @isRegional = 1 AND @isTotal = 0

    BEGIN

    DECLARE @DivisionId INT;
    SET @DivisionId = (SELECT DivisionId FROM StockChecks
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id
                        WHERE StockChecks.Id = @StockCheckId AND StockChecks.IsRegional = 1)

    SELECT 
    Scans.Id AS ScanId, 
    Scans.Reg AS RegPerScan, 
    StockItems.Reg AS RegPerStockList, 
    Scans.Vin AS VinPerScan, 
    StockItems.Vin AS VinPerStockList,
	u.Name as ScannerName,
		loc.Description as LocationDescription,
		scans.ScanDateTime,
    CASE WHEN Scans.Reg = StockItems.Reg THEN 1 ELSE 0 END AS RegMatches
    FROM StockItems
    INNER JOIN Scans ON Scans.Id = StockItems.ScanId
	INNER JOIN Users u on u.Id = scans.UserId
		INNER JOIN Locations loc on loc.Id = scans.LocationId
    INNER JOIN StockChecks ON StockChecks.Id=StockItems.StockCheckId
    INNER JOIN Sites ON Sites.Id=StockChecks.SiteId
    INNER JOIN Divisions ON Divisions.Id=Sites.DivisionId
    WHERE Divisions.Id = @DivisionId
    AND
    ((Scans.Reg != StockItems.Reg) OR (StockItems.Vin != Scans.Vin))
	AND
	Scans.Reg != '' AND StockItems.Reg != '' AND Scans.Vin != '' AND StockItems.Vin != '';


    END

IF @isTotal = 1 AND @isRegional = 0

    BEGIN

    DECLARE @DealerGroupId INT;
    SET @DealerGroupId = (SELECT DealerGroupId FROM StockChecks
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id
                        WHERE StockChecks.Id = @StockCheckId)

    SELECT 
	Scans.Id AS ScanId, 
	Scans.Reg AS RegPerScan, 
	StockItems.Reg AS RegPerStockList, 
    Scans.Vin AS VinPerScan, 
	u.Name as ScannerName,
		loc.Description as LocationDescription,
		scans.ScanDateTime,
	StockItems.Vin AS VinPerStockList,
    CASE WHEN Scans.Reg = StockItems.Reg THEN 1 ELSE 0 END AS RegMatches
    FROM StockItems
    INNER JOIN Scans ON Scans.Id = StockItems.ScanId
	INNER JOIN Users u on u.Id = scans.UserId
		INNER JOIN Locations loc on loc.Id = scans.LocationId
    INNER JOIN StockChecks ON StockChecks.Id=[StockItems].StockCheckId
    INNER JOIN Sites ON Sites.Id=StockChecks.SiteId
    INNER JOIN Divisions ON Divisions.Id=Sites.DivisionId
    INNER JOIN DealerGroup ON Divisions.DealerGroupId=DealerGroup.Id
    WHERE DealerGroup.Id = @DealerGroupId
    AND
    ((Scans.Reg != StockItems.Reg OR StockItems.Vin != Scans.Vin))
	AND
	Scans.Reg != '' AND StockItems.Reg != '' AND Scans.Vin != '' AND StockItems.Vin != '';

    END

END

GO



--To use this run 
--exec [GET_ScansWithRegDifference] 8495, 389

--############################## Next SP #############################################﻿
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO  
  

CREATE OR ALTER PROCEDURE [dbo].[GET_ScansWithResolution]  
(  
    @StockCheckId INT = NULL,  
    @UserId INT = NULL  
)  
AS  
BEGIN  
  
SET NOCOUNT ON;  
   
DECLARE @isRegional INT;    
DECLARE @isTotal INT;    
DECLARE @StockCheckDate DateTime;  
DECLARE @DivisionId INT = NULL;  
DECLARE @DealerGroupId INT = NULL;  
DECLARE @SCId INT = NULL;  
  
  
IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END

  
SELECT   
@isRegional = IsRegional,   
@isTotal = IsTotal,   
@StockCheckDate = Date   
FROM StockChecks    
WHERE StockChecks.Id = @StockCheckId  
    

  
IF @isRegional = 0 AND @isTotal = 0  
   
    BEGIN   
  
 SET @SCId = @StockCheckId;  
      
   
    END  
  
ELSE IF @isRegional = 1 AND @isTotal = 0  
  
    BEGIN   
  
  
    SET @DivisionId = (SELECT DivisionId FROM StockChecks  
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId  
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id  
                        WHERE StockChecks.Id = @StockCheckId AND StockChecks.IsRegional = 1)  
  
  
    END  
  
IF @isRegional = 0 AND @isTotal = 1  
  
    BEGIN   
  
      
    SET @DealerGroupId = (SELECT DealerGroupId FROM StockChecks  
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId  
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id  
                        WHERE StockChecks.Id = @StockCheckId)  
  
      
    END  



  
 select   
    scns.Id as ScanId,
    scns.UserId,
    scns.StockCheckId,
    LastEditedById,
    LocationId,
    scns.UnknownResolutionId,
    StockItemId,
    scns.ReconcilingItemId,
    scns.IsDuplicate,
    LastEditedDateTime,
    RegConfidence,
    VinConfidence,
    --IsEdited,
    scns.Longitude,
    scns.Latitude,
    ScanDateTime,
    scns.Comment as ScanComment,
    scns.Reg as ScanReg,
    scns.Vin as ScanVin,
    scns.Description as ScanDescription,
    CoordinatesJSON,
    HasVinImage,  
    locns.Description as 'LocationDescription',  
    usrs.Name as 'ScannerName',  
    restypes.Description as 'ResolutionTypeDescription',  
    usrsRes.Name as 'ResolvedBy',  
    res.IsResolved,  
    res.ResolutionDateTime as ResolutionDate,
    res.Id as 'ResolutionId',  
    res.Notes as 'ResolutionNotes',  
	(select String_Agg(CONCAT(uri.Id,'|',ISNULL(uri.FileName,'FileName')),'::')) AS ResolutionImageIds, --this seems slow TODO
    Sites.Description AS SiteName,
    SC.Date as 'StockCheckDate',
    Sites.Longitude as StockCheckLongitude,
    Sites.Latitude as StockCheckLatitude,

    CASE
        WHEN scns.IsRegEditedOnWeb = 1 THEN 'Web app'
        WHEN scns.IsRegEditedOnDevice = 1 THEN 'Mobile app'
        ELSE NULL
    END AS RegEditStatus,
    CASE
        WHEN scns.IsVinEditedOnWeb = 1 THEN 'Web app'
        WHEN scns.IsVinEditedOnDevice = 1 THEN 'Mobile app'
        ELSE NULL
    END AS VinEditStatus
    FROM   
    Scans scns  
    LEFT JOIN UnknownResolutions res on res.Id = scns.UnknownResolutionId   
	LEFT JOIN UnknownResolutionImages uri on uri.UnknownResolutionId = res.Id
    LEFT JOIN ResolutionTypes restypes on restypes.Id = res.ResolutionTypeId  
    LEFT JOIN Locations locns on locns.Id = scns.LocationId  
    LEFT JOIN Users usrs on usrs.Id = scns.UserId  
    LEFT JOIN Users usrsRes on usrsRes.Id = res.UserId  
    LEFT JOIN StockChecks AS SC ON SC.Id=scns.StockCheckId  
    LEFT JOIN Sites ON Sites.Id=SC.SiteId  
    LEFT JOIN ReconcilingItems ri on ri.id = scns.ReconcilingItemId  
 INNER JOIN Divisions AS D ON D.Id=Sites.DivisionId  
 INNER JOIN DealerGroup AS DG ON D.DealerGroupId=DG.Id  
    WHERE   
    scns.StockItemId is null  
    AND scns.IsDuplicate = 0  
    AND (scns.ReconcilingItemId is null OR (select rt.ExplainsMissingVehicle from ReconcilingItemTypes rt where rt.Id = ri.ReconcilingItemTypeId) = 1)  
 AND SC.Id = ISNULL(@SCId, SC.Id)  
 AND SC.Date = @StockCheckDate  
 AND D.Id = ISNULL(@DivisionId, D.Id)  
 AND DG.Id = ISNULL(@DealerGroupId, DG.Id)  
 GROUP BY 
 scns.Id ,
    scns.UserId,
    scns.StockCheckId,
    LastEditedById,
    LocationId,
    scns.UnknownResolutionId,
    StockItemId,
    scns.ReconcilingItemId,
    scns.IsDuplicate,
    LastEditedDateTime,
    RegConfidence,
    VinConfidence,
    --IsEdited,
    scns.Longitude,
    scns.Latitude,
    ScanDateTime,
    scns.Comment,
    scns.Reg,
    scns.Vin,
    scns.Description,
    CoordinatesJSON,
    HasVinImage,  
    locns.Description,  
    usrs.Name,  
    restypes.Description ,  
    usrsRes.Name,  
    res.IsResolved,  
    res.ResolutionDateTime,
    res.Id,  
    res.Notes,  
    Sites.Description,
    SC.Date,
    Sites.Longitude,
    Sites.Latitude,
    scns.IsRegEditedOnWeb,
    scns.IsRegEditedOnDevice,
    scns.IsVinEditedOnWeb,
    scns.IsVinEditedOnDevice
  

  
END  
  

  GO

--############################## Next SP #############################################


SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE OR ALTER PROCEDURE [dbo].[GET_SiteNameLookups]
(
    @UserId INT = NULL
)
AS
BEGIN

SET NOCOUNT ON

SELECT
sdd.Id,
sdd.Description AS InputName,
sdd.IsPrimarySiteId,
s.Description AS StockPulseName,
sdd.SiteId
FROM import.SiteDescriptionDictionary sdd
INNER JOIN Sites s ON s.Id = sdd.SiteId
INNER JOIN Users u ON u.Id = @UserId
WHERE
sdd.DealerGroupId = u.DealerGroupId

END

--############################## Next SP #############################################﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].GET_Sites
(
    @UserId INT = NULL
)
AS
BEGIN

SET NOCOUNT ON

SELECT Sites.[Id]
      ,[Description]
      ,[IsActive]
      ,[Longitude]
      ,[Latitude]
      ,[DivisionId]
  FROM [dbo].[Sites] 
  INNER JOIN UserSites ON UserSites.SiteId=Sites.Id
  WHERE UserSites.UserId = @UserId


END

GO





--To use this run 
--exec [GET_Scans] @StockCheckId = 1

--############################## Next SP #############################################﻿/****** Object:  StoredProcedure [dbo].[GET_SitesForDivision]    Script Date: 12/3/21 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].GET_SitesForDivision
(
    @UserId INT = NULL,
    @DivisionId INT = NULL
)
AS
BEGIN

SET NOCOUNT ON

SELECT Sites.[Id]
      ,[Description]
      ,[IsActive]
      ,[Longitude]
      ,[Latitude]
      ,[DivisionId]
  FROM [dbo].[Sites] 
  WHERE DivisionId = @DivisionId


	
	

END

GO



--To use this run 
--exec [GET_SitesForDivision] @DivisionId = 1, @UserId = 1

--############################## Next SP #############################################SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].[GET_SitesWithStockChecksForDate]
(
    @StockCheckDate Date,
    @SiteIds varchar(max)
)
AS
BEGIN

SET NOCOUNT ON;

SELECT SiteId FROM StockChecks
WHERE Date = @StockCheckDate AND SiteId IN (SELECT * FROM STRING_SPLIT(@SiteIds,','))
	
END

--############################## Next SP #############################################﻿/****** Object:  StoredProcedure [dbo].[GET_SourceReports]    Script Date: 11/3/21 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].GET_SourceReports
(
    @UserId INT = NULL	
)
AS
BEGIN

SET NOCOUNT ON


	
SELECT [Id]
      ,[UserId]
      ,[Filename]
      ,[DateTime]
  FROM [dbo].[SourceReports]
  WHERE UserId = @UserId 	

	
	

END

GO



--To use this run 
--exec [GET_SourceReports] @UserId = 1

--############################## Next SP #############################################﻿

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].GET_StatusChangeLogItems
(
	@StockCheckId int = NULL
)
AS
BEGIN

SET NOCOUNT ON

SELECT
scl.StockCheckId,
scl.UserId,
scl.StatusId,
u.Name AS "UserName",
s.Description AS "Status",
scl.Date
FROM StatusChangeLogItems scl
INNER JOIN Users u ON u.Id = scl.UserId
INNER JOIN Statuses s ON s.Id = scl.StatusId
WHERE scl.StockCheckId = @StockCheckId

END

GO


--############################## Next SP #############################################SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].[GET_StockCheckIdsForUserDealerGroup]
(
    @UserId INT = NULL
)
AS
BEGIN

SET NOCOUNT ON;

DECLARE @userDealerGroup int = (SELECT DealerGroupId FROM Users WHERE Id = @userId)

SELECT sc.Id
FROM StockChecks sc
INNER JOIN Sites s on s.Id = sc.SiteId
INNER JOIN Divisions d on d.Id = s.DivisionId
INNER JOIN DealerGroup dg on dg.Id = d.DealerGroupId
WHERE
dg.Id = @userDealerGroup
AND sc.IsActive = 1

END


--############################## Next SP #############################################

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].[GET_StockCheckSiteName]
(
    @UserId INT,
	@StockCheckId INT
)
AS
BEGIN

SET NOCOUNT ON;


IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END

SELECT
si.Description as SiteName
FROM StockChecks sc
INNER JOIN Sites si on si.Id = sc.SiteId
WHERE sc.Id = @StockCheckId



  END



  GO

--############################## Next SP #############################################

CREATE OR ALTER PROCEDURE [dbo].[GET_StockChecksOverview]
(
    @UserId INT = NULL,
	@IsActive BIT,   --nullable
	@StockCheckId INT,  --nullable
	@FromDate NVARCHAR(50),
	@ToDate NVARCHAR(50)
)
AS
BEGIN

SET NOCOUNT ON;

DECLARE @userDealerGroup int = (SELECT DealerGroupId FROM Users WHERE Id = @userId)


DECLARE @IsRegional BIT = NULL
DECLARE @IsTotal BIT = NULL

SELECT @IsRegional = IsRegional, @IsTotal = IsTotal FROM dbo.StockChecks WHERE Id = @StockCheckId AND @StockCheckId IS NOT NULL





SELECT
SiteId
INTO #userSites
FROM UserSites
WHERE UserId = @UserId

-----------------------------------------------
-- 1. Work out regional stats
-----------------------------------------------
;WITH RegionalStats AS 
(SELECT  
	mainSC.Date as StockCheckDate,
	si.DivisionId as DivisionId,

	(SELECT Count(sti.Id) 
	FROM StockItems sti 
	INNER JOIN StockChecks sc on sc.id = sti.StockCheckId
	INNER JOIN Sites site on site.id = sc.SiteId 
	INNER JOIN #userSites us on sc.SiteId = us.SiteId
	WHERE  
	(@IsActive IS NULL OR sc.IsActive = @IsActive)
	AND site.DivisionId = si.DivisionId
	AND sc.Date = mainSC.Date
	) 
	as StockItemsCount,

	(SELECT SUM(sti.StockValue)
	FROM StockItems sti 
	INNER JOIN StockChecks sc on sc.id = sti.StockCheckId
	INNER JOIN Sites site on site.id = sc.SiteId 
	INNER JOIN #userSites us on sc.SiteId = us.SiteId
	WHERE  
	(@IsActive IS NULL OR sc.IsActive = @IsActive)
	AND site.DivisionId = si.DivisionId
	AND sc.Date = mainSC.Date
	) 
	as StockValue,
	
	(SELECT COUNT(sca.Id) 
	FROM Scans sca 
	INNER JOIN StockChecks sc on sc.id = sca.StockCheckId 
	INNER JOIN Sites site on site.id = sc.SiteId 
	INNER JOIN #userSites us on sc.SiteId = us.SiteId
	WHERE 
	(@IsActive IS NULL OR sc.IsActive = @IsActive)
	AND site.DivisionId = si.DivisionId
	AND sc.Date = mainSC.Date
	) as Scans,
	
	(SELECT COUNT(sti.Id) 
	FROM StockItems sti 
	INNER JOIN StockChecks sc on sc.id = sti.StockCheckId
	INNER JOIN Sites site on site.id = sc.SiteId
	INNER JOIN #userSites us on sc.SiteId = us.SiteId
	INNER JOIN Scans scans on scans.Id = sti.ScanId AND scans.StockCheckId = sti.StockCheckId
	WHERE
	(@IsActive IS NULL OR sc.IsActive = @IsActive)
	AND site.DivisionId = si.DivisionId
	AND sc.Date = mainSC.Date
	AND sti.IsDuplicate = 0
	) as ScannedInStock,
	
	(SELECT COUNT(sca.Id) 
	FROM Scans sca
	INNER JOIN StockChecks sc on sc.id = sca.StockCheckId
	INNER JOIN Sites site on site.id = sc.SiteId 
	INNER JOIN #userSites us on sc.SiteId = us.SiteId
	WHERE
	(@IsActive IS NULL OR sc.IsActive = @IsActive)
	AND site.DivisionId = si.DivisionId
	AND sc.Date = mainSC.Date
	AND sca.IsDuplicate = 0 
	AND sca.StockItemId IS NULL 
	AND sca.ReconcilingItemId IS NULL
	) as UnknownsTot,

	(SELECT COUNT(sca.Id) 
	FROM Scans sca
	INNER JOIN StockChecks sc on sc.id = sca.StockCheckId
	INNER JOIN Sites site on site.id = sc.SiteId 
	INNER JOIN #userSites us on sc.SiteId = us.SiteId
	LEFT JOIN UnknownResolutions ur on ur.Id = sca.UnknownResolutionId
	WHERE 
	(@IsActive IS NULL OR sc.IsActive = @IsActive)
	AND site.DivisionId = si.DivisionId 
	AND sc.Date = mainSC.Date
	AND sca.IsDuplicate = 0 
	AND sca.StockItemId IS NULL 
	AND sca.ReconcilingItemId IS NULL
	AND 
		(ur.Id IS  NULL
			OR
			ur.IsResolved = 0
		)
	) as UnknownOs,

	(SELECT COUNT(sti.Id) 
	FROM StockItems sti
	INNER JOIN StockChecks sc on sc.id = sti.StockCheckId
	INNER JOIN Sites site on site.id = sc.SiteId 
	INNER JOIN #userSites us on sc.SiteId = us.SiteId
	WHERE 
	(@IsActive IS NULL OR sc.IsActive = @IsActive)
	AND site.DivisionId = si.DivisionId
	AND sc.Date = mainSC.Date
	AND IsDuplicate = 0 
	AND ScanId IS NULL 
	AND ReconcilingItemId IS NULL
	) 	as MissingsTot,

	(SELECT SUM(sti.StockValue) * 0.001
	FROM StockItems sti
	INNER JOIN StockChecks sc on sc.id = sti.StockCheckId 
	INNER JOIN Sites site on site.id = sc.SiteId 
	INNER JOIN #userSites us on sc.SiteId = us.SiteId
	LEFT JOIN MissingResolutions mr on mr.Id = sti.MissingResolutionId 
	WHERE 
	(@IsActive IS NULL OR sc.IsActive = @IsActive)
	AND site.DivisionId = si.DivisionId
	AND sc.Date = mainSC.Date
	AND IsDuplicate = 0 
	AND ScanId IS NULL 
	AND ReconcilingItemId IS NULL
	AND 
		(mr.Id IS  NULL
		OR
		mr.IsResolved = 0
		)
	) 
	as MissingValue,

	(SELECT COUNT(sti.Id) 
	FROM StockItems sti
	INNER JOIN StockChecks sc on sc.id = sti.StockCheckId 
	INNER JOIN Sites site on site.id = sc.SiteId 
	INNER JOIN #userSites us on sc.SiteId = us.SiteId
	LEFT JOIN MissingResolutions mr on mr.Id = sti.MissingResolutionId 
	WHERE 
	(@IsActive IS NULL OR sc.IsActive = @IsActive)
	AND site.DivisionId = si.DivisionId
	AND sc.Date = mainSC.Date
	AND IsDuplicate = 0 
	AND ScanId IS NULL 
	AND ReconcilingItemId IS NULL
	AND 
		(mr.Id IS  NULL
		OR
		mr.IsResolved = 0
		)
	) 
	as MissingOs,--

	(SELECT TOP 1 ScanDateTime
	FROM Scans s
	INNER JOIN StockChecks sc on sc.id = s.StockCheckId 
	INNER JOIN Sites site on site.id = sc.SiteId 
	WHERE
	(@IsActive IS NULL OR sc.IsActive = @IsActive)
	AND site.DivisionId = si.DivisionId
	AND sc.Date = mainSC.Date
	ORDER BY s.ScanDateTime
	) as FirstScan,

	(SELECT TOP 1 ScanDateTime
	FROM Scans s
	INNER JOIN StockChecks sc on sc.id = s.StockCheckId 
	INNER JOIN Sites site on site.id = sc.SiteId 
	WHERE
	(@IsActive IS NULL OR sc.IsActive = @IsActive)
	AND site.DivisionId = si.DivisionId
	AND sc.Date = mainSC.Date
	ORDER BY s.ScanDateTime DESC
	) as LastScan,

	(SELECT SUM(Balance)
	FROM FinancialLines fl
	INNER JOIN StockChecks sc ON sc.Id = fl.StockCheckId
	INNER JOIN Sites s on s.Id = sc.SiteId
	INNER JOIN #userSites us on sc.SiteId = us.SiteId
	WHERE
	sc.Date = mainSC.Date
	AND s.DivisionId = si.DivisionId
	) AS GLValue
	

 
FROM [dbo].[StockChecks] AS mainSC
INNER JOIN  Sites AS si ON si.Id = mainSC.SiteId
INNER JOIN #userSites us on mainSC.SiteId = us.SiteId
WHERE  
	(@IsActive IS NULL OR mainSC.IsActive = @IsActive)
	AND si.Id IN (SELECT SiteId FROM #userSites)
	AND (@FromDate IS NULL OR mainSC.Date >= @FromDate)
	AND (@ToDate IS NULL OR mainSC.Date <= @ToDate)
	AND mainSC.IsRegional = 1
	AND @IsRegional IS NULL OR @IsRegional = 1

GROUP BY 
	mainSC.Date,
	si.DivisionId
	),

	
-----------------------------------------------
-- 2. Do total stats
-----------------------------------------------
TotalStats AS (
SELECT  
	mainSC.Date as StockCheckDate,

	(SELECT Count(sti.Id) 
	FROM StockItems sti 
	INNER JOIN StockChecks sc on sc.id = sti.StockCheckId
	INNER JOIN #userSites us on sc.SiteId = us.SiteId
	WHERE  
	sc.Date = mainSC.Date
	) as StockItemsCount,
	
	(SELECT SUM(sti.StockValue) 
	FROM StockItems sti 
	INNER JOIN StockChecks sc on sc.id = sti.StockCheckId
	INNER JOIN #userSites us on sc.SiteId = us.SiteId
	WHERE  
	sc.Date = mainSC.Date
	) as StockValue,

	(SELECT COUNT(sca.Id) 
	FROM Scans sca 
	INNER JOIN StockChecks sc on sc.id = sca.StockCheckId
	INNER JOIN #userSites us on sc.SiteId = us.SiteId
	WHERE  
	sc.Date = mainSC.Date
	) as Scans,
	
	(SELECT COUNT(sti.Id) 
	FROM StockItems sti 
	INNER JOIN StockChecks sc on sc.id = sti.StockCheckId 
	INNER JOIN Scans scans on scans.Id = sti.ScanId AND scans.StockCheckId = sti.StockCheckId
	INNER JOIN #userSites us on sc.SiteId = us.SiteId
	WHERE  
	sc.Date = mainSC.Date
	AND sti.IsDuplicate = 0
	) as ScannedInStock,
	
	(SELECT COUNT(sca.Id) 
	FROM Scans sca
	INNER JOIN StockChecks sc on sc.id = sca.StockCheckId
	INNER JOIN #userSites us on sc.SiteId = us.SiteId
	WHERE  
	(@IsActive IS NULL OR sc.IsActive = @IsActive)
	AND sc.Date = mainSC.Date
	AND sca.IsDuplicate = 0 
	AND sca.StockItemId IS NULL 
	AND sca.ReconcilingItemId IS NULL
	) as UnknownsTot,

	(SELECT COUNT(sca.Id) 
	FROM Scans sca
	INNER JOIN StockChecks sc on sc.id = sca.StockCheckId
	INNER JOIN #userSites us on sc.SiteId = us.SiteId
	LEFT JOIN UnknownResolutions ur on ur.Id = sca.UnknownResolutionId
	WHERE  
	(@IsActive IS NULL OR sc.IsActive = @IsActive)
	AND sc.Date = mainSC.Date
	AND sca.IsDuplicate = 0 
	AND sca.StockItemId IS NULL 
	AND sca.ReconcilingItemId IS NULL
	AND		
		(ur.Id IS  NULL
			OR
			ur.IsResolved = 0
		)
	) as UnknownOs,

	(SELECT COUNT(sti.Id) 
	FROM StockItems sti
	INNER JOIN StockChecks sc on sc.id = sti.StockCheckId
	INNER JOIN #userSites us on sc.SiteId = us.SiteId
	WHERE
	(@IsActive IS NULL OR sc.IsActive = @IsActive)
	AND sc.Date = mainSC.Date
	AND IsDuplicate = 0 
	AND ScanId IS NULL 
	AND ReconcilingItemId IS NULL
	) 	as MissingsTot,

	(SELECT SUM(sti.StockValue) * 0.001 
	FROM StockItems sti
	INNER JOIN StockChecks sc on sc.id = sti.StockCheckId
	INNER JOIN #userSites us on sc.SiteId = us.SiteId
	LEFT JOIN MissingResolutions mr on mr.Id = sti.MissingResolutionId 
	WHERE  
	(@IsActive IS NULL OR sc.IsActive = @IsActive)
	AND sc.Date = mainSC.Date
	AND IsDuplicate = 0 
	AND ScanId IS NULL 
	AND ReconcilingItemId IS NULL
	AND 
		(mr.Id IS  NULL
		OR
		mr.IsResolved = 0
		)
	) 
	as MissingValue,

	(SELECT COUNT(sti.Id) 
	FROM StockItems sti
	INNER JOIN StockChecks sc on sc.id = sti.StockCheckId
	INNER JOIN #userSites us on sc.SiteId = us.SiteId
	LEFT JOIN MissingResolutions mr on mr.Id = sti.MissingResolutionId 
	WHERE  
	(@IsActive IS NULL OR sc.IsActive = @IsActive)
	AND sc.Date = mainSC.Date
	AND IsDuplicate = 0 
	AND ScanId IS NULL 
	AND ReconcilingItemId IS NULL
	AND 
		(mr.Id IS  NULL
		OR
		mr.IsResolved = 0
		)
	) 
	as MissingOs,

	(SELECT TOP 1 ScanDateTime
	FROM Scans s
	INNER JOIN StockChecks sc on sc.id = s.StockCheckId
	WHERE
	(@IsActive IS NULL OR sc.IsActive = @IsActive)
	AND sc.Date = mainSC.Date
	ORDER BY s.ScanDateTime
	) as FirstScan,

	(SELECT TOP 1 ScanDateTime
	FROM Scans s
	INNER JOIN StockChecks sc on sc.id = s.StockCheckId
	WHERE
	(@IsActive IS NULL OR sc.IsActive = @IsActive)
	AND sc.Date = mainSC.Date
	ORDER BY s.ScanDateTime DESC
	) as LastScan,

	(
	SELECT SUM(Balance)
	FROM FinancialLines fl
	INNER JOIN StockChecks sc ON sc.Id = fl.StockCheckId
	INNER JOIN #userSites us on sc.SiteId = us.SiteId
	WHERE
	sc.Date = mainSC.Date
	) AS GLValue
	
	--select top 1 * from FinancialLines
 
FROM [dbo].[StockChecks] AS mainSC
INNER JOIN  Sites AS si ON si.Id = mainSC.SiteId
INNER JOIN #userSites us on mainSC.SiteId = us.SiteId
WHERE  
	(@IsActive IS NULL OR mainSC.IsActive = @IsActive)
	AND si.Id IN (SELECT SiteId FROM #userSites)
	AND (@FromDate IS NULL OR mainSC.Date >= @FromDate)
	AND (@ToDate IS NULL OR mainSC.Date <= @ToDate)
	AND mainSC.IsTotal = 1
	AND @IsTotal IS NULL OR @IsTotal = 1

GROUP BY 
	mainSC.Date)


---------------------------------
-- 3. Do main query
---------------------------------

	SELECT  
	sc.Id,
	si.[Description] AS Site,
	si.[Id] AS SiteId,
	u.[Name] AS Person,
	[Date],
	[LastUpdated],
	Ua.Name AS ApprovedByAccountant,
	Uaa.Name AS ApprovedBy,
	[IsRegional],
	[IsTotal],
	st.[Description] AS Status,
	st.[Id] AS StatusId,
	sc.HasSignoffImage,
	sc.[IsActive],
	
	CASE
		WHEN sc.IsRegional = 1 AND sc.IsTotal = 0 THEN SUM(regFigs.StockItemsCount)
		WHEN sc.IsRegional = 0 AND sc.IsTotal = 1 THEN SUM(totFigs.StockItemsCount)
		ELSE(SELECT Count(Id) FROM StockItems WHERE StockCheckId = sc.Id)
	END as StockItemsCount,

	CASE
		WHEN sc.IsRegional = 1 AND sc.IsTotal = 0 THEN SUM(regFigs.StockValue) * 0.001
		WHEN sc.IsRegional = 0 AND sc.IsTotal = 1 THEN SUM(totFigs.StockValue)* 0.001
		ELSE(SELECT SUM(StockValue) * 0.001 FROM StockItems WHERE StockCheckId = sc.Id)
	END as InStockValue,

	CASE
		WHEN sc.IsRegional = 1 AND sc.IsTotal = 0 THEN SUM(regFigs.MissingValue)
		WHEN sc.IsRegional = 0 AND sc.IsTotal = 1 THEN SUM(totFigs.MissingValue)
		ELSE(
			SELECT SUM(StockValue) * 0.001 FROM StockItems sti
			LEFT JOIN MissingResolutions mr on mr.Id = sti.MissingResolutionId 
			WHERE sti.StockCheckId = sc.Id
			AND sti.IsDuplicate = 0 
			AND sti.ScanId IS NULL 
			AND sti.ReconcilingItemId IS NULL
			AND 
			(mr.Id IS  NULL
			OR
			mr.IsResolved = 0
			)
		)
	END as UnresolvedMissingValue,

	CASE
		WHEN sc.IsRegional = 1 AND sc.IsTotal = 0 THEN SUM(regFigs.Scans)
		WHEN sc.IsRegional = 0 AND sc.IsTotal = 1 THEN SUM(totFigs.Scans)
		ELSE (SELECT COUNT(Id) FROM Scans WHERE StockCheckId = sc.Id) 
	END as Scans,
	CASE
		WHEN sc.IsRegional = 1 AND sc.IsTotal = 0 THEN SUM(regFigs.ScannedInStock)
		WHEN sc.IsRegional = 0 AND sc.IsTotal = 1 THEN SUM(totFigs.ScannedInStock)
		ELSE 
		(
			SELECT COUNT(si.Id) 
			FROM StockItems si 
			INNER JOIN Scans scans on scans.Id = si.ScanId AND scans.StockCheckId = sc.Id
			WHERE si.StockCheckId = sc.Id
			AND si.IsDuplicate = 0
		)
	END as ScannedInStock,
	
	CASE
		WHEN sc.IsRegional = 1 AND sc.IsTotal = 0 THEN SUM(regFigs.UnknownsTot)
		WHEN sc.IsRegional = 0 AND sc.IsTotal = 1 THEN SUM(totFigs.UnknownsTot)
		ELSE (SELECT COUNT(Id) FROM Scans WHERE StockCheckId = sc.Id AND IsDuplicate = 0 AND StockItemId IS NULL AND ReconcilingItemId IS NULL) 
	END as UnknownsTot,
	CASE
		WHEN sc.IsRegional = 1 AND sc.IsTotal = 0 THEN SUM(regFigs.UnknownOs)
		WHEN sc.IsRegional = 0 AND sc.IsTotal = 1 THEN SUM(totFigs.UnknownOs)
		ELSE (
			SELECT COUNT(sca.Id) 
			FROM Scans sca
			LEFT JOIN UnknownResolutions ur on ur.Id = sca.UnknownResolutionId
			WHERE sca.StockCheckId = sc.Id 
			AND sca.IsDuplicate = 0 
			AND sca.StockItemId IS NULL 
			AND sca.ReconcilingItemId IS NULL 
			AND 
				(ur.Id IS  NULL
				OR
				ur.IsResolved = 0
				)
			)
	END as UnknownOs,

	CASE
		WHEN sc.IsRegional = 1 AND sc.IsTotal = 0 THEN SUM(regFigs.MissingsTot)
		WHEN sc.IsRegional = 0 AND sc.IsTotal = 1 THEN SUM(totFigs.MissingsTot)
		ELSE (SELECT COUNT(Id) FROM StockItems WHERE StockCheckId = sc.Id AND IsDuplicate = 0 AND ScanId IS NULL AND ReconcilingItemId IS NULL) 
	END as MissingsTot,
	CASE
		WHEN sc.IsRegional = 1 AND sc.IsTotal = 0 THEN SUM(regFigs.MissingOs)
		WHEN sc.IsRegional = 0 AND sc.IsTotal = 1 THEN SUM(totFigs.MissingOs)
		ELSE (
			SELECT COUNT(st.Id) 
			FROM StockItems st 
			LEFT JOIN MissingResolutions mr on mr.Id = st.MissingResolutionId 
			WHERE st.StockCheckId = sc.Id 
			AND st.IsDuplicate = 0 
			AND st.ScanId IS NULL 
			AND st.ReconcilingItemId IS NULL 
			AND 
				(mr.Id IS  NULL
				OR
				mr.IsResolved = 0
				)
			)
	END as MissingOs,
	sc.ReconciliationCompletedDate,
	sc.ReconciliationApprovedDate,
	si.Longitude AS SiteLongitude,
	si.Latitude AS SiteLatitude,
	si.OverrideLongLat,
	D.Description AS SiteRegion,

	CASE
		WHEN sc.IsRegional = 1 AND sc.IsTotal = 0 THEN regFigs.FirstScan
		WHEN sc.IsRegional = 0 AND sc.IsTotal = 1 THEN totFigs.FirstScan
		ELSE (SELECT TOP 1 ScanDateTime FROM Scans WHERE StockCheckId = sc.Id ORDER BY ScanDateTime)
	END AS FirstScan,
	
	CASE
		WHEN sc.IsRegional = 1 AND sc.IsTotal = 0 THEN regFigs.LastScan
		WHEN sc.IsRegional = 0 AND sc.IsTotal = 1 THEN totFigs.LastScan
		ELSE (SELECT TOP 1 ScanDateTime FROM Scans WHERE StockCheckId = sc.Id ORDER BY ScanDateTime DESC)
	END AS LastScan,

	CASE
		WHEN sc.IsRegional = 1 AND sc.IsTotal = 0 THEN (regFigs.GLValue / 1000)
		WHEN sc.IsRegional = 0 AND sc.IsTotal = 1 THEN (totFigs.GLValue / 1000)
		ELSE (SUM(fl.Balance) / 1000)
	END AS GLValue
  
FROM [dbo].[StockChecks] AS sc
  INNER JOIN  Sites AS si ON si.Id = sc.SiteId
  INNER JOIN  Users AS U ON U.Id = sc.UserId
  INNER JOIN  Statuses AS st ON st.Id = sc.StatusId
  INNER JOIN Divisions as D ON D.Id = si.DivisionId 
  LEFT JOIN FinancialLines fl ON fl.StockCheckId = sc.Id
  LEFT JOIN Users Ua ON Ua.Id = sc.ApprovedByAccountantId
  LEFT JOIN Users Uaa ON Uaa.Id = sc.ApprovedById
  LEFT JOIN RegionalStats regFigs on regFigs.DivisionId = si.DivisionId and regFigs.StockCheckDate = sc.Date and sc.IsRegional = 1
  LEFT JOIN TotalStats totFigs on totFigs.StockCheckDate = sc.Date and sc.IsTotal = 1
  

WHERE  
	(@IsActive IS NULL OR SC.IsActive = @IsActive)
	AND si.Id IN (SELECT SiteId FROM #userSites)
	AND (@stockCheckId IS NULL OR sc.Id = @stockCheckId)
	AND (@FromDate IS NULL OR sc.Date >= @FromDate)
	AND (@ToDate IS NULL OR sc.Date <= @ToDate)

GROUP BY 
	sc.Id,
	si.[Description],
	si.[Id],
	u.[Name] ,
	[Date],
	[LastUpdated],
	Ua.Name ,
	Uaa.Name,
	[IsRegional],
	[IsTotal],
	ST.[Description] ,
	ST.[Id] ,
	SC.HasSignoffImage,
	SC.[IsActive],
	SC.ReconciliationCompletedDate,
	SC.ReconciliationApprovedDate,
	si.Longitude,
	si.Latitude,
	si.OverrideLongLat,
	D.Description,
	regFigs.FirstScan,
	regFigs.LastScan,
	totFigs.FirstScan,
	totFigs.LastScan,
	regFigs.GLValue,
	totFigs.GLValue

ORDER BY
	sc.[Date] desc, si.[Description]



DROP TABLE #userSites

END





--############################## Next SP #############################################﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].[GET_StockConsignment]
(  
    @StockCheckId INT = NULL,  
    @UserId INT = NULL  
)  
AS  
BEGIN  
  
SET NOCOUNT ON;  
  
IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END

  
DECLARE @isRegional INT;  
SET @isRegional = (SELECT IsRegional FROM StockChecks  
                    WHERE StockChecks.Id = @StockCheckId)  
  
DECLARE @isTotal INT;  
SET @isTotal = (SELECT IsTotal FROM StockChecks  
                    WHERE StockChecks.Id = @StockCheckId)  
  
IF @isRegional = 0 AND @isTotal = 0  
   
    BEGIN   
  
	   SELECT Count(1) AS Units, SUM(StockValue) AS Balance 
	   FROM StockItems 
	   WHERE StockType LIKE '%Consign%'
	   AND StockCheckId = @StockCheckId
  
    END  
  
IF @isRegional = 1 AND @isTotal = 0  
  
    BEGIN   
      
    DECLARE @DivisionId INT;  
  
    SET @DivisionId = (SELECT DivisionId FROM StockChecks  
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId  
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id  
                        WHERE StockChecks.Id = @StockCheckId AND StockChecks.IsRegional = 1)  
  
    SELECT Count(1) AS Units, SUM(StockValue) AS Balance 
    FROM StockItems
    INNER JOIN StockChecks ON StockChecks.Id=StockItems.StockCheckId  
    INNER JOIN Sites ON Sites.Id=StockChecks.SiteId  
    INNER JOIN Divisions ON Divisions.Id=Sites.DivisionId  
    WHERE Divisions.Id = @DivisionId  
    
  
    END  
  
IF @isRegional = 0 AND @isTotal = 1  
  
    BEGIN   
      
    DECLARE @DealerGroupId INT;  
      
    SET @DealerGroupId = (SELECT DealerGroupId FROM StockChecks  
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId  
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id  
                        WHERE StockChecks.Id = @StockCheckId)  
  
    SELECT Count(1) AS Units, SUM(StockValue) AS Balance 
    FROM StockItems
    INNER JOIN StockChecks ON StockChecks.Id=StockItems.StockCheckId  
    INNER JOIN Sites ON Sites.Id=StockChecks.SiteId  
    INNER JOIN Divisions ON Divisions.Id=Sites.DivisionId  
    INNER JOIN DealerGroup ON Divisions.DealerGroupId=DealerGroup.Id  
    WHERE DealerGroup.Id = @DealerGroupId  
    
  
    END  
  
END  
  
GO




--############################## Next SP #############################################﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].GET_StockItem
(
    @StockItemId INT = NULL,
	@UserId INT = NULL
)
AS
BEGIN

SET NOCOUNT ON;

IF dbo.[AuthenticateUser](@UserId, (select StockCheckId from dbo.StockItems where Id = @StockItemId)) = 0
BEGIN 
    RETURN
END


	
SELECT 
Id,Reg,Vin,Description,DIS,GroupDIS,Branch,StockType,Comment,Reference,StockValue
FROM [dbo].[StockItems] 
WHERE Id = @StockItemId

	
END

GO



--To use this run 
 --exec [GET_StockItem] @StockItemId = 1, @UserId = 1

--############################## Next SP #############################################﻿
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO



CREATE OR ALTER PROCEDURE [dbo].[GET_StockItemFullDetail]
(
    @StockItemId INT = NULL	,
	@UserId INT = NULL,
	@SkipAuthCheck bit = 0
)
AS
BEGIN

SET NOCOUNT ON

IF (dbo.[AuthenticateUser](@UserId, (SELECT StockCheckId FROM dbo.StockItems WHERE Id=@StockItemId)) = 0 AND @SkipAuthCheck = 0) 
BEGIN 
    RETURN
END

DECLARE @DealerGroupId INT;
SET @DealerGroupId = (SELECT DealerGroupId FROM Users WHERE Id = @UserId)

DECLARE @StockCheckId INT = (SELECT StockCheckId FROM StockItems WHERE Id = @StockItemId)

--work out first instance of same stockItems
  SELECT
  si.Reg,si.Vin,
  MIN(si.Id) as Id
  INTO #UniqueIds
  FROM StockItems si
  INNER JOIN StockChecks  SC ON SC.Id=SI.StockCheckId 
  WHERE SC.Id = @StockCheckId
  AND si.IsDuplicate = 0
  GROUP BY si.Reg,si.Vin

  --find out everything about that first instance
  SELECT
  si.Id, si.StockCheckId, si.Reg,si.Vin,si.StockType,si.Comment,si.Reference
  INTO #firstItems
  FROM StockItems si
  INNER JOIN #uniqueIds u on u.id = si.id
  DROP TABLE #uniqueIds



select 
--normal stuff
s.Id as StockItemId,
s.Reg,
s.Vin,
s.Description,
DIS,
GroupDIS,
Branch,
s.StockType,
s.Comment,
s.Reference,
StockValue,
s.Flooring,
si.Description as SiteName,
CASE
	WHEN s.IsDuplicate = 1 THEN 'Duplicate'
	WHEN sc.Id IS NOT NULL AND sc.StockCheckId <> s.StockCheckId THEN 'MatchedToOtherSite'
	WHEN sc.Id IS NOT NULL AND sc.StockCheckId = s.StockCheckId THEN  'MatchedToStockOrScan'
	WHEN s.ReconcilingItemId IS NOT NULL THEN 'MatchedToReport'
	WHEN mr.Id IS NOT NULL AND mr.IsResolved = 1 THEN 'Resolved'
	ELSE 'OutstandingIssue'
END as [State],

--scan stuff
sc.Id as ScanId,
locns.Description as LocationDescription,
usrs.Name as ScannerName,
sc.RegConfidence as RegConfidence,
sc.VinConfidence as VinConfidence,
sc.Longitude as Longitude,
sc.Latitude as Latitude,
sc.ScanDateTime,
sc.Comment as ScanComment,
sc.Reg as ScanReg,
sc.Vin as ScanVin,
sc.Description as ScanDescription, 
sc.HasVinImage,
CASE
	WHEN sc.Id IS NOT NULL AND sc.StockCheckId <> s.StockCheckId THEN 'MatchedToOtherSite'
	WHEN sc.Id IS NOT NULL AND sc.StockCheckId = s.StockCheckId THEN  'MatchedToStockOrScan'
	ELSE NULL
END as ScanState,
othrSite.Description as ScanSiteName,


--reconciling item stuff
recitems.Id as ReconcilingItemId,
rit.Id as ReconcilingItemTypeId,
rit.[Description] as ReconcilingItemTypeDescription,
recitems.[Reg] AS ReconcilingItemReg,
recitems.[Vin] AS ReconcilingItemVin,
recitems.[Description] AS ReconcilingItemDesc,
recitems.[Comment] AS ReconcilingItemComment,
recitems.Reference AS ReconcilingItemRef,

--resolution stuff
mr.Id as ResolutionId,
rt.Id as ResolutionTypeId,
rt.Description as ResolutionTypeDescription,
rt.BackupRequired as ResolutionTypeBackup,
mr.IsResolved,
resUsers.Name as ResolvedBy,
mr.ResolutionDate as ResolutionDate,
mr.Notes as ResolutionNotes,
(select String_Agg(CONCAT(Id,'|',ISNULL([FileName],'FileName')),'::') from MissingResolutionImages where MissingResolutionId= mr.Id) as ResolutionImageIds,

--other site stuff
IIF(sc.Id IS NOT NULL AND sc.StockCheckId <> s.StockCheckId,othrSite.Description,'') as OtherSiteName,


--original item stuff if it's a duplicate
IIF(s.IsDuplicate=1,f.Id,null) as OriginalId,
IIF(s.IsDuplicate=1,f.StockType,null) as OriginalStockType,
IIF(s.IsDuplicate=1,f.Comment,null) as OriginalComment,
IIF(s.IsDuplicate=1,f.Reference,null) as OriginalReference,

s.StockCheckId


FROM stockitems s
LEFT JOIN StockChecks AS SCK ON S.StockCheckId = SCK.Id
LEFT JOIN Sites AS SI ON SCK.SiteId = SI.Id
LEFT JOIN Divisions AS D ON SI.DivisionId = D.Id

--Scans joins
LEFT JOIN Scans sc on sc.Id = s.ScanId 
LEFT JOIN Users usrs on usrs.Id = sc.UserId
LEFT JOIN Locations locns on locns.Id = sc.LocationId
LEFT JOIN StockChecks scanStockCheck on scanStockCheck.Id = sc.StockCheckId
LEFT JOIN Sites othrSite on othrSite.Id = scanStockCheck.SiteId

--resolution joins
LEFT JOIN MissingResolutions mr on mr.Id = s.MissingResolutionId
LEFT JOIN ResolutionTypes rt on rt.Id = mr.ResolutionTypeId
LEFT JOIN Users resUsers on resUsers.Id = mr.UserId

--reconciling item joins
LEFT JOIN ReconcilingItems recitems ON recitems.Id = s.ReconcilingItemId
LEFT JOIN ReconcilingItemTypes rit ON rit.Id = recitems.ReconcilingItemTypeId

--duplicate item joins
LEFT JOIN #firstItems f on (
	(f.Reg <> '' AND f.Reg = s.Reg) OR 
	(f.Vin <> '' AND f.Vin = s.Vin)
	) AND f.StockCheckId = s.StockCheckId

where s.Id =  @StockItemId 
AND D.DealerGroupId = @DealerGroupId

DROP TABLE #firstItems
	

END

GO




--############################## Next SP #############################################

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO
  
CREATE OR ALTER PROCEDURE [dbo].[GET_StockItemMatchedToOtherSiteScan]  
(  
    @StockCheckId INT = NULL,  
    @UserId INT = NULL  
)  
AS  
BEGIN  
  
SET NOCOUNT ON;  
  
DECLARE @isRegional INT;    
DECLARE @isTotal INT;    
DECLARE @StockCheckDate DateTime;  
DECLARE @DivisionId INT = NULL;  
DECLARE @DealerGroupId INT = NULL;  
DECLARE @SCId INT = NULL;  
  
  
IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END

  
SELECT   
@isRegional = IsRegional,   
@isTotal = IsTotal,   
@StockCheckDate = Date   
FROM StockChecks    
WHERE StockChecks.Id = @StockCheckId  
    
  

  
  
   
IF @isRegional = 0 AND @isTotal = 0    
    
    BEGIN    
  
 SET @SCId = @StockCheckId;  
    
  
    END    
    
ELSE IF @isRegional = 1 AND @isTotal = 0    
    
    BEGIN    
    
    
    SET @DivisionId = (SELECT DivisionId FROM StockChecks    
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId    
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id    
                        WHERE StockChecks.Id = @StockCheckId AND StockChecks.IsRegional = 1)    
    
    END    
    
ELSE IF @isRegional = 0 AND @isTotal = 1    
    
    BEGIN    
    
        
    SET @DealerGroupId = (SELECT DealerGroupId FROM StockChecks    
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId    
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id    
                        WHERE StockChecks.Id = @StockCheckId)    
    
    
    END   
  
  
     
        SELECT StockItems.[Id]   as StockItemId
        ,StockItems.ScanId  
        ,StockItems.[ReconcilingItemId]  
        ,StockItems.[MissingResolutionId]  
        ,StockItems.[StockCheckId]  
        ,StockItems.[SourceReportId]  
        ,StockItems.[Reg]  
        ,StockItems.[Vin]  
        ,StockItems.[Description]  
        ,StockItems.[DIS]  
        ,StockItems.[GroupDIS]  
        ,StockItems.[Branch]  
        ,StockItems.[Comment]  
        ,StockItems.[StockType]  
        ,StockItems.[Reference]  
        ,StockItems.[StockValue]  
        ,StockItems.[Flooring]
        ,Sites.Id AS matchingSiteId   
        ,matchSite.Description AS MatchingSiteDescription  
        ,Scans.Id AS matchingScanId  
        ,Scans.[Description] AS matchingScanDescription  
        ,Locations.[Description] AS matchingScanLocationDesc  
        ,Users.Name AS matchingScanScannedBy  
        ,Scans.[ScanDateTime] AS matchingScanDateTime  
        ,Sites.Description AS SiteName  ,
        'MatchedToOtherSite' as State
        FROM [dbo].[StockItems]   
        INNER JOIN Scans ON Scans.Id=StockItems.ScanId  
        INNER JOIN StockChecks AS SC ON SC.Id=[StockItems].StockCheckId  
        INNER JOIN Sites ON Sites.Id=SC.SiteId 
		INNER JOIN StockChecks matchScheck on matchScheck.id = scans.StockCheckId
		INNER JOIN Sites matchSite on matchSite.id = matchScheck.SiteId
      INNER JOIN Users ON Users.Id=Scans.UserId  
      INNER JOIN Locations ON Locations.Id=Scans.LocationId  
      INNER JOIN Divisions AS D ON D.Id=Sites.DivisionId  
      INNER JOIN DealerGroup AS DG ON D.DealerGroupId=DG.Id  
        WHERE   
  Scans.StockCheckId <> StockItems.StockCheckId   
  AND SC.Id = ISNULL(@SCId, SC.Id)  
  AND SC.Date = @StockCheckDate  
  AND D.Id = ISNULL(@DivisionId, D.Id)  
  AND DG.Id = ISNULL(@DealerGroupId, DG.Id)  
  
  

  
END  
  
GO

--############################## Next SP #############################################

CREATE OR ALTER PROCEDURE [dbo].[GET_StockItemMatchedToRecItem]    
(    
    @StockCheckId INT = NULL,    
    @UserId INT = NULL    
)    
AS    
BEGIN    
    
SET NOCOUNT ON;    
    
DECLARE @isRegional INT;    
DECLARE @isTotal INT;    
DECLARE @StockCheckDate DateTime;  
DECLARE @DivisionId INT = NULL;  
DECLARE @DealerGroupId INT = NULL;  
DECLARE @SCId INT = NULL;  
  

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END


  
SELECT   
@isRegional = IsRegional,   
@isTotal = IsTotal,   
@StockCheckDate = Date   
FROM StockChecks    
WHERE StockChecks.Id = @StockCheckId  
    

  
    
IF @isRegional = 0 AND @isTotal = 0    
    
    BEGIN    
  
 SET @SCId = @StockCheckId;  
    
  
    END    
    
ELSE IF @isRegional = 1 AND @isTotal = 0    
    
    BEGIN    
    
    
    SET @DivisionId = (SELECT DivisionId FROM StockChecks    
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId    
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id    
                        WHERE StockChecks.Id = @StockCheckId AND StockChecks.IsRegional = 1)    
    
    END    
    
ELSE IF @isRegional = 0 AND @isTotal = 1    
    
    BEGIN    
    
        
    SET @DealerGroupId = (SELECT DealerGroupId FROM StockChecks    
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId    
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id    
                        WHERE StockChecks.Id = @StockCheckId)    
    
    
    END   
  
  
  SELECT     
 si.Id as StockItemId,    
 si.Reg,    
 si.Vin,    
 si.[Description],    
 si.DIS,    
 si.GroupDIS,    
 si.Branch,    
 si.StockType,    
 si.Comment,    
 si.Reference,    
 si.StockValue,
 si.Flooring,
    sites.Description as SiteName,
 si.ReconcilingItemId,    
 rit.Id as 'ReconcilingItemTypeId',    
 rit.[Description] as 'ReconcilingItemTypeDescription',    
 recitems.[Description] AS MatchingDesc,    
 recitems.[Comment] AS MatchingComment,    
 recitems.Reference AS 'MatchingRef'    
 FROM StockItems si    
 INNER JOIN ReconcilingItems recitems ON recitems.Id = si.ReconcilingItemId    
 INNER JOIN ReconcilingItemTypes rit ON rit.Id = recitems.ReconcilingItemTypeId    
 INNER JOIN StockChecks AS SC ON SC.Id=si.StockCheckId    
 INNER JOIN Sites ON Sites.Id=SC.SiteId    
 INNER JOIN Divisions AS D ON D.Id=Sites.DivisionId  
 INNER JOIN DealerGroup AS DG ON D.DealerGroupId=DG.Id  
 WHERE si.ReconcilingItemId IS NOT NULL    
    --AND si.StockCheckId = @StockCheckId     
 AND si.ScanId IS NULL --Added si.ScanId IS NULL to fix (vindis - skoda vw cambridge) issue  
 AND SC.Id = ISNULL(@SCId, SC.Id)  
 AND SC.Date = @StockCheckDate  
 AND D.Id = ISNULL(@DivisionId, D.Id)  
 AND DG.Id = ISNULL(@DealerGroupId, DG.Id)  

UNION ALL

SELECT 
si.Id,    
si.Reg,    
si.Vin,    
si.[Description],    
si.DIS,    
si.GroupDIS,    
si.Branch,    
si.StockType,    
si.Comment,    
si.Reference,    
si.StockValue,
si.Flooring,
sites.Description as SiteName,
si.ReconcilingItemId, 
NULL as 'ReconcilingItemTypeId',    
'Scanned at another site' as 'ReconcilingItemTypeDescription',    
NULL AS MatchingDesc,    
NULL AS MatchingComment,    
NULL AS 'MatchingRef'    
FROM StockItems si     
INNER JOIN Scans sc on sc.Id = si.ScanId    
--INNER JOIN ReconcilingItems recitems ON recitems.Id = si.ReconcilingItemId    
--INNER JOIN ReconcilingItemTypes rit ON rit.Id = recitems.ReconcilingItemTypeId    
INNER JOIN StockChecks AS sck ON sck.Id=si.StockCheckId    
INNER JOIN Sites ON Sites.Id=sck.SiteId    
INNER JOIN Divisions AS D ON D.Id=Sites.DivisionId  
INNER JOIN DealerGroup AS DG ON D.DealerGroupId=DG.Id 
WHERE sc.StockCheckId <> @StockCheckId     
AND ScanId IS NOT NULL
AND sc.IsDuplicate = 0    
AND si.IsDuplicate = 0    
AND si.StockCheckId = @StockCheckId
    
   
END  


GO


--############################## Next SP #############################################SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].GET_StockItemMatchItems
(
    @StockCheckId INT,
	@UserId INT
)
AS
BEGIN

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END


SELECT
s.Id as ItemId,
s.Reg,
s.Vin
FROM StockItems s
INNER JOIN StockChecks sc on sc.Id = s.StockCheckId AND sc.Id = @StockCheckId
ORDER BY s.Id


END


GO



--############################## Next SP #############################################
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].GET_StockItemMatchItemsOtherSites
(
    @StockCheckId INT,
	@UserId INT
)
AS
BEGIN

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END


DECLARE @stockcheckDate Date = (SELECT CONVERT(date,Date) FROM Stockchecks where Id = @StockCheckId);

----------------------------------------------------------
--Find out the dealerGroupId
----------------------------------------------------------
DECLARE @dealerGroupId int = 
(
	SELECT div.DealerGroupId
	FROM Stockchecks sc 
	INNER JOIN Sites s on s.Id = sc.SiteId
	INNER JOIN Divisions div on div.Id = s.DivisionId
	WHERE sc.Id = @StockCheckId
);

DECLARE @IntraGroupMatchingRange int = (SELECT numberValue FROM GlobalParams WHERE DealerGroupId = @dealerGroupId AND Name = 'IntraGroupMatchingRange')
DECLARE @DateFrom date = DATEADD(DAY, -@IntraGroupMatchingRange, @stockcheckDate)
DECLARE @DateTo date = DATEADD(DAY, @IntraGroupMatchingRange, @stockcheckDate)

----------------------------------------------------------
--Firstly work out which relevant stockchecks are
----------------------------------------------------------
SELECT
sc.Id
INTO #relevantStockCheckIds
FROM StockChecks sc
INNER JOIN Sites si on si.id = sc.SiteId
INNER JOIN Divisions div on div.id = si.DivisionId
AND sc.Id <> @StockCheckId
AND div.DealerGroupId = @dealerGroupId
AND
(
	(@IntraGroupMatchingRange > 0 AND CONVERT(date, sc.Date) BETWEEN @DateFrom AND @DateTo)
    OR
    (@IntraGroupMatchingRange = 0 AND CONVERT(date, sc.Date) = @stockcheckDate)
)


----------------------------------------
--Build up final result
----------------------------------------
--Find out which stockItems at other sites do not match to a scan at all
SELECT
s.Id as ItemId,
s.Reg,
s.Vin
FROM StockItems s
INNER JOIN #relevantStockCheckIds rel on rel.Id = s.StockCheckId

WHERE s.ScanId IS NULL

UNION ALL

--Also add on stockitems at other sites that already are matched to my site (is likely will still be the case)
SELECT
s.Id as ItemId,
s.Reg,
s.Vin
FROM StockItems s
INNER JOIN #relevantStockCheckIds rel on rel.Id = s.StockCheckId
LEFT JOIN Scans alreadyMatchedScan on alreadyMatchedScan.Id = s.scanId  --FK to PK.   Assume fast

WHERE alreadyMatchedScan.StockCheckId = @StockCheckId

ORDER BY s.Id



END

GO




--############################## Next SP #############################################﻿  
  
CREATE OR ALTER PROCEDURE [dbo].[GET_StockItems]  
(  
     @StockCheckId INT = NULL,  
     @UserId INT = NULL  
)  
AS  
BEGIN  
  
SET NOCOUNT ON;  
  
DECLARE @isRegional INT;  
DECLARE @isTotal INT;  
DECLARE @StockCheckDate DateTime;  
DECLARE @DivisionId INT = NULL;  
DECLARE @DealerGroupId INT = NULL;  
DECLARE @SCId INT = NULL;  
  
  
IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END

  
SELECT   
@isRegional = IsRegional,   
@isTotal = IsTotal,   
@StockCheckDate = Date   
FROM StockChecks    
WHERE StockChecks.Id = @StockCheckId  
  
  

  
 IF @isRegional = 0 AND @isTotal = 0  
  
  BEGIN  
  
  SET @SCId = @StockCheckId;  
  
  END  
  
 ELSE IF @isRegional = 1 AND @isTotal = 0  
  
  BEGIN  
    
  SET @DivisionId = (SELECT DivisionId FROM StockChecks  
       INNER JOIN Sites ON Sites.Id=StockChecks.SiteId  
       INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id  
       WHERE StockChecks.Id = @StockCheckId AND StockChecks.IsRegional = 1)  
    
  
  END  
  
 ELSE IF @isRegional = 0 AND @isTotal = 1  
  
  BEGIN  
  
    
  SET @DealerGroupId = (SELECT DealerGroupId FROM StockChecks  
       INNER JOIN Sites ON Sites.Id=StockChecks.SiteId  
       INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id  
       WHERE StockChecks.Id = @StockCheckId)  
  
  
  END;  
  

  
  SELECT   
  SI.Id as StockItemId,
  SI.Reg,
  SI.Vin,
  SI.[Description],
  SI.DIS,
  SI.GroupDIS,
  si.Branch,
  si.StockType,
  si.Comment,
  si.Reference,
  si.StockValue,
  si.Flooring,   
  Sites.Description AS SiteName,
  SI.IsAgencyStock,
  CASE
	WHEN si.IsDuplicate = 1 THEN 'Duplicate'
	WHEN sca.Id IS NOT NULL AND sca.StockCheckId <> sca.StockCheckId THEN 'MatchedToOtherSite'
	WHEN sca.Id IS NOT NULL AND sca.StockCheckId = sca.StockCheckId THEN  'MatchedToStockOrScan'
	WHEN si.ReconcilingItemId IS NOT NULL THEN 'MatchedToReport'
	WHEN mr.Id IS NOT NULL THEN 'Resolved'
	ELSE 'OutstandingIssue'
END as [State],
rt.Description as ResolutionTypeDescription,
fi.FileName,
fi.FileDate,
fi.LoadDate,
u.Name As UserName

  FROM [dbo].[StockItems] AS SI   
  INNER JOIN StockChecks AS SC ON SC.Id=SI.StockCheckId   
  INNER JOIN Sites ON Sites.Id=SC.SiteId  
  INNER JOIN Divisions AS D ON D.Id=Sites.DivisionId  
  INNER JOIN DealerGroup AS DG ON D.DealerGroupId=DG.Id  
  LEFT JOIN Scans sca on sca.Id = si.ScanId
  LEFT JOIN MissingResolutions mr on mr.id = si.MissingResolutionId
  LEFT JOIN ResolutionTypes rt on rt.id = mr.ResolutionTypeId
  LEFT JOIN [import].[FileImports] fi ON fi.Id = SI.FileImportId
  LEFT JOIN Users u ON u.Id = fi.LoadedByUserId
  WHERE SC.Id = ISNULL(@SCId, SC.Id)   
  AND SC.Date = @StockCheckDate  
  AND D.Id = ISNULL(@DivisionId, D.Id)  
  AND DG.Id = ISNULL(@DealerGroupId, DG.Id)  
    
  
END  

GO


  
  

--############################## Next SP #############################################

CREATE OR ALTER PROCEDURE [dbo].[GET_StockItemsForSite]  
(  
     @SiteId INT = NULL
)  
AS  
BEGIN  
  
SET NOCOUNT ON;

  SELECT * FROM input.StockItems
  WHERE SiteId = @SiteId
  
END  

GO


--############################## Next SP #############################################﻿  
  
CREATE OR ALTER PROCEDURE [dbo].[GET_StockItemsThatAreDuplicated]  
(  
    @StockCheckId INT = NULL,  
 @UserId INT = NULL  
)  
AS  
BEGIN  
  
SET NOCOUNT ON;  
  
DECLARE @isRegional INT;  
DECLARE @isTotal INT;  
DECLARE @StockCheckDate DateTime;  
DECLARE @DivisionId INT = NULL;  
DECLARE @DealerGroupId INT = NULL;  
DECLARE @SCId INT = NULL;  
  
  
IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END

  
SELECT   
@isRegional = IsRegional,   
@isTotal = IsTotal,   
@StockCheckDate = Date   
FROM StockChecks    
WHERE StockChecks.Id = @StockCheckId  
  
  

  
 IF @isRegional = 0 AND @isTotal = 0  
  
  BEGIN  
  
  SET @SCId = @StockCheckId;  
  
  END  
  
 ELSE IF @isRegional = 1 AND @isTotal = 0  
  
  BEGIN  
    
  SET @DivisionId = (SELECT DivisionId FROM StockChecks  
       INNER JOIN Sites ON Sites.Id=StockChecks.SiteId  
       INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id  
       WHERE StockChecks.Id = @StockCheckId AND StockChecks.IsRegional = 1)  
    
  
  END  
  
 ELSE IF @isRegional = 0 AND @isTotal = 1  
  
  BEGIN  
  
    
  SET @DealerGroupId = (SELECT DealerGroupId FROM StockChecks  
       INNER JOIN Sites ON Sites.Id=StockChecks.SiteId  
       INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id  
       WHERE StockChecks.Id = @StockCheckId)  
  END;  
  

  --work out first instance of each stockItem
  SELECT
  si.Reg,
  si.Vin,
  MIN(si.Id) as Id
  INTO #UniqueIds
  FROM StockItems si
  INNER JOIN StockChecks  SC ON SC.Id=SI.StockCheckId 
  INNER JOIN Sites sit ON sit.Id=SC.SiteId  
  INNER JOIN Divisions  D ON D.Id=sit.DivisionId  
  INNER JOIN DealerGroup  DG ON D.DealerGroupId=DG.Id  
  WHERE SC.Id = ISNULL(@SCId, SC.Id)   
  AND SC.Date = @StockCheckDate  
  AND D.Id = ISNULL(@DivisionId, D.Id)  
  AND DG.Id = ISNULL(@DealerGroupId, DG.Id)  
  AND si.IsDuplicate = 0
  GROUP BY si.Reg,si.Vin

  --find out everything about that first instance
  SELECT
  si.Id, si.StockCheckId, si.Reg,si.Vin,si.StockType,si.Comment,si.Reference
  INTO #firstItems
  FROM StockItems si
  INNER JOIN #uniqueIds u on u.id = si.id
  DROP TABLE #uniqueIds

  SELECT   
SI.Id as StockItemId,
SI.Reg,
SI.Vin,
SI.[Description],
SI.DIS,
si.Flooring,
SI.GroupDIS,
si.Branch,
si.StockType,
si.Comment,
si.Reference,
si.StockValue,  
sit.Description AS SiteName  ,
CASE
	WHEN si.IsDuplicate = 1 THEN 'Duplicate'
	WHEN sca.Id IS NOT NULL AND sca.StockCheckId <> sca.StockCheckId THEN 'MatchedToOtherSite'
	WHEN sca.Id IS NOT NULL AND sca.StockCheckId = sca.StockCheckId THEN  'MatchedToStockOrScan'
	WHEN si.ReconcilingItemId IS NOT NULL THEN 'MatchedToReport'
	WHEN mr.Id IS NOT NULL THEN 'Resolved'
	ELSE 'OutstandingIssue'
END as [State],
rt.Description as ResolutionTypeDescription,
f.Id as OriginalStockItemId,
f.StockType as OriginalStockType,
f.Comment as OriginalComment,
f.Reference as OriginalReference


  FROM [dbo].[StockItems]  SI   
  INNER JOIN StockChecks  SC ON SC.Id=SI.StockCheckId   
  INNER JOIN Sites sit ON sit.Id=SC.SiteId  
  INNER JOIN Divisions  D ON D.Id=sit.DivisionId  
  INNER JOIN DealerGroup  DG ON D.DealerGroupId=DG.Id  
  LEFT JOIN Scans sca on sca.Id = si.ScanId
  LEFT JOIN MissingResolutions mr on mr.id = si.MissingResolutionId
  LEFT JOIN ResolutionTypes rt on rt.id = mr.ResolutionTypeId
  INNER JOIN #firstItems f on (
	(f.Reg <> '' AND f.Reg = si.Reg) OR 
	(f.Vin <> '' AND f.Vin = si.Vin)
	) AND f.StockCheckId = si.StockCheckId
  WHERE SC.Id = ISNULL(@SCId, SC.Id)   
  AND SC.Date = @StockCheckDate  
  AND D.Id = ISNULL(@DivisionId, D.Id)  
  AND DG.Id = ISNULL(@DealerGroupId, DG.Id)  
  AND SI.IsDuplicate = 1
  
  DROP TABLE #firstItems

END  
GO


  
  

--############################## Next SP #############################################

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].[GET_StockItemsWithResolution]  
(  
    @StockCheckId INT = NULL,  
    @UserId INT = NULL  
)  
AS  
BEGIN  
  
SET NOCOUNT ON;  
   
DECLARE @isRegional INT;    
DECLARE @isTotal INT;    
DECLARE @StockCheckDate DateTime;  
DECLARE @DivisionId INT = NULL;  
DECLARE @DealerGroupId INT = NULL;  
DECLARE @SCId INT = NULL;  
  
  
IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END


  
SELECT   
@isRegional = IsRegional,   
@isTotal = IsTotal,   
@StockCheckDate = Date   
FROM StockChecks    
WHERE StockChecks.Id = @StockCheckId  
  
  

  
  
IF @isRegional = 0 AND @isTotal = 0  
  
    BEGIN  
  
  SET @SCId = @StockCheckId;  
  
  
    END  
  
ELSE IF @isRegional = 1 AND @isTotal = 0  
  
    BEGIN  
  
  
    SET @DivisionId = (SELECT DivisionId FROM StockChecks  
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId  
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id  
                        WHERE StockChecks.Id = @StockCheckId AND StockChecks.IsRegional = 1)  
  
  
    END  
  
ELSE IF  @isRegional = 0 AND @isTotal = 1  
  
    BEGIN  
  
      
      
    SET @DealerGroupId = (SELECT DealerGroupId FROM StockChecks  
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId  
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id  
                        WHERE StockChecks.Id = @StockCheckId)  
  
  
    END  
  
   
 select   
 s.Id as StockItemId,
 Reg,
 Vin,
 s.Description,
 DIS,
 GroupDIS,
 Branch,
 StockType,
 Flooring,
 Comment,
 Reference,
 StockValue,  
 rt.Id as ResolutionTypeId,  
 rt.Description as ResolutionTypeDescription,  
 mr.Id as ResolutionId,  

 mr.IsResolved,  
 usrs.Name as ResolvedBy,  
 mr.Notes as ResolutionNotes,  
    Sites.Description AS SiteName,
 (select String_Agg(CONCAT(mri.Id,'|',ISNULL(mri.FileName,'FileName')),'::')) AS ResolutionImageIds, --this seems slow TODO
 mr.ResolutionDate as ResolutionDate,
 SC.Date as StockCheckDate
 from stockitems s  
 LEFT JOIN MissingResolutions mr on mr.Id = s.MissingResolutionId  
 LEFT JOIN MissingResolutionImages mri on mri.MissingResolutionId = mr.Id
 LEFT JOIN ResolutionTypes rt on rt.Id = mr.ResolutionTypeId  
 LEFT JOIN Users usrs on usrs.Id = mr.UserId  
 INNER JOIN StockChecks AS SC ON SC.Id=s.StockCheckId  
    INNER JOIN Sites ON Sites.Id=SC.SiteId  
 INNER JOIN Divisions AS D ON D.Id=Sites.DivisionId  
 INNER JOIN DealerGroup AS DG ON D.DealerGroupId=DG.Id  
 where scanId is null  
 and ReconcilingItemId is null  
 --and StockCheckId = @StockCheckId   
 AND s.isDuplicate = 0  
 AND SC.Id = ISNULL(@SCId, SC.Id)  
 AND SC.Date = @StockCheckDate  
 AND D.Id = ISNULL(@DivisionId, D.Id)  
 AND DG.Id = ISNULL(@DealerGroupId, DG.Id)  
 GROUP BY 
 s.Id,
 Reg,
 Vin,
 s.Description,
 DIS,
 GroupDIS,
 Branch,
 StockType,
 Comment,
 Reference,
 Flooring,
 StockValue,  
 rt.Id,  
 rt.Description,  
 mr.Id,  
 mr.IsResolved,  
 usrs.Name,  
 mr.Notes,  
 Sites.Description,
 mr.ResolutionDate,
 SC.Date
  
  
  
END  
  

GO

--############################## Next SP #############################################

DROP PROC IF EXISTS [dbo].[GET_StockItemsWithScan]  
GO
CREATE OR ALTER PROCEDURE [dbo].[GET_StockItemsWithScan]  
(  
    @StockCheckId INT = NULL,  
    @UserId INT = NULL   
)  
AS  
BEGIN  
  
SET NOCOUNT ON;  
  
  
DECLARE @isRegional INT;    
DECLARE @isTotal INT;    
DECLARE @StockCheckDate DateTime;  
DECLARE @DivisionId INT = NULL;  
DECLARE @DealerGroupId INT = NULL;  
DECLARE @SCId INT = NULL;  
  
  
IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END

  
SELECT   
@isRegional = IsRegional,   
@isTotal = IsTotal,   
@StockCheckDate = Date   
FROM StockChecks    
WHERE StockChecks.Id = @StockCheckId  
  
  

IF @isRegional = 0 AND @isTotal = 0  
  
    BEGIN  
   
 SET @SCId = @StockCheckId;  
  
  
  
  
    END  
  
ELSE IF @isRegional = 1 AND @isTotal = 0  
  
    BEGIN  
  
  
    SET @DivisionId = (SELECT DivisionId FROM StockChecks  
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId  
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id  
                        WHERE StockChecks.Id = @StockCheckId AND StockChecks.IsRegional = 1)  
  
      
  
    END  
  
ELSE IF @isRegional = 0 AND @isTotal = 1  
  
    BEGIN  
  
      
    SET @DealerGroupId = (SELECT DealerGroupId FROM StockChecks  
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId  
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id  
                        WHERE StockChecks.Id = @StockCheckId)  
  
     
  
    END  
  
    SELECT   
    st.[Id] as StockItemId, 
    st.Reg,
    st.Vin,
    st.Description, 
    st.DIS,
    st.GroupDIS, 
    st.Branch, 
    st.StockType, 
    st.Comment, 
    st.Reference, 
    st.StockValue,  
    S.Description as ScanDescription,   
    usrs.Name as ScannedBy,  
    S.ScanDateTime,  
    locns.Description as LocationDescription,  
    S.Id as ScanId, 
    st.Flooring, 
    Sites.Description AS SiteName  ,
    s.Longitude,s.Latitude,
    Sites.Longitude as StockCheckLongitude,
    Sites.Latitude as StockCheckLatitude,
    CASE
        WHEN S.IsRegEditedOnWeb = 1 THEN 'Web app'
        WHEN S.IsRegEditedOnDevice = 1 THEN 'Mobile app'
        ELSE NULL
    END AS RegEditStatus,
    CASE
        WHEN S.IsVinEditedOnWeb = 1 THEN 'Web app'
        WHEN S.IsVinEditedOnDevice = 1 THEN 'Mobile app'
        ELSE NULL
    END AS VinEditStatus
  
    from StockItems  st   
    inner join Scans AS S on S.Id = st.ScanId   
    inner join Users usrs on usrs.Id = S.UserId  
    inner join Locations locns on locns.Id = S.LocationId  
    INNER JOIN StockChecks AS SC ON SC.Id=S.StockCheckId  
    INNER JOIN Sites ON SC.SiteId=Sites.Id  
 INNER JOIN Divisions AS D ON D.Id=Sites.DivisionId  
 INNER JOIN DealerGroup AS DG ON D.DealerGroupId=DG.Id  
    WHERE   
 --S.StockCheckId = @StockCheckId   
    ScanId is not null    
    --AND st.StockCheckId = @StockCheckId  
 AND S.StockCheckId = St.StockCheckId  
 AND SC.Id = ISNULL(@SCId, SC.Id)  
 AND SC.Date = @StockCheckDate  
 AND D.Id = ISNULL(@DivisionId, D.Id)  
 AND DG.Id = ISNULL(@DealerGroupId, DG.Id)  

   
   
END  
  


GO

--############################## Next SP #############################################﻿
/****** Object:  StoredProcedure [dbo].[GET_StockItemWithScan]    Script Date: 16/3/21 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].GET_StockItemWithScan
(
    @ScanId INT = NULL	,
	@UserId INT = NULL
)
AS
BEGIN

SET NOCOUNT ON


IF dbo.[AuthenticateUser](@UserId, (select StockCheckId from dbo.Scans where Id = @ScanId)) = 0
BEGIN 
    RETURN
END



	
SELECT 
st.[Id], st.Reg,st.Vin,st.Description, st.DIS,st.GroupDIS, st.Branch, st.StockType, st.Comment, st.Reference, st.StockValue,
sc.Description as 'ScanDescription', 
usrs.Name as 'ScannedBy',
sc.ScanDateTime,
locns.Description as 'LocationDescription',
sc.Id as 'ScanId'

  from StockItems   st 
  inner join Scans sc on sc.Id = st.ScanId 
  inner join Users usrs on usrs.Id = sc.UserId
  inner join Locations locns on locns.Id = sc.LocationId

  WHERE st.ScanId = @ScanId

  

END

GO


--To use this run 
--exec [GET_StockItemWithScan] @StockCheckId = 1

--############################## Next SP #############################################

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].[GET_StockReport]  
(  
    @StockCheckId INT = NULL,  
    @UserId INT = NULL  
)  
AS  
BEGIN  
  
SET NOCOUNT ON;  
  
DECLARE @isRegional INT;  
DECLARE @isTotal INT;  
DECLARE @StockCheckDate DateTime;  
DECLARE @DivisionId INT = NULL;  
DECLARE @DealerGroupId INT = NULL;  
DECLARE @SCId INT = NULL;  
  
  
IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END

  
SELECT   
@isRegional = IsRegional,   
@isTotal = IsTotal,   
@StockCheckDate = Date   
FROM StockChecks    
WHERE StockChecks.Id = @StockCheckId  
  
  

  
 IF @isRegional = 0 AND @isTotal = 0  
  
  BEGIN  
  
  SET @SCId = @StockCheckId;  
  
  END  
  
 ELSE IF @isRegional = 1 AND @isTotal = 0  
  
  BEGIN  
    
  SET @DivisionId = (SELECT DivisionId FROM StockChecks  
       INNER JOIN Sites ON Sites.Id=StockChecks.SiteId  
       INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id  
       WHERE StockChecks.Id = @StockCheckId AND StockChecks.IsRegional = 1)  
    
  
  END  
  
 ELSE IF @isRegional = 0 AND @isTotal = 1  
  
  BEGIN  
  
    
  SET @DealerGroupId = (SELECT DealerGroupId FROM StockChecks  
       INNER JOIN Sites ON Sites.Id=StockChecks.SiteId  
       INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id  
       WHERE StockChecks.Id = @StockCheckId)  
  
  
  END;  
  
    SELECT SourceReports.[Id], SourceReports.[Filename], COUNT(SI.Id) AS Units, SUM(SI.StockValue) AS Balance  
    FROM SourceReports  
    INNER JOIN StockItems AS SI ON SI.SourceReportId=SourceReports.Id  
 INNER JOIN StockChecks AS SC ON SC.Id=SI.StockCheckId   
 INNER JOIN Sites ON Sites.Id=SC.SiteId  
 INNER JOIN Divisions AS D ON D.Id=Sites.DivisionId  
 INNER JOIN DealerGroup AS DG ON D.DealerGroupId=DG.Id  
    WHERE   
 SC.Id = ISNULL(@SCId, SC.Id)   
  AND SC.Date = @StockCheckDate  
  AND D.Id = ISNULL(@DivisionId, D.Id)  
  AND DG.Id = ISNULL(@DealerGroupId, DG.Id)  
    
 --SI.StockCheckId = @StockCheckId   
    GROUP BY SourceReports.[Id], SourceReports.[Filename]  
  
  
  
  END  
  


  GO
  
  

--############################## Next SP #############################################SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].GET_StocksWithLocation
(
    @StockCheckId INT = NULL,
    @UserId INT = NULL
)
AS
BEGIN

SET NOCOUNT ON;

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END


DECLARE @isRegional INT;
SET @isRegional = (SELECT IsRegional FROM StockChecks
                    WHERE StockChecks.Id = @StockCheckId)

DECLARE @isTotal INT;
SET @isTotal = (SELECT IsTotal FROM StockChecks
                    WHERE StockChecks.Id = @StockCheckId)

IF @isRegional = 0 AND @isTotal = 0

    BEGIN
        
    WITH temporaryTable AS 
    (
    SELECT StockItems.Id AS StockItemId, 
    StockItems.[Description] AS Description,
    Scans.Id AS ScanId,
    Locations.[Description] AS Location1,
    ReconcilingItemTypes.Description AS Location2,
    StockItems.Dis,
        CASE WHEN (StockItems.ScanId IS NOT NULL) THEN 'Scanned, InStock' END AS Status1,
        CASE WHEN (StockItems.ReconcilingItemId IS NOT NULL) THEN 'Not scanned, but matched to report' END AS Status2,
        CASE WHEN (StockItems.MissingResolutionId IS NOT NULL) THEN 'Missing vehicles, resolved' END AS Status3,
        CASE WHEN (StockItems.MissingResolutionId IS NULL AND StockItems.ReconcilingItemId IS NULL AND StockItems.ScanId IS NULL) THEN 'Missing vehicles' END AS Status4
    FROM [dbo].[StockItems]
    LEFT JOIN Scans ON StockItems.ScanId = Scans.Id
    LEFT JOIN ReconcilingItems ON StockItems.ReconcilingItemId = ReconcilingItems.Id
    LEFT JOIN Locations ON Scans.LocationId = Locations.Id
    LEFT JOIN ReconcilingItemTypes ON ReconcilingItems.[ReconcilingItemTypeId] = ReconcilingItemTypes.Id
    WHERE [StockItems].[StockCheckId] = @StockCheckId
    )
    SELECT StockItemId AS Id, Description, CONCAT(Location1, Location2) AS LocationName, CONCAT(Status1,Status2,Status3,Status4) AS Status, Dis, ScanId AS ScanId FROM temporaryTable;	

    END

IF @isRegional = 1

    BEGIN

    DECLARE @DivisionId INT;

    SET @DivisionId = (SELECT DivisionId FROM StockChecks
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id
                        WHERE StockChecks.Id = @StockCheckId AND StockChecks.IsRegional = 1)
        
    ;WITH temporaryTable AS 
    (
    SELECT StockItems.Id AS StockItemId, 
    StockItems.[Description] AS Description,
    Scans.Id AS ScanId,
    Locations.[Description] AS Location1,
    ReconcilingItemTypes.Description AS Location2,
    StockItems.Dis,
        CASE WHEN (StockItems.ScanId IS NOT NULL) THEN 'Scanned, InStock' END AS Status1,
        CASE WHEN (StockItems.ReconcilingItemId IS NOT NULL) THEN 'Not scanned, but matched to report' END AS Status2,
        CASE WHEN (StockItems.MissingResolutionId IS NOT NULL) THEN 'Missing vehicles, resolved' END AS Status3,
        CASE WHEN (StockItems.MissingResolutionId IS NULL AND StockItems.ReconcilingItemId IS NULL AND StockItems.ScanId IS NULL) THEN 'Missing vehicles' END AS Status4
    FROM [dbo].[StockItems]
    LEFT JOIN Scans ON StockItems.ScanId = Scans.Id
    LEFT JOIN ReconcilingItems ON StockItems.ReconcilingItemId = ReconcilingItems.Id
    LEFT JOIN Locations ON Scans.LocationId = Locations.Id
    LEFT JOIN ReconcilingItemTypes ON ReconcilingItems.[ReconcilingItemTypeId] = ReconcilingItemTypes.Id
    INNER JOIN StockChecks ON StockChecks.Id=StockItems.StockCheckId
    INNER JOIN Sites ON Sites.Id=StockChecks.SiteId
    INNER JOIN Divisions ON Divisions.Id=Sites.DivisionId
    WHERE Divisions.Id = @DivisionId
    )
    SELECT StockItemId AS Id, Description, CONCAT(Location1, Location2) AS LocationName, CONCAT(Status1,Status2,Status3,Status4) AS Status, Dis, ScanId AS ScanId FROM temporaryTable;	

    END

IF @isTotal = 1

    BEGIN

    DECLARE @DealerGroupId INT;
    SET @DealerGroupId = (SELECT DealerGroupId FROM StockChecks
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id
                        WHERE StockChecks.Id = @StockCheckId)
        
    ;WITH temporaryTable AS 
    (
    SELECT StockItems.Id AS StockItemId, 
    StockItems.[Description] AS Description,
    Scans.Id AS ScanId,
    Locations.[Description] AS Location1,
    ReconcilingItemTypes.Description AS Location2,
    StockItems.Dis,
        CASE WHEN (StockItems.ScanId IS NOT NULL) THEN 'Scanned, InStock' END AS Status1,
        CASE WHEN (StockItems.ReconcilingItemId IS NOT NULL) THEN 'Not scanned, but matched to report' END AS Status2,
        CASE WHEN (StockItems.MissingResolutionId IS NOT NULL) THEN 'Missing vehicles, resolved' END AS Status3,
        CASE WHEN (StockItems.MissingResolutionId IS NULL AND StockItems.ReconcilingItemId IS NULL AND StockItems.ScanId IS NULL) THEN 'Missing vehicles' END AS Status4
    FROM [dbo].[StockItems]
    LEFT JOIN Scans ON StockItems.ScanId = Scans.Id
    LEFT JOIN ReconcilingItems ON StockItems.ReconcilingItemId = ReconcilingItems.Id
    LEFT JOIN Locations ON Scans.LocationId = Locations.Id
    LEFT JOIN ReconcilingItemTypes ON ReconcilingItems.[ReconcilingItemTypeId] = ReconcilingItemTypes.Id
    INNER JOIN StockChecks ON StockChecks.Id=StockItems.StockCheckId
    INNER JOIN Sites ON Sites.Id=StockChecks.SiteId
    INNER JOIN Divisions ON Divisions.Id=Sites.DivisionId
    INNER JOIN DealerGroup ON Divisions.DealerGroupId=DealerGroup.Id
    WHERE DealerGroup.Id = @DealerGroupId
    )
    SELECT StockItemId AS Id, Description, CONCAT(Location1, Location2) AS LocationName, CONCAT(Status1,Status2,Status3,Status4) AS Status, Dis, ScanId AS ScanId FROM temporaryTable;	

    END

END

GO



--To use this run 
--exec [GET_StocksWithLocation] @StockCheckId = 1, @UserId = 104

--############################## Next SP #############################################﻿/****** Object:  StoredProcedure [dbo].[StocksWithStockType]    Script Date: 31/3/21 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].GET_StocksWithStockType
(
    @StockCheckId INT = NULL,
    @UserId INT = NULL
)
AS
BEGIN

SET NOCOUNT ON;

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END


DECLARE @isRegional INT;
SET @isRegional = (SELECT IsRegional FROM StockChecks
                    WHERE StockChecks.Id = @StockCheckId)

DECLARE @isTotal INT;
SET @isTotal = (SELECT IsTotal FROM StockChecks
                    WHERE StockChecks.Id = @StockCheckId)

IF @isRegional = 0 AND @isTotal = 0

    BEGIN
	
    WITH temporaryTable AS 
    (
    SELECT StockItems.Id AS StockItemId, 
    StockItems.[Description] AS Description,
    StockItems.[GroupDIS],
    StockItems.[DIS],
    StockItems.[StockType],
        CASE WHEN (StockItems.ScanId IS NOT NULL) THEN 'Scanned, InStock' END AS Status1,
        CASE WHEN (StockItems.ReconcilingItemId IS NOT NULL) THEN 'Not scanned, but matched to report' END AS Status2,
        CASE WHEN (StockItems.MissingResolutionId IS NOT NULL) THEN 'Missing vehicles, resolved' END AS Status3,
        CASE WHEN (StockItems.MissingResolutionId IS NULL AND StockItems.ReconcilingItemId IS NULL AND StockItems.ScanId IS NULL) THEN 'Missing vehicles' END AS Status4
    FROM [dbo].[StockItems]
    LEFT JOIN ReconcilingItems ON StockItems.ReconcilingItemId = ReconcilingItems.Id
    WHERE [StockItems].[StockCheckId] = @StockCheckId
    )
    SELECT StockItemId AS Id, Description, GroupDIS, DIS, [StockType], CONCAT(Status1,Status2,Status3,Status4) AS Status FROM temporaryTable;	

    END

IF @isRegional = 1

    BEGIN

    DECLARE @DivisionId INT;

    SET @DivisionId = (SELECT DivisionId FROM StockChecks
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id
                        WHERE StockChecks.Id = @StockCheckId AND StockChecks.IsRegional = 1)

	
    ;WITH temporaryTable AS 
    (
    SELECT StockItems.Id AS StockItemId, 
    StockItems.[Description] AS Description,
    StockItems.[GroupDIS],
    StockItems.[DIS],
    StockItems.[StockType],
        CASE WHEN (StockItems.ScanId IS NOT NULL) THEN 'Scanned, InStock' END AS Status1,
        CASE WHEN (StockItems.ReconcilingItemId IS NOT NULL) THEN 'Not scanned, but matched to report' END AS Status2,
        CASE WHEN (StockItems.MissingResolutionId IS NOT NULL) THEN 'Missing vehicles, resolved' END AS Status3,
        CASE WHEN (StockItems.MissingResolutionId IS NULL AND StockItems.ReconcilingItemId IS NULL AND StockItems.ScanId IS NULL) THEN 'Missing vehicles' END AS Status4
    FROM [dbo].[StockItems]
    LEFT JOIN ReconcilingItems ON StockItems.ReconcilingItemId = ReconcilingItems.Id
    INNER JOIN StockChecks ON StockChecks.Id=StockItems.StockCheckId
    INNER JOIN Sites ON Sites.Id=StockChecks.SiteId
    INNER JOIN Divisions ON Divisions.Id=Sites.DivisionId
    WHERE Divisions.Id = @DivisionId
    )
    SELECT StockItemId AS Id, Description, GroupDIS, DIS, [StockType], CONCAT(Status1,Status2,Status3,Status4) AS Status FROM temporaryTable;	

    END

IF @isTotal = 1

    BEGIN

    DECLARE @DealerGroupId INT;
    SET @DealerGroupId = (SELECT DealerGroupId FROM StockChecks
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id
                        WHERE StockChecks.Id = @StockCheckId)

	
    ;WITH temporaryTable AS 
    (
    SELECT StockItems.Id AS StockItemId, 
    StockItems.[Description] AS Description,
    StockItems.[GroupDIS],
    StockItems.[DIS],
    StockItems.[StockType],
        CASE WHEN (StockItems.ScanId IS NOT NULL) THEN 'Scanned, InStock' END AS Status1,
        CASE WHEN (StockItems.ReconcilingItemId IS NOT NULL) THEN 'Not scanned, but matched to report' END AS Status2,
        CASE WHEN (StockItems.MissingResolutionId IS NOT NULL) THEN 'Missing vehicles, resolved' END AS Status3,
        CASE WHEN (StockItems.MissingResolutionId IS NULL AND StockItems.ReconcilingItemId IS NULL AND StockItems.ScanId IS NULL) THEN 'Missing vehicles' END AS Status4
    FROM [dbo].[StockItems]
    LEFT JOIN ReconcilingItems ON StockItems.ReconcilingItemId = ReconcilingItems.Id
    INNER JOIN StockChecks ON StockChecks.Id=StockItems.StockCheckId
    INNER JOIN Sites ON Sites.Id=StockChecks.SiteId
    INNER JOIN Divisions ON Divisions.Id=Sites.DivisionId
    INNER JOIN DealerGroup ON Divisions.DealerGroupId=DealerGroup.Id
    WHERE DealerGroup.Id = @DealerGroupId
    )
    SELECT StockItemId AS Id, Description, GroupDIS, DIS, [StockType], CONCAT(Status1,Status2,Status3,Status4) AS Status FROM temporaryTable;	

    END


END

GO



--To use this run 
--exec [GET_StocksWithStockType] @StockCheckId = 1, @UserId = 1

--############################## Next SP #############################################/****** Object:  StoredProcedure [dbo].[GET_StockVehicles]    Script Date: 22/3/21 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].GET_StockVehicles
(
    @StockCheckId INT = NULL,
    @UserId INT = NULL
)
AS
BEGIN

SET NOCOUNT ON;

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END


SELECT [Id]
  ,[ScanId]
  ,[ReconcilingItemId]
  ,[MissingResolutionId]
  ,[StockCheckId]
  ,[SourceReportId]
  ,[Reg]
  ,[Vin]
  ,[Description]
  ,[DIS]
  ,[GroupDIS]
  ,[Branch]
  ,[Comment]
  ,[StockType]
  ,[Reference]
  ,[StockValue]
FROM [dbo].[StockItems] 
WHERE StockCheckId = @StockCheckId 


END

GO



--To use this run 
--exec [GET_StockVehicles] @StockCheckId = 1, @UserId = 104

--############################## Next SP #############################################﻿/****** Object:  StoredProcedure [dbo].[GET_TotalItems]    Script Date: 19/3/21 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO



CREATE OR ALTER PROCEDURE [dbo].[GET_TotalItems]
(
    @StockCheckId INT = NULL,
	@UserId INT = NULL
)
AS
BEGIN

SET NOCOUNT ON;

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
	RETURN
END



	SELECT COUNT(Id)
	FROM [dbo].[StockItems] 
	WHERE StockCheckId = @StockCheckId 	
	AND IsAgencyStock = 0


	
END

GO





--############################## Next SP #############################################﻿
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO




CREATE OR ALTER PROCEDURE [dbo].[GET_TotalScansCount]
(
    @StockCheckIds varchar(max),
    @UserId INT
)
AS
BEGIN

SET NOCOUNT ON;


IF (dbo.[AuthenticateUserMultiStockChecks](@UserId, @StockCheckIds) = 0)
BEGIN 
    RETURN
END


SELECT COUNT(1) FROM dbo.Scans
WHERE StockCheckId IN (SELECT * FROM STRING_SPLIT(@StockCheckIds,','))

	
END

GO




--############################## Next SP #############################################﻿
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO




CREATE OR ALTER PROCEDURE [dbo].[GET_TotalStockItemsCount]
(
    @StockCheckIds varchar(max),
    @UserId INT
)
AS
BEGIN

SET NOCOUNT ON;


IF (dbo.[AuthenticateUserMultiStockChecks](@UserId, @StockCheckIds) = 0)
BEGIN 
    RETURN
END


SELECT COUNT(1) FROM dbo.StockItems
WHERE StockCheckId IN (SELECT * FROM STRING_SPLIT(@StockCheckIds,','))

	
END

GO




--############################## Next SP #############################################SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].GET_UnknownResolutionImageIds
(
    @ResolutionId INT = NULL
)
AS
BEGIN

SET NOCOUNT ON;

SELECT
Id
FROM UnknownResolutionImages
WHERE UnknownResolutionId = @ResolutionId


END

GO



--To use this run 
--exec [GET_MissingVehicles] @StockCheckId = 1, @UserId = 104

--############################## Next SP #############################################﻿
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROC [dbo].[GET_UserSiteRoleToCache]
AS
BEGIN 
SET NOCOUNT ON

	  SELECT 
	  us.UserId,
	  div.Description
	  INTO #userDivs
	  FROM UserSites us
	  INNER JOIN Sites s on s.id = us.SiteId
	  INNER JOIN Divisions div on div.id = s.DivisionId
	  GROUP BY UserId,div.Description
	  
	  SELECT 
	  UserId, STRING_AGG(Description,',') as DivisionsList
	  INTO #userDivList
	  FROM #UserDivs
	  GROUP BY UserId
	 
	 

  SELECT  
  D.Description as DealerGroupName,
  U.DealerGroupId, 
  U.Id AS UserId,
  aspU.Id as AspNetUserId,
  aspU.UserName,
  aspU.Email, 
  U.Name,
  COALESCE(R.Name,'Missing Role') as RoleName,  
  STRING_AGG(si.Id,',') as SiteIds,
  STRING_AGG(CAST(si.Description AS NVARCHAR(MAX)),',') as SiteNames, 
  sitesDefault.Description as DefaultSiteName,
  udl.DivisionsList as DivisionNames
 
  FROM [AspNetUsers] as aspU
  LEFT JOIN AspNetUserRoles UR ON aspU.Id = UR.UserId
  LEFT JOIN AspNetRoles R on UR.RoleId = R.Id
  INNER JOIN Users as U on aspU.LinkedPersonId = u.Id
  INNER JOIN DealerGroup d on d.Id = U.DealerGroupId
  LEFT JOIN UserSites defaultSite on defaultSite.IsDefault = 1 AND defaultSite.UserId = u.id
  LEFT JOIN Sites sitesDefault on sitesDefault.Id = defaultSite.SiteId
  LEFT JOIN UserSites US ON U.Id = US.UserId
  LEFT JOIN Sites si on si.Id = us.SiteId
  LEFT JOIN #userDivList udl on udl.UserId = U.Id
  GROUP BY 
  D.Description,
  U.DealerGroupId, 
  U.Id, 
  aspU.Id,
  aspU.UserName,
  aspU.Email, 
  U.Name,
  R.Name,
  udl.DivisionsList,
  sitesDefault.Description


   DROP TABLE #userDivList
	  DROP TABLE #userDivs

END
GO





--############################## Next SP #############################################﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [import].[MERGE_Stocks]
(  
	@DealerGroupId INT,
	@StockTypes VARCHAR(300) = NULL
)  
AS
BEGIN

    -- SET NOCOUNT ON added to prevent extra result sets from
    -- interfering with SELECT statements.
    SET NOCOUNT ON

	BEGIN TRAN

	DROP TABLE IF EXISTS #CurrentStockItems 
	DROP TABLE IF EXISTS #NewStockItems
	DROP TABLE IF EXISTS #SameItems

	-- Get the current stock items in the db
	SELECT 
	CONCAT(ISNULL(StkItms.Reg,''), ISNULL(StkItms.Vin,''), Sit.Id) AS [UniqueId],
	StkItms.Id
	INTO #CurrentStockItems 
	FROM StockItems AS StkItms
	INNER JOIN StockChecks AS StkCheck ON StkItms.StockCheckId = StkCheck.Id 
	INNER JOIN Sites as Sit on StkCheck.SiteId = Sit.Id
	INNER JOIN Divisions as Div on Sit.DivisionId = Div.Id
	WHERE Div.DealerGroupId = @DealerGroupId 
	AND StkCheck.IsActive = 1

	-- Get the new stock items
	SELECT
	CONCAT(ISNULL(Inputs.Reg,''), ISNULL(Inputs.Vin,''), Inputs.SiteId) AS [UniqueId]
	INTO #NewStockItems
	FROM [input].[StockItems] As Inputs
	INNER JOIN Sites AS Sit ON Sit.Id = Inputs.SiteId 
	LEFT JOIN StockChecks AS StkCheck ON Inputs.SiteId = StkCheck.SiteId AND StkCheck.IsActive = 1
	LEFT JOIN StockItems AS StkItms ON Inputs.Reg = StkItms.Reg AND Inputs.Vin = StkItms.Vin AND StkCheck.SiteId = Inputs.SiteId
	WHERE StkItms.Id IS NULL
	AND StkCheck.IsActive = 1
	AND Inputs.DealerGroupId = @DealerGroupId

	-- // 1. DELETING OLD STOCK //
	-- Remove any references from Scans to the StockItems we are about to remove
	UPDATE S
	SET S.StockItemId = NULL 
	FROM Scans AS S
	INNER JOIN StockChecks AS StkCheck ON S.StockCheckId = StkCheck.Id
	INNER JOIN Sites as Sit on StkCheck.SiteId = Sit.Id
	INNER JOIN Divisions as Div on Sit.DivisionId = Div.Id
	WHERE Div.DealerGroupId = @DealerGroupId
	AND StkCheck.IsActive = 1
	AND S.StockItemId IN (SELECT Id FROM #CurrentStockItems WHERE UniqueId NOT IN (SELECT * FROM #NewStockItems))

	-- Delete the old stock
	DELETE SI
	FROM StockItems AS SI
	INNER JOIN StockChecks AS StkCheck ON SI.StockCheckId = StkCheck.Id
	INNER JOIN Sites as Sit on StkCheck.SiteId = Sit.Id
	INNER JOIN Divisions as Div on Sit.DivisionId = Div.Id
	WHERE Div.DealerGroupId = @DealerGroupId
	AND StkCheck.IsActive = 1
	AND SI.IsAgencyStock = 0
	AND CONCAT(ISNULL(SI.Reg,''), ISNULL(SI.Vin,''), StkCheck.SiteId) IN (SELECT UniqueId FROM #CurrentStockItems WHERE UniqueId NOT IN (SELECT * FROM #NewStockItems))

	--- // 2. ADDING NEW STOCK 
	INSERT INTO Stockitems 
			   ([ScanId]
			   ,[ReconcilingItemId]
			   ,[MissingResolutionId]
			   ,[StockCheckId]
			   ,[SourceReportId]
			   ,[Reg]
			   ,[Vin]
			   ,[Description]
			   ,[DIS]
			   ,[GroupDIS]
			   ,[Branch]
			   ,[Comment]
			   ,[StockType]
			   ,[Reference]
			   ,[StockValue])
           
	SELECT
	NULL, -- ScanId
	NULL, -- ReconcilingItemId
	NULL, -- MissingResolutionId
	StkCheck.Id, -- StockCheckId
	1, -- SourceReportId
	STK.Reg, 
	STK.Vin,
	STK.Description,
	STK.DIS,
	STK.GroupDIS,
	STK.Branch,
	STK.Comment, 
	STK.StockType, 
	STK.Reference, 
	ISNULL(STK.StockValue,0)
	FROM [input].[StockItems] As STK
	INNER JOIN Sites AS Sit ON Sit.Id = STK.SiteId 
	INNER JOIN Divisions as Div on Sit.DivisionId = Div.Id
	INNER JOIN StockChecks AS StkCheck ON STK.SiteId = StkCheck.SiteId AND StkCheck.IsActive = 1
	WHERE
	Div.DealerGroupId = @DealerGroupId 
	AND
	CONCAT(ISNULL(STK.Reg,''), ISNULL(STK.Vin,''), STK.SiteId) IN 
	(SELECT UniqueId FROM #NewStockItems WHERE UniqueId NOT IN (SELECT UniqueId FROM #CurrentStockItems))

	---- // 3. UPDATE SAME ITEMS //
	SELECT
	STK.Branch,
	STK.Comment,
	STK.Description,
	STK.DealerGroupId,
	STK.DIS,
	STK.FileImportId,
	STK.GroupDIS,
	STK.Reference,
	STK.Reg,
	STK.SiteId,
	STK.SourceReportId,
	STK.StockType,
	STK.StockValue,
	STK.Vin,
	StkCheck.Id AS StockCheckId
	INTO #SameItems
	FROM [input].[StockItems] As STK
	INNER JOIN Sites AS Sit ON Sit.Id = STK.SiteId 
	INNER JOIN Divisions as Div on Sit.DivisionId = Div.Id
	INNER JOIN StockChecks AS StkCheck ON STK.SiteId = StkCheck.SiteId AND StkCheck.IsActive = 1
	WHERE Div.DealerGroupId = 10
 

	UPDATE SI
	SET SI.Reg = STK.Reg,
	SI.Vin = STK.Vin,
	SI.Description = STK.Description,
	SI.DIS = STK.DIS,
	SI.GroupDIS = STK.GroupDIS,
	SI.Branch = STK.Branch,
	SI.Comment = STK.Comment,
	SI.StockType = STK.StockType,
	SI.Reference = STK.Reference,
	SI.StockValue = STK.StockValue
	FROM StockItems As SI
	INNER JOIN StockChecks AS SC ON SI.StockCheckId = SC.Id AND SC.IsActive = 1
	INNER JOIN #SameItems As STK ON ISNULL(SI.Reg,'') = ISNULL(STK.Reg,'') AND ISNULL(SI.Vin,'') = ISNULL(STK.Vin,'') AND SC.SiteId = STK.SiteId
	INNER JOIN Sites as Sit on SC.SiteId = Sit.Id
	INNER JOIN Divisions as Div on Sit.DivisionId = Div.Id
	WHERE Div.DealerGroupId = @DealerGroupId


COMMIT TRAN



END

GO

--############################## Next SP #############################################﻿
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].[INSERT_ErrorReport]
(
    @UserId int,
    @Report nvarchar(max)

)
AS
BEGIN

SET NOCOUNT ON;



    INSERT INTO ErrorReports (UserId,Report)
    Values (@UserId, @Report)

    END 
	


GO




--############################## Next SP #############################################﻿
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].[INSERT_FinancialLine]
(
    @StockCheckId INT = NULL,
    @UserId INT = NULL,
    @FinancialLine FinancialLineType READONLY
)
AS
BEGIN

SET NOCOUNT ON;

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END



INSERT into [FinancialLines] (Code,AccountDescription,Notes,IsExplanation,Balance,StockCheckId,FileImportId) (select * from @FinancialLine)


EXEC [dbo].[UPDATE_StockCheckLastUpdate] @StockCheckId, @UserId


END
	


GO




--############################## Next SP #############################################﻿/****** Object:  StoredProcedure [dbo].[[INSERT_RegScanStat]]    Script Date: 17/3/21 ******/


SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].[INSERT_RegScanStat]
(
    @UserId int,
    @ScanDate datetime,
    @Priority int,
    @WaitTime decimal,
    @DidSucceed bit,
    @FromLocallySaved bit

)
AS
BEGIN

SET NOCOUNT ON

INSERT INTO RegScanStats (UserId,ScanDate,Priority,WaitTime,DidSucceed,FromLocallySaved)  VALUES ( @UserId,@ScanDate,@Priority,@WaitTime,@DidSucceed,@FromLocallySaved )


END

GO








--############################## Next SP #############################################

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE OR ALTER PROCEDURE [dbo].[INSERT_ScanLocationForSiteId]
(
    @SiteId INT = NULL,
	@NewLocation NVARCHAR(MAX) = NULL
)
AS
BEGIN

DECLARE @LocationId INT;

SELECT @LocationId = Id FROM Locations WHERE Description = @NewLocation;

IF @LocationId IS NULL
BEGIN
    INSERT INTO Locations (Description)
    VALUES (@NewLocation);

    SELECT @LocationId = SCOPE_IDENTITY();
END

SET NOCOUNT ON

INSERT INTO SiteLocations (SiteId, LocationId)
VALUES (@SiteId, @LocationId)

END

GO



--############################## Next SP #############################################

CREATE OR ALTER PROCEDURE [dbo].[INSERT_SiteNameLookup]
(
	@Name varchar(max),
	@IsPrimary bit,
	@UserId int,
	@SiteId int
)
  
AS  
BEGIN  

SET NOCOUNT ON;

DECLARE @userDealerGroupId INT;

SET @userDealerGroupId = (SELECT DealerGroupId FROM Users WHERE Id = @UserId)
  
INSERT INTO import.SiteDescriptionDictionary (Description, DealerGroupId, SiteId, IsPrimarySiteId)
VALUES
(@Name, @userDealerGroupId, @SiteId, @IsPrimary)

END
GO


--############################## Next SP #############################################/****** Object:  StoredProcedure [dbo].[INSERT_Stockcheck]    Script Date: 22/04/2023 10:03:22 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].[INSERT_Stockcheck]
(
    @SiteIds varchar(max),
	@UserId INT,
	@Date DateTime
    )
AS
BEGIN

SET NOCOUNT ON

INSERT INTO StockChecks (SiteId,StatusId,Userid,Date,LastUpdated,IsActive, HasSignoffImage, IsRegional,IsTotal)
SELECT VALUE, 1, @UserId, @Date, GETUTCDATE(), 1,0,0,0  from string_split(@siteIds, ',')
	
--DECLARE @NewStockCheckId  int = SCOPE_IDENTITY()

--SELECT @NewStockCheckId AS StockCheckId

END
GO




--############################## Next SP #############################################/****** Object:  StoredProcedure [dbo].[INSERT_StockchecksForAllSites] ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].[INSERT_StockchecksForAllSites]
(
	@SelectedDate Date,
    @DealerGroupId INT,
	@UserId INT
)
AS
BEGIN

SET NOCOUNT ON

	DECLARE @firstDayOfMonth Date = DATEADD(DAY, 1 - DAY(GETDATE()), CAST(GETDATE() AS Date));
	DECLARE @todayDate Date = GETDATE();

	UPDATE SC
	SET IsActive = 0
	FROM StockChecks AS SC
	WHERE SC.SiteId IN (

		SELECT S.ID from Sites AS S 
		INNER JOIN Divisions AS D ON S.DivisionId = D.Id
		WHERE D.DealerGroupId = @DealerGroupId
	)

	--Insert new stockchecks and make them Active
	INSERT INTO [dbo].[StockChecks]
			   ([SiteId]
			   ,[StatusId]
			   ,[UserId]
			   ,[ApprovedByAccountantId]
			   ,[ApprovedByGMId]
			   ,[ApprovedById]
			   ,[Date]
			   ,[LastUpdated]
			   ,[IsActive]
			   ,[HasSignoffImage]
			   ,[IsRegional]
			   ,[IsTotal])
		   
	SELECT S.Id, 1, @UserId, NULL, NULL, NULL, @SelectedDate, @todayDate,1,0,0,0 
	FROM Sites AS S 
	INNER JOIN Divisions AS D ON S.DivisionId = D.Id
	WHERE D.DealerGroupId = @DealerGroupId AND S.IsActive = 1

END

GO

--############################## Next SP #############################################﻿
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].[INSERT_StockItem]
(
    @Reg VARCHAR(50) = NULL,
    @Vin VARCHAR(50) = NULL,
    @Description VARCHAR(500) = NULL,
    @DIS INT = NULL,
    @GroupDIS INT = NULL,
    @Branch VARCHAR(50) = NULL,
    @StockType VARCHAR(50) = NULL,
    @Comment VARCHAR(500) = NULL,
    @Reference VARCHAR(50) = NULL,
    @StockCheckId INT = NULL,
    @UserId INT = NULL,
    @StockValue INT = NULL
)
AS
BEGIN

SET NOCOUNT ON;

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END



-- Note source report is currently hardcoded, needs to change.
INSERT INTO StockItems (Reg, Vin, [Description], DIS, GroupDIS, Branch, StockType, Comment, Reference, 
StockValue, StockCheckId, SourceReportId)
VALUES (@Reg, @Vin, @Description, @DIS, @GroupDIS, @Branch, @StockType, @Comment, @Reference, @StockValue, @StockCheckId, 1);


EXEC [dbo].[UPDATE_StockCheckLastUpdate] @StockCheckId, @UserId


END

GO




--############################## Next SP #############################################﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [import].[Jardine_ImportData]
(  
 @StockChecks varchar(max)  
)  
AS  
BEGIN  

    -- Begin Transaction
    BEGIN TRANSACTION;

    -- Lock the tables involved in the process to prevent concurrent access
    BEGIN TRY

        DECLARE @DealerGroupId INT;
        SET @DealerGroupId = 7 -- Jardine
        --------------------------------------------------------------------------------------------------

        DROP TABLE IF EXISTS #StockCheckIds
        SELECT Value as Id INTO #StockCheckIds from STRING_SPLIT(@StockChecks,',');


          --------------------------------------------------------------------------------------------------
        --Logic same across all imports-- this should be at a common place, - if you change it here , check and change for all----- Lithia UK, Lithia US and Marshalls

		--Check if Stockitems are linked to Scans of other stockchecks and the status of those stockchecks is not complted or approved. 
		------------------------------------------------------------------------------------------
		select DISTINCT s.Description, sc.Id, sc.StatusId, sn.StockItemId
		INTO #StockItemsLinkedToScansInOtherStockChecks
		from Scans sn
		INNER JOIN StockChecks sc ON sc.Id = sn.StockCheckId
		INNER JOIN Sites s ON s.Id = sc.SiteId
		INNER JOIN Divisions di ON di.Id = s.DivisionId
		INNER JOIN DealerGroup dg ON dg.Id = di.DealerGroupId
		INNER JOIN #StockCheckIds sci on sci.Id != sc.Id -- NOT IN 
		WHERE dg.Id = @DealerGroupId 
			and sn.StockItemid in (
				select si.Id
				FROM dbo.StockItems si
				INNER JOIN StockChecks sc ON sc.Id = si.StockCheckId
				INNER JOIN Sites s ON s.Id = sc.SiteId
				INNER JOIN Divisions di ON di.Id = s.DivisionId
				INNER JOIN DealerGroup dg ON dg.Id = di.DealerGroupId
				INNER JOIN #StockCheckIds sci on sci.Id = sc.Id
				WHERE dg.Id = @DealerGroupId 
			)

		
		IF EXISTS (SELECT 1 FROM #StockItemsLinkedToScansInOtherStockChecks where StatusId in (4,5))
		BEGIN
		
			--check if the stockitems which are mapped to other sites is available in the imports
			select siosc.StockItemId, si.Id, si.Reg as OrgReg, si.Vin as OrgVin, isi.Reg as NewReg, isi.Vin as NewVin
			INTO #StockItemsInExistingAndImportTable
				FROM #StockItemsLinkedToScansInOtherStockChecks siosc 
				INNER JOIN dbo.StockItems si on si.Id = siosc.StockItemId
				INNER JOIN StockChecks sc ON sc.Id = si.StockCheckId
				INNER JOIN Sites s ON s.Id = sc.SiteId
				INNER JOIN Divisions di ON di.Id = s.DivisionId
				INNER JOIN DealerGroup dg ON dg.Id = di.DealerGroupId
				INNER JOIN #StockCheckIds sci on sci.Id = sc.Id
				LEFT JOIN input.StockItems isi on isi.SiteId = s.Id and ISNULL(isi.Reg,'-1') = ISNULL(si.Reg,'-1') and ISNULL(isi.Vin,'-1') = ISNULL(si.Vin,'-1')
				WHERE dg.Id = @DealerGroupId and siosc.StatusId in (4,5)
			
			IF EXISTS (select * from #StockItemsInExistingAndImportTable where ISNULL(OrgReg,'-1') != ISNULL(NewReg,'-1') OR ISNULL(OrgVin,'-1') != ISNULL(NewVin,'-1'))
			BEGIN 

				DECLARE @siteNames nvarchar(max);
				DECLARE @errorMessage nvarchar(max);
				SELECT @siteNames = STRING_AGG(ConcatValue, ',') FROM ( SELECT DISTINCT CONCAT(Id, '(', Description, ')') AS ConcatValue FROM #StockItemsLinkedToScansInOtherStockChecks) AS DistinctValues;
				SELECT @errorMessage = CONCAT('Re-importing data for this Stock Check would affect the reconciliation of the following Completed/Approved Stock Checks: ',@siteNames, '. Please move these Stock Checks back to ''Scans Completed'' to re-import data for this Stock Check.');
				;THROW 51000, @errorMessage ,1
			END 

		END

		 
		--Removing links of Stockitems to Scans of other stockchecks 
		UPDATE sca
		SET sca.StockItemId = NULL
		FROM Scans sca
		INNER JOIN StockChecks sc ON sc.Id = sca.StockCheckId
		INNER JOIN Sites s ON s.Id = sc.SiteId
		INNER JOIN Divisions di ON di.Id = s.DivisionId
		INNER JOIN DealerGroup dg ON dg.Id = di.DealerGroupId
		INNER JOIN #StockItemsLinkedToScansInOtherStockChecks sci on sci.Id = sc.Id
		WHERE dg.Id = @DealerGroupId 
		------------------------------------------------------------------------------------------



        -- STOCKCHECK ITEMS
        UPDATE sca
        SET sca.StockItemId = NULL
        FROM Scans sca
        INNER JOIN StockChecks sc ON sc.Id = sca.StockCheckId
        INNER JOIN Sites s ON s.Id = sc.SiteId
        INNER JOIN Divisions di ON di.Id = s.DivisionId
        INNER JOIN DealerGroup dg ON dg.Id = di.DealerGroupId
        INNER JOIN #StockCheckIds sci on sci.Id = sc.Id
        WHERE dg.Id = @DealerGroupId 

        DELETE si
        FROM dbo.StockItems si WITH (TABLOCKX) -- Locking table
        INNER JOIN StockChecks sc ON sc.Id = si.StockCheckId
        INNER JOIN Sites s ON s.Id = sc.SiteId
        INNER JOIN Divisions di ON di.Id = s.DivisionId
        INNER JOIN DealerGroup dg ON dg.Id = di.DealerGroupId
        INNER JOIN #StockCheckIds sci on sci.Id = sc.Id
        WHERE dg.Id = @DealerGroupId
        
        -- RECONCILING ITEMS
        -- Make FKs null so we can do the delete
        UPDATE sca
        SET sca.ReconcilingItemId = NULL
        FROM Scans sca
        INNER JOIN ReconcilingItems r ON r.Id = sca.ReconcilingItemId
        INNER JOIN StockChecks sc ON sc.Id = r.StockCheckId
        INNER JOIN Sites s ON s.Id = sc.SiteId
        INNER JOIN Divisions di ON di.Id = s.DivisionId
        INNER JOIN DealerGroup dg ON dg.Id = di.DealerGroupId
        INNER JOIN #StockCheckIds sci on sci.Id = sc.Id
        WHERE dg.Id = @DealerGroupId 
        AND r.ReconcilingItemTypeId IN (71); 


        -- Delete existing items for StockChecks
        DELETE r
        FROM dbo.ReconcilingItems r WITH (TABLOCKX) -- Locking table
        INNER JOIN StockChecks sc ON sc.Id = r.StockCheckId
        INNER JOIN Sites s ON s.Id = sc.SiteId
        INNER JOIN Divisions di ON di.Id = s.DivisionId
        INNER JOIN DealerGroup dg ON dg.Id = di.DealerGroupId
        INNER JOIN #StockCheckIds sci on sci.Id = sc.Id
        WHERE dg.Id = @DealerGroupId
        AND ReconcilingItemTypeId IN (71);

        -- FL
        DELETE fl
        FROM dbo.FinancialLines fl WITH (TABLOCKX) -- Locking table
        INNER JOIN #StockCheckIds sci on sci.Id = fl.StockCheckId

        -- Declare variables to hold data from the cursor
        DECLARE @StockCheckId INT;
        DECLARE @SiteId INT;

        -- Declare the cursor
        DECLARE stockCheck_cursor CURSOR FOR
            SELECT Id FROM #StockCheckIds;

        -- Open the cursor
        OPEN stockCheck_cursor;

        -- Fetch the first row from the cursor
        FETCH NEXT FROM stockCheck_cursor INTO @StockCheckId;

        -- Loop until there are no more rows
        WHILE @@FETCH_STATUS = 0
        BEGIN

            -- Processing logic for each @StockCheckId
            SET @SiteId = (SELECT SiteId FROM StockChecks WHERE Id = @StockCheckId)

            -- FinancialLines
            INSERT INTO dbo.FinancialLines(Code, AccountDescription, Notes, IsExplanation, Balance, StockCheckId, FileImportId)
            SELECT Code, AccountDescription, NULL, 0, Balance, @StockCheckId, FileImportId
            FROM input.FinancialLines
            WHERE input.FinancialLines.SiteId = @SiteId
            AND input.FinancialLines.DealerGroupId = @DealerGroupId;

            -- Stock
            INSERT INTO dbo.StockItems(ScanId, ReconcilingItemId, MissingResolutionId, StockCheckId, SourceReportId, Reg, Vin, Description, DIS, GroupDIS, Branch, Comment, StockType, Reference, StockValue, IsDuplicate, IsAgencyStock, FileImportId)
            SELECT NULL, NULL, NULL, @StockCheckId, SourceReportId, Reg, SUBSTRING(Vin, LEN(Vin) - 7, 8), Description, DIS, GroupDIS, Branch, Comment, StockType, Reference, StockValue, 0, 0, FileImportId
            FROM input.StockItems
            WHERE input.StockItems.SiteId = @SiteId
            AND input.StockItems.DealerGroupId = @DealerGroupId;
            
            -- Wips
            INSERT INTO dbo.ReconcilingItems(ReconcilingItemTypeId, Reg, Vin, Description, Comment, Reference, SourceReportId, StockCheckId, FileImportId)
            SELECT ReconcilingItemTypeId, Reg, SUBSTRING(Vin, LEN(Vin) - 7, 8), Description, Comment, Reference, SourceReportId, @StockCheckId, FileImportId
            FROM input.ReconcilingItems
            WHERE input.ReconcilingItems.SiteId = @SiteId
            AND input.ReconcilingItems.ReconcilingItemTypeId = 71
            AND input.ReconcilingItems.DealerGroupId = @DealerGroupId;

            -- Fetch the next row from the cursor
            FETCH NEXT FROM stockCheck_cursor INTO @StockCheckId;

        END

        -- Close and deallocate the cursor
        CLOSE stockCheck_cursor;
        DEALLOCATE stockCheck_cursor;

        SELECT DISTINCT Id FROM #StockItemsLinkedToScansInOtherStockChecks -- StockcheckIds to be reconciled again.

        -- Commit the transaction
        COMMIT TRANSACTION;
    
    END TRY
    BEGIN CATCH
        -- Rollback in case of an error
        ROLLBACK TRANSACTION;
        THROW;
    END CATCH;

 END
GO


--############################## Next SP #############################################﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [import].[Lithia_ImportData]
(  
 @StockChecks varchar(max)  
)  
AS  
BEGIN  

    BEGIN TRANSACTION;

    -- Lock the tables involved in the process to prevent concurrent access
    -- This will block other transactions trying to access these tables until this transaction completes
    BEGIN TRY

        -- The following queries will lock the relevant tables until the transaction is completed

        DECLARE @DealerGroupId INT;
        SET @DealerGroupId = 10 -- Lithia
        --------------------------------------------------------------------------------------------------
		
        DROP TABLE IF EXISTS #StockCheckIds
        SELECT Value as Id INTO #StockCheckIds from STRING_SPLIT(@StockChecks,',');

      
        --------------------------------------------------------------------------------------------------
        --Logic same across all imports-- this should be at a common place, - if you change it here , check and change for all----- Lithia UK, Lithia US and Marshalls

		--Check if Stockitems are linked to Scans of other stockchecks and the status of those stockchecks is not complted or approved. 
		------------------------------------------------------------------------------------------
		select DISTINCT s.Description, sc.Id, sc.StatusId, sn.StockItemId
		INTO #StockItemsLinkedToScansInOtherStockChecks
		from Scans sn
		INNER JOIN StockChecks sc ON sc.Id = sn.StockCheckId
		INNER JOIN Sites s ON s.Id = sc.SiteId
		INNER JOIN Divisions di ON di.Id = s.DivisionId
		INNER JOIN DealerGroup dg ON dg.Id = di.DealerGroupId
		INNER JOIN #StockCheckIds sci on sci.Id != sc.Id -- NOT IN 
		WHERE dg.Id = @DealerGroupId 
			and sn.StockItemid in (
				select si.Id
				FROM dbo.StockItems si
				INNER JOIN StockChecks sc ON sc.Id = si.StockCheckId
				INNER JOIN Sites s ON s.Id = sc.SiteId
				INNER JOIN Divisions di ON di.Id = s.DivisionId
				INNER JOIN DealerGroup dg ON dg.Id = di.DealerGroupId
				INNER JOIN #StockCheckIds sci on sci.Id = sc.Id
				WHERE dg.Id = @DealerGroupId 
			)

		
		IF EXISTS (SELECT 1 FROM #StockItemsLinkedToScansInOtherStockChecks where StatusId in (4,5))
		BEGIN
		
			--check if the stockitems which are mapped to other sites is available in the imports
			select siosc.StockItemId, si.Id, si.Reg as OrgReg, si.Vin as OrgVin, isi.Reg as NewReg, isi.Vin as NewVin
			INTO #StockItemsInExistingAndImportTable
				FROM #StockItemsLinkedToScansInOtherStockChecks siosc 
				INNER JOIN dbo.StockItems si on si.Id = siosc.StockItemId
				INNER JOIN StockChecks sc ON sc.Id = si.StockCheckId
				INNER JOIN Sites s ON s.Id = sc.SiteId
				INNER JOIN Divisions di ON di.Id = s.DivisionId
				INNER JOIN DealerGroup dg ON dg.Id = di.DealerGroupId
				INNER JOIN #StockCheckIds sci on sci.Id = sc.Id
				LEFT JOIN input.StockItems isi on isi.SiteId = s.Id and ISNULL(isi.Reg,'-1') = ISNULL(si.Reg,'-1') and ISNULL(isi.Vin,'-1') = ISNULL(si.Vin,'-1')
				WHERE dg.Id = @DealerGroupId and siosc.StatusId in (4,5)
			
			IF EXISTS (select * from #StockItemsInExistingAndImportTable where ISNULL(OrgReg,'-1') != ISNULL(NewReg,'-1') OR ISNULL(OrgVin,'-1') != ISNULL(NewVin,'-1'))
			BEGIN 

				DECLARE @siteNames nvarchar(max);
				DECLARE @errorMessage nvarchar(max);
				SELECT @siteNames = STRING_AGG(ConcatValue, ',') FROM ( SELECT DISTINCT CONCAT(Id, '(', Description, ')') AS ConcatValue FROM #StockItemsLinkedToScansInOtherStockChecks) AS DistinctValues;
				SELECT @errorMessage = CONCAT('Re-importing data for this Stock Check would affect the reconciliation of the following Completed/Approved Stock Checks: ',@siteNames, '. Please move these Stock Checks back to ''Scans Completed'' to re-import data for this Stock Check.');
				;THROW 51000, @errorMessage ,1
			END 

		END

		 
		--Removing links of Stockitems to Scans of other stockchecks 
		UPDATE sca
		SET sca.StockItemId = NULL
		FROM Scans sca
		INNER JOIN StockChecks sc ON sc.Id = sca.StockCheckId
		INNER JOIN Sites s ON s.Id = sc.SiteId
		INNER JOIN Divisions di ON di.Id = s.DivisionId
		INNER JOIN DealerGroup dg ON dg.Id = di.DealerGroupId
		INNER JOIN #StockItemsLinkedToScansInOtherStockChecks sci on sci.Id = sc.Id
		WHERE dg.Id = @DealerGroupId 
		------------------------------------------------------------------------------------------

        -- STOCKCHECK ITEMS
        UPDATE sca
        SET sca.StockItemId = NULL
        FROM Scans sca
        INNER JOIN StockChecks sc ON sc.Id = sca.StockCheckId
        INNER JOIN Sites s ON s.Id = sc.SiteId
        INNER JOIN Divisions di ON di.Id = s.DivisionId
        INNER JOIN DealerGroup dg ON dg.Id = di.DealerGroupId
        INNER JOIN #StockCheckIds sci on sci.Id = sc.Id
        WHERE dg.Id = @DealerGroupId 

        DELETE si
        FROM dbo.StockItems si WITH (TABLOCKX) -- Locking table
        INNER JOIN StockChecks sc ON sc.Id = si.StockCheckId
        INNER JOIN Sites s ON s.Id = sc.SiteId
        INNER JOIN Divisions di ON di.Id = s.DivisionId
        INNER JOIN DealerGroup dg ON dg.Id = di.DealerGroupId
        INNER JOIN #StockCheckIds sci on sci.Id = sc.Id
        WHERE dg.Id = @DealerGroupId
        
        -- RECONCILING ITEMS
        -- Make FKs null so we can do the delete
        UPDATE sca
        SET sca.ReconcilingItemId = NULL
        FROM Scans sca
        INNER JOIN ReconcilingItems r ON r.Id = sca.ReconcilingItemId
        INNER JOIN StockChecks sc ON sc.Id = r.StockCheckId
        INNER JOIN Sites s ON s.Id = sc.SiteId
        INNER JOIN Divisions di ON di.Id = s.DivisionId
        INNER JOIN DealerGroup dg ON dg.Id = di.DealerGroupId
        INNER JOIN #StockCheckIds sci on sci.Id = sc.Id
        WHERE dg.Id = @DealerGroupId 
        AND r.ReconcilingItemTypeId IN (102,101,107); 


        -- Delete existing items for StockChecks
        DELETE r
        FROM dbo.ReconcilingItems r WITH (TABLOCKX) -- Locking table
        INNER JOIN StockChecks sc ON sc.Id = r.StockCheckId
        INNER JOIN Sites s ON s.Id = sc.SiteId
        INNER JOIN Divisions di ON di.Id = s.DivisionId
        INNER JOIN DealerGroup dg ON dg.Id = di.DealerGroupId
        INNER JOIN #StockCheckIds sci on sci.Id = sc.Id
        WHERE dg.Id = @DealerGroupId
        AND ReconcilingItemTypeId IN (102,101,107);


        -- FL
        DELETE fl
        FROM dbo.FinancialLines fl WITH (TABLOCKX) -- Locking table
        INNER JOIN #StockCheckIds sci on sci.Id = fl.StockCheckId

        -- Declare variables to hold data from the cursor
        DECLARE @StockCheckId INT;
        DECLARE @SiteId INT;

        -- Declare the cursor
        DECLARE stockCheck_cursor CURSOR FOR
            SELECT Id FROM #StockCheckIds;

        -- Open the cursor
        OPEN stockCheck_cursor;

        -- Fetch the first row from the cursor
        FETCH NEXT FROM stockCheck_cursor INTO @StockCheckId;

        -- Loop until there are no more rows
        WHILE @@FETCH_STATUS = 0
        BEGIN


            SET @SiteId = (SELECT SiteId FROM StockChecks WHERE Id = @StockCheckId)

            -- FinancialLines
            INSERT INTO dbo.FinancialLines(Code, AccountDescription, Notes, IsExplanation, Balance, StockCheckId, FileImportId)
            SELECT Code, AccountDescription, NULL, 0, Balance, @StockCheckId, FileImportId
            FROM input.FinancialLines
            WHERE input.FinancialLines.SiteId = @SiteId
            AND input.FinancialLines.DealerGroupId = @DealerGroupId;

            -- StockCheckItems
            INSERT INTO dbo.StockItems(ScanId, ReconcilingItemId, MissingResolutionId, StockCheckId, SourceReportId, Reg, Vin, Description, DIS, GroupDIS, Branch, Comment, StockType, Reference, StockValue, IsDuplicate, IsAgencyStock, Flooring, FileImportId)
            SELECT NULL, NULL, NULL, @StockCheckId, SourceReportId, Reg, SUBSTRING(Vin, LEN(Vin) - 7, 8), Description, DIS, GroupDIS, Branch, Comment, StockType, Reference, StockValue, 0, 0, Flooring, FileImportId
            FROM input.StockItems
            WHERE input.StockItems.SiteId = @SiteId
            AND input.StockItems.DealerGroupId = @DealerGroupId;

            ---- BookedAndPending
            INSERT INTO dbo.ReconcilingItems(ReconcilingItemTypeId, Reg, Vin, Description, Comment, Reference, SourceReportId, StockCheckId, FileImportId)
            SELECT ReconcilingItemTypeId, Reg, SUBSTRING(Vin, LEN(Vin) - 7, 8), Description, Comment, Reference, SourceReportId, @StockCheckId, FileImportId
            FROM input.ReconcilingItems
            WHERE input.ReconcilingItems.SiteId = @SiteId
            AND input.ReconcilingItems.ReconcilingItemTypeId = 102
            AND input.ReconcilingItems.DealerGroupId = @DealerGroupId;

            ---- BookedAndPending - Trade
            INSERT INTO dbo.ReconcilingItems(ReconcilingItemTypeId, Reg, Vin, Description, Comment, Reference, SourceReportId, StockCheckId, FileImportId)
            SELECT ReconcilingItemTypeId, Reg, SUBSTRING(Vin, LEN(Vin) - 7, 8), Description, Comment, Reference, SourceReportId, @StockCheckId, FileImportId
            FROM input.ReconcilingItems
            WHERE input.ReconcilingItems.SiteId = @SiteId
            AND input.ReconcilingItems.ReconcilingItemTypeId = 107
            AND input.ReconcilingItems.DealerGroupId = @DealerGroupId;

            ---- WIPs
            INSERT INTO dbo.ReconcilingItems(ReconcilingItemTypeId, Reg, Vin, Description, Comment, Reference, SourceReportId, StockCheckId, FileImportId)
            SELECT ReconcilingItemTypeId, Reg, SUBSTRING(Vin, LEN(Vin) - 7, 8), Description, Comment, Reference, SourceReportId, @StockCheckId, FileImportId
            FROM input.ReconcilingItems
            WHERE input.ReconcilingItems.SiteId = @SiteId
            AND input.ReconcilingItems.ReconcilingItemTypeId = 101
            AND input.ReconcilingItems.DealerGroupId = @DealerGroupId;

            -- Fetch the next row from the cursor
            FETCH NEXT FROM stockCheck_cursor INTO @StockCheckId;

        END

        CLOSE stockCheck_cursor;
        DEALLOCATE stockCheck_cursor;

		SELECT DISTINCT Id FROM #StockItemsLinkedToScansInOtherStockChecks -- StockcheckIds to be reconciled again.

        COMMIT TRANSACTION;
    
    END TRY
    BEGIN CATCH
        -- In case of an error, rollback the transaction to avoid partial updates
        ROLLBACK TRANSACTION;
        THROW;
    END CATCH;

 END
GO


--############################## Next SP #############################################﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [import].[MMG_ImportData]
(  
 @StockChecks varchar(max)  
)  
AS  
BEGIN  

    -- Begin Transaction
    BEGIN TRANSACTION;

    -- Lock the tables involved in the process to prevent concurrent access
    BEGIN TRY
        DECLARE @DealerGroupId INT;
        SET @DealerGroupId = 11 -- MMG
        --------------------------------------------------------------------------------------------------

        DROP TABLE IF EXISTS #StockCheckIds;
        SELECT Value as Id INTO #StockCheckIds from STRING_SPLIT(@StockChecks,',');


        --------------------------------------------------------------------------------------------------
        --Logic same across all imports-- this should be at a common place, - if you change it here , check and change for all----- Lithia UK, Lithia US and Marshalls

		--Check if Stockitems are linked to Scans of other stockchecks and the status of those stockchecks is not complted or approved. 
		------------------------------------------------------------------------------------------
		select DISTINCT s.Description, sc.Id, sc.StatusId, sn.StockItemId
		INTO #StockItemsLinkedToScansInOtherStockChecks
		from Scans sn
		INNER JOIN StockChecks sc ON sc.Id = sn.StockCheckId
		INNER JOIN Sites s ON s.Id = sc.SiteId
		INNER JOIN Divisions di ON di.Id = s.DivisionId
		INNER JOIN DealerGroup dg ON dg.Id = di.DealerGroupId
		INNER JOIN #StockCheckIds sci on sci.Id != sc.Id -- NOT IN 
		WHERE dg.Id = @DealerGroupId 
			and sn.StockItemid in (
				select si.Id
				FROM dbo.StockItems si
				INNER JOIN StockChecks sc ON sc.Id = si.StockCheckId
				INNER JOIN Sites s ON s.Id = sc.SiteId
				INNER JOIN Divisions di ON di.Id = s.DivisionId
				INNER JOIN DealerGroup dg ON dg.Id = di.DealerGroupId
				INNER JOIN #StockCheckIds sci on sci.Id = sc.Id
				WHERE dg.Id = @DealerGroupId 
			)

		
		IF EXISTS (SELECT 1 FROM #StockItemsLinkedToScansInOtherStockChecks where StatusId in (4,5))
		BEGIN
		
			--check if the stockitems which are mapped to other sites is available in the imports
			select siosc.StockItemId, si.Id, si.Reg as OrgReg, si.Vin as OrgVin, isi.Reg as NewReg, isi.Vin as NewVin
			INTO #StockItemsInExistingAndImportTable
				FROM #StockItemsLinkedToScansInOtherStockChecks siosc 
				INNER JOIN dbo.StockItems si on si.Id = siosc.StockItemId
				INNER JOIN StockChecks sc ON sc.Id = si.StockCheckId
				INNER JOIN Sites s ON s.Id = sc.SiteId
				INNER JOIN Divisions di ON di.Id = s.DivisionId
				INNER JOIN DealerGroup dg ON dg.Id = di.DealerGroupId
				INNER JOIN #StockCheckIds sci on sci.Id = sc.Id
				LEFT JOIN input.StockItems isi on isi.SiteId = s.Id and ISNULL(isi.Reg,'-1') = ISNULL(si.Reg,'-1') and ISNULL(isi.Vin,'-1') = ISNULL(si.Vin,'-1')
				WHERE dg.Id = @DealerGroupId and siosc.StatusId in (4,5)
			
			IF EXISTS (select * from #StockItemsInExistingAndImportTable where ISNULL(OrgReg,'-1') != ISNULL(NewReg,'-1') OR ISNULL(OrgVin,'-1') != ISNULL(NewVin,'-1'))
			BEGIN 

				DECLARE @siteNames nvarchar(max);
				DECLARE @errorMessage nvarchar(max);
				SELECT @siteNames = STRING_AGG(ConcatValue, ',') FROM ( SELECT DISTINCT CONCAT(Id, '(', Description, ')') AS ConcatValue FROM #StockItemsLinkedToScansInOtherStockChecks) AS DistinctValues;
				SELECT @errorMessage = CONCAT('Re-importing data for this Stock Check would affect the reconciliation of the following Completed/Approved Stock Checks: ',@siteNames, '. Please move these Stock Checks back to ''Scans Completed'' to re-import data for this Stock Check.');
				;THROW 51000, @errorMessage ,1
			END 

		END

		 
		--Removing links of Stockitems to Scans of other stockchecks 
		UPDATE sca
		SET sca.StockItemId = NULL
		FROM Scans sca
		INNER JOIN StockChecks sc ON sc.Id = sca.StockCheckId
		INNER JOIN Sites s ON s.Id = sc.SiteId
		INNER JOIN Divisions di ON di.Id = s.DivisionId
		INNER JOIN DealerGroup dg ON dg.Id = di.DealerGroupId
		INNER JOIN #StockItemsLinkedToScansInOtherStockChecks sci on sci.Id = sc.Id
		WHERE dg.Id = @DealerGroupId 
		------------------------------------------------------------------------------------------



        -- Step 1: Set StockItemId to NULL in Scans table manually
        UPDATE dbo.Scans
        SET StockItemId = NULL
        WHERE StockItemId IN (
            SELECT si.Id 
            FROM dbo.StockItems si WITH (TABLOCKX) -- Locking table
            INNER JOIN StockChecks sc ON sc.Id = si.StockCheckId
            INNER JOIN Sites s ON s.Id = sc.SiteId
            INNER JOIN Divisions di ON di.Id = s.DivisionId
            INNER JOIN DealerGroup dg ON dg.Id = di.DealerGroupId
            INNER JOIN #StockCheckIds sci ON sci.Id = sc.Id
            WHERE dg.Id = @DealerGroupId
        );

        -- Step 2: Set ReconcilingItemId in StockItems to NULL to avoid conflicts
        UPDATE dbo.StockItems
        SET ReconcilingItemId = NULL
        WHERE StockCheckId IN (
            SELECT sc.Id
            FROM StockChecks sc
            INNER JOIN Sites s ON s.Id = sc.SiteId
            INNER JOIN Divisions di ON di.Id = s.DivisionId
            INNER JOIN DealerGroup dg ON dg.Id = di.DealerGroupId
            INNER JOIN #StockCheckIds sci ON sci.Id = sc.Id
            WHERE dg.Id = @DealerGroupId
        );

        -- Step 3: Delete from StockItems after handling dependencies
        DELETE si
        FROM dbo.StockItems si WITH (TABLOCKX) -- Locking table
        INNER JOIN StockChecks sc ON sc.Id = si.StockCheckId
        INNER JOIN Sites s ON s.Id = sc.SiteId
        INNER JOIN Divisions di ON di.Id = s.DivisionId
        INNER JOIN DealerGroup dg ON dg.Id = di.DealerGroupId
        INNER JOIN #StockCheckIds sci ON sci.Id = sc.Id
        WHERE dg.Id = @DealerGroupId;

        -- Step 4: Nullify ReconcilingItemId in Scans before deleting ReconcilingItems
        UPDATE dbo.Scans
        SET ReconcilingItemId = NULL
        FROM Scans sca
        INNER JOIN ReconcilingItems r ON r.Id = sca.ReconcilingItemId
        INNER JOIN StockChecks sc ON sc.Id = r.StockCheckId
        INNER JOIN Sites s ON s.Id = sc.SiteId
        INNER JOIN Divisions di ON di.Id = s.DivisionId
        INNER JOIN DealerGroup dg ON dg.Id = di.DealerGroupId
        INNER JOIN #StockCheckIds sci ON sci.Id = sc.Id
        WHERE dg.Id = @DealerGroupId
        AND r.ReconcilingItemTypeId IN (93, 94, 95);

        -- Step 5: Delete from ReconcilingItems
        DELETE r
        FROM dbo.ReconcilingItems r WITH (TABLOCKX) -- Locking table
        INNER JOIN StockChecks sc ON sc.Id = r.StockCheckId
        INNER JOIN Sites s ON s.Id = sc.SiteId
        INNER JOIN Divisions di ON di.Id = s.DivisionId
        INNER JOIN DealerGroup dg ON dg.Id = di.DealerGroupId
        INNER JOIN #StockCheckIds sci ON sci.Id = sc.Id
        WHERE dg.Id = @DealerGroupId
        AND r.ReconcilingItemTypeId IN (93, 94, 95);

        -- Declare variables to hold data from the cursor
        DECLARE @StockCheckId INT;
        DECLARE @SiteId INT;

        -- Declare the cursor
        DECLARE stockCheck_cursor CURSOR FOR
            SELECT Id FROM #StockCheckIds;

        -- Open the cursor
        OPEN stockCheck_cursor;

        -- Fetch the first row from the cursor
        FETCH NEXT FROM stockCheck_cursor INTO @StockCheckId;

        -- Loop until there are no more rows
        WHILE @@FETCH_STATUS = 0
        BEGIN

            -- Processing logic for each @StockCheckId
            SET @SiteId = (SELECT SiteId FROM StockChecks WHERE Id = @StockCheckId)

            -- FinancialLines
			INSERT INTO dbo.FinancialLines(Code, AccountDescription, Notes, IsExplanation, Balance, StockCheckId, FileImportId)
			SELECT Code, AccountDescription, NULL, 0, Balance, @StockCheckId, FileImportId
			FROM input.FinancialLines i
			WHERE i.SiteId = @SiteId
			AND i.DealerGroupId = @DealerGroupId
			AND NOT EXISTS (
				SELECT 1 FROM dbo.FinancialLines f
				WHERE f.StockCheckId = @StockCheckId
				AND f.Code = i.Code
				AND f.AccountDescription = i.AccountDescription
				AND f.Balance = i.Balance
                AND f.FileImportId = i.FileImportId
			);

            -- StockCheckItems
            INSERT INTO dbo.StockItems(ScanId, ReconcilingItemId, MissingResolutionId, StockCheckId, SourceReportId, Reg, Vin, Description, DIS, GroupDIS, Branch, Comment, StockType, Reference, StockValue, IsDuplicate, IsAgencyStock, FileImportId)
            SELECT NULL, NULL, NULL, @StockCheckId, SourceReportId, Reg, SUBSTRING(Vin, LEN(Vin) - 7, 8), Description, DIS, GroupDIS, Branch, Comment, StockType, Reference, StockValue, 0, 0, FileImportId
            FROM input.StockItems
            WHERE input.StockItems.SiteId = @SiteId
            AND input.StockItems.DealerGroupId = @DealerGroupId;

            ---- AtAuctions
            INSERT INTO dbo.ReconcilingItems(ReconcilingItemTypeId, Reg, Vin, Description, Comment, Reference, SourceReportId, StockCheckId, FileImportId)
            SELECT ReconcilingItemTypeId, Reg, SUBSTRING(Vin, LEN(Vin) - 7, 8), Description, Comment, Reference, SourceReportId, @StockCheckId, FileImportId
            FROM input.ReconcilingItems
            WHERE input.ReconcilingItems.SiteId = @SiteId
            AND input.ReconcilingItems.ReconcilingItemTypeId = 93
            AND input.ReconcilingItems.DealerGroupId = @DealerGroupId;

            ---- MMGStockOnLoans
            INSERT INTO dbo.ReconcilingItems(ReconcilingItemTypeId, Reg, Vin, Description, Comment, Reference, SourceReportId, StockCheckId, FileImportId)
            SELECT ReconcilingItemTypeId, Reg, SUBSTRING(Vin, LEN(Vin) - 7, 8), Description, Comment, Reference, SourceReportId, @StockCheckId, FileImportId
            FROM input.ReconcilingItems
            WHERE input.ReconcilingItems.SiteId = @SiteId
            AND input.ReconcilingItems.ReconcilingItemTypeId = 94
            AND input.ReconcilingItems.DealerGroupId = @DealerGroupId;
            
            ---- MMGWIPs
            INSERT INTO dbo.ReconcilingItems(ReconcilingItemTypeId, Reg, Vin, Description, Comment, Reference, SourceReportId, StockCheckId, FileImportId)
            SELECT ReconcilingItemTypeId, Reg, SUBSTRING(Vin, LEN(Vin) - 7, 8), Description, Comment, Reference, SourceReportId, @StockCheckId, FileImportId
            FROM input.ReconcilingItems
            WHERE input.ReconcilingItems.SiteId = @SiteId
            AND input.ReconcilingItems.ReconcilingItemTypeId = 95
            AND input.ReconcilingItems.DealerGroupId = @DealerGroupId;

            ---- ECOS
            INSERT INTO dbo.FinancialLines(Code, AccountDescription, Notes, IsExplanation, Balance, StockCheckId, FileImportId)
            SELECT NULL, StockType, Reg + ' ' + Vin + '  ' + Description, 1, StockValue, @StockCheckId, FileImportId
            FROM input.StockItems
            WHERE input.StockItems.SiteId = @SiteId
            AND input.StockItems.StockType = 'ECOS/CBS'
            AND input.StockItems.DealerGroupId = @DealerGroupId;

            -- Fetch the next row from the cursor
            FETCH NEXT FROM stockCheck_cursor INTO @StockCheckId;

        END

        -- Close and deallocate the cursor
        CLOSE stockCheck_cursor;
        DEALLOCATE stockCheck_cursor;

        SELECT DISTINCT Id FROM #StockItemsLinkedToScansInOtherStockChecks -- StockcheckIds to be reconciled again.

        -- Commit the transaction
        COMMIT TRANSACTION;
    
    END TRY
    BEGIN CATCH
        -- Rollback in case of an error
        ROLLBACK TRANSACTION;
        THROW;
    END CATCH;

 END
GO


--############################## Next SP #############################################﻿

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROC [dev].Setup_Dev
AS
BEGIN

UPDATE GlobalParams
SET StringValue = 'sp=r&st=2021-12-17T17:27:52Z&se=2041-02-14T00:27:52Z&spr=https&sv=2020-08-04&sr=c&sig=HC3gt1Pj6Vbiph%2BmA4TeTjWlR7Toghvt%2BXV3CAiFym4%3D'
WHERE [Name] = 'ImageKey'

UPDATE GlobalParams
SET StringValue = 'https://stockpulseimages.blob.core.windows.net/images-dev/'
WHERE [Name] = 'ConfigFilePath'

UPDATE AspNetUsers
SET LockoutEnd = '2099-01-01'
WHERE email not like '%@cphi.co.uk' AND email not like '%@cphinsight.com'


END

GO




--############################## Next SP #############################################﻿


CREATE OR ALTER PROC [dev].[Setup_Dev_US]
AS
BEGIN

UPDATE GlobalParams
SET StringValue = 'sp=r&st=2023-11-09T11:57:46Z&se=2053-11-09T19:57:46Z&sv=2022-11-02&sr=c&sig=7V7TMsrGWWBJiFqR5svCF4D5tW9GG9l3xUX5UQk2cUQ%3D'
WHERE [Name] = 'ImageKey'

UPDATE GlobalParams
SET StringValue = 'https://cphius.blob.core.windows.net/stockpulseimages-dev/'
WHERE [Name] = 'ConfigFilePath'

UPDATE AspNetUsers
SET LockoutEnd = '2099-01-01'
WHERE email not like '%@cphi.co.uk' AND email not like '%@cphinsight.com'


END

GO

--############################## Next SP #############################################﻿

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER   PROC [dev].[Setup_Test]
AS
BEGIN

UPDATE GlobalParams
SET StringValue = 'sp=r&st=2023-05-24T09:01:09Z&se=2053-02-01T18:01:09Z&spr=https&sv=2022-11-02&sr=c&sig=CmvraZF9dOTVaPNo7%2Fks1mJUK7ChQHHhgILXRh5Q2zs%3D'
WHERE [Name] = 'ImageKey'

UPDATE GlobalParams
SET StringValue = 'https://stockpulseimages.blob.core.windows.net/images-test/'
WHERE [Name] = 'ConfigFilePath'

UPDATE AspNetUsers
SET LockoutEnd = '2099-01-01'
WHERE email not like '%@cphi.co.uk' AND email not like '%@cphinsight.com'


END



--############################## Next SP #############################################﻿
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].[UPDATE_FinancialLine]
(
    @StockCheckId INT = NULL,
    @UserId INT = NULL,
    @FinancialLineId INT = NULL,
    @Description NVarchar(50),
    @Notes nvarchar(250),
    @Balance decimal(15,3)
    
)
AS
BEGIN

SET NOCOUNT ON;


IF (dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0 OR dbo.[CanAmmendStockCheck](@StockCheckId) = 0 )
BEGIN 
    RETURN
END


UPDATE [FinancialLines] Set AccountDescription = @Description, Notes = @Notes, Balance = @Balance 
WHERE Id = @FinancialLineId

EXEC [dbo].[UPDATE_StockCheckLastUpdate] @StockCheckId, @UserId


	
END

GO




--############################## Next SP #############################################﻿  
CREATE OR ALTER PROCEDURE [dbo].[UPDATE_ImportMask]  
(
@UserId int,  
@Id int,
@Name varchar(30) = NULL ,  
 
 @TopRowsToSkip int = NULL ,  
 @ColumnValueEqualsesJSON varchar(500) = NULL ,  
 @ColumnValueDifferentFromsJSON varchar(500) = NULL ,  
 @ColumnValueNotNullsJSON varchar(500) = NULL ,  
 @ColumnsWeWantJSON varchar(500) = NULL,
 @IsStandard bit = 0,
 @IsMultiSite bit = 0,
 @IgnoreZeroValues bit = 0   
)  
AS  
BEGIN  
  
SET NOCOUNT ON  

UPDATE ImportMasks
SET 
[Name] = @Name,
TopRowsToSkip = @TopRowsToSkip,
ColumnValueEqualsesJSON  = @ColumnValueEqualsesJSON,
ColumnValueDifferentFromsJSON = @ColumnValueDifferentFromsJSON,
ColumnValueNotNullsJSON = @ColumnValueNotNullsJSON,
ColumnsWeWantJSON = @ColumnsWeWantJSON,
UserId = @UserId,
IsStandard = @IsStandard,
IsMultiSite = @IsMultiSite,
IgnoreZeroValues = @IgnoreZeroValues

WHERE Id = @Id
  

END  

GO
  

--############################## Next SP #############################################

CREATE OR ALTER PROCEDURE [dbo].[UPDATE_ImportMaskName]
(
	@Id int,
	@Name varchar(max),
	@IsStandard bit
)
  
AS  
BEGIN  

SET NOCOUNT ON;  
  
UPDATE ImportMasks
SET Name = @Name, IsStandard = @IsStandard
WHERE Id = @Id

END
GO


--############################## Next SP #############################################﻿
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].[UPDATE_Location]
(
    @ScanId INT = NULL	,
	@UserId INT = null,
    @NewLocationId INT = NULL	
)
AS
BEGIN

SET NOCOUNT ON

Declare @StockCheckId int = null
set @StockCheckId = (select StockCheckId from Scans where Id = @ScanId)


IF (dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0 OR dbo.[CanSaveScans](@StockCheckId) = 0)
BEGIN 
	RETURN
END


UPDATE Scans SET LocationId = @NewLocationId WHERE Id = @ScanId

EXEC [dbo].[UPDATE_StockCheckLastUpdate] @StockCheckId, @UserId

END

GO

	




--############################## Next SP #############################################﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].[UPDATE_MissingResolution]
(
    @StockCheckId INT,
	@UserId INT,
	@MissingResolutionId INT,
	@ResolutionTypeId INT,
	@ResolutionDate datetime,
	@Notes nvarchar(250),
	@IsResolved bit
)
AS
BEGIN

SET NOCOUNT ON;

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END


UPDATE [MissingResolutions] 
SET ResolutionTypeId = @ResolutionTypeId,
ResolutionDate = @ResolutionDate,
Notes = @Notes,
IsResolved = @IsResolved,
UserId = @UserId
WHERE Id = @MissingResolutionId


EXEC [dbo].[UPDATE_StockCheckLastUpdate] @StockCheckId, @UserId


END
	


GO




--############################## Next SP #############################################﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].UPDATE_SaveReconciliationResults
(
    @duplicatedScans MatchedItem readonly,
    @scansMatchedToStockItems MatchedItem readonly,
    @scansMatchedToOtherSiteStockItems MatchedItem readonly,
    @scansMatchedToRecItems MatchedItem readonly,
    @remainingUnmatchedScans MatchedItem readonly,
    
    @duplicatedStockItems MatchedItem readonly,
    @stockItemsMatchedToScans MatchedItem readonly,
    @stockItemsMatchedToOtherSiteScans MatchedItem readonly,
    @stockItemsMatchedToRecItems MatchedItem readonly,
	@remainingUnmatchedStockItems MatchedItem readonly,
	
    @stockCheckId INT,
	@userId int
)
AS
BEGIN

SET NOCOUNT ON;



--Only proceed if allowed
IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0 OR dbo.[CanAmmendStockCheck](@StockCheckId)  = 0
BEGIN 
    RETURN 
END


-------------------
--Scans
-------------------
--Update duplicatedScans
CREATE TABLE #duplicatedScans  ([ItemId] INT NOT NULL,[MatchItemId] INT NULL);	INSERT INTO #duplicatedScans SELECT ItemId, MatchItemId from @duplicatedScans
UPDATE Scans
SET Scans.IsDuplicate = IIF(#duplicatedScans.ItemId IS NOT NULL,1,0) --this will adjust all of them within the current stockcheck
FROM Scans LEFT JOIN #duplicatedScans on #duplicatedScans.ItemId = Scans.Id
WHERE Scans.StockCheckId = @stockCheckId
--also now release any scanIds they have against them
UPDATE Scans
SET StockItemId = NULL, UnknownResolutionId = NULL, ReconcilingItemId = NULL
FROM Scans INNER JOIN #duplicatedScans on #duplicatedScans.ItemId = Scans.Id
WHERE Scans.StockCheckId = @stockCheckId


--Update scansMatchedToStockItems
CREATE TABLE #scansMatchedToStockItems  ([ItemId] INT NOT NULL,[MatchItemId] INT NULL);	INSERT INTO #scansMatchedToStockItems SELECT ItemId, MatchItemId from @scansMatchedToStockItems
UPDATE Scans
SET Scans.StockItemId = #scansMatchedToStockItems.MatchItemId, Scans.ReconcilingItemId = NULL
FROM Scans INNER JOIN #scansMatchedToStockItems on #scansMatchedToStockItems.ItemId = Scans.Id
WHERE Scans.StockCheckId = @stockCheckId

--Update scansMatchedToOtherSiteStockItems
CREATE TABLE #scansMatchedToOtherSiteStockItems  ([ItemId] INT NOT NULL,[MatchItemId] INT NULL);	INSERT INTO #scansMatchedToOtherSiteStockItems SELECT ItemId, MatchItemId from @scansMatchedToOtherSiteStockItems
UPDATE Scans
SET Scans.StockItemId = #scansMatchedToOtherSiteStockItems.MatchItemId, Scans.ReconcilingItemId = NULL
FROM Scans INNER JOIN #scansMatchedToOtherSiteStockItems on #scansMatchedToOtherSiteStockItems.ItemId = Scans.Id
WHERE Scans.StockCheckId = @stockCheckId

--         --Update scansMatchedToRecItems
CREATE TABLE #scansMatchedToRecItems  ([ItemId] INT NOT NULL,[MatchItemId] INT NULL);	INSERT INTO #scansMatchedToRecItems SELECT ItemId, MatchItemId from @scansMatchedToRecItems
UPDATE Scans
SET Scans.ReconcilingItemId = #scansMatchedToRecItems.MatchItemId, Scans.StockItemId = NULL
FROM Scans INNER JOIN #scansMatchedToRecItems on #scansMatchedToRecItems.ItemId = Scans.Id
WHERE Scans.StockCheckId = @stockCheckId

--         --Update remainingUnmatchedScans
CREATE TABLE #remainingUnmatchedScans  ([ItemId] INT NOT NULL,[MatchItemId] INT NULL);	INSERT INTO #remainingUnmatchedScans SELECT ItemId, MatchItemId from @remainingUnmatchedScans
UPDATE Scans
SET Scans.ReconcilingItemId = NULL, Scans.StockItemId = NULL
FROM Scans INNER JOIN #remainingUnmatchedScans on #remainingUnmatchedScans.ItemId = Scans.Id
WHERE Scans.StockCheckId = @stockCheckId


-------------------
--StockItems
-------------------
--Update duplicatedStockItems
CREATE TABLE #duplicatedStockItems  ([ItemId] INT NOT NULL,[MatchItemId] INT NULL);	INSERT INTO #duplicatedStockItems SELECT ItemId, MatchItemId from @duplicatedStockItems
UPDATE StockItems
SET StockItems.IsDuplicate = IIF(#duplicatedStockItems.ItemId IS NOT NULL,1,0) -- will adjust them all
FROM StockItems LEFT JOIN #duplicatedStockItems on #duplicatedStockItems.ItemId = StockItems.Id
WHERE StockItems.StockCheckId = @stockCheckId
--also now release any scanIds they have against them
UPDATE StockItems
SET ScanId = NULL, MissingResolutionId = NULL, ReconcilingItemId = NULL
FROM StockItems INNER JOIN #duplicatedStockItems on #duplicatedStockItems.ItemId = StockItems.Id
WHERE StockItems.StockCheckId = @stockCheckId


--Update stockItemsMatchedToScans
CREATE TABLE #stockItemsMatchedToScans  ([ItemId] INT NOT NULL,[MatchItemId] INT NULL);	INSERT INTO #stockItemsMatchedToScans SELECT ItemId, MatchItemId from @stockItemsMatchedToScans
UPDATE StockItems
SET StockItems.ScanId = #stockItemsMatchedToScans.MatchItemId, StockItems.ReconcilingItemId = null
FROM StockItems INNER JOIN #stockItemsMatchedToScans on #stockItemsMatchedToScans.ItemId = StockItems.Id
WHERE StockItems.StockCheckId = @stockCheckId

--Update stockItemsMatchedToOtherSiteScans
CREATE TABLE #stockItemsMatchedToOtherSiteScans  ([ItemId] INT NOT NULL,[MatchItemId] INT NULL);	INSERT INTO #stockItemsMatchedToOtherSiteScans SELECT ItemId, MatchItemId from @stockItemsMatchedToOtherSiteScans
UPDATE StockItems
SET StockItems.ScanId = #stockItemsMatchedToOtherSiteScans.MatchItemId, StockItems.ReconcilingItemId = null
FROM StockItems INNER JOIN #stockItemsMatchedToOtherSiteScans on #stockItemsMatchedToOtherSiteScans.ItemId = StockItems.Id
WHERE StockItems.StockCheckId = @stockCheckId

--Update stockItemsMatchedToRecItems
CREATE TABLE #stockItemsMatchedToRecItems  ([ItemId] INT NOT NULL,[MatchItemId] INT NULL);	INSERT INTO #stockItemsMatchedToRecItems SELECT ItemId, MatchItemId from @stockItemsMatchedToRecItems
UPDATE StockItems
SET StockItems.ReconcilingItemId = #stockItemsMatchedToRecItems.MatchItemId, StockItems.ScanId = null
FROM StockItems INNER JOIN #stockItemsMatchedToRecItems on #stockItemsMatchedToRecItems.ItemId = StockItems.Id
WHERE StockItems.StockCheckId = @stockCheckId

--Update remainingUnmatchedStockItems
CREATE TABLE #remainingUnmatchedStockItems  ([ItemId] INT NOT NULL,[MatchItemId] INT NULL);	INSERT INTO #remainingUnmatchedStockItems SELECT ItemId, MatchItemId from @remainingUnmatchedStockItems
UPDATE StockItems
SET StockItems.ScanId = NULL, StockItems.ReconcilingItemId = NULL
FROM StockItems INNER JOIN #remainingUnmatchedStockItems on #remainingUnmatchedStockItems.ItemId = StockItems.Id
WHERE StockItems.StockCheckId = @stockCheckId



--         -------------------
--         --CleanUp
--         -------------------
DROP TABLE #duplicatedScans
DROP TABLE #scansMatchedToStockItems
DROP TABLE #scansMatchedToOtherSiteStockItems
DROP TABLE #scansMatchedToRecItems
DROP TABLE #remainingUnmatchedScans
            
DROP TABLE #duplicatedStockItems
DROP TABLE #stockItemsMatchedToScans
DROP TABLE #stockItemsMatchedToOtherSiteScans
DROP TABLE #stockItemsMatchedToRecItems
DROP TABLE #remainingUnmatchedStockItems




END

GO



--############################## Next SP #############################################﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].[UPDATE_ScanNote]
(
    @ScanId INT = NULL	,
    @NewNote nvarchar(500) = NULL	,
	@UserId INT = NULL

)
AS
BEGIN

SET NOCOUNT ON

Declare @StockCheckId int = null
set @StockCheckId = (select StockCheckId from Scans where Id = @ScanId)

IF (dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0 OR dbo.[CanSaveScans](@StockCheckId) = 0)
BEGIN 
	RETURN
END
	

update Scans set Comment = @NewNote where Id = @ScanId

EXEC [dbo].[UPDATE_StockCheckLastUpdate] @StockCheckId, @UserId

	

END

GO




--############################## Next SP #############################################﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].[UPDATE_ScanReg]
(
    @ScanId INT = NULL	,
    @NewReg nvarchar(10) = NULL	,
	@IsMobileApp INT,
	@UserId INT = NULL

)
AS
BEGIN

SET NOCOUNT ON

DECLARE @StockCheckId INT = null
SET @StockCheckId = (SELECT StockCheckId FROM Scans WHERE Id = @ScanId)

IF (dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0 OR dbo.[CanSaveScans](@StockCheckId) = 0)
BEGIN 
	RETURN
END
	
UPDATE Scans SET 
	Reg = @NewReg, 
	IsRegEditedOnDevice = IIF(@IsMobileApp = 1, 1, IsRegEditedOnDevice)  ,
	IsRegEditedOnWeb = IIF(@IsMobileApp = 0, 1, IsRegEditedOnWeb)  
	WHERE Id = @ScanId

EXEC [dbo].[UPDATE_StockCheckLastUpdate] @StockCheckId, @UserId

SELECT @StockCheckId

	

END

GO




--############################## Next SP #############################################﻿
/****** Object:  StoredProcedure [dbo].[UPDATE_ScansStarted]    Script Date: 30/04/2021 10:30:35 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


  
CREATE OR ALTER PROCEDURE [dbo].[UPDATE_ScansStarted]  
(  
    @StockCheckId INT = NULL,  
    @UserId INT = NULL  
)  
AS  
BEGIN  
  
SET NOCOUNT ON;  
  

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
	RETURN
END

  
UPDATE StockChecks  
SET StatusId = 2  
WHERE StockChecks.StatusId = 1  
AND Id = @StockCheckId  

-- If status was updated, log it
IF @@ROWCOUNT > 0
    BEGIN
        EXEC dbo.ADD_StatusChangeLogItem 
            @StockCheckId = @StockCheckId,
            @UserId = @UserId,
            @StatusId = 2;
    END
  
  
END  
  
GO




--############################## Next SP #############################################﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].[UPDATE_ScanVin]
(
    @ScanId INT = NULL	,
    @NewVin NVARCHAR(10) = NULL	,
	@IsMobileApp INT,
	@UserId INT = NULL

)
AS
BEGIN

SET NOCOUNT ON

Declare @StockCheckId INT = null
SET @StockCheckId = (SELECT StockCheckId FROM Scans WHERE Id = @ScanId)

IF (dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0 OR dbo.[CanSaveScans](@StockCheckId) = 0)
BEGIN 
	RETURN
END
	
UPDATE Scans SET 
	Vin = @NewVin ,
	IsVinEditedOnDevice = IIF(@IsMobileApp = 1, 1, IsVinEditedOnDevice)  ,
	IsVinEditedOnWeb = IIF(@IsMobileApp = 0, 1, IsVinEditedOnWeb)  
	WHERE Id = @ScanId

EXEC [dbo].[UPDATE_StockCheckLastUpdate] @StockCheckId, @UserId

SELECT @StockCheckId



END

GO




--############################## Next SP #############################################﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].[UPDATE_ScanWithUnknownResolution]
(
    @StockCheckId INT,
	@UserId INT,
	@UnknownResolutionId INT,
	@ScanId INT
)
AS
BEGIN

SET NOCOUNT ON;

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END

UPDATE dbo.Scans
SET UnknownResolutionId = @UnknownResolutionId 
WHERE Id = @ScanId

EXEC [dbo].[UPDATE_StockCheckLastUpdate] @StockCheckId, @UserId


END
	


GO




--############################## Next SP #############################################

CREATE OR ALTER PROCEDURE [dbo].[UPDATE_SiteNameLookups]
(
	@Name varchar(max),
	@IsPrimary bit,
	@UserId int,
	@SiteId int,
	@Id int
)
  
AS  
BEGIN  

SET NOCOUNT ON;

DECLARE @userDealerGroupId INT;

SET @userDealerGroupId = (SELECT DealerGroupId FROM Users WHERE Id = @UserId)
  
UPDATE import.SiteDescriptionDictionary
SET Description = @Name, IsPrimarySiteId = @IsPrimary, SiteId = @SiteId
WHERE
Id = @Id AND
DealerGroupId = @userDealerGroupId

END
GO


--############################## Next SP #############################################﻿
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO



CREATE OR ALTER PROCEDURE [dbo].[UPDATE_StockCheckHasImageflag]
(
    @UserId INT = NULL,
	@StockCheckId INT = NULL,
    @HasSignOffImage INT = NULL	
)
AS
BEGIN

SET NOCOUNT ON;

--Firstly check if allowed
IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END



UPDATE dbo.[StockChecks] SET [HasSignoffImage] = @HasSignOffImage WHERE [Id] = @StockCheckId 

EXEC [dbo].[UPDATE_StockCheckLastUpdate] @StockCheckId, @UserId



END

GO




--############################## Next SP #############################################﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].[UPDATE_StockCheckLastUpdate]
(
    @StockCheckId INT = NULL,
    @UserId INT = NULL
)
AS
BEGIN

SET NOCOUNT ON;

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END


UPDATE StockChecks 
SET UserId = @UserId, LastUpdated = GETUTCDATE()
WHERE Id = @StockCheckId

	
END

GO




--############################## Next SP #############################################﻿/****** Object:  StoredProcedure [dbo].[UPDATE_StockCheckStatus]    Script Date: 01/05/2021 20:06:53 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].[UPDATE_StockCheckStatus]
(
    @UserId INT = NULL,
	@StockCheckId INT = NULL,
    @NewStatusId INT = NULL	
)
AS
BEGIN

SET NOCOUNT ON;


IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END

--Check if allowed to update
DECLARE @userCanDoThis INT;
set @userCanDoThis = dbo.[UserCanMoveStatusTo](@NewStatusId,@UserId,@StockCheckId)

if @userCanDoThis = 1

BEGIN 

	--Get existing approvedByAccountant
	DECLARE @existingApprovedByAccountantId int;
	set @existingApprovedByAccountantId = (select ApprovedByAccountantId from dbo.[stockchecks] where id = @StockCheckId)

	--Get existing approvedBy
	DECLARE @existingApprovedById int;
	set @existingApprovedById  = (select ApprovedById from dbo.[stockchecks] where id = @StockCheckId)
										
	update dbo.[StockChecks] set [StatusId] = @NewStatusId WHERE [Id] = @StockCheckId 

	--set approved by accountant if required
	if @NewStatusId > 3 AND @existingApprovedByAccountantId is null
		BEGIN
			update dbo.[StockChecks]
			set [ApprovedByAccountantId] = @UserId, [ReconciliationCompletedDate] = GETUTCDATE()
			where id = @StockCheckId
		END

	--set approved if required
	if @NewStatusId > 4 AND @existingApprovedById is null
		BEGIN
			update dbo.[StockChecks]
			set [ApprovedById] = @UserId, [ReconciliationApprovedDate] = GETUTCDATE()
			where id = @StockCheckId
		END

	--unset approved if required
	if @NewStatusId <5 AND @existingApprovedById is not null
		BEGIN
			update dbo.[StockChecks]
			set [ApprovedById] = null, [ReconciliationApprovedDate] = null
			where id = @StockCheckId
		END

	--unset approvedByAccountant if required
	if @NewStatusId <4 AND @existingApprovedByAccountantId is not null
		BEGIN
			update dbo.[StockChecks]
			set [ApprovedByAccountantId] = null, [ReconciliationCompletedDate] = null
			where id = @StockCheckId
		END

					
	EXEC [dbo].[UPDATE_StockCheckLastUpdate] @StockCheckId, @UserId

END




END



--############################## Next SP #############################################﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].[UPDATE_StockCheckToArchiveState]
(
    @StockCheckIds varchar(max),
    @UserId INT
)
AS
BEGIN

SET NOCOUNT ON;


IF (dbo.[AuthenticateUserMultiStockChecks](@UserId, @StockCheckIds) = 0)
BEGIN 
    RETURN
END

    -- Temporary table to store the updated StockCheckIds and StatusId
    DECLARE @UpdatedStockChecks TABLE
    (
        StockCheckId INT,
        StatusId INT
    );

    -- Update StockChecks and capture the updated StockCheckId and StatusId
    UPDATE StockChecks
    SET
        IsActive = 0,
        StatusId = CASE
            WHEN StatusId < 3 THEN 3 
            ELSE StatusId 
        END
    OUTPUT INSERTED.Id, INSERTED.StatusId INTO @UpdatedStockChecks(StockCheckId, StatusId)
    WHERE Id IN (SELECT value FROM STRING_SPLIT(@StockCheckIds, ','));

    -- Log the status changes for all updated StockCheckIds
    INSERT INTO StatusChangeLogItems (StockCheckId, UserId, Date, StatusId)
    SELECT StockCheckId, @UserId, GETUTCDATE(), StatusId FROM @UpdatedStockChecks;

END

GO




--############################## Next SP #############################################﻿
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].[UPDATE_StockCheckToUnArchiveState]
(
    @StockCheckIds varchar(max),
    @UserId INT
)
AS
BEGIN

SET NOCOUNT ON;


IF (dbo.[AuthenticateUserMultiStockChecks](@UserId, @StockCheckIds) = 0)
BEGIN 
    RETURN
END


--check if no other stockcheck for the same site is active.
SELECT * INTO #InActiveStockcheckIds FROM STRING_SPLIT(@StockCheckIds,',')

SELECT SiteId INTO #SiteIds FROM StockChecks WHERE Id IN (SELECT * FROM #InActiveStockcheckIds)

IF EXISTS (
SELECT SiteId FROM #SiteIds GROUP BY SiteId HAVING COUNT(SiteId) > 1
)
BEGIN 
	SELECT 0;
	RETURN;
END


IF EXISTS (
	SELECT Id FROM StockChecks 
	WHERE 
	Id NOT IN (SELECT * FROM #InActiveStockcheckIds) AND
	SiteId IN (SELECT * FROM #SiteIds) AND
	IsActive = 1
)
BEGIN
	SELECT 0;
	RETURN;
END



UPDATE StockChecks
SET IsActive = 1
WHERE Id IN (SELECT * FROM STRING_SPLIT(@StockCheckIds,','))

SELECT 1;
	
END

GO




--############################## Next SP #############################################﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO



CREATE OR ALTER PROCEDURE [dbo].[UPDATE_StockWithMissingResolutions]
(
    @StockCheckId INT,
	@UserId INT,
	@MissingResolutionId INT,
	@StockItemId INT
)
AS
BEGIN

SET NOCOUNT ON;

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END

UPDATE dbo.StockItems 
SET MissingResolutionId = @MissingResolutionId
WHERE Id = @StockItemId


EXEC [dbo].[UPDATE_StockCheckLastUpdate] @StockCheckId, @UserId

END
	


GO




--############################## Next SP #############################################﻿
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].[UPDATE_UnknownResolution]
(
    @StockCheckId INT,
	@UserId INT,
	@UnknownResolutionId INT,
	@ResolutionTypeId INT,
	@ResolutionDate datetime,
	@Notes nvarchar(250),
	@IsResolved bit
)
AS
BEGIN

SET NOCOUNT ON;

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
	RETURN
END


UPDATE [UnknownResolutions] 
SET ResolutionTypeId = @ResolutionTypeId,
ResolutionDateTime = @ResolutionDate,
Notes = @Notes,
IsResolved = @IsResolved,
UserId = @UserId
WHERE Id = @UnknownResolutionId

EXEC [dbo].[UPDATE_StockCheckLastUpdate] @StockCheckId, @UserId



END
	
  
	


GO




--############################## Next SP #############################################﻿/****** Object:  StoredProcedure [dbo].[CREATE_User]    Script Date: 29/03/2021 18:46:10 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].[UPDATE_User]
(
	@UserId int,
    @UserToUpdateId int,
	@Name varchar(50),
	@DefaultSiteId int,
	@NewEmail varchar(50),
	@NewEmployeeNumber varchar(50)
)
AS
BEGIN



SET NOCOUNT ON



DECLARE @UserDealerGroupId INT;
DECLARE @NewUserDealerGroupId INT;
SELECT @UserDealerGroupId = DealerGroupId FROM Users WHERE Id = @UserId
SELECT @NewUserDealerGroupId = DealerGroupId FROM Users WHERE Id = @UserToUpdateId
IF (@NewUserDealerGroupId != @UserDealerGroupId)
BEGIN 
    RETURN
END


UPDATE U
SET U.Name = @Name
FROM [Users] As U
WHERE U.Id = @UserToUpdateId

UPDATE U
SET U.EmployeeNumber = @NewEmployeeNumber
FROM [Users] As U
WHERE U.Id = @UserToUpdateId

UPDATE US 
SET US.IsDefault = 0
FROM UserSites AS US
WHERE US.UserId = @UserToUpdateId


UPDATE US 
SET US.IsDefault = 1
FROM UserSites AS US
WHERE US.UserId = @UserToUpdateId AND US.SiteId = @DefaultSiteId

UPDATE ANU
SET ANU.Email = CASE WHEN @NewEmail IS NOT NULL THEN @NewEmail ELSE ANU.Email END
FROM [AspNetUsers] As ANU
WHERE ANU.LinkedPersonId = @UserToUpdateId

UPDATE ANU
SET ANU.NormalizedEmail = CASE WHEN @NewEmail IS NOT NULL THEN UPPER(@NewEmail) ELSE ANU.NormalizedEmail END
FROM [AspNetUsers] As ANU
WHERE ANU.LinkedPersonId = @UserToUpdateId

UPDATE ANU
SET ANU.UserName = CASE WHEN @NewEmail IS NOT NULL THEN @NewEmail ELSE ANU.Email END
FROM [AspNetUsers] As ANU
WHERE ANU.LinkedPersonId = @UserToUpdateId

UPDATE ANU
SET ANU.NormalizedUserName = CASE WHEN @NewEmail IS NOT NULL THEN UPPER(@NewEmail) ELSE ANU.NormalizedEmail END
FROM [AspNetUsers] As ANU
WHERE ANU.LinkedPersonId = @UserToUpdateId

END

Go



--############################## Next SP #############################################SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE dbo.UPDATE_UserPreference
    @UserId INT,
    @PreferenceName NVARCHAR(50),
    @Preference NVARCHAR(50)
AS
BEGIN
    SET NOCOUNT ON;

    -- Check if the record exists
    IF EXISTS (
        SELECT 1 
        FROM UserPreferences
        WHERE Person_Id = @UserId AND PreferenceName = @PreferenceName
    )
    BEGIN
        -- Update the existing record
        UPDATE UserPreferences
        SET Preference = @Preference
        WHERE Person_Id = @UserId AND PreferenceName = @PreferenceName;
    END
    ELSE
    BEGIN
        -- Insert a new record
        INSERT INTO UserPreferences (Person_Id, PreferenceName, Preference)
        VALUES (@UserId, @PreferenceName, @Preference);
    END
END

GO


--############################## Next SP #############################################