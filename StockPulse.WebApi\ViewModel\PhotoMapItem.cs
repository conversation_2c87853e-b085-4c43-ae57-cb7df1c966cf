﻿namespace StockPulse.WebApi.ViewModel
{
    public class PhotoMapItem
    {

        public PhotoMapItem(StockItem stockItem, string groupingLabel)
        {
            Id = (int)stockItem.StockItemId;
            IsScan = false;
            ReconciliationState = stockItem.State;
            Reg = stockItem.Reg;
            Description = stockItem.Description;
            GroupingLabel = groupingLabel;
        }

        public PhotoMapItem(Scan scan, string groupingLabel)
        {
            Id = scan.ScanId;
            IsScan = true;
            ReconciliationState =scan.ScanState;
            Reg = scan.ScanReg;
            Description = scan.ScanDescription; 

            GroupingLabel = groupingLabel;
            ScannerName = scan.ScannerName;
        }


        public int Id { get; set; }
        public bool IsScan { get; set; }
        public ReconciliationState ReconciliationState { get; set; }
        public string Reg { get; set; }
        public string Description { get; set; }
        public string GroupingLabel { get; set; } //might be location, or stockType etc.
        public string ScannerName { get; set; }





    }
}
