#gridHolder {
  position: relative;
  overflow-y: auto;
  overflow-x: hidden;
  width: 100%;
}

ag-grid-angular {
  height: 100%;
  width: 100%;
  z-index: 2;
}

:host ::ng-deep .ag-header-icon {
  margin-left: 0px !important;
}

:host ::ng-deep .ag-icon-desc {
  color: var(--secondary) !important;
  font-weight: 700;
  transform: scale(1.7);
}

:host ::ng-deep .ag-icon-asc {
  color: var(--secondary) !important;
  font-weight: 700;
  transform: scale(1.7);
}

:host ::ng-deep .ag-header-icon>.ag-icon-filter {
  color: var(--secondary) !important;
  font-weight: 700;
  transform: scale(1.2);
}

:host ::ng-deep .ag-cell {
  cursor: pointer;
  display: flex;
  align-items: center;
}

:host ::ng-deep .ag-cell.good {
  background-color: var(--successLight) !important;
}

:host ::ng-deep .ag-cell.bad {
  background-color: var(--dangerLight);
}

:host ::ng-deep .ag-cell.total {
  background-color: #ECF0F1;
  display: flex;
  justify-content: flex-end;
}

:host ::ng-deep .agAlignRight {
  display: flex;
  justify-content: flex-end;
}

:host ::ng-deep ag-grid-angular.fadedToNothing .ag-root-wrapper {
  opacity: 0;
}

:host ::ng-deep .ag-floating-bottom-viewport {
  background: var(--grey90);
  font-weight: 700;
}

:host ::ng-deep .ag-floating-bottom-viewport .ag-row-hover .ag-cell {
  background: var(--secondaryLighter) !important;
}

:host ::ng-deep .ag-floating-bottom-viewport .ag-cell {
  display: flex;
  align-items: center;
}

:host ::ng-deep customHeader {
  width: 100%;
}


:host ::ng-deep .ag-theme-balham .ag-cell {
  padding: 0px 1em;
}

:host ::ng-deep .ag-theme-balham .ag-cell.agAlignRight {
  justify-content: flex-end;
  text-align: right;
}

:host ::ng-deep .ag-header-cell {
  padding: 0px 1em;
}

:host ::ng-deep .ag-icon-desc {
  color: var(--secondary) !important;
  font-weight: 700;
  transform: scale(1.2);
  margin-left: -4px
}

:host ::ng-deep .ag-icon-asc {
  color: var(--secondary) !important;
  font-weight: 700;
  transform: scale(1.2);
  margin-left: -4px
}

:host ::ng-deep {
  .ag-row-selected::before {
    background-color: var(--secondaryLightest) !important;
  }

  .ag-cell[col-id="percentageComplete"],
  .ag-cell[col-id="selectRow"],
  .ag-floating-bottom-viewport .ag-row-hover.regionalRow .ag-cell[col-id="percentageComplete"],
  .ag-floating-bottom-viewport .ag-row-hover.regionalRow .ag-cell[col-id="selectRow"],
  .ag-floating-bottom-viewport .ag-row-hover.totalRow .ag-cell[col-id="percentageComplete"],
  .ag-floating-bottom-viewport .ag-row-hover.totalRow .ag-cell[col-id="selectRow"] {
    background-color: #FFFFFF !important;
  }
}

:host ::ng-deep .hideCheckboxChecked .ag-checkbox-input-wrapper.ag-checked::after {
  content: '';
}