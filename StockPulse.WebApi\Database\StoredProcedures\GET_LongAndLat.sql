﻿/****** Object:  StoredProcedure [dbo].[GET_LongAndLat]    Script Date: 24/03/21 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].GET_LongAndLat
(
    @StockCheckId INT = NULL,
    @UserId INT = NULL
)
AS
BEGIN

SET NOCOUNT ON;

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END


SELECT Longitude, Latitude 
FROM StockChecks
INNER JOIN Sites ON StockChecks.SiteId=Sites.Id
WHERE StockChecks.Id = @StockCheckId



END

GO




--To use this run 
--exec [GET_LongAndLat] @StockCheckId = 99, @UserId=104