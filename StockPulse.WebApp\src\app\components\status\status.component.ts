import { Component, OnInit,  ElementRef, ViewChild, Input } from "@angular/core";
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Status } from "src/app/model/Status";
import { BarNew } from "src/app/model/BarNew";
import { GlobalParam } from "src/app/model/GlobalParam";
import { ApiAccessService } from "src/app/services/apiAccess.service";
import { ConstantsService } from "src/app/services/constants.service";
import { IconService } from "src/app/services/icon.service";
import { ToastService } from "src/app/services/newToast.service";
import { SaveDataService } from "src/app/services/saveData.service";
import { SelectionsService } from "src/app/services/selections.service";
import { ConfirmModalComponent } from "../confirmModal/confirmModal.component";
import { ImageToUpdate } from "src/app/model/ImageToUpdate";
import { StatusUpdate } from "src/app/model/StatusUpdate";


@Component({
  selector: 'status',
  templateUrl: './status.component.html',
  styleUrls: ['./status.component.scss']
})


export class StatusComponent implements OnInit {

  
  @ViewChild('statusModal', { static: true }) statusModal: ElementRef;
  @ViewChild('confirmModal', { static: true }) confirmModal: ConfirmModalComponent;
  @ViewChild('zoomImageModal', { static: true }) zoomImageModal: ElementRef;



  signOffPicture: string;
  modalHeader: string;
  amReviewingImage: boolean;
  statusRequested: Status;
  chosenNewStatus: Status;
  statusUpdate: StatusUpdate;

  constructor(
    public selections: SelectionsService,
    public constants: ConstantsService,
    public modalService: NgbModal,
    public save: SaveDataService,
    public icon: IconService,
    public api: ApiAccessService,
    public toastService: ToastService,
    
  ) {

  }


  ngOnInit(): void {

    this.initParams();
    
  }
  
  


  initParams() {
    this.statusUpdate = {
      statusId: this.selections.stockCheck.statusId,
      stockCheckId: this.selections.stockCheck.id,
      images:[]
    } as StatusUpdate


    if (this.selections.stockCheck.hasSignoffImage){
      let img = {
        status: "BLOB",
        url: this.selections.stockCheck.signoffImageURL
      } as ImageToUpdate

      this.statusUpdate.images.push(img);
    }

    this.signOffPicture = '';
    this.statusRequested = null;
    this.chosenNewStatus = null;
    //this.statusUpdate;
    
  }



  public showNewStatusModal() {


    this.modalService.open(this.statusModal, { keyboard: true, ariaLabelledBy: 'modal-basic-title' }).result.then((result) => {
      //'ok'
     
      this.saveNewStatus()


    }, (reason) => {

    });

  }


  public chooseNewStatus(status: Status) {

    this.statusRequested = status;
  
    // ask to confirm if about to put to complete
    if (this.statusUpdate.statusId < 4 && status.id === 4) {
      //are setting to complete, which can't be undone, confirm ok?
      this.confirmModal.confirmModalHeader = "Stock check will be set to complete, which can only be undone by a reviewer, continue?"
      
      this.modalService.open(this.confirmModal, { size: 'sm', keyboard: true, ariaLabelledBy: 'modal-basic-title' }).result.then((result) => {
        //'ok'
       this.chosenNewStatus = status
       this.statusUpdate.statusId = status.id;
        
      }, (reason) => {
        //cancel, don't do anything
      }
      );
    } else {
      //not set to completed, just change it
      this.statusUpdate.statusId = status.id;
      this.chosenNewStatus = status

    }
  }


  saveNewStatus(){

    this.api.post('StockChecks','UpdateStatus',this.statusUpdate).subscribe(res=>{
      //ok it's updated
      if (this.chosenNewStatus)
      {
      this.selections.stockCheck.status = this.chosenNewStatus.description;
      this.selections.stockCheck.statusId = this.chosenNewStatus.id;
      }
      if (this.statusUpdate.images[0]?.status == 'DELETE'){
        this.selections.stockCheck.hasSignoffImage = false;
        this.selections.stockCheck.signoffImageURL = '';
      }
      else if (this.statusUpdate.images[0]?.status == 'ADD'){
        this.selections.stockCheck.hasSignoffImage = true;
        this.selections.stockCheck.signoffImageURL = this.statusUpdate.images[0].fileBase64;
      }
      this.initParams();
      
      
      this.toastService.successToast('Updated stock check status');
    },e=>{
      //failed to update
      this.toastService.errorToast('Failed to update status')
    })

  }


  public pastePicture(event: ClipboardEvent) {
    if (this.selections.userIsReadOnly) { return; }
    this.amReviewingImage = false;
    let file = event.clipboardData.items[0].getAsFile();
    let reader = new FileReader();

    reader.onload = (e) => {
      this.signOffPicture = reader.result as string;
      this.zoomInToProposedNewImage()

    }
    reader.readAsDataURL(file);
  }



  zoomInToProposedNewImage() {
    this.modalHeader = 'Save Sign-off Form?'
    this.amReviewingImage = false;
    this.modalService.open(this.zoomImageModal, { size:'lg',keyboard: true, ariaLabelledBy: 'modal-basic-title' }).result.then((result) => {

      let img = {fileBase64: this.signOffPicture, status: 'ADD' } as ImageToUpdate;
      this.statusUpdate.images.push(img);
      //have chosen to 'OK' selections
      
    }, (reason) => {
    });
  }
 
 
  public zoomInToExistingImage() {
    this.modalHeader = 'Review Sign-off Form'
    this.amReviewingImage = true;
    //this.signOffPicture = this.selections.stockCheck.signoffImageBase64;
    this.modalService.open(this.zoomImageModal, { size:'lg',keyboard: true, ariaLabelledBy: 'modal-basic-title' }).result.then((result) => {
      
      //chosen to delete!
      this.statusUpdate.images[0].status = "DELETE";
    
    }, (reason) => {
    });
  }


}


