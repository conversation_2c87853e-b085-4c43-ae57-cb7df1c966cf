#sitesDropdownMenu {
    min-width: 250px;
    max-height: 90vh;
    overflow-y: auto;
    background-color: var(--primaryLighter);
    padding: 0;
}

#sitesMenuItems button,
#selectAll button {
    height: 30px;
    width: 100%;
    display: block;
}

#sitesMenuItems {
    max-height: 30vh;
    overflow-y: auto;
}

@media (min-width: 1440px) {
    #sitesMenuItems {
        max-height: 40vh;
        overflow-y: auto;
    }
}

.dropdown-menu {
    max-height: unset;
    overflow: hidden;
}

.dropdown-body {
    max-height: 45vh;
    overflow-y: auto;
}

.dropdown-footer {
    background: #035359;
}

#sitesMenuItems button {
    text-align: left;
}

#selectAll button:nth-of-type(1) {
    border-top: 1px solid #444444;
}

#confirmCancelButtons {
    display: flex;
    border-top: 1px solid #444444;

    button {
        width: 100%;
    }
}

#confirmCancelButtons button {
    text-align: center;
}

.checkboxIcon {
    color: var(--secondary);
}

#sitesMenuItems button:focus {
    background-color: var(--primaryLight);
    color: var(--bodyColour);
}

#sitesMenuItems button:hover .checkboxIcon {
    color: #000000;
}

.toggleNoCaret::after {
    content: none;
}