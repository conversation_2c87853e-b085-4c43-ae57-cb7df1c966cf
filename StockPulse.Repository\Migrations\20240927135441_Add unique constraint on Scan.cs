﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace StockPulse.Repository.Migrations
{
    /// <inheritdoc />
    public partial class AdduniqueconstraintonScan : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            //migrationBuilder.DropIndex(
            //    name: "IX_Scans_UserId",
            //    schema: "dbo",
            //    table: "Scans");

            migrationBuilder.CreateIndex(
                name: "IX_Scans_UserId_ScanDateTime_Reg_Vin",
                schema: "dbo",
                table: "Scans",
                columns: new[] { "UserId", "ScanDateTime", "Reg", "Vin" },
                unique: true,
                filter: "[Reg] IS NOT NULL AND [Vin] IS NOT NULL");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            //migrationBuilder.DropIndex(
            //    name: "IX_Scans_UserId_ScanDateTime_Reg_Vin",
            //    schema: "dbo",
            //    table: "Scans");

            migrationBuilder.CreateIndex(
                name: "IX_Scans_UserId",
                schema: "dbo",
                table: "Scans",
                column: "UserId");
        }
    }
}
