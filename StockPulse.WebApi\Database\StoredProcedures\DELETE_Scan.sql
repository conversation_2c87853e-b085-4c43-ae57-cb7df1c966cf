﻿
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO





CREATE OR ALTER PROCEDURE [dbo].[DELETE_Scan]
(
	@UserId INT,
	@ScanId INT
)
AS
BEGIN

SET NOCOUNT ON;

DECLARE @StockcheckId int;

set @StockCheckId = (SELECT StockCheckId FROM Scans WHERE Id = @ScanId)


IF (dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0 OR dbo.[CanSaveScans](@StockCheckId) = 0)
BEGIN 
	RETURN
END

DELETE FROM scans WHERE Id = @ScanId

EXEC [dbo].[UPDATE_StockCheckLastUpdate] @StockCheckId, @UserId



END
	
  
	


GO


