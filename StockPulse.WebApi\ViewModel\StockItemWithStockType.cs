﻿namespace StockPulse.WebApi.ViewModel
{
    public class StockItemWithStockType
    {
        public int Id { get; set; }
        public string Description { get; set; }
        public int DIS { get; set; }
        public int GroupDIS { get; set; }
        public string StockType { get; set; }

        public string Status { get; set; } //InStock / Reconciled / UnknownResolved / Unknown   use case to work out

    }
}
