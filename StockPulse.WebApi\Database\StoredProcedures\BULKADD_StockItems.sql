/****** Object:  StoredProcedure [dbo].[BULKADD_StockItems]    Script Date: 17/04/2023 18:19:37 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO



CREATE  OR ALTER PROCEDURE [dbo].[BULKADD_StockItems]
(
    @StockCheckId INT = NULL,
	@UserId INT = NULL,
    @StockItems StockItemType READONLY
)
AS
BEGIN

SET NOCOUNT ON;


IF (dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0 OR dbo.[CanSaveScans](@StockCheckId) = 0)
BEGIN 
	RETURN
END
	
INSERT INTO StockItems (ScanId,ReconcilingItemId,MissingResolutionId,StockCheckId,SourceReportId,Reg,Vin,Description,DIS,GroupDIS,Branch,Comment,StockType,Reference,StockValue,Flooring,IsAgencyStock,FileImportId)
(SELECT ScanId,ReconcilingItemId,MissingResolutionId,StockCheckId,SourceReportId,Reg,Vin,Description,DIS,GroupDIS,Branch,Comment,StockType,Reference,StockValue,Flooring,IsAgencyStock,FileImportId FROM @StockItems)



	
END

GO


