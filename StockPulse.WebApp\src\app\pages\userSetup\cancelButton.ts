import { Component, ViewChild } from "@angular/core";

import { ICellRendererAngularComp } from "ag-grid-angular";
import { RowNode } from 'ag-grid-community';
import { UserAndLogin } from "../../model/UserAndLogin";
import { IconService } from '../../services/icon.service';
import { SelectionsService } from '../../services/selections.service';


@Component({
    selector: 'cancelButtonUserSetup-cell',
    template: `
   
        <button *ngIf="params.data?.hasChanged" class="btn btn-danger" (click)="cancel()">
            <fa-icon [icon]="icon.faTimesCircle"> Cancel </fa-icon>
            </button>    

    

    `
    ,
    styles: [
        `
        button{
            height: 1.6em;
    margin: 0px;
    margin-top: -5px;
    padding: 0px 10px;

        }
       
      `
    ]
})
export class CancelButtonUserSetupComponent implements ICellRendererAngularComp {
   
params:any;


    constructor(
        public selections: SelectionsService,
        public icon: IconService

    ) { }


    agInit(params: any): void {

    this.params = params

    }
    refresh(): boolean {
        return false;
    }


    cancel(){
        let item:UserAndLogin = this.params.node.data
        item.newValues = {
            name: null,
            nameShort: null,
            roleName: null,   //The 'Name' of the item within AspNetRoles.  Ensure manually validate.
            sites: null,
            siteCode: null,
            email: null
        },
        item.name = item.originalValues.name;
        item.nameShort = item.originalValues.nameShort;
        item.roleName = item.originalValues.roleName;
        item.sites = item.originalValues.sites;
        item.siteCode = item.originalValues.siteCode;
        item.email = item.originalValues.email;
        item.hasChanged = false;
        
        this.params.node.setData(item);
        this.params.api.redrawRows(this.params.node)
    }



     


}


