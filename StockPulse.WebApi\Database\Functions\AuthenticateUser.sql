﻿/****** Object:  StoredProcedure [dbo].[AuthenticateUser]    Script Date: 19/3/21 ******/

/****** Object:  UserDefinedFunction [dbo].[AuthenticateUser]    Script Date: 19/03/2021 17:42:49 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

ALTER FUNCTION [dbo].[AuthenticateUser] (@UserId INT, @StockCheckId INT)
RETURNS BIT
BEGIN

-- If user has access to the site linked to the stockcheck, return true
IF EXISTS(
  SELECT Users.Name AS Name, StockChecks.Id AS StockCheckId
  FROM [dbo].[Users]
  INNER JOIN UserSites ON UserSites.UserId=Users.Id
  INNER JOIN StockChecks ON StockChecks.SiteId=UserSites.SiteId
  WHERE Users.Id = @UserId AND StockChecks.Id = @StockCheckId
)
    RETURN 1

-- Else, return false.
RETURN 0;
END;
