  
  
GO  
CREATE OR ALTER PROCEDURE [dbo].[DELETE_StockItem]  
(  
    @StockCheckId INT = NULL,  
    @UserId INT = NULL,  
    @ItemId INT = NULL  
)  
AS  
BEGIN  
  
SET NOCOUNT ON;  
  
IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0  
BEGIN   
    RETURN  
END  
  
UPDATE Scans  
SET StockItemId = NULL  
WHERE  
StockItemId = @ItemId  


DELETE FROM StockItems   
WHERE StockCheckId =  @StockCheckId  
AND Id = @ItemId  
  
  
EXEC [dbo].[UPDATE_StockCheckLastUpdate] @StockCheckId, @UserId  
  
  
   
  
END  
  
SET ANSI_NULLS ON  
GO


