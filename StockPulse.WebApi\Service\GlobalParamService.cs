﻿using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using StockPulse.WebApi.DataAccess;
using StockPulse.Model;
using StockPulse.WebApi.ViewModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace StockPulse.WebApi.Service
{
    public interface IGlobalParamService
    {
        //Task<IEnumerable<ResolutionType>> GetSitesForDivision(int userId);
        Task<IEnumerable<ResolutionTypeVM>> GetResolutionTypes(int userId);
        Task<IEnumerable<GlobalParam>> GetAllParamsAsync(int userId);
        //Task<object> GetAllRoles();
        //Task<IEnumerable<SiteVM>> GetAllSites(int userId);
        Task<string> GetPlateTypeFromCacheAsync(int userId);
        Task<Dictionary<string, GlobalParam>> ReLoadGlobalParamsCache();
        Task<bool> IsSSOEnabledFromCacheAsync(int dealerGroupId);
    }




    public class GlobalParamService: IGlobalParamService
    {

        private readonly IGlobalParamDataAccess globalParamDataAccess;
        private readonly RoleManager<IdentityRole> roleManager;
        private readonly ISiteService siteService;
        private readonly IMemoryCache memoryCache;

        public GlobalParamService(IGlobalParamDataAccess globalParamDataAccess, RoleManager<IdentityRole> roleManager, ISiteService siteService, IMemoryCache memoryCache)
        {
            this.globalParamDataAccess = globalParamDataAccess;
            this.roleManager = roleManager;
            this.siteService = siteService;
            this.memoryCache = memoryCache;
        }

        public async Task<IEnumerable<GlobalParam>> GetAllParamsAsync(int dealerGroupId)
        {
            var globalParamsDictionary = await GetGlobalParamsDictionary();
            var result = globalParamsDictionary.Where(g => g.Key.StartsWith($"{dealerGroupId}|")).Select(g => g.Value);
            return result;  
        }

        public async Task<string> GetPlateTypeFromCacheAsync(int dealerGroupId)
        {
            var globalParamsDictionary = await GetGlobalParamsDictionary();
            var plateType = globalParamsDictionary[$"{dealerGroupId}|PlateType"].StringValue;
            return plateType;
        }

        public async Task<bool> IsSSOEnabledFromCacheAsync(int dealerGroupId)
        {
            var globalParamsDictionary = await GetGlobalParamsDictionary();
            var isSSOEnabled = globalParamsDictionary[$"{dealerGroupId}|isSSOEnabled"].BoolValue;
            return isSSOEnabled;
        }

        //public async Task<object> GetAllRoles()
        //{
        //    return await roleManager.Roles.ToListAsync();
        //}

        //public async Task<IEnumerable<SiteVM>> GetAllSites(int userId)
        //{
        //    return await siteService.GetAllSites(userId);
        //}

        public async Task<IEnumerable<ResolutionTypeVM>> GetResolutionTypes(int userId)
        {
            return await globalParamDataAccess.GetResolutionTypes(userId);
        }

        //public async Task<IEnumerable<ResolutionType>> GetSitesForDivision(int userId)
        //{
        //    return await globalParamDataAccess.GetSitesForDivision(userId);
        //}

        private async Task<Dictionary<string, GlobalParam>> GetGlobalParamsDictionary()
        {
            Dictionary<string, GlobalParam> globalParamsDictionary = new Dictionary<string, GlobalParam>();

            if (!memoryCache.TryGetValue("GlobalParams", out Dictionary<string, GlobalParam> cachedGlobalParamsDictionary))
            {
                //Get fresh data from DB
                globalParamsDictionary = await ReLoadGlobalParamsCache();
            }

            if (cachedGlobalParamsDictionary != null)
            {
                globalParamsDictionary = cachedGlobalParamsDictionary;
            }

            return globalParamsDictionary;
        }

        public async Task<Dictionary<string, GlobalParam>> ReLoadGlobalParamsCache()
        {
            IEnumerable<GlobalParam> globalParams = await globalParamDataAccess.GetAllAsync();
            var globalParamsDictionary = globalParams.ToDictionary(u => $"{ u.DealerGroupId}|{u.Name}", u => u);
            memoryCache.Set("GlobalParams", globalParamsDictionary, DateTime.UtcNow.AddDays(1));
            return globalParamsDictionary;
        }



    }
}
