﻿
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE OR ALTER PROCEDURE [admin].[GET_Last5DayStats] 
AS  
BEGIN  
  
SET NOCOUNT ON;  
  

CREATE TABLE #last5days (DayDate varchar(12))
DECLARE @todayDate Date = DATEADD(DAY,-4,getDate())

WHILE (@todayDate < getDate())

BEGIN
	INSERT INTO #last5days (DayDate) SELECT REPLACE(CONCAT('_',CONVERT(varchar(11), @todayDate, 113)),' ','')
	SET @todayDate = DATEADD(day,1,@todayDate)
END

DECLARE @cols as nvarchar(max);
set @cols =   (select STRING_AGG(DayDate,',') FROM #last5days);
DROP TABLE #last5days

--SELECT @cols

DECLARE @sqlstat nvarchar(max);
SET @sqlstat = N'
SELECT * FROM
(
	SELECT
	REPLACE(CONCAT(''_'',CONVERT(varchar(12), sca.ScanDateTime, 113)),'' '','''') as DayDate,
	dg.Description,
	ISNULL(COUNT(sca.Id),0) as Scans
	FROM Scans sca
	INNER JOIN StockChecks sc on sc.id = sca.StockCheckId
	INNER JOIN Sites si on si.id = sc.SiteId
	INNER JOIN Divisions div on div.id = si.DivisionId
	INNER JOIN DealerGroup dg on dg.id = div.DealerGroupId
	WHERE 
	sca.ScanDateTime >= DATEADD(DAY,-5,getDate())
	GROUP BY dg.Description,REPLACE(CONCAT(''_'',CONVERT(varchar(12), sca.ScanDateTime, 113)),'' '','''')

	UNION ALL

	SELECT
	''Total'' DayDate,
	dg.Description,
	ISNULL(COUNT(sca.Id),0) as Scans
	FROM Scans sca
	INNER JOIN StockChecks sc on sc.id = sca.StockCheckId
	INNER JOIN Sites si on si.id = sc.SiteId
	INNER JOIN Divisions div on div.id = si.DivisionId
	INNER JOIN DealerGroup dg on dg.id = div.DealerGroupId
	WHERE 
	sca.ScanDateTime >= DATEADD(DAY,-5,getDate())
	GROUP BY dg.Description

) as SourceTable
PIVOT --create the pivot
(
	SUM(Scans) --The measure you want as the value
	FOR [DayDate] in ('+@cols+',Total'+')
	
) as PivotTable;


';

--3. Run pivot
EXEC(@sqlstat)
   
END  
  
GO