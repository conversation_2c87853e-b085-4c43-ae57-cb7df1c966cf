﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace StockPulse.Model
{
    public class LogMessage
    {
        [Key]
        public int Id { get; set; }
        public string Job { get; set; }
        public DateTime SourceDate { get; set; }
        public DateTime? FinishDate { get; set; }
        public int StartCount { get; set; }
        public int ProcessedCount { get; set; }
        public int AddedCount { get; set; }
        public int RemovedCount { get; set; }
        public int ChangedCount { get; set; }
        public int FinishCount { get; set; }
        public int ErrorCount { get; set; }
        public bool IsCompleted { get; set; }
        [StringLength(99999)]
        public string FailNotes { get; set; }

        //new March 22
        public int InterpretFileSeconds { get; set; }
        public int UpdateDbSeconds { get; set; }
        public int FinalPartsSeconds { get; set; }

        public int? DealerGroup_Id { get; set; }
        [ForeignKey("DealerGroup_Id")]
        public DealerGroup DealerGroup { get; set; }

    }
}