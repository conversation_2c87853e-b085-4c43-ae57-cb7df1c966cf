/****** Object:  StoredProcedure [dbo].[DMSStocks]    Script Date: 22/3/21 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].GET_DMSStocks
(
    @StockCheckId INT = NULL,
    @UserId INT = NULL
)
AS
BEGIN

SET NOCOUNT ON;

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END


SELECT 
[Id]
    ,[ReconcilingItemId]
    ,[MissingResolutionId]
    ,StockItems.[Description]
    ,[DIS]
    ,[GroupDIS]
    ,[Branch]
    ,[StockType]
    ,[StockValue]
FROM [dbo].[StockItems] 
WHERE StockItems.StockCheckId = @StockCheckId 	

	

END

GO


--To use this run 
--exec [GET_DMSStocks] @StockCheckId = 1, @UserId = 104