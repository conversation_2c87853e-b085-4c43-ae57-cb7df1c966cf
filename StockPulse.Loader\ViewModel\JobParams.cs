﻿using StockPulse.Loader.Services;
using StockPulse.Loader.ViewModel;
using System.Collections.Generic;

namespace StockPulse.Loader.Stockpulse.ViewModel
{
    public class JobParams
    {
        public LoaderJob jobType { get; set; }
        public string jobName { get; set; }

        public string customerFolder { get; set; }
        public string filename { get; set; }
        public string importSPName { get; set; }
        public string loadingTableName { get; set; }
        public string fileSource { get; set; }
        public DateTimeWrapper pulse { get; set; }

        public FileType fileType { get; set; }
        public string regexPattern { get;set; }
        public string headerFailColumn { get; set; }
        //public Type parsedItemType { get; set; }
        public Dictionary<string, string> headerDefinitions { get; set; }
        public int errorCount { get; set; }

        public int? rowsToSkip { get; set; } // If header is on row 3, skip 2
        public int? fileImportId { get; set; }
        public string[] allMatchingFiles { get; set; }

        public int dealerGroupId { get; set; }



        // These will be the types to clear on the input table
        public string reconcilingItemTypeIdsToInclude { get; set; }
        public string reconcilingItemTypeIdsToExclude { get; set; }

        public string stockTypes { get; set; }


        // If true, change file extension from csv to xlsx
        // We are using this for MMG AtAuction which gets sent as a csv but is 
        // actually an xlsx
        public bool convertToXlsx { get; set; } = false;

        public bool isUS { get; set; } = false;

        // If we have multiple types used by one set of params, we can use to hold this info
        public string additionalInfo { get; set; } = null;

        // Will replace certain specific strings in a row
        // When these cause issues that can't be solved via a regex
        public Dictionary<string, string> replaceStrings { get; set; } = null;

        public char delimiter { get; set; } = ',';
        // If we don't have headers but we know the expected number of fields
        public int? knownHeaderLength { get; set; } = null;

    }
}
