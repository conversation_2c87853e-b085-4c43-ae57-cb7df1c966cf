import { Component, OnInit } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { IconService } from 'src/app/services/icon.service';
import { Label } from '../labelPrinter.service';

@Component({
  selector: 'app-labelPrinterModal',
  templateUrl: './modal.component.html',
  styleUrls: ['./modal.component.scss']
})
export class LabelPrinterModalComponent implements OnInit {
  labels: Label[];
  copies: number = 1;

  constructor(
    public activeModal: NgbActiveModal,
    public icon: IconService
  ) {

  }

  ngOnInit(): void {
    this.labels = [{
      vin: '',
      stockNumber: '',
      description: '',
      colour: ''
    }]
  }

  onSaveButtonClick() {
    this.labels.forEach(l => {
      l.vin = l.vin.toUpperCase(),
      l.stockNumber = l.stockNumber.toUpperCase(),
      l.description = l.description.toUpperCase(),
      l.colour = l.colour.toUpperCase()
    })

    this.activeModal.close({ labels: this.labels, copies: this.copies });
  }


  onCancelButtonClick() {
    this.activeModal.dismiss();
  }

  validateCopies() {
    if (this.copies === 0) { return this.copies = 1; }
    return;
  }

  addLabel() {
    this.labels.push({
      vin: '',
      stockNumber: '',
      description: '',
      colour: ''
    })
  }

  deleteLabel() {
    this.labels.pop();
  }
}
