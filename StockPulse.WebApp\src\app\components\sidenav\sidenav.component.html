<div *ngIf="!keepMenuFixed" id="menuToggle" class="h4" (mouseenter)="makeMenuWide()"
    (mouseleave)="maybeHideMenu($event)">
    <fa-icon [icon]="icon.faBars"></fa-icon>
</div>

<div id="menuTrigger" (mouseenter)="makeMenuWide()"></div>

<menu id="sidenav" class="doNotPrint" [ngClass]="{'wide': isMenuWide}" (mouseenter)="amHoveringSideMenu = true"
    (mouseleave)="maybeHideMenu($event)">
    <ng-container>
        <div *ngIf="showFixMenuToggle()" id="fixMenuButtonContainer" (click)="fixMenu()">
            <div id="fixMenuButton">
                <div [ngClass]="{ 'rotate': keepMenuFixed }">
                    <fa-icon [icon]="icon.faAnglesRight"></fa-icon>
                </div>
            </div>
        </div>

        <div id="menuToggle" class="h4" (mouseenter)="makeMenuWide()" (mouseleave)="maybeHideMenu($event)">
            <fa-icon [icon]="icon.faBars"></fa-icon>
        </div>
        <div class="button-group" *ngFor="let section of menuSections; let i = index">

            <div class="sectionLabel">
                <span *ngIf="isMenuWide">{{ section.header }}</span>
            </div>

            <ng-container *ngFor="let item of section.items">
                <button
                    *ngIf="!item.hide && isMenuWide"
                    class="btn btn-primary"
                    [ngClass]="{ 'active': routeIsActive(item) }"
                    [disabled]="!enableRoute(item.link)"
                    placement="right"
                    triggers="mouseenter:mouseleave"
                    [ngbPopover]="!enableRoute(item.link) ? 'Please load a Stock Check' : ''"
                    (click)="goTo(item.link)">
                    <div class="icon-holder">
                        <fa-icon [icon]="icon[item.icon]"></fa-icon>
                    </div>
                    <div class="animated menuLabel">
                        {{ item.name }}
                    </div>
                </button>
            </ng-container>
        </div>
        <div *ngIf="isMenuWide" id="sidenav-footer" class="animated">
            <img [src]="getLogo()" alt="CPHi logo">
            <div id="version">v{{ version }}</div>
        </div>
    </ng-container>
</menu>