﻿using StockPulse.Model.Import;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace StockPulse.Model.Input
{

    [Table("ReconcilingItems", Schema = "input")]
    public class ReconcilingItem
    {
        [Key]
        public int Id { get; set; }

        public int ReconcilingItemTypeId { get; set; }
        [ForeignKey("ReconcilingItemTypeId")]
        public virtual ReconcilingItemType ReconcilingItemType { get; set; }


        public string Reg { get; set; }

        public string Vin { get; set; }

        public string Description { get; set; }

        public string Comment { get; set; }

        public string Reference { get; set; }


        public int SourceReportId { get; set; }
        [ForeignKey("SourceReportId")]
        public virtual SourceReport SourceReports { get; set; }


        public int SiteId { get; set; }
        [ForeignKey("SiteId")]
        public virtual Site Sites { get; set; }


        public int? FileImportId { get; set; }
        [ForeignKey("FileImportId")]
        public virtual FileImport FileImport { get; set; }


        public int DealerGroupId { get; set; }
        [ForeignKey("DealerGroupId")]
        public virtual DealerGroup DealerGroup { get; set; }

    }

}