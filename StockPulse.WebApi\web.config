<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <location path="." inheritInChildApplications="false">
    <system.webServer>
      <httpProtocol>
        <customHeaders>
          <!--<add name="Strict-Transport-Security" value="max-age=31536000; includeSubDomains" />-->
          <add name="X-Content-Type-Options" value="nosniff" />
        </customHeaders>
      </httpProtocol>
      <handlers>
        <add name="aspNetCore" path="*" verb="*" modules="AspNetCoreModuleV2" resourceType="Unspecified" />
      </handlers>
      <aspNetCore processPath="dotnet"
                  arguments=".\StockPulse.WebApi.dll"
                  stdoutLogEnabled="true"
                  stdoutLogFile="\\?\%home%\LogFiles\stdout"
                  hostingModel="inprocess" />
    </system.webServer>
  </location>
</configuration>