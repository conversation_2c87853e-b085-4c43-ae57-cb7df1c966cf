﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using StockPulse.WebApi.Attribute;
using StockPulse.WebApi.Service;
using System;
using System.Threading.Tasks;
using static StockPulse.WebApi.Attribute.UserLevelAccessAttribute;

namespace StockPulse.WebApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    [UserLevelAccess(new[] { UserRole.SysAdministrator, UserRole.Reconciler, UserRole.Resolver, UserRole.Scanner, UserRole.Approver, UserRole.GeneralManager, UserRole.ReadOnly, UserRole.ScanAndPrint, UserRole.ScanAndView })]
    public class ErrorReportController : ControllerBase, IAttributeValueProvider
    {
        private readonly string userRole;
        private readonly int userId;
        private readonly IErrorReportService errorReportService;
        public UserRole attributeUserRole => GetUserRole();
        private UserRole GetUserRole()
        {
            return (UserRole)Enum.Parse(typeof(UserRole), userRole);
        }


        //constructor
        public ErrorReportController(IUserService userService, IErrorReportService errorReportService)
        {
            userId = userService.GetUserId();
            userRole = userService.GetUserRole();
            this.errorReportService = errorReportService;
        }



        [HttpPost]
        [Route("Report")]
        public async Task Report(ErrorReport errors)
        {
            await errorReportService.SaveErrorReport(errors.errors, userId);
            //await errorReportService.SaveErrorReport(errors.selections, userId);
        }


        public class ErrorReport
        {
            public string errors { get; set; }
            public string selections { get; set; }
        }

    }
}
