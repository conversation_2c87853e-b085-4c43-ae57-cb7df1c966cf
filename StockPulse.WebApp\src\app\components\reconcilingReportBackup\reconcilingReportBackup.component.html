<div class="modal-header">
    <h4 id="modal-basic-title" class="modal-title">Attach / Review Backup Files</h4>
    <button type="button" class="close" aria-label="Close" (click)="onCancelButtonClick()">
        <span aria-hidden="true">&times;</span>
    </button>
</div>

<div class="modal-body">
    <div id="backupContainer">
        <div *ngIf="existingBackupsFiles && existingBackupsFiles.length > 0" id="existingBackupFiles">
            <div *ngFor="let file of existingBackupsFiles" class="existingBackupFile">
                <img [id]="'backup-image-'+file.id" [src]="file.url" (error)="updateUrl(file)"
                    (click)="downloadFile(file)">
                <span>{{ file.fileName }}</span>
                <fa-icon *ngIf="selectionsService.stockCheck.statusId < 4" [icon]="iconService.faTrash" width="20px"
                    alt="delete" class="deleteIcon" (click)="deleteBackup(file)">
                </fa-icon>
            </div>
        </div>
        <div *ngIf="existingBackupsFiles && existingBackupsFiles.length === 0" class="mb-4">
            There are currently no backup files to support this report
        </div>

        <!-- Files uploaded, but not yet saved -->
        <span *ngIf="fileSizeExceeded" class="text-danger">
            1 or more files exceeded the 10mb size limit and were not uploaded.
        </span>
        <div *ngIf="resolutionFiles.length" id="filesList">
            <div *ngFor="let file of resolutionFiles; let i = index" class="singleFile">
                <div class="d-flex align-items-center">
                    <img class="fileThumbnail" [src]="getThumbnail(file)">
                    <div class="info">
                        <span class="name">{{ file.name }}</span>
                        <br>
                        <span class="size">{{ formatBytes(file?.size) }}</span>
                    </div>
                </div>
                <fa-icon [icon]="iconService.faTrash" width="20px"
                    alt="delete" class="deleteIcon" (click)="deleteFile(i)"></fa-icon>
            </div>
        </div>

        <!-- Drag drop area -->
        <div id="fileDropAreaContainer">
            <div *ngIf="selectionsService.stockCheck.statusId < 4" id="fileDropArea" appDragAndDrop contenteditable="true"
                (filesDropped)="onFileDropped($event)" (paste)="onFilePaste($event)">
                <div class="uploadFileWrapper mt-2">
                    <ng-container>
                        <input #fileDropRef id="file" type="file" multiple="multiple" class="chooseFileInput"
                            (click)="$event.target.value = null" (change)="fileBrowseHandler($event)" />
                        <label for="file">
                            <fa-icon [icon]="iconService.faUpload"></fa-icon>Choose <span
                                *ngIf="thereAreFiles()">Additional</span> File(s)
                        </label>
                    </ng-container>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal-footer">
    <button *ngIf="resolutionFiles?.length > 0" type="button" class="btn btn-success" (click)="saveBackupFiles()">
        Save
    </button>
    <button type="button" class="btn btn-primary" (click)="onCancelButtonClick()">Close</button>
</div>