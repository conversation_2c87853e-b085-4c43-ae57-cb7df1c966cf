﻿using System.Collections.Generic;

namespace StockPulse.WebApi.ViewModel
{
    public class Resolution
    {
        public int ResolutionId { get; set; }
        public int StockCheckId { get; set; }
        public int OriginalItemId { get; set; } //scanId or stockItemId depending if is unknown or missing
        public bool IsResolved { get; set; }
        public int? ResolutionTypeId { get; set; }
        public string Notes { get; set; }
        public List<ImageToUpdate> Images { get; set; }
    }


}


