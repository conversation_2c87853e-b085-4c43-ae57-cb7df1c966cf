/****** Object:  StoredProcedure [dbo].[GET_Scans]    Script Date: 17/02/2023 12:51:36 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].[GET_Scans]
(
    @StockCheckId INT = NULL,
    @UserId INT = NULL,
    @LimitToIds varchar(max) = NULL
)
AS
BEGIN
 
SET NOCOUNT ON;  

DECLARE @isRegional INT;  
DECLARE @isTotal INT;  
DECLARE @StockCheckDate DateTime;
DECLARE @DivisionId INT = NULL;
DECLARE @DealerGroupId INT = NULL;
DECLARE @SCId INT = NULL;


--Early return if no access
IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0 
BEGIN 
	RETURN
END


SELECT Value as Id INTO #limitIds from STRING_SPLIT(@LimitToIds,',')

SELECT 
@isRegional = IsRegional, 
@isTotal = IsTotal, 
@StockCheckDate = Date 
FROM StockChecks  
WHERE StockChecks.Id = @StockCheckId
  



--Still here so continue  
IF @isRegional = 0 AND @isTotal = 0  

    BEGIN  
    SET @SCId = @StockCheckId;
    END  

ELSE IF @isRegional = 1 AND @isTotal = 0  

    BEGIN  
    SET @DivisionId = (SELECT DivisionId FROM StockChecks  
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId  
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id  
                        WHERE StockChecks.Id = @StockCheckId AND StockChecks.IsRegional = 1)  
    END  

ELSE IF @isRegional = 0 AND @isTotal = 1  

    BEGIN  
    SET @DealerGroupId = (SELECT DealerGroupId FROM StockChecks  
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId  
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id  
                        WHERE StockChecks.Id = @StockCheckId)  
    END 





SELECT 
sca.Id as ScanId,
Locations.[Description] AS LocationDescription,
u.Name AS ScannerName,
sca.[RegConfidence],
sca.[VinConfidence],
sca.[Longitude],
sca.[Latitude],
sca.[ScanDateTime],
sca.[Comment] as ScanComment,
sca.[Reg] as ScanReg,
sca.[Vin] as ScanVin,
sca.[HasVinImage],
sca.Description as ScanDescription,
CASE
	WHEN sca.IsDuplicate = 1 THEN 'Duplicate'
	WHEN sto.Id IS NOT NULL AND sto.StockCheckId <> sca.StockCheckId THEN 'MatchedToOtherSite'
	WHEN sto.Id IS NOT NULL AND sto.StockCheckId = sca.StockCheckId THEN  'MatchedToStockOrScan'
	WHEN sca.ReconcilingItemId IS NOT NULL THEN 'MatchedToReport'
	WHEN ur.Id IS NOT NULL THEN 'Resolved'
	ELSE 'OutstandingIssue'
END as ScanState,
rt.Description as ResolutionTypeDescription,
si.Longitude as StockCheckLongitude,
si.Latitude as StockCheckLatitude,

CASE
    WHEN sca.IsRegEditedOnWeb = 1 THEN 'Web app'
    WHEN sca.IsRegEditedOnDevice = 1 THEN 'Mobile app'
    ELSE NULL
END AS RegEditStatus,
CASE
    WHEN sca.IsVinEditedOnWeb = 1 THEN 'Web app'
    WHEN sca.IsVinEditedOnDevice = 1 THEN 'Mobile app'
    ELSE NULL
END AS VinEditStatus,
si.Description AS SiteName

FROM [dbo].[Scans] sca
INNER JOIN Users u ON u.Id=sca.UserId
INNER JOIN Locations ON Locations.Id=sca.LocationId
INNER JOIN StockChecks AS st ON st.Id=sca.StockCheckId
INNER JOIN Sites si ON si.Id=ST.SiteId
INNER JOIN Divisions div  ON div.Id= si.DivisionId
INNER JOIN DealerGroup dg ON dg.Id=div.DealerGroupId
LEFT JOIN StockItems sto ON sto.Id = sca.StockItemId
LEFT JOIN UnknownResolutions ur on ur.id = sca.UnknownResolutionId AND ur.IsResolved = 1
LEFT JOIN ResolutionTypes rt on rt.id = ur.ResolutionTypeId
LEFT JOIN #limitIds lids on lids.id = sca.id
WHERE 
--StockCheckId = @StockCheckId
st.Id = ISNULL(@SCId, st.Id)
AND st.Date = @StockCheckDate
AND div.Id = ISNULL(@DivisionId, div.Id)
AND DG.Id = ISNULL(@DealerGroupId, DG.Id)
AND (@LimitToIds IS NULL OR lids.id IS NOT NULL)
ORDER BY sca.Id desc



END
GO





--GO


