﻿  
  
CREATE OR ALTER PROCEDURE [dbo].[GET_StockItemsThatAreDuplicated]  
(  
    @StockCheckId INT = NULL,  
 @UserId INT = NULL  
)  
AS  
BEGIN  
  
SET NOCOUNT ON;  
  
DECLARE @isRegional INT;  
DECLARE @isTotal INT;  
DECLARE @StockCheckDate DateTime;  
DECLARE @DivisionId INT = NULL;  
DECLARE @DealerGroupId INT = NULL;  
DECLARE @SCId INT = NULL;  
  
  
IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END

  
SELECT   
@isRegional = IsRegional,   
@isTotal = IsTotal,   
@StockCheckDate = Date   
FROM StockChecks    
WHERE StockChecks.Id = @StockCheckId  
  
  

  
 IF @isRegional = 0 AND @isTotal = 0  
  
  BEGIN  
  
  SET @SCId = @StockCheckId;  
  
  END  
  
 ELSE IF @isRegional = 1 AND @isTotal = 0  
  
  BEGIN  
    
  SET @DivisionId = (SELECT DivisionId FROM StockChecks  
       INNER JOIN Sites ON Sites.Id=StockChecks.SiteId  
       INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id  
       WHERE StockChecks.Id = @StockCheckId AND StockChecks.IsRegional = 1)  
    
  
  END  
  
 ELSE IF @isRegional = 0 AND @isTotal = 1  
  
  BEGIN  
  
    
  SET @DealerGroupId = (SELECT DealerGroupId FROM StockChecks  
       INNER JOIN Sites ON Sites.Id=StockChecks.SiteId  
       INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id  
       WHERE StockChecks.Id = @StockCheckId)  
  END;  
  

  --work out first instance of each stockItem
  SELECT
  si.Reg,
  si.Vin,
  MIN(si.Id) as Id
  INTO #UniqueIds
  FROM StockItems si
  INNER JOIN StockChecks  SC ON SC.Id=SI.StockCheckId 
  INNER JOIN Sites sit ON sit.Id=SC.SiteId  
  INNER JOIN Divisions  D ON D.Id=sit.DivisionId  
  INNER JOIN DealerGroup  DG ON D.DealerGroupId=DG.Id  
  WHERE SC.Id = ISNULL(@SCId, SC.Id)   
  AND SC.Date = @StockCheckDate  
  AND D.Id = ISNULL(@DivisionId, D.Id)  
  AND DG.Id = ISNULL(@DealerGroupId, DG.Id)  
  AND si.IsDuplicate = 0
  GROUP BY si.Reg,si.Vin

  --find out everything about that first instance
  SELECT
  si.Id, si.StockCheckId, si.Reg,si.Vin,si.StockType,si.Comment,si.Reference
  INTO #firstItems
  FROM StockItems si
  INNER JOIN #uniqueIds u on u.id = si.id
  DROP TABLE #uniqueIds

  SELECT   
SI.Id as StockItemId,
SI.Reg,
SI.Vin,
SI.[Description],
SI.DIS,
si.Flooring,
SI.GroupDIS,
si.Branch,
si.StockType,
si.Comment,
si.Reference,
si.StockValue,  
sit.Description AS SiteName  ,
CASE
	WHEN si.IsDuplicate = 1 THEN 'Duplicate'
	WHEN sca.Id IS NOT NULL AND sca.StockCheckId <> sca.StockCheckId THEN 'MatchedToOtherSite'
	WHEN sca.Id IS NOT NULL AND sca.StockCheckId = sca.StockCheckId THEN  'MatchedToStockOrScan'
	WHEN si.ReconcilingItemId IS NOT NULL THEN 'MatchedToReport'
	WHEN mr.Id IS NOT NULL THEN 'Resolved'
	ELSE 'OutstandingIssue'
END as [State],
rt.Description as ResolutionTypeDescription,
f.Id as OriginalStockItemId,
f.StockType as OriginalStockType,
f.Comment as OriginalComment,
f.Reference as OriginalReference


  FROM [dbo].[StockItems]  SI   
  INNER JOIN StockChecks  SC ON SC.Id=SI.StockCheckId   
  INNER JOIN Sites sit ON sit.Id=SC.SiteId  
  INNER JOIN Divisions  D ON D.Id=sit.DivisionId  
  INNER JOIN DealerGroup  DG ON D.DealerGroupId=DG.Id  
  LEFT JOIN Scans sca on sca.Id = si.ScanId
  LEFT JOIN MissingResolutions mr on mr.id = si.MissingResolutionId
  LEFT JOIN ResolutionTypes rt on rt.id = mr.ResolutionTypeId
  INNER JOIN #firstItems f on (
	(f.Reg <> '' AND f.Reg = si.Reg) OR 
	(f.Vin <> '' AND f.Vin = si.Vin)
	) AND f.StockCheckId = si.StockCheckId
  WHERE SC.Id = ISNULL(@SCId, SC.Id)   
  AND SC.Date = @StockCheckDate  
  AND D.Id = ISNULL(@DivisionId, D.Id)  
  AND DG.Id = ISNULL(@DealerGroupId, DG.Id)  
  AND SI.IsDuplicate = 1
  
  DROP TABLE #firstItems

END  
GO


  
  