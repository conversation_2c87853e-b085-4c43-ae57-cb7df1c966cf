﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

using StockPulse.WebApi.ViewModel;
using StockPulse.WebApi.Service;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using static StockPulse.WebApi.Attribute.UserLevelAccessAttribute;
using StockPulse.WebApi.Attribute;

namespace StockPulse.WebApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    [UserLevelAccess(new[] { UserRole.SysAdministrator, UserRole.Reconciler, UserRole.Resolver, UserRole.Approver, UserRole.GeneralManager, UserRole.ReadOnly, UserRole.ScanAndView })]
    public class ReconcilingItemsController : ControllerBase, IAttributeValueProvider
    {
        private readonly IReconcilingItemService reconcilingItemService;
        private readonly int userId;
        private readonly string userRole;

        public UserRole attributeUserRole => GetUserRole();
        private UserRole GetUserRole()
        {
            return (UserRole)Enum.Parse(typeof(UserRole), userRole);
        }
        public ReconcilingItemsController(IReconcilingItemService reconcilingItemService, IUserService userService)
        {
            this.reconcilingItemService = reconcilingItemService;
            userId = userService.GetUserId();
            userRole = userService.GetUserRole();
        }


        // Get recon items for a stockcheck
        [HttpGet]
        [Route("ReconcilingItemTypeStats")]
        public async Task<IEnumerable<ReconcilingItemTypeStat>> GetReconcilingItemTypeStats(int stockcheckId)
        {
            return await reconcilingItemService.GetReconcilingItemTypeStats(stockcheckId, userId);
        }

        [HttpGet]
        [Route("ReconcilingItemArray")]
        public async Task<IEnumerable<ReconcilingItemVM>> GetReconcilingItemArray(int stockcheckId, int reconcilingItemTypeId)
        {
            return await reconcilingItemService.GetReconcilingItemArray(stockcheckId, userId, reconcilingItemTypeId);
        }


        [HttpGet]
        [Route("DeleteAllItems")]
        [UserLevelAccess(new[] { UserRole.SysAdministrator, UserRole.Reconciler, UserRole.Resolver, UserRole.Approver })]
        public async Task DeleteAllItems(int stockcheckId, int reconcilingItemType)
        {
            await reconcilingItemService.DeleteAllItems(stockcheckId, userId, reconcilingItemType);
        }

        [HttpGet]
        [Route("DeleteRecItem")]
        [UserLevelAccess(new[] { UserRole.SysAdministrator, UserRole.Reconciler, UserRole.Resolver, UserRole.Approver })]
        public async Task DeleteRecItems(int stockcheckId, int itemId)
        {
            await reconcilingItemService.DeleteRecItem(stockcheckId, userId, itemId);
        }

        [HttpPost]
        [Route("ReconcilingItems")]
        [UserLevelAccess(new[] { UserRole.SysAdministrator, UserRole.Reconciler, UserRole.Resolver, UserRole.Approver })]
        public async Task<IActionResult> SaveRecItems(List<ReconcilingItemSave> items)
        {
            try
            {
                await reconcilingItemService.SaveNewReconcilingItems(items, userId);
                return Ok();
            }
            catch(Exception e)
            {
                return BadRequest(e.Message);
            }
        }

        [HttpPost]
        [Route("SaveBackup")]
        [UserLevelAccess(new[] { UserRole.SysAdministrator, UserRole.Reconciler, UserRole.Resolver, UserRole.Approver })]
        public async Task<IActionResult> SaveBackup(SaveBackupForReconcilingItemsParams parms)
        {
            try
            {
                await reconcilingItemService.SaveBackup(parms, userId);
                return Ok();
            }
            catch (Exception e)
            {
                return BadRequest(e.Message);
            }
        }

        [HttpGet]
        [Route("GetBackups")]
        [UserLevelAccess(new[] { UserRole.SysAdministrator, UserRole.Reconciler, UserRole.Resolver, UserRole.Approver })]
        public async Task<IEnumerable<ImageToUpdate>> GetBackups(int stockCheckId, int reconcilingItemTypeId)
        {
            return await reconcilingItemService.GetBackups(stockCheckId, reconcilingItemTypeId, userId);
        }
    }
}
