﻿
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO  
  

CREATE OR ALTER PROCEDURE [dbo].[GET_ScansWithResolution]  
(  
    @StockCheckId INT = NULL,  
    @UserId INT = NULL  
)  
AS  
BEGIN  
  
SET NOCOUNT ON;  
   
DECLARE @isRegional INT;    
DECLARE @isTotal INT;    
DECLARE @StockCheckDate DateTime;  
DECLARE @DivisionId INT = NULL;  
DECLARE @DealerGroupId INT = NULL;  
DECLARE @SCId INT = NULL;  
  
  
IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END

  
SELECT   
@isRegional = IsRegional,   
@isTotal = IsTotal,   
@StockCheckDate = Date   
FROM StockChecks    
WHERE StockChecks.Id = @StockCheckId  
    

  
IF @isRegional = 0 AND @isTotal = 0  
   
    BEGIN   
  
 SET @SCId = @StockCheckId;  
      
   
    END  
  
ELSE IF @isRegional = 1 AND @isTotal = 0  
  
    BEGIN   
  
  
    SET @DivisionId = (SELECT DivisionId FROM StockChecks  
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId  
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id  
                        WHERE StockChecks.Id = @StockCheckId AND StockChecks.IsRegional = 1)  
  
  
    END  
  
IF @isRegional = 0 AND @isTotal = 1  
  
    BEGIN   
  
      
    SET @DealerGroupId = (SELECT DealerGroupId FROM StockChecks  
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId  
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id  
                        WHERE StockChecks.Id = @StockCheckId)  
  
      
    END  



  
 select   
    scns.Id as ScanId,
    scns.UserId,
    scns.StockCheckId,
    LastEditedById,
    LocationId,
    scns.UnknownResolutionId,
    StockItemId,
    scns.ReconcilingItemId,
    scns.IsDuplicate,
    LastEditedDateTime,
    RegConfidence,
    VinConfidence,
    --IsEdited,
    scns.Longitude,
    scns.Latitude,
    ScanDateTime,
    scns.Comment as ScanComment,
    scns.Reg as ScanReg,
    scns.Vin as ScanVin,
    scns.Description as ScanDescription,
    CoordinatesJSON,
    HasVinImage,  
    locns.Description as 'LocationDescription',  
    usrs.Name as 'ScannerName',  
    restypes.Description as 'ResolutionTypeDescription',  
    usrsRes.Name as 'ResolvedBy',  
    res.IsResolved,  
    res.ResolutionDateTime as ResolutionDate,
    res.Id as 'ResolutionId',  
    res.Notes as 'ResolutionNotes',  
	(select String_Agg(CONCAT(uri.Id,'|',ISNULL(uri.FileName,'FileName')),'::')) AS ResolutionImageIds, --this seems slow TODO
    Sites.Description AS SiteName,
    SC.Date as 'StockCheckDate',
    Sites.Longitude as StockCheckLongitude,
    Sites.Latitude as StockCheckLatitude,

    CASE
        WHEN scns.IsRegEditedOnWeb = 1 THEN 'Web app'
        WHEN scns.IsRegEditedOnDevice = 1 THEN 'Mobile app'
        ELSE NULL
    END AS RegEditStatus,
    CASE
        WHEN scns.IsVinEditedOnWeb = 1 THEN 'Web app'
        WHEN scns.IsVinEditedOnDevice = 1 THEN 'Mobile app'
        ELSE NULL
    END AS VinEditStatus
    FROM   
    Scans scns  
    LEFT JOIN UnknownResolutions res on res.Id = scns.UnknownResolutionId   
	LEFT JOIN UnknownResolutionImages uri on uri.UnknownResolutionId = res.Id
    LEFT JOIN ResolutionTypes restypes on restypes.Id = res.ResolutionTypeId  
    LEFT JOIN Locations locns on locns.Id = scns.LocationId  
    LEFT JOIN Users usrs on usrs.Id = scns.UserId  
    LEFT JOIN Users usrsRes on usrsRes.Id = res.UserId  
    LEFT JOIN StockChecks AS SC ON SC.Id=scns.StockCheckId  
    LEFT JOIN Sites ON Sites.Id=SC.SiteId  
    LEFT JOIN ReconcilingItems ri on ri.id = scns.ReconcilingItemId  
 INNER JOIN Divisions AS D ON D.Id=Sites.DivisionId  
 INNER JOIN DealerGroup AS DG ON D.DealerGroupId=DG.Id  
    WHERE   
    scns.StockItemId is null  
    AND scns.IsDuplicate = 0  
    AND (scns.ReconcilingItemId is null OR (select rt.ExplainsMissingVehicle from ReconcilingItemTypes rt where rt.Id = ri.ReconcilingItemTypeId) = 1)  
 AND SC.Id = ISNULL(@SCId, SC.Id)  
 AND SC.Date = @StockCheckDate  
 AND D.Id = ISNULL(@DivisionId, D.Id)  
 AND DG.Id = ISNULL(@DealerGroupId, DG.Id)  
 GROUP BY 
 scns.Id ,
    scns.UserId,
    scns.StockCheckId,
    LastEditedById,
    LocationId,
    scns.UnknownResolutionId,
    StockItemId,
    scns.ReconcilingItemId,
    scns.IsDuplicate,
    LastEditedDateTime,
    RegConfidence,
    VinConfidence,
    --IsEdited,
    scns.Longitude,
    scns.Latitude,
    ScanDateTime,
    scns.Comment,
    scns.Reg,
    scns.Vin,
    scns.Description,
    CoordinatesJSON,
    HasVinImage,  
    locns.Description,  
    usrs.Name,  
    restypes.Description ,  
    usrsRes.Name,  
    res.IsResolved,  
    res.ResolutionDateTime,
    res.Id,  
    res.Notes,  
    Sites.Description,
    SC.Date,
    Sites.Longitude,
    Sites.Latitude,
    scns.IsRegEditedOnWeb,
    scns.IsRegEditedOnDevice,
    scns.IsVinEditedOnWeb,
    scns.IsVinEditedOnDevice
  

  
END  
  

  GO