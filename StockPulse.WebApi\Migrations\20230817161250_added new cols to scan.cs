﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace StockPulse.WebApi.Migrations
{
    /// <inheritdoc />
    public partial class addednewcolstoscan : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "InterpretedReg",
                schema: "dbo",
                table: "Scans",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "InterpretedVin",
                schema: "dbo",
                table: "Scans",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsRegEditedOnDevice",
                schema: "dbo",
                table: "Scans",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsVinEdited",
                schema: "dbo",
                table: "Scans",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsVinEditedOnDevice",
                schema: "dbo",
                table: "Scans",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<decimal>(
                name: "VinConfidence",
                schema: "dbo",
                table: "Scans",
                type: "decimal(15,3)",
                nullable: false,
                defaultValue: 0m);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "InterpretedReg",
                schema: "dbo",
                table: "Scans");

            migrationBuilder.DropColumn(
                name: "InterpretedVin",
                schema: "dbo",
                table: "Scans");

            migrationBuilder.DropColumn(
                name: "IsRegEditedOnDevice",
                schema: "dbo",
                table: "Scans");

            migrationBuilder.DropColumn(
                name: "IsVinEdited",
                schema: "dbo",
                table: "Scans");

            migrationBuilder.DropColumn(
                name: "IsVinEditedOnDevice",
                schema: "dbo",
                table: "Scans");

            migrationBuilder.DropColumn(
                name: "VinConfidence",
                schema: "dbo",
                table: "Scans");
        }
    }
}
