﻿/****** Object:  StoredProcedure [dbo].[GET_ScansFromLatestStockchecksForSpark]    Script Date: 08/01/2022 13:21:10 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].[GET_ScansFromLatestStockchecksForSpark]
(
    @DealerGroupId INT
)
AS
BEGIN

SET NOCOUNT ON


;WITH LatestStockChecks AS (SELECT SC.SiteId, MAX(SC.[Date]) As [Date] FROM StockChecks AS SC WITH (NOLOCK)
INNER JOIN Sites AS SI WITH (NOLOCK) ON SI.Id=SC.SiteId
INNER JOIN Divisions AS D WITH (NOLOCK) ON SI.DivisionId=D.Id
INNER JOIN DealerGroup AS DG WITH (NOLOCK) ON D.DealerGroupId = DG.Id
WHERE DG.Id = @DealerGroupId AND StatusId > 1
GROUP BY SC.SiteId)


SELECT S.Id, S.Reg, S.Vin,loc.Description as Location,u.Name as ScannerName,S.ScanDateTime
FROM Scans AS S WITH (NOLOCK) 
INNER JOIN StockChecks AS SC WITH (NOLOCK) ON S.StockCheckId = SC.Id
INNER JOIN LatestStockChecks AS LSC ON SC.SiteId = LSC.SiteId AND SC.Date = LSC.Date
INNER JOIN Locations loc on loc.Id = S.LocationId
INNER JOIN Users u on u.Id = S.UserId

	

END



GO
