<button class="btn btn-primary" id="mainButton" (click)="showNewStatusModal()"> {{selections.stockCheck.status}}</button>

<!-- change status Modal -->
<ng-template #statusModal let-modal>

  <div class="modal-header">
    <h4 class="modal-title" id="modal-basic-title">
      Stock Check Status: {{selections.stockCheck.status}}
    </h4>
    <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">

    <table>
      <tbody>
        <tr *ngIf="selections.stockCheck.statusId < 4 || selections.userIsApprover">
          <td>Choose New Status: </td>
          <td>
            <div class="buttonGroup">
              <button *ngFor="let status of constants.Statuses" (click)="chooseNewStatus(status)"
                [disabled]="selections.userIsReadOnly"
                [ngClass]="{'active':status.id == statusUpdate.statusId}"
                class="btn btn-primary statusButton">{{status.description}}</button>
            </div>

          </td>
        </tr>

        <!-- Message - can't change status -->
        <tr *ngIf="selections.stockCheck.statusId >= 4 && !selections.userIsApprover">
          <td> </td>
          <td>

            To make changes to this stock check please ask an approver to move the status back below
            {{selections.stockCheck.status}}

          </td>
        </tr>
        <br>
        <tr>
          <td>Sign-off scan</td>
          <td>
            <textarea *ngIf="statusUpdate.statusId == 4 && !selections.stockCheck.hasSignoffImage && !signOffPicture"
              class="pastePicture" (paste)="pastePicture($event)" placeholder="Paste Signed Form Here"></textarea>
              <img class="backupImage" *ngIf="signOffPicture" [src]="signOffPicture">
              <img class="backupImage" *ngIf="selections.stockCheck.hasSignoffImage && statusUpdate.images[0].status != 'DELETE'" (click)="zoomInToExistingImage()" [src]="statusUpdate.images[0].url">
            <!--<fa-icon [icon]="icon.faFileImage" *ngIf="selections.stockCheck.hasSignoffImage"
              (click)="zoomInToExistingImage()"></fa-icon> -->
          </td>

        </tr>
      </tbody>
    </table>




    <div class="summaryStats">
      <table class="cph">
        <thead>
          <tr>
            <th>Current Statistics for Stock Check</th>
          </tr>
        <tbody>
          <tr>
            <td>DMS Stock</td>
            <td>{{selections.stockCheck.stockItemsCount|cph:'number':0}}</td>
          </tr>
          <tr>
            <td>Scanned and InStock</td>
            <td>{{selections.stockCheck.scannedInStock|cph:'number':0}}</td>
          </tr>
          <tr>
            <td>Total scans</td>
            <td>{{selections.stockCheck.scans|cph:'number':0}}</td>
          </tr>
          <tr>
            <td>Missing Vehicles</td>
            <td>{{selections.stockCheck.missings|cph:'number':0}}</td>
          </tr>
          <tr>
            <td>Unknown Vehicles</td>
            <td>{{selections.stockCheck.unknowns|cph:'number':0}}</td>
          </tr>
          <tr>
            <td>Missing Vehicles Outstanding Issues</td>
            <td [ngClass]="{'badFont':selections.stockCheck.missingOs}">
              {{selections.stockCheck.missingOs|cph:'number':0}}</td>
          </tr>
          <tr>
            <td>Unknown Vehicles Outstanding Issues</td>
            <td [ngClass]="{'badFont':selections.stockCheck.unknownOs}">
              {{selections.stockCheck.unknownOs|cph:'number':0}}</td>
          </tr>

        </tbody>
      </table>

    </div>




  </div>
  <div class="modal-footer">

    <button type="button" class="btn btn-success" (click)="modal.close('Save')" [disabled]="selections.userIsReadOnly">Save Stock Check</button>
    <button type="button" class="btn btn-primary" (click)="modal.dismiss('Cancelled')">Cancel</button>
  </div>

</ng-template>


<!-- zoomImage Modal -->
<ng-template #zoomImageModal let-modal>
  <div class="modal-header">
    <h4 class="modal-title" id="modal-basic-title">
      {{modalHeader}}
    </h4>
    <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body zoomImageBody">

    <img id="modalImage" *ngIf="!selections.stockCheck.hasSignoffImage" [src]="signOffPicture" class="w-100">
    <img id="modalImage" *ngIf="selections.stockCheck.hasSignoffImage" [src]="statusUpdate.images[0].url" class="w-100">

  </div>
  <div class="modal-footer">

    <button type="button" *ngIf="!amReviewingImage" class="btn btn-primary" (click)="modal.close('Ok')">OK</button>
    <button type="button" *ngIf="amReviewingImage" class="btn btn-danger" (click)="modal.close('Ok')">Delete</button>
    <button type="button" class="btn btn-primary" (click)="modal.dismiss('Cancelled')">Close</button>
  </div>
</ng-template>



<ng-template #confirmModal let-modal>

  <div class="modal-header">
    <h4 class="modal-title" id="modal-basic-title">
      Are you sure?
    </h4>
    <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body lowHeight">

    <div class="warningNote">
      If you move the stock check status to {{statusRequested.description}}, only an approver can move it back. Continue?
    </div>



  </div>
  <div class="modal-footer">

    <button type="button" class="btn btn-success" (click)="modal.close('Ok')">OK</button>
    <button type="button" class="btn btn-primary" (click)="modal.dismiss('Cancelled')">Cancel</button>
  </div>



</ng-template>