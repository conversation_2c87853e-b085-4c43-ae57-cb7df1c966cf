using AspNetCoreRateLimit;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.IdentityModel.Tokens;
using StockPulse.WebApi.Auth;
using StockPulse.WebApi.Auth.Model;
using StockPulse.WebApi.Auth.Service;
using StockPulse.WebApi.Dapper;
using StockPulse.WebApi.DataAccess;
using StockPulse.WebApi.Service;
using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Newtonsoft.Json.Serialization;
using StockPulse.Repository.Database;
using System.Collections.Generic;
using System.Linq;
using System.IdentityModel.Tokens.Jwt;
using Microsoft.Extensions.Caching.Memory;


namespace StockPulse.WebApi
{

   public static class FormFileExtensions
   {
      public static async Task<byte[]> GetBytes(this IFormFile formFile)
      {
         using (var memoryStream = new MemoryStream())
         {
            await formFile.CopyToAsync(memoryStream);
            return memoryStream.ToArray();
         }
      }
   }


   public class Startup
   {
      public Startup(IConfiguration configuration)
      {
         Configuration = configuration;
      }

      public IConfiguration Configuration { get; }

      // This method gets called by the runtime. Use this method to add services to the container.
      public void ConfigureServices(IServiceCollection services)
      {


         services.AddOptions();
         services.AddMemoryCache();

         services.Configure<IpRateLimitOptions>(Configuration.GetSection("IpRateLimiting"));
         services.Configure<IpRateLimitPolicies>(Configuration.GetSection("IpRateLimitPolicies"));

         services.AddInMemoryRateLimiting();

         services.AddHttpContextAccessor();
         //---Auth Start
         services.AddDbContext<ApplicationDbContext>(options =>
            options.UseSqlServer(
                Configuration.GetConnectionString("DefaultConnection")));


         services.AddIdentity<ApplicationUser, IdentityRole>(options =>
         {
            options.User.AllowedUserNameCharacters = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-._@+/' ";
         })
             .AddEntityFrameworkStores<ApplicationDbContext>()
             .AddDefaultTokenProviders();

         // Verify - Update as per old app
         services.Configure<IdentityOptions>(options =>
         {
            options.User.RequireUniqueEmail = false;
            


            options.Password.RequiredLength = 12;
            options.Password.RequireNonAlphanumeric = false;
            options.Password.RequireDigit = false;
            options.Password.RequireLowercase = true;
            options.Password.RequireUppercase = true;
            options.Lockout.AllowedForNewUsers = true;
            options.Lockout.DefaultLockoutTimeSpan = TimeSpan.FromDays(999);
            options.Lockout.MaxFailedAccessAttempts = 5;
         }
         );


         services.AddCors(o => o.AddPolicy("CorsPolicy", builder =>
         {
            //.AllowAnyOrigin()//  
            builder.WithOrigins(
                   "https://stockpulseprod-g0cgdfhab2fcancm.uksouth-01.azurewebsites.net",
                   "http://localhost:4200",
                   "https://localhost:4200",
                   "http://localhost:8100",
                   "https://stockpulsetest.cphi.co.uk",
                   "https://stockpulsedev.cphi.co.uk",
                   "https://stockpulse.cphi.co.uk",
                   "https://stockpulsemobile.azurewebsites.net",
                   "https://stockpulsemobiledev.azurewebsites.net",
                   "http://localhost",
                   "https://localhost",
                   "https://go.stockpulse.app",
                   "https://godev.stockpulse.app",
                   "https://gotest.stockpulse.app",
                   "capacitor://localhost",
                   "ionic://localhost")
                      .AllowAnyMethod()
                      .AllowAnyHeader();
         }));

         services.Configure<CookiePolicyOptions>(options =>
         {
            // This lambda determines whether user consent for non-essential cookies is needed for a given request.
            options.CheckConsentNeeded = context => true;
            options.MinimumSameSitePolicy = SameSiteMode.Lax;
            options.HttpOnly = Microsoft.AspNetCore.CookiePolicy.HttpOnlyPolicy.Always;
            options.Secure = CookieSecurePolicy.Always;
         });

         string AzureADAuthority = this.Configuration.GetSection("AzureAd").GetValue<string>("TenantId");
         // Authentication
         services.AddAuthentication(x =>
         {
            x.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
            x.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
            x.DefaultScheme = JwtBearerDefaults.AuthenticationScheme;
         }).AddJwtBearer(x =>
         {
            x.RequireHttpsMetadata = false; ;
            x.SaveToken = false;
            x.TokenValidationParameters = new Microsoft.IdentityModel.Tokens.TokenValidationParameters
            {
               ValidateIssuerSigningKey = true,
               IssuerSigningKey = new SymmetricSecurityKey(AuthConfiguration.GetTokenKey()),
               ValidateIssuer = false,
               ValidateAudience = false,
               ClockSkew = TimeSpan.Zero
            };
         }).AddJwtBearer("AzureAD", options =>
         {
            options.Audience = "e1f38153-846a-4d51-8cb0-3ebd16b1590f";   //Stockpulse App in Azure Portal
            options.Authority = $"https://login.microsoftonline.com/common";
            options.TokenValidationParameters = new TokenValidationParameters
            {

               ValidateIssuer = true,
               IssuerValidator = (issuer, securityToken, validationParameter) =>
                  {
                    return TokenValidator.ValidateTenantId(issuer, securityToken, validationParameter);
                 }

            };

         });

         // Authorization
         services.AddAuthorization(options =>
         {
            var defaultAuthorizationPolicyBuilder = new AuthorizationPolicyBuilder(
                   JwtBearerDefaults.AuthenticationScheme,
                   "AzureAD");
            defaultAuthorizationPolicyBuilder =
                   defaultAuthorizationPolicyBuilder.RequireAuthenticatedUser();
            options.DefaultPolicy = defaultAuthorizationPolicyBuilder.Build();
         });



         //---Auth End

         services.AddApplicationInsightsTelemetry();

         services.AddControllers().AddNewtonsoftJson(
            options =>
            {
               options.SerializerSettings.PreserveReferencesHandling = Newtonsoft.Json.PreserveReferencesHandling.None;
               options.SerializerSettings.ReferenceLoopHandling = Newtonsoft.Json.ReferenceLoopHandling.Ignore;
               options.SerializerSettings.DateTimeZoneHandling = Newtonsoft.Json.DateTimeZoneHandling.Utc;
               options.SerializerSettings.DateParseHandling = Newtonsoft.Json.DateParseHandling.DateTimeOffset;
               //options.SerializerSettings.ContractResolver = new DefaultContractResolver(); //Enabling this makes the reponse properties in CAPS. 
            });

         //services.AddDbContext<DataContext.AppContext>(options =>
         //             options.UseSqlServer(
         //                 Configuration.GetConnectionString("DefaultConnection")));
         //Register dapper in scope    
         services.AddScoped<IDapper, Dapperr>();

         //Register Services
         services.AddScoped<IGlobalParamService, GlobalParamService>();
         services.AddScoped<IScanService, ScanService>();
         services.AddScoped<IStockItemService, StockItemService>();
         services.AddScoped<IEmailSenderCustom, EmailSenderService>();
         services.AddScoped<IImageService, ImageService>();
         services.AddScoped<IRefreshTokenService, RefreshTokenService>();
         services.AddScoped<IUserService, UserService>();
         services.AddScoped<IFinancialLineService, FinancialLineService>();
         services.AddScoped<IImportMaskService, ImportMaskService>();
         services.AddScoped<ISiteService, SiteService>();
         //services.AddScoped<ISourceReportService, SourceReportService>();
         services.AddScoped<IStockCheckService, StockCheckService>();
         services.AddScoped<IReconcilingItemService, ReconcilingItemService>();
         services.AddScoped<IRecogniseService, RecogniseService>();
         services.AddScoped<IErrorReportService, ErrorReportService>();
         services.AddScoped<IPlateService, PlateService>();
         services.AddScoped<IStockCheckReconciliationService, StockCheckReconciliationService>();
         //services.AddScoped<IAzureADService, AzureADService>();
         services.AddScoped<IExcelParseService, ExcelParseService>();
         services.AddScoped<IScanLocationsService, ScanLocationsService>();
         services.AddScoped<ILabelPrinterService, LabelPrinterService>();
         services.AddScoped<IStatusChangeLogItemsService, StatusChangeLogItemsService>();
         services.AddScoped<IMaintenanceTableService, MaintenanceTableService>();
         services.AddScoped<IFileImportService, FileImportService>();

         //Register DataAccess
         services.AddScoped<IGlobalParamDataAccess, GlobalParamDataAccess>();
         services.AddScoped<IStockItemDataAccess, StockItemDataAccess>();
         services.AddScoped<IUserDataAccess, UserDataAccess>();
         services.AddScoped<IScanDataAccess, ScanDataAccess>();
         services.AddScoped<IFinancialLineDataAccess, FinancialLineDataAccess>();
         services.AddScoped<IImportMaskDataAccess, ImportMaskDataAccess>();
         services.AddScoped<IReconcilingItemDataAccess, ReconcilingItemDataAccess>();
         services.AddScoped<ISiteDataAccess, SiteDataAccess>();
         //services.AddScoped<ISourceReportDataAccess, SourceReportDataAccess>();
         services.AddScoped<IStockCheckDataAccess, StockCheckDataAccess>();
         services.AddScoped<IRecogniseDataAccess, RecogniseDataAccess>();
         services.AddScoped<IErrorReportDataAccess, ErrorReportDataAccess>();
         services.AddScoped<IScanLocationsDataAccess, ScanLocationsDataAccess>();
         services.AddScoped<ILabelPrinterDataAccess, LabelPrinterDataAccess>();
         services.AddScoped<IStatusChangeLogItemsDataAccess, StatusChangeLogItemsDataAccess>();
         services.AddScoped<IMaintenanceTableDataAccess, MaintenanceTableDataAccess>();
         services.AddScoped<IFileImportDataAccess, FileImportDataAccess>();


         ////UNCOMMENT THIS TO DO A MIGRATION
         // services.AddEntityFrameworkSqlServer().AddDbContext<StockpulseContext>(options =>
         //   options.UseSqlServer(Configuration.GetConnectionString("DefaultConnection")));

         services.AddSingleton<IRateLimitConfiguration, RateLimitConfiguration>();


         //Swagger
         services.AddEndpointsApiExplorer();
         services.AddSwaggerGen();


      }

      // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
      public void Configure(IApplicationBuilder app, IWebHostEnvironment env, IServiceProvider serviceProvider)
      {
         StockpulseContext.envi = env.EnvironmentName;

         app.UseIpRateLimiting();

         if (env.IsDevelopment())
         {
            app.UseDeveloperExceptionPage();
            app.UseSwagger();
            app.UseSwaggerUI();
         }

         app.UseHttpsRedirection();

         app.UseRouting();

         app.UseCors("CorsPolicy");
         //-- Auth Start
         app.UseAuthentication();

         app.UseMiddleware<CompressionMiddleware>();

         //-- Auth End
         app.UseAuthorization();
         app.UseMiddleware<ErrorHandlerMiddleware>();
         app.UseEndpoints(endpoints =>
         {
            endpoints.MapControllers();
         });

         //CreateRoles(serviceProvider);
      }


   }
}
