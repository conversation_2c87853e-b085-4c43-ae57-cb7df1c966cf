﻿using Microsoft.EntityFrameworkCore;
using StockPulse.Loader.Stockpulse.ViewModel;
using StockPulse.Model;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using StockPulse.Loader.ViewModel;
using StockPulse.Repository.Database;

namespace StockPulse.Loader.Services.Lithia
{


   public class LithiaStockLoaderService : GenericLoaderJobServiceParams
   {

      //constructor
      public LithiaStockLoaderService()
      {

      }


      public JobParams GetMatchingFilesAndImportParams(string incomingRoot)
      {
         string customer = "lithia";
         string filePattern = "*GLSchedule_*.csv";

         // As we are processing different stock types dependent on file
         // the StockTypes property will be amended in the interpret file
         JobParams parms = new JobParams()
         {
            jobType = LoaderJob.LithiaStockItems,
            customerFolder = customer,
            filename = filePattern,
            importSPName = null,
            loadingTableName = "StockItems",
            jobName = "LithiaStockItems",
            pulse = PulsesService.STK_LithiaStock,
            fileType = FileType.csv,
            regexPattern = "((?<=\")[^\"]*(?=\"(,|$)+)|(?<=,|^)[^,\"]*(?=,|$))",
            headerFailColumn = null,
            errorCount = 0,
            dealerGroupId = 10,
            headerDefinitions = BuildHeaderDictionary(),
            allMatchingFiles = Directory.GetFiles(incomingRoot.Replace("{customer}", customer), filePattern),
            isUS = true
         };

         return parms;
      }


      public DataTable InterpretFile(JobParams parms, RowsAndHeadersResult rowsAndHeaders, LogMessage logMessage)
      {
         List<Model.Input.StockItem> incomingLines = new List<Model.Input.StockItem>(10000); // Preset the list size

         LithiaStockType stockType = GetStockType(parms.filename);

         Dictionary<string, int> missingSitesDictionary = new Dictionary<string, int>();

         parms.additionalInfo = stockType.ToString();

         using (var db = new StockpulseContext(true))
         {
            // Only get sites that are primary sites
            var siteDescriptionDictionary = db.SiteDescriptionDictionary
                                              .Where(x => x.IsPrimarySiteId && x.DealerGroupId == parms.dealerGroupId)
                                              .AsNoTracking()
                                              .ToList();

            var siteDictionaryLookup = siteDescriptionDictionary.ToLookup(x => x.Description);

            int total = rowsAndHeaders.rowsAndCells.Count;

            int incomingProcessCount = 0;

            // Cache the header lookups
            var headerLookup = rowsAndHeaders.headerLookup;

            foreach (var rowCols in rowsAndHeaders.rowsAndCells)
            {
               try
               {
                  incomingProcessCount++;

                  System.Console.WriteLine($"Count {incomingProcessCount} / {total}");

                  string siteName = rowCols[headerLookup["StockCheckSite"]].Trim();

                  if (SharedLoaderService.SkipSiteForLithiaUS(siteName))
                  {
                     continue;
                  }

                  // Use the lookup for fast lookups
                  var siteDictionary = siteDictionaryLookup[siteName].FirstOrDefault();

                  if (siteDictionary == null)
                  {
                     parms.errorCount++;

                     if (siteName == "" || siteName == null)
                     {
                        siteName = "[BLANK NAME]";
                     }

                     if (!missingSitesDictionary.ContainsKey(siteName))
                     {
                        missingSitesDictionary[siteName] = 1;
                     }
                     else
                     {
                        missingSitesDictionary[siteName] += 1;
                     }

                     continue;
                  }

                  int siteId = siteDictionary.SiteId;

                  // Retrieve and trim necessary values once
                  var vin = rowCols[headerLookup["Vin"]].ToString().Trim();
                  
                  var description = rowCols[headerLookup["Description"]].ToString().Trim();
                  var dis = GetNullableInt(rowCols[headerLookup["DIS"]]);
                  var comment = rowCols[headerLookup["Comment"]].ToString().Trim();
                  var reference = rowCols[headerLookup["Reference"]].ToString().Trim();
                  decimal stockValue = 0;

                  var vinLoanerStock = "";

                  if (stockType == LithiaStockType.Loaner)
                  {
                     string control = rowCols[headerLookup["VinLoanerStock"]].ToString().Trim();

                     if (control.Length > 8) { control = control.Substring(0, 8); };
                     
                     // If Vin & Control both blank, skip
                     if (vin == "" && control == "") { continue; }

                     // If Vin, not blank use vin
                     else if (vin != "") { vinLoanerStock = vin; }

                     // Else use Control value for vin
                     else { vinLoanerStock = control; }
                  }
                  else
                  {
                     if (vin == "") { continue; }
                  }

                  switch (stockType)
                  {
                     case LithiaStockType.New:
                        parms.stockTypes = "NEW";
                        stockValue = GetDecimal(rowCols[headerLookup["NewStockValue1"]]) + GetDecimal(rowCols[headerLookup["NewStockValue2"]]);
                        decimal flooring = Math.Abs(GetDecimal(rowCols[headerLookup["Flooring"]]));

                        incomingLines.Add(new Model.Input.StockItem()
                        {
                           SiteId = siteId,
                           Branch = siteName,
                           Vin = vin,
                           Description = description,
                           DIS = dis,
                           GroupDIS = dis,
                           Comment = comment,
                           StockType = "NEW",
                           Reference = reference,
                           StockValue = stockValue,
                           FileImportId = parms.fileImportId,
                           DealerGroupId = parms.dealerGroupId,
                           SourceReportId = 1,
                           Flooring = flooring
                        });
                        break;

                     case LithiaStockType.Used:
                        parms.stockTypes = "USED";
                        stockValue = GetDecimal(rowCols[headerLookup["UsedStockValue1"]])
                                   + GetDecimal(rowCols[headerLookup["UsedStockValue2"]])
                                   + GetDecimal(rowCols[headerLookup["UsedStockValue3"]])
                                   + GetDecimal(rowCols[headerLookup["UsedStockValue4"]])
                                   + GetDecimal(rowCols[headerLookup["UsedStockValue5"]])
                                   + GetDecimal(rowCols[headerLookup["UsedStockValue6"]]);
                        incomingLines.Add(new Model.Input.StockItem()
                        {
                           SiteId = siteId,
                           Branch = siteName,
                           Vin = vin,
                           Description = description,
                           DIS = dis,
                           GroupDIS = dis,
                           Comment = comment,
                           StockType = "USED",
                           Reference = reference,
                           StockValue = stockValue,
                           FileImportId = parms.fileImportId,
                           DealerGroupId = parms.dealerGroupId,
                           SourceReportId = 1
                        });
                        break;

                     case LithiaStockType.Program:
                        parms.stockTypes = "PROGRAM";
                        stockValue = GetDecimal(rowCols[headerLookup["ProgramStockValue1"]])
                                   + GetDecimal(rowCols[headerLookup["ProgramStockValue2"]])
                                   + GetDecimal(rowCols[headerLookup["ProgramStockValue3"]])
                                   + GetDecimal(rowCols[headerLookup["ProgramStockValue4"]]);
                        incomingLines.Add(new Model.Input.StockItem()
                        {
                           SiteId = siteId,
                           Branch = siteName,
                           Vin = vin,
                           Description = description,
                           DIS = dis,
                           GroupDIS = dis,
                           Comment = comment,
                           StockType = "PROGRAM",
                           Reference = reference,
                           StockValue = stockValue,
                           FileImportId = parms.fileImportId,
                           DealerGroupId = parms.dealerGroupId,
                           SourceReportId = 1
                        });
                        break;

                     case LithiaStockType.Loaner:
                        parms.stockTypes = "LOANER";
                        stockValue = GetDecimal(rowCols[headerLookup["LoanerStockValue1"]])
                                   + GetDecimal(rowCols[headerLookup["LoanerStockValue2"]]);
                        incomingLines.Add(new Model.Input.StockItem()
                        {
                           SiteId = siteId,
                           Branch = siteName,
                           Vin = vinLoanerStock,
                           Description = description,
                           DIS = dis,
                           GroupDIS = dis,
                           Comment = comment,
                           StockType = parms.stockTypes,
                           Reference = reference,
                           StockValue = stockValue,
                           FileImportId = parms.fileImportId,
                           DealerGroupId = parms.dealerGroupId,
                           SourceReportId = 1
                        });
                        break;

                     case LithiaStockType.Rental:
                        parms.stockTypes = "RENTAL";
                        stockValue = GetDecimal(rowCols[headerLookup["RentalStockValue1"]])
                                   + GetDecimal(rowCols[headerLookup["RentalStockValue2"]]);
                        incomingLines.Add(new Model.Input.StockItem()
                        {
                           SiteId = siteId,
                           Branch = siteName,
                           Vin = vin,
                           Description = description,
                           DIS = dis,
                           GroupDIS = dis,
                           Comment = comment,
                           StockType = parms.stockTypes,
                           Reference = reference,
                           StockValue = stockValue,
                           FileImportId = parms.fileImportId,
                           DealerGroupId = parms.dealerGroupId,
                           SourceReportId = 1
                        });
                        break;

                     case LithiaStockType.FleetNew:
                        parms.stockTypes = "FLEETNEW";
                        stockValue = GetDecimal(rowCols[headerLookup["FleetNewStockValue1"]]);
                        flooring = Math.Abs(GetDecimal(rowCols[headerLookup["Flooring"]]));

                        incomingLines.Add(new Model.Input.StockItem()
                        {
                           SiteId = siteId,
                           Branch = siteName,
                           Vin = vin,
                           Description = description,
                           DIS = dis,
                           GroupDIS = dis,
                           Comment = comment,
                           StockType = "FLEETNEW",
                           Reference = reference,
                           StockValue = stockValue,
                           FileImportId = parms.fileImportId,
                           DealerGroupId = parms.dealerGroupId,
                           SourceReportId = 1,
                           Flooring = flooring
                        });
                        break;
                  }
               }
               catch (Exception err)
               {
                  logMessage.FailNotes = logMessage.FailNotes + $" failed on adding item, Item: {incomingProcessCount} {err}  <br>";
                  parms.errorCount++;
                  continue;
               }
            }
         }

         missingSitesDictionary = missingSitesDictionary
          .OrderBy(kvp => kvp.Key) // Sort by siteName
          .ToDictionary(kvp => kvp.Key, kvp => kvp.Value);

         foreach (var item in missingSitesDictionary)
         {
            logMessage.FailNotes += $"Unable to find site: {item.Key} ({item.Value}) <br>";
         }

         DataTable result = incomingLines.ToDataTable();
         result.Columns.Remove("Sites");
         result.Columns.Remove("FileImport");
         result.Columns.Remove("DealerGroup");
         result.Columns.Remove("SourceReports");
         return result;
      }


      private Dictionary<string, string> BuildHeaderDictionary()
      {
         Dictionary<string, string> headerDefinitions = new Dictionary<string, string>()
                {
                        { "StockCheckSite",  "COMPANY_NAME"},
                        { "Vin", "VIN" },
                        { "VinLoanerStock", "CONTROL" },
                        { "Description", "YEAR_MAKE_MODEL" },
                        { "DIS", "AGE" },
                        { "Comment", "VEHICLE_COLOR" },
                        { "StockType", "VEHICLE_CLASSIFICATION" },
                        { "Reference", "CONTROL" },
                        { "Branch", "BRANCH" },

                        { "FleetNewStockValue1", "ACCOUNT_23206_AMOUNT" },
                        { "NewStockValue1", "ACCOUNT_23200_AMOUNT" },
                        { "NewStockValue2", "ACCOUNT_23210_AMOUNT" },
                        { "Flooring", "ACCOUNT_33000_AMOUNT" },


                        { "UsedStockValue1", "ACCOUNT_24000_AMOUNT" },
                        { "UsedStockValue2", "ACCOUNT_24010_AMOUNT" },
                        { "UsedStockValue3", "ACCOUNT_24020_AMOUNT" },
                        { "UsedStockValue4", "ACCOUNT_24030_AMOUNT" },
                        { "UsedStockValue5", "ACCOUNT_24040_AMOUNT" },
                        { "UsedStockValue6", "ACCOUNT_24045_AMOUNT" },

                        { "ProgramStockValue1", "ACCOUNT_24070_AMOUNT" },
                        { "ProgramStockValue2", "ACCOUNT_24080_AMOUNT" },
                        { "ProgramStockValue3", "ACCOUNT_24090_AMOUNT" },
                        { "ProgramStockValue4", "ACCOUNT_24095_AMOUNT" },

                        { "RentalStockValue1", "ACCOUNT_26100_AMOUNT" },
                        { "RentalStockValue2", "ACCOUNT_26110_AMOUNT" },

                        { "LoanerStockValue1", "ACCOUNT_26150_AMOUNT" },
                        { "LoanerStockValue2", "ACCOUNT_26160_AMOUNT" },
                };

         return headerDefinitions;
      }

      private LithiaStockType GetStockType(string fileName)
      {
         if (fileName.ToUpper().Contains("FLEETNEW")) { return LithiaStockType.FleetNew; }
         if (fileName.ToUpper().Contains("NEW")) { return LithiaStockType.New; }
         if (fileName.ToUpper().Contains("USED")) { return LithiaStockType.Used; }
         if (fileName.ToUpper().Contains("PROGRAM")) { return LithiaStockType.Program; }
         if (fileName.ToUpper().Contains("RENTAL")) { return LithiaStockType.Rental; }
         if (fileName.ToUpper().Contains("LOANER")) { return LithiaStockType.Loaner; }

         // Default value - shouldn't ever happen
         return LithiaStockType.New; ;
      }

      private decimal GetDecimal(string raw)
      {
         try
         {
            return decimal.Parse(raw);
         }
         catch
         {
            return 0;
         }
         ;
      }

      private int? GetNullableInt(string raw)
      {
         try
         {
            return int.Parse(raw);
         }
         catch
         {
            return null;
         }
         ;
      }

      private string GetVinLastEightChars(string raw)
      {
         try
         {
            string vinRaw = raw.ToString().Trim();
            int length = vinRaw.Length;
            return vinRaw.Substring(length - 8);
         }
         catch
         {
            return null;
         }

      }




   }
}
