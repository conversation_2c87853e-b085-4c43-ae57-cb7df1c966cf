﻿﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].[DELETE_AllDMSStockItems]  
(  
    @StockCheckId INT = NULL,  
    @UserId INT = NULL
)  
AS  
BEGIN  
  
SET NOCOUNT ON;  

IF dbo.[AuthenticateUser](@UserId, @StockCheckId)  = 0
BEGIN
	RETURN SELECT 0
END

   
UPDATE Scans
SET StockItemId = NULL
WHERE StockItemId IN (
	SELECT Id 
	FROM StockItems   
	WHERE StockCheckId =  @StockCheckId
	AND IsAgencyStock = 0
)


DELETE FROM StockItems   
WHERE StockCheckId =  @StockCheckId  
AND IsAgencyStock = 0
	
  
EXEC [dbo].[UPDATE_StockCheckLastUpdate] @StockCheckId, @UserId  
  
 
   
  
END  
GO
  