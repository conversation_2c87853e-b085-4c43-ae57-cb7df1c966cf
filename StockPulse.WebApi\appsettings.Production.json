{

  "ConnectionStrings": {
    "DefaultConnection": "Server=tcp:cphi.database.windows.net,1433; Initial Catalog=stockpulse; Persist Security Info=False; User ID=StockPulseApiUser; Password=***************; MultipleActiveResultSets=False; Encrypt=True; TrustServerCertificate=False; Connection Timeout=30;"
  },


  "BlobStorage": {
    "StorageAccount": "stockpulseimages",
    "StorageAccountKey": "****************************************************************************************",

    //Live
    "FilePath": "*****************************************************/",
    "ReadOnlyKey": "sp=r&st=2021-12-17T18:02:00Z&se=2041-02-14T02:02:00Z&spr=https&sv=2020-08-04&sr=c&sig=bQPFJJ8efVK3eBL4Lwf6mMEAjEV%2B2ojsSI53fJHaeT4%3D"
  },


  "WebApp": {
    "URL": "https://stockpulse.cphi.co.uk/",
    "Env": "LOCAL", // LOCAL, DEV, TEST, PROD
    "Country": "UK" //UK, US

  }
}
