﻿using System.ComponentModel.DataAnnotations.Schema;

namespace StockPulse.Model
{
    public class UserAndLogin
        {
        public string AppUserId { get; set; }
        public int? Code { get; set; }
        public string Name { get; set; }
        public string NameShort { get; set; }
        public string UserName { get; set; }
        public string RoleName { get; set; }   //The 'Name' of the item within AspNetRoles.  Ensure manually validate.
        public string Email { get; set; }
        public string Sites { get; set; }

        public int? SiteCode { get; set; }

        [ForeignKey("SiteCode")]
        public virtual Site Site { get; set; }
        public string NewEmail { get; set; }
        public string EmployeeNumber { get; set; }
        public bool IsDeleted { get; set; }
        public bool IsLocked { get; set; }
    }

   
}
