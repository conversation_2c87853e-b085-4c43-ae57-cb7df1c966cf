﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace StockPulse.Model.Import
{
    public class SiteDescriptionDictionary
    {
        [Key]
        public int Id { get; set; }
        public string Description { get; set; }


        public int DealerGroupId { get; set; }
        [ForeignKey("DealerGroupId")]
        public virtual DealerGroup DealerGroup { get; set; }


        public int SiteId { get; set; }
        [ForeignKey("SiteId")]
        public virtual Site Sites { get; set; }


        public bool IsPrimarySiteId { get; set; }


    }

}