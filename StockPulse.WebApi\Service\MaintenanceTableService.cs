﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using StockPulse.Model;
using StockPulse.WebApi.DataAccess;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace StockPulse.WebApi.Service
{
    public interface IMaintenanceTableService
    {
        Task<IEnumerable<MaintenanceTable>> GetTables();
        Task<string> GetData(int tableId);
        Task<bool> SaveData(MaintenanceTableSaveVM maintenanceTableSaveVM);
        Task<bool> DeleteData(MaintenanceTableDeleteVM maintenanceTableDeleteVM);
    }

    public class MaintenanceTableService : IMaintenanceTableService
    {

        private readonly IMaintenanceTableDataAccess maintenanceTableDataAccess;

        public MaintenanceTableService(IMaintenanceTableDataAccess maintenanceTableDataAccess)
        {
            this.maintenanceTableDataAccess = maintenanceTableDataAccess;
        }


        public async Task<string> GetData(int tableId)
        {
            //Get table name
            var tableName = await maintenanceTableDataAccess.getTableName(tableId);
            //generate query
            var query = $"SELECT * FROM {tableName} ORDER BY 1 ASC";

            var tableData = await maintenanceTableDataAccess.getData(query);

            return tableData;

        }

        public async Task<IEnumerable<MaintenanceTable>> GetTables()
        {
            return await this.maintenanceTableDataAccess.GetTables();
        }

        public async Task<bool> SaveData(MaintenanceTableSaveVM maintenanceTableSaveVM)
        {
            var childrens = ((Newtonsoft.Json.Linq.JObject)JsonConvert.DeserializeObject(maintenanceTableSaveVM.RowData));
            JToken primaryKey = childrens.GetValue("Id");
            childrens.Remove("Id");

            string query = string.Empty;
            string queryValues = string.Empty;

            //Check if Id is Zero means its a new records.
            if (primaryKey == null)
            {
                //Insert
                query = $"INSERT INTO {maintenanceTableSaveVM.TableName} (";

                //List of all columns
                foreach (var child in childrens)
                {
                    query += $"\"{child.Key}\" ,";
                    queryValues += $"'{child.Value}' ,";


                }
                query = query.Substring(0, query.Length - 1);
                queryValues = queryValues.Substring(0, queryValues.Length - 1);

                query += $") VALUES ( {queryValues})";
            }
            else
            {
                //Update

                query = $"UPDATE {maintenanceTableSaveVM.TableName} ";
                query += $"SET ";
                foreach (var child in childrens)
                {
                    if (child.Value.ToString().Length == 0)
                    {
                        query += $"\"{child.Key}\" = null ,";
                    }
                    else {
                        query += $"\"{child.Key}\" = '{EscapeSingleQuotes(child.Value)}' ,";
                    } 
                }

                query = query.Substring(0, query.Length - 1);

                query += $"WHERE \"Id\" =  '{primaryKey}'";

            }




            return await maintenanceTableDataAccess.SaveData(query);

        }
        public async Task<bool> DeleteData(MaintenanceTableDeleteVM maintenanceTableDeleteVM)
        {
            //var childrens = ((Newtonsoft.Json.Linq.JObject)JsonConvert.DeserializeObject(maintenanceTableDeleteVM.RowId));
            //JToken PrimaryKey = childrens.First;
            var query = $"DELETE FROM {maintenanceTableDeleteVM.TableName} WHERE \"Id\" = {maintenanceTableDeleteVM.RowId}";

            return await maintenanceTableDataAccess.DeleteData(query);
        }

        private string EscapeSingleQuotes(JToken input)
        {
            string value = input?.ToString() ?? string.Empty;
            
            return value.Replace("'", "''");
        }
    }
}
