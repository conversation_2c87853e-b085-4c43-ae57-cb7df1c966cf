﻿using Microsoft.Graph;

using System;
using System.Collections.Generic;

namespace StockPulse.WebApi.ViewModel
{

    public class WaterfallBarDetailItem
    {

        //----------------------------------
        //construct from scans
        //----------------------------------
        public WaterfallBarDetailItem(ViewModel.Scan scan, string siteName)
        {
            PopulateFieldsFromScan(scan, siteName);
        }



        public WaterfallBarDetailItem(ScanWithResolution scan, string siteName)
        {
            PopulateFieldsFromScan(scan, siteName);
            ResolutionTypeDesc = scan.ResolutionTypeDescription;
            ResolutionNotes = scan.ResolutionNotes;
            ResolutionDate = scan.ResolutionDate;
            ResolvedBy = scan.ResolvedBy;
            ResolutionImageIds = scan.ResolutionImageIds;
            ResolutionImages = scan.ResolutionImages;
        }


        public WaterfallBarDetailItem(ScanMatchedToRecItem scan, string siteName)
        {
            PopulateFieldsFromScan(scan, siteName);
            MatchingItemTypeDesc = scan.ReconcilingItemTypeDescription;
            MatchingItemDesc = scan.RecItemDescription;
            MatchingItemComment = scan.RecItemComment;
            MatchingItemRef = scan.RecItemReference;
        }

        public WaterfallBarDetailItem(ScanMatchedToOtherSiteStockItem scan, string siteName)
        {
            PopulateFieldsFromScan(scan, siteName);
            //Reference = scan.MatchingStockItemReference;
            OtherSiteName = scan.MatchingSiteDescription;
            MatchingItemDesc = scan.MatchingStockItemDescription;
            MatchingItemRef = scan.MatchingStockItemReference;
            MatchingItemComment = scan.MatchingStockItemComment;
        }

        public WaterfallBarDetailItem(ScanThatIsADuplicate scan, string siteName)
        {
            PopulateFieldsFromScan(scan, siteName);
            OriginalLocationDescription = scan.OriginalLocationDescription;
            OriginalId = scan.OriginalScanId;
            OriginalScannedBy = scan.OriginalScannedBy;
            OriginalScannedDate = scan.OriginalScannedDate;
        }


        //----------------------------------
        // construct from stockitems
        //----------------------------------
        public WaterfallBarDetailItem(ViewModel.StockItem stockitem)
        {
            PopulateFieldsFromStockitem(stockitem);
        }

        public WaterfallBarDetailItem(StockItemWithResolution stockItem)
        {
            PopulateFieldsFromStockitem(stockItem);
            ResolvedBy = stockItem.ResolvedBy;
            ResolutionTypeDesc = stockItem.ResolutionTypeDescription;
            ResolutionNotes = stockItem.ResolutionNotes;
            ResolutionDate = stockItem.ResolutionDate;
            ResolutionImageIds = stockItem.ResolutionImageIds;
            ResolutionImages = stockItem.ResolutionImages;
        }


        public WaterfallBarDetailItem(StockItemWithScan stockitem)
        {
            PopulateFieldsFromStockitem(stockitem);
            ScanId = stockitem.ScanId;
            ScannedBy = stockitem.ScannedBy;
            ScannedDate = stockitem.ScanDateTime;
            ScanLocation = stockitem.LocationDescription;
            Distance = stockitem.DistanceFromDealershipInMiles;
            Latitude = Decimal.ToDouble(stockitem.Latitude);
            Longitude = Decimal.ToDouble(stockitem.Longitude);
            SiteName = stockitem.SiteName;
            RegEditStatus = stockitem.RegEditStatus;
            VinEditStatus = stockitem.VinEditStatus;
        }

        public WaterfallBarDetailItem(StockItemMatchedToRecItem stockItem)
        {
            PopulateFieldsFromStockitem(stockItem);
            MatchingItemTypeDesc = stockItem.ReconcilingItemTypeDescription;
            MatchingItemDesc = stockItem.MatchingDesc;
            MatchingItemComment = stockItem.MatchingComment;
            MatchingItemRef = stockItem.MatchingRef;
        }

        public WaterfallBarDetailItem(StockItemMatchedToOtherSiteScan stockItem)
        {
            PopulateFieldsFromStockitem(stockItem);
            OtherSiteName = stockItem.MatchingSiteDescription;
            MatchingItemDesc = stockItem.MatchingScanDescription;
            MatchingItemRef = stockItem.MatchingScanReference;
            ScanId = stockItem.MatchingScanId;
            ScanLocation = stockItem.MatchingScanLocationDesc;
            ScannedBy = stockItem.MatchingScanScannedBy;
        }

        public WaterfallBarDetailItem(StockItemThatIsADuplicate stockItem)
        {
            PopulateFieldsFromStockitem(stockItem);
            OriginalId = stockItem.OriginalStockItemId;
            OriginalStockType = stockItem.OriginalStockType;
            OriginalComment = stockItem.OriginalComment;
            OriginalReference = stockItem.OriginalReference;
        }


        public int StockItemId { get; set; }
        public string Reg { get; set; }
        public string Vin { get; set; }
        public string SiteName { get; set; }
        public string BranchName { get; set; }
        public string Description { get; set; }
        public string Comment { get; set; }
        public string Reference { get; set; }
        public decimal RegConfidence { get; set; }
        public decimal VinConfidence { get; set; }
        public int ScanId { get; set; }
        public string ScannedBy { get; set; }
        public DateTime ScannedDate { get; set; }
        public string ScanLocation { get; set; }
        public decimal Distance { get; set; }
        public int DIS { get; set; }
        public int GroupDIS { get; set; }
        public string StockType { get; set; }
        public decimal SIV { get; set; }
        public string MatchingItemTypeDesc { get; set; }
        public string MatchingItemDesc { get; set; }
        public string MatchingItemComment { get; set; }
        public string MatchingItemRef { get; set; }
        public string OtherSiteName { get; set; }
        public DateTime? ResolutionDate { get; set; }
        public string ResolvedBy { get; set; }
        public string ResolutionTypeDesc { get; set; }
        public string ResolutionNotes { get; set; }
        public string ResolutionImageIds { get; set; }
        public List<ImageToUpdate> ResolutionImages { get; set; }

        public int OriginalId { get; set; } //the matching scan which this is a  duplicate of.   Should only ever be 1 for the same stockcheck, based on matching on either reg or vin
        public string OriginalLocationDescription { get; set; }
        public string OriginalScannedBy { get; set; }
        public DateTime? OriginalScannedDate { get; set; }
        public string OriginalStockType { get; set; }
        public string OriginalComment { get; set; }
        public string OriginalReference { get; set; }

        public double Longitude { get; set; }
        public double Latitude { get; set; }
        public decimal StockValue { get; set; }
        public decimal Flooring { get; set; }
        public string RegEditStatus { get; set; }
        public string VinEditStatus { get; set; }

        private void PopulateFieldsFromScan(Scan scan, string siteName)
        {
            StockItemId = scan.ScanId;
            Reg = scan.ScanReg;
            Vin = scan.ScanVin;
            RegConfidence = scan.RegConfidence;
            VinConfidence = scan.VinConfidence;
            SiteName = scan.SiteName;
            Description = scan.ScanDescription;
            Comment = scan.ScanComment;
            ScanId = scan.ScanId;
            ScannedBy = scan.ScannerName;
            ScannedDate = scan.ScanDateTime;
            ScanLocation = scan.LocationDescription;
            Distance = scan.DistanceFromDealershipInMiles;
            Latitude = Decimal.ToDouble(scan.Latitude);
            Longitude = Decimal.ToDouble(scan.Longitude);
            RegEditStatus = scan.RegEditStatus;
            VinEditStatus = scan.VinEditStatus;
        }

        private void PopulateFieldsFromStockitem(StockItem stockItem)
        {
            StockItemId = (int)stockItem.StockItemId;
            Reg = stockItem.Reg;
            Vin = stockItem.Vin;
            SiteName = stockItem.SiteName;
            BranchName = stockItem.Branch;
            Description = stockItem.Description;
            Comment = stockItem.Comment;
            Reference = stockItem.Reference;
            DIS = stockItem.DIS;
            GroupDIS = stockItem.GroupDIS;
            StockType = stockItem.StockType;
            SIV = stockItem.StockValue;
            StockValue = stockItem.StockValue;
            Flooring = stockItem.Flooring;
        }
    }


}
