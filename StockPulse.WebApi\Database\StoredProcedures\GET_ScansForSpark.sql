﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
  
  
    
CREATE OR ALTER PROCEDURE [dbo].[GET_ScansForSpark]    
(    
    @DealerGroupId INT,    
 @Reg NVARCHAR(50) = NULL,    
 @Vin NVARCHAR(50) = NULL    
    
)    
AS    
BEGIN    
    
SET NOCOUNT ON    

  
    
    
DECLARE @ImageKeyValue nvarchar(500)    
DECLARE @ConfigFilePathValue NVARCHAR(500)    
    
SET @ImageKeyValue = (SELECT StringValue FROM GlobalParams WHERE DealerGroupId = @DealerGroupId AND [Name] = 'ImageKey')    
SET @ConfigFilePathValue = (SELECT StringValue FROM GlobalParams WHERE DealerGroupId = @DealerGroupId AND [Name] = 'ConfigFilePath')    



  
IF (@Reg = '' OR @Reg = ' ' OR @Reg = 'INPUT' OR @Reg = 'TBC')  
BEGIN   
 SET @Reg = NULL  
END  
  
IF (@Vin = '' OR @Vin = ' ' OR @Vin = 'INPUT' OR @Vin = 'TBC')  
BEGIN   
 SET @Vin = NULL  
END  

PRINT @Reg
PRINT @Vin
  

;WITH LatestStockChecks AS (SELECT SC.SiteId, SC.[Date],
ROW_NUMBER() OVER (PARTITION BY SC.SiteId
                              ORDER BY SC.[Date] DESC
                             ) as RowNumber
FROM StockChecks AS SC WITH (NOLOCK)  
INNER JOIN Sites AS SI WITH (NOLOCK) ON SI.Id=SC.SiteId  
INNER JOIN Divisions AS D WITH (NOLOCK) ON SI.DivisionId=D.Id  
INNER JOIN DealerGroup AS DG WITH (NOLOCK) ON D.DealerGroupId = DG.Id  
WHERE DG.Id = @DealerGroupId AND StatusId > 1)    
    
SELECT TOP 3 S.Id, S.Reg, S.Vin, U.Name,S.Description,    
CONCAT(@ConfigFilePathValue, S.Id, 'T.jpg?',@ImageKeyValue) AS URL,    
S.ScanDateTime, L.Description AS 'Location'
FROM Scans AS S WITH (NOLOCK)     
INNER JOIN StockChecks AS SC WITH (NOLOCK) ON S.StockCheckId = SC.Id    
INNER JOIN LatestStockChecks AS LSC ON SC.SiteId = LSC.SiteId AND SC.Date = LSC.Date  
INNER JOIN Sites AS SI WITH (NOLOCK) ON SI.Id=SC.SiteId    
INNER JOIN Divisions AS D WITH (NOLOCK) ON SI.DivisionId=D.Id    
INNER JOIN DealerGroup AS DG WITH (NOLOCK) ON D.DealerGroupId = DG.Id    
INNER JOIN Locations AS L WITH (NOLOCK) ON S.LocationId = L.Id    
LEFT JOIN Users AS U WITH (NOLOCK) ON S.UserId = U.Id    
WHERE   
LSC.RowNumber <=3
AND DG.Id = @DealerGroupId     
AND SC.IsRegional = 0 and SC.IsTotal = 0
AND (ISNULL(@Reg, '') != ISNULL(@Vin, '')) --both can't be null 
AND 
(
(ISNULL(@Reg, '') != '' AND ISNULL(@Reg, '') = CASE WHEN S.Reg = '' OR S.Reg = ' ' OR S.Reg = 'INPUT' OR S.Reg = 'TBC'  THEN '' ELSE S.Reg END)
OR (ISNULL(@Vin, '') != '' AND ISNULL(@Vin, '') = CASE WHEN S.Vin = '' OR S.Vin= ' ' OR S.Vin= 'INPUT' OR S.Vin= 'TBC'  THEN '' ELSE S.Vin END)
)

    
ORDER BY SC.Id DESC    
    
    
     
    
END    
    
  
