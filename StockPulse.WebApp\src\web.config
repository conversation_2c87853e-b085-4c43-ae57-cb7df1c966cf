<configuration> 
<system.webServer> 
<httpProtocol>
    <customHeaders>
	    <remove name="X-Powered-By"/>
      <add name="X-Content-Type-Options" value="nosniff"/>
      <add name="Cache-Control" value="no-cache, no-store, must-revalidate"/>
      <add name="X-Frame-Options" value="SAMEORIGIN" />
    </customHeaders>
</httpProtocol>
<security>
				<requestFiltering removeServerHeader="true" />
			</security>
 <staticContent>
  <remove fileExtension=".json"/>
  <mimeMap fileExtension=".json" mimeType="application/json"/>
</staticContent>
  <rewrite> 
    <rules> 
      <rule name="Angular Routes 1" stopProcessing="true"> 
        <match url=".*" /> 
          <conditions logicalGrouping="MatchAll"> 
            <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" /> 
            <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" /> 
          </conditions> 
        <action type="Rewrite" url="/" /> 
      </rule> 
    </rules> 
  </rewrite> 
</system.webServer> 
</configuration>