

  
CREATE OR ALTER PROCEDURE [dbo].[GET_DuplicateScans]  
(  
    @StockCheckId INT = NULL,  
    @UserId INT = NULL  
)  
AS  
BEGIN  
  
SET NOCOUNT ON;  

DECLARE @accessVar INT;    
DECLARE @isRegional INT;    
DECLARE @isTotal INT;    
DECLARE @StockCheckDate DateTime;  
DECLARE @DivisionId INT = NULL;  
DECLARE @DealerGroupId INT = NULL;  
DECLARE @SCId INT = NULL;  
  
  
IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END
 
  
SELECT   
@isRegional = IsRegional,   
@isTotal = IsTotal,   
@StockCheckDate = Date   
FROM StockChecks    
WHERE StockChecks.Id = @StockCheckId  
    

  
    
IF @isRegional = 0 AND @isTotal = 0    
    
    BEGIN    
  
 SET @SCId = @StockCheckId;  
    
  
    END    
    
ELSE IF @isRegional = 1 AND @isTotal = 0    
    
    BEGIN    
    
    
    SET @DivisionId = (SELECT DivisionId FROM StockChecks    
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId    
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id    
                        WHERE StockChecks.Id = @StockCheckId AND StockChecks.IsRegional = 1)    
    
    END    
    
ELSE IF @isRegional = 0 AND @isTotal = 1    
    
    BEGIN    
    
        
    SET @DealerGroupId = (SELECT DealerGroupId FROM StockChecks    
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId    
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id    
                        WHERE StockChecks.Id = @StockCheckId)    
    END   
  

  --work out first instance of each scan
  SELECT
  sca.Reg,sca.Vin,
  MIN(sca.Id) as Id
  INTO #UniqueIds
  FROM Scans sca
  INNER JOIN StockChecks  SC ON SC.Id=Sca.StockCheckId 
  INNER JOIN Sites sit ON sit.Id=SC.SiteId  
  INNER JOIN Divisions  D ON D.Id=sit.DivisionId  
  INNER JOIN DealerGroup  DG ON D.DealerGroupId=DG.Id  
  WHERE SC.Id = ISNULL(@SCId, SC.Id)   
  AND SC.Date = @StockCheckDate  
  AND D.Id = ISNULL(@DivisionId, D.Id)  
  AND DG.Id = ISNULL(@DealerGroupId, DG.Id)  
  AND sca.IsDuplicate = 0
  GROUP BY sca.Reg,sca.Vin

  --find out everything about that first instance
  SELECT
  sca.Id, 
  sca.StockCheckId, 
  sca.Reg,
  sca.Vin,
  loc.Description as Location,
  usrs.Name as ScannedName,
  sca.ScanDateTime
  INTO #firstItems
  FROM Scans sca
  INNER JOIN Locations loc on loc.Id = sca.LocationId
  INNER JOIN Users usrs ON usrs.Id=sca.UserId   
  INNER JOIN #uniqueIds u on u.id = sca.id
  DROP TABLE #uniqueIds
  
  ;WITH allRecords AS (
        SELECT Scans.Id as ScanId ,
        Scans.[UserId]  ,
        scans.[Reg] as ScanReg ,
        scans.[IsDuplicate]  ,
        scans.[StockCheckId]  ,
        scans.[StockItemId]  ,
        scans.[ReconcilingItemId]  ,
        scans.[RegConfidence]  ,
        scans.[VinConfidence],
        scans.[ScanDateTime]  ,
        scans.[Vin]   as ScanVin,
        usrs.Name AS ScannerName  ,
        scans.[LocationId]  ,
        l.Description AS 'LocationDescription'  ,
        scans.[Comment] as ScanComment ,
        scans.[CoordinatesJSON]  ,
        Scans.[Description] as ScanDescription ,
        scans.[HasVinImage]  ,
        --scans.[IsEdited]  ,
        scans.[LastEditedById]  ,
        scans.[LastEditedDateTime]  ,
        Scans.[Latitude]  ,
        Scans.[Longitude]  ,
        Sites.Description AS SiteName  ,
		f.Id as OriginalScanId,
		f.Location as OriginalLocationDescription,
		f.ScannedName as OriginalScannedBy,
		f.ScanDateTime as OriginalScannedDate,
		ROW_NUMBER() OVER (Partition By Scans.Id Order By f.Id) AS RowNumber,
        Sites.Longitude as StockCheckLongitude,
        Sites.Latitude as StockCheckLatitude,
        CASE
            WHEN scans.IsRegEditedOnWeb = 1 THEN 'Web app'
            WHEN scans.IsRegEditedOnDevice = 1 THEN 'Mobile app'
            ELSE NULL
        END AS RegEditStatus,
        CASE
            WHEN scans.IsVinEditedOnWeb = 1 THEN 'Web app'
            WHEN scans.IsVinEditedOnDevice = 1 THEN 'Mobile app'
            ELSE NULL
        END AS VinEditStatus


        FROM [dbo].[Scans] scans  
        INNER JOIN Users usrs ON usrs.Id=Scans.UserId  
        INNER JOIN Locations l ON l.Id=Scans.LocationId  
        INNER JOIN StockChecks AS SC ON SC.Id=Scans.StockCheckId  
        INNER JOIN Sites ON Sites.Id=SC.SiteId  
		INNER JOIN Divisions AS D ON D.Id=Sites.DivisionId  
		INNER JOIN DealerGroup AS DG ON D.DealerGroupId=DG.Id  
		LEFT JOIN #firstItems f on (
		(f.Reg <> '' AND f.Reg = scans.Reg) OR 
		(f.Vin <> '' AND f.Vin = scans.Vin)
		) AND f.StockCheckId = scans.StockCheckId
        WHERE   
        Scans.IsDuplicate = 1  
		AND SC.Id = ISNULL(@SCId, SC.Id)  
		AND SC.Date = @StockCheckDate  
		AND D.Id = ISNULL(@DivisionId, D.Id)  
		AND DG.Id = ISNULL(@DealerGroupId, DG.Id)  
	)

	SELECT * FROM allRecords where RowNumber = 1
     
DROP TABLE #firstItems
  
END  


GO


