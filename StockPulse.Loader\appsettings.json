{
  //-------------------------------------------------x
  // THIS IS LOADER
  //-------------------------------------------------x


  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },

  "ConnectionStrings": {
    "DefaultConnection": "Server=tcp:cphi.database.windows.net,1433; Initial Catalog=stockpulse;Persist Security Info=True;User ID=StockpulseLoaderUK; Password=********************************************; MultipleActiveResultSets=False; Encrypt=True; TrustServerCertificate=False; Connection Timeout=90;",
  },
  "EmailSettings": {
    "mailAppTenantId": "7def64f8-8b3f-400a-8ad0-e50eb7e77eef",
    "mailAppId": "9a44d66f-cfe2-4d19-b129-62b3e0dd28aa",
    "mailSecretValue": "****************************************",
    "mailAccountInbound": "<EMAIL>",
    "mailAccountOutbound": "<EMAIL>"
  },

  "AppSettings": {
    "AllowedHosts": "*",
    "ClientSettingsProvider.ServiceUri": "",
    "timeZoneInfo": "UTC", //TimeZoneInfo --- Coordinated Universal Time, Central Standard Time, Eastern Standard Time, GMT Standard Time, Hawaiian Standard Time, Mountain Standard Time, Pacific Standard Time, West Pacific Standard Time

    "isDev": "false",
    "isUS": "false",

    "incomingRoot": "C:\\cphiRoot\\{customer}\\inbound",

    "overrideRunJobNow": null, //put the job name in here if you wish to force run it  e.g. "PeopleJob".   Or null (without quotes).  Only ever change appsettings.json not the underlying rrgDev file

    "stockPulseLoaderJob": "REPEAT EVERY 1 MINUTES"
    




  },
  "Monitor": {
    "AddLogMessageURL": "https://cphimonitorapi.azurewebsites.net/api/monitor/",
    "AppKey": "7"
  }


} 
