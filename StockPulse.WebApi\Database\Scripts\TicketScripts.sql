﻿



--STK-1470
/*
1. Run script - to add new DealerGroupId column
2. Run script to update DealerGroupId col in AspNetUsers table
3. Run script to drop old index and add new unique index on AspNetUsers table
*/

--STEP:1
ALTER TABLE AspNetUsers ADD DealerGroupId int NULL;

--STEP:2
update a
set a.DealerGroupId = u.DealerGroupId
from AspNetUsers a 
inner join Users u on u.Id = a.LinkedPersonId


--STEP:3

/****** Object:  Index [IX_AspNetUsers_UserName]    Script Date: 28/02/2025 09:54:42 ******/
DROP INDEX [IX_AspNetUsers_UserName] ON [dbo].[AspNetUsers]
GO

/****** Object:  Index [IX_AspNetUsers_NormalizedUserName]    Script Date: 28/02/2025 09:54:34 ******/
DROP INDEX [IX_AspNetUsers_NormalizedUserName] ON [dbo].[AspNetUsers]
GO

/****** Object:  Index [IX_AspNetUsers_NormalizedEmail]    Script Date: 28/02/2025 09:54:17 ******/
DROP INDEX [IX_AspNetUsers_NormalizedEmail] ON [dbo].[AspNetUsers]
GO

/****** Object:  Index [IX_AspNetUsers_Email]    Script Date: 28/02/2025 09:53:48 ******/
DROP INDEX [IX_AspNetUsers_Email] ON [dbo].[AspNetUsers]
GO

/****** Object:  Index [IX_AspNetUsers_UserName_Email]    Script Date: 28/02/2025 09:58:16 ******/
DROP INDEX [IX_AspNetUsers_UserName_Email] ON [dbo].[AspNetUsers]
GO



CREATE UNIQUE INDEX IX_AspNetUsers_UserName_DealerGroupId 
ON AspNetUsers (UserName, DealerGroupId);
GO

CREATE UNIQUE INDEX IX_AspNetUsers_NormalizedUserName_DealerGroupId 
ON AspNetUsers (NormalizedUserName, DealerGroupId);
GO

CREATE UNIQUE INDEX IX_AspNetUsers_NormalizedEmail_DealerGroupId 
ON AspNetUsers (NormalizedEmail, DealerGroupId);
GO

CREATE UNIQUE INDEX IX_AspNetUsers_Email_DealerGroupId 
ON AspNetUsers (Email, DealerGroupId);
GO

CREATE UNIQUE INDEX IX_AspNetUsers_UserName_Email_DealerGroupId 
ON AspNetUsers (UserName, Email, DealerGroupId);
GO