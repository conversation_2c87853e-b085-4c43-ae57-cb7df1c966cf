﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO



CREATE OR ALTER PROCEDURE [dbo].[UPDATE_StockWithMissingResolutions]
(
    @StockCheckId INT,
	@UserId INT,
	@MissingResolutionId INT,
	@StockItemId INT
)
AS
BEGIN

SET NOCOUNT ON;

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END

UPDATE dbo.StockItems 
SET MissingResolutionId = @MissingResolutionId
WHERE Id = @StockItemId


EXEC [dbo].[UPDATE_StockCheckLastUpdate] @StockCheckId, @UserId

END
	


GO


