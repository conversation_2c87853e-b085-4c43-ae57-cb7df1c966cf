import { DealerGroupVM } from "./DealerGroupVM";
import { GlobalParam } from "./GlobalParam";
import { ResolutionType } from "./ResolutionType";
import { UserPreference } from "./UserPreference";



export interface WhoAmIResult {
  role: string;
  userId: number;
  usersName: string;
  userSiteIds: string;
  dealerGroupsForSysAdmin: DealerGroupVM[];
  userHomeSiteName:string;

  resolutionTypes: ResolutionType[];
  globalParams: GlobalParam[];
  userUsername: string;
  userPreferences: UserPreference[];
}
