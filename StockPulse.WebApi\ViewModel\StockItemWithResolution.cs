﻿using System;
using System.Collections.Generic;

namespace StockPulse.WebApi.ViewModel
{
    public class StockItemWithResolution : StockItem
    {
        public int ResolutionId { get; set; }
        public int ResolutionTypeId { get; set; }
        public string ResolutionTypeDescription { get; set; }
        public bool IsResolved { get; set; }
        public string ResolvedBy { get; set; }
        public string ResolutionNotes { get; set; }
        public DateTime? ResolutionDate { get; set; }
        public DateTime StockCheckDate { get; set; }
        public string ResolutionImageIds { get; set; }
        public List<ImageToUpdate> ResolutionImages { get; set; }
        //public List<int> ResolutionImageIdsList { get; set; }
    }
}
