﻿
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO



CREATE OR ALTER PROCEDURE [dbo].[UPDATE_StockCheckHasImageflag]
(
    @UserId INT = NULL,
	@StockCheckId INT = NULL,
    @HasSignOffImage INT = NULL	
)
AS
BEGIN

SET NOCOUNT ON;

--Firstly check if allowed
IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END



UPDATE dbo.[StockChecks] SET [HasSignoffImage] = @HasSignOffImage WHERE [Id] = @StockCheckId 

EXEC [dbo].[UPDATE_StockCheckLastUpdate] @StockCheckId, @UserId



END

GO


