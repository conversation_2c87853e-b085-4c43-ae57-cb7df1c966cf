﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using StockPulse.Model.Import;

namespace StockPulse.Model
{
    // Formerly StockCheckItems
    public class StockItem
    {
        [Key]
        public int Id { get; set; }

        public int? ScanId { get; set; }
        [ForeignKey("ScanId")]
        public virtual Scan Scan { get; set; }

        public int? ReconcilingItemId { get; set; }
        [ForeignKey("ReconcilingItemId")]
        public virtual ReconcilingItem ReconcilingItem { get; set; }

        public int? MissingResolutionId { get; set; }
        [ForeignKey("MissingResolutionId")]
        public virtual MissingResolution MissingResolution { get; set; }

        public int StockCheckId { get; set; }
        [ForeignKey("StockCheckId")]
        public virtual StockCheck StockCheck { get; set; }

        public int SourceReportId { get; set; }
        [ForeignKey("SourceReportId")]
        public virtual SourceReport SourceReport { get; set; }

        public string Reg { get; set; }

        public string Vin { get; set; }

        public string Description { get; set; }

        public int? DIS { get; set; }

        public int? GroupDIS { get; set; }

        public string Branch { get; set; }

        public string Comment { get; set; }

        public string StockType { get; set; }

        public string Reference { get; set; }

        public decimal StockValue { get; set; }

        public bool IsAgencyStock { get; set; }

        public int? FileImportId { get; set; }
        [ForeignKey("FileImportId")]
        public virtual FileImport FileImport { get; set; }

        public decimal Flooring { get; set; }

    }

}