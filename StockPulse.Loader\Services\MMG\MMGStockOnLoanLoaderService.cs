﻿using Microsoft.EntityFrameworkCore;
using StockPulse.Loader.Services.MMG;
using StockPulse.Loader.Stockpulse.ViewModel;
using StockPulse.Repository.Database;
using StockPulse.Model;
using StockPulse.Model.Import;
using StockPulse.Loader.ViewModel;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;

namespace StockPulse.Loader.Services
{


    public class MMGStockOnLoanLoaderService : GenericLoaderJobServiceParams
    {

        //constructor
        public MMGStockOnLoanLoaderService()
        {

        }


        public JobParams GetMatchingFilesAndImportParams(string incomingRoot)
        {
            string customer = "mmg";
            string filePattern = "*StockPulseStockOnLoans*.csv";

            JobParams parms = new JobParams()
            {
                jobType = LoaderJob.MMGStockOnLoan,
                customerFolder = "mmg",
                filename = filePattern,
                importSPName = null,
                loadingTableName = "ReconcilingItems",
                jobName = "MMGStockOnLoans",
                pulse = PulsesService.STK_MMGStockOnLoan,
                fileType = FileType.csv,
                regexPattern = "((?<=\")[^\"]*(?=\"(,|$)+)|(?<=,|^)[^,\"]*(?=,|$))",
                headerFailColumn = null,
                headerDefinitions = null,
                errorCount = 0,
                dealerGroupId = 11,
                allMatchingFiles = Directory.GetFiles(incomingRoot.Replace("{customer}", customer), filePattern),
                reconcilingItemTypeIdsToInclude = "94"
            };

            return parms;
        }


        public DataTable InterpretFile(JobParams parms, RowsAndHeadersResult rowsAndHeaders, LogMessage logMessage)
        {
            MMGSharedService mmgSharedService = new MMGSharedService(logMessage);
            List<Model.Input.ReconcilingItem> incomingLines = new List<Model.Input.ReconcilingItem>();// ExtractIncoming(parms, ref parms.errorCount, logMessage, allRows, headerLookup);
            
            int incomingProcessCount = 0;

            int total = rowsAndHeaders.rowsAndCells.Count;

            logMessage.FailNotes = "";

            incomingLines = new List<Model.Input.ReconcilingItem>(10000);  //preset the list size (slightly quicker than growing it each time)

            using (var db = new StockpulseContext())
            {
                IEnumerable<SiteDescriptionDictionary> siteDescriptionDictionary = db.SiteDescriptionDictionary.Where(x => x.DealerGroupId == parms.dealerGroupId).AsNoTracking().AsEnumerable();

                foreach (var rowCols in rowsAndHeaders.rowsAndCells)
                {
                    incomingProcessCount++;

                    System.Console.WriteLine($"Count {incomingProcessCount} / {total}");

                    try
                    {
                        string siteName = rowCols[0].ToString();

                        if (siteName == "Toyota Bristol South") { continue; }

                        SiteDescriptionDictionary siteDictionary = siteDescriptionDictionary.Where(x => x.Description == siteName && x.IsPrimarySiteId).FirstOrDefault();

                        // Unable to find Site
                        if(siteDictionary == null)
                        {
                            if (mmgSharedService.IsSiteSkippable(siteName))
                            {
                                continue;
                            }
                            else
                            {
                                logMessage.FailNotes = logMessage.FailNotes + $" failed on adding item, Item: {incomingProcessCount}. Site: {siteName} not found. \r\n";
                                parms.errorCount++;
                                continue;
                            }
                        }


                        // Found site in dictionary - get SiteId (will go to catch if not found)
                        int siteId = siteDictionary.SiteId;

                        Model.Input.ReconcilingItem incomingLine = new Model.Input.ReconcilingItem()
                        {
                            SiteId = siteId,
                            Reg = rowCols[2]?.ToString().Replace(" ", "") ?? "",
                            Description = rowCols[4].ToString(),
                            Comment = rowCols[7].ToString(),
                            Reference = rowCols[9].ToString(),
                            FileImportId = parms.fileImportId,
                            ReconcilingItemTypeId = 94,
                            SourceReportId = 1,
                            DealerGroupId = parms.dealerGroupId,
                            Vin = rowCols[3].ToString().Trim(),
                        };

                        incomingLines.Add(incomingLine);
                    }

                    catch (Exception err)
                    {
                        //if (parms.errorCount < 30) logMessage.FailNotes = logMessage.FailNotes + $" failed on adding item, Item: {incomingProcessCount} {err.ToString()}";
                        parms.errorCount++;
                        continue;
                    }
                }
            }

            DataTable result = incomingLines.ToDataTable();

            result.Columns.Remove("Sites");
            result.Columns.Remove("FileImport");
            result.Columns.Remove("ReconcilingItemType");
            result.Columns.Remove("DealerGroup");
            result.Columns.Remove("SourceReports");

            return result;
        }

        private string GetVinLastEightChars(string raw)
        {
            try
            {
                string vinRaw = raw.ToString().Trim();
                int length = vinRaw.Length;
                return vinRaw.Substring(length - 8);
            }
            catch
            {
                return null;
            }

        }


 

    }
}
