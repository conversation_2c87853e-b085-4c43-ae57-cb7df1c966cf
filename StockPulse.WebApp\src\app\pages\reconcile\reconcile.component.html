<nav class="page-specific-navbar">
  <singleSitePickerWithSearch></singleSitePickerWithSearch>

  <button class="btn btn-primary ms-3" id="downloadAllData" (click)="downloadWaterfall()">
    <img [src]="service.logo.provideExcelLogo()">
    <span class="ms-2">Download Reconciliation</span>
  </button>

  <!-- Download missing / unknowns reports -->
  <reportsExport></reportsExport>

  <button
    *ngIf="service.selectedRows && service.selectedRows?.length > 1 && selectionsService.stockCheck.statusId < 4 && service.chosenBar.isProblem"
    class="btn btn-success ms-3" (click)="maybeBulkResolve()">
    Bulk Resolve ({{ service.selectedRows.length }})
  </button>

  <statusAndBarChart [showStatusPicker]="true"></statusAndBarChart>
</nav>



<!-- Main Page -->
<!-- [ngClass]="{ 'hidden': !gridLoaded || !waterfallLoaded }" -->
<div class="content-new" >

  <instructionRow [message]="provideInstructionMessage()"></instructionRow>

        <div id="waterfallContainer">
          <div id="waterfall">
            
            <div id="plotArea">
              <div id="xAxis">
              </div>
              <!-- <div id="yAxis">
              </div> -->
              
              <!-- The loading, unknown and missing labels -->
              <div *ngIf="service.chosenBar"  class="barTypeLabel" [ngStyle]="{'left.px':unknownLabelLeft(), 'width.px':unknownLabelWidth()}"> Unknown </div>
              <div *ngIf="service.chosenBar"  class="barTypeLabel" [ngStyle]="{'left.px':waterfall ? waterfall.barWidth : 0, 'width.px':missingLabelWidth()}"> Missing </div>
              <div *ngIf="!service.chosenBar"  class="barsLoadingLabel" > Loading data... </div>
              

              <!-- The waterfall -->
              <div id="barArea">
                <div id="barAreaInsideArea"></div>
                <div 
                  class="bar animated zoomIn reveal"
                  *ngFor="let bar of waterfall?.bars; let i = index; trackBy: rowTrackByFunction"
                
                  (mouseover)="hoveredBar=bar"
                  triggers="mouseenter:mouseleave"
                  container="body"
                  popoverClass="solidBackground"
                  placement="auto"
                  (click)="selectBarOnClick(bar)"
                  [ngStyle]="{'height.px':bar.height === 0 ? 1 : bar.height,'bottom.px':bar.bottomEdge+2,'left.px':waterfall.barWidth * i+2,'width.px':waterfall.barWidth}"
                  [ngClass]="{'problem':bar.isProblem,'isFullHeight':bar.isFullHeight, 'blackLine': bar.height === 0, 'active': service.chosenBar === bar}">

                  <div class="caption" [ngClass]="{ 'bold': bar.isFullHeight }">
                    {{bar.vehicleCount|cph:'numberNoPrefix':0:false}}
                  </div>
                </div>
                <div class="label" *ngFor="let bar of waterfall?.bars; let i = index"
                  [ngStyle]="{'left.px':waterfall.barWidth * i,'width.px':waterfall.barWidth}"
                  [ngClass]="{ 'bold': bar.isFullHeight, 'active': service.chosenBar === bar }">
                  {{bar.description}}

                </div>
              </div>
            </div>

          </div>

          

        </div>


        <div id="gridHolder">
          <ag-grid-angular
            *ngIf="service.gridOptions.rowData && service.gridOptions.columnDefs" 
            class="ag-theme-balham" 
            [gridOptions]="service.gridOptions"
            
           
            [frameworkComponents]="frameworkComponents"
          >
          </ag-grid-angular>
          <button *ngIf="service.gridOptions.rowData && service.gridOptions.columnDefs"  class="btn floatTopRightOfGrid" (click)="downloadGrid()">
            <img style="width:2em;" [src]="service.logo.provideExcelLogo()">
          </button>
        </div>

      </div>




<ng-template #popoverBar>
  <div class="newLabel">
    {{hoveredBar.popoverLabel}}
  </div>

</ng-template>