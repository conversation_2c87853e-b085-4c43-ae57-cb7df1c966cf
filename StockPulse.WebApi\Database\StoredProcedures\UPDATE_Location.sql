﻿
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].[UPDATE_Location]
(
    @ScanId INT = NULL	,
	@UserId INT = null,
    @NewLocationId INT = NULL	
)
AS
BEGIN

SET NOCOUNT ON

Declare @StockCheckId int = null
set @StockCheckId = (select StockCheckId from Scans where Id = @ScanId)


IF (dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0 OR dbo.[CanSaveScans](@StockCheckId) = 0)
BEGIN 
	RETURN
END


UPDATE Scans SET LocationId = @NewLocationId WHERE Id = @ScanId

EXEC [dbo].[UPDATE_StockCheckLastUpdate] @StockCheckId, @UserId

END

GO

	


