.menuContainer {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    width: 100%;
    margin-top: 50px;
    color: var(--bodyColour);

    h1 {
        margin-bottom: 50px;
        margin-top: 0;
        font-weight: 400;
    }

    .menu {
        width: 60%;
        display: flex;

        .columnAndHeaderContainer {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 33.3%;

            .listHeader {
                margin-bottom: 10px;
                font-weight: 600;
            }

            .column {
                flex: 1;
                border-left: 1px solid var(--secondary);

                ul {
                    padding-left: 10px;
                    margin-bottom: 0;
                    line-height: 3em;
                    
                    li {
                        cursor: pointer;

                        &:hover {
                            color: var(--secondaryLighter);
                        }

                        &.disabled {
                            pointer-events: none;
                            opacity: 0.7;
                        }
                    }
                }
            }
        }
    }

    @media screen and (min-width: 1500px) {
        .menu {
            width: 50%;
        }
    }
    
    @media screen and (min-width: 1800px) {
        .menu {
            width: 40%;
        }
    }
}

ul,
li {
    list-style: none;
}