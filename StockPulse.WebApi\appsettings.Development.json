{

  "ConnectionStrings": {
    "DefaultConnection": "Server=tcp:cphidev.database.windows.net,1433; Initial Catalog=stockpulseDev; Persist Security Info=False; User ID=StockPulseApiUserDev; Password=****************; MultipleActiveResultSets=False; Encrypt=True; TrustServerCertificate=False; Connection Timeout=30;"
  },


  "BlobStorage": {
    "StorageAccount": "stockpulseimages",
    "StorageAccountKey": "****************************************************************************************",

    //Dev
    "FilePath": "*********************************************************/",
    "ReadOnlyKey": "sp=r&st=2021-12-17T17:27:52Z&se=2041-02-14T00:27:52Z&spr=https&sv=2020-08-04&sr=c&sig=HC3gt1Pj6Vbiph%2BmA4TeTjWlR7Toghvt%2BXV3CAiFym4%3D"

  },


  "WebApp": {
    "URL": "http://localhost:4200",
    "Env": "LOCAL", // LOCAL, DEV, TEST, PROD
    "Country":  "UK" //UK, US
  }


}
