
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].GET_ScanMatchItemsOtherSites
(
    @StockCheckId INT,
	@UserId INT
)
AS
BEGIN

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END

DECLARE @stockcheckDate Date = (SELECT CONVERT(date,Date) FROM Stockchecks where Id = @StockCheckId);
DECLARE @dealerGroupId int = 
(
SELECT div.DealerGroupId
FROM Stockchecks sc 
INNER JOIN Sites s on s.Id = sc.SiteId
INNER JOIN Divisions div on div.Id = s.DivisionId
WHERE sc.Id = @StockCheckId
);

DECLARE @IntraGroupMatchingRange int = (SELECT numberValue FROM GlobalParams WHERE DealerGroupId = @dealerGroupId AND Name = 'IntraGroupMatchingRange')
DECLARE @DateFrom date = DATEADD(DAY, -@IntraGroupMatchingRange, @stockcheckDate)
DECLARE @DateTo date = DATEADD(DAY, @IntraGroupMatchingRange, @stockcheckDate)

SELECT
s.Id as ItemId,
s.Reg,
s.Vin
FROM Scans s
INNER JOIN StockChecks sc on sc.Id = s.StockCheckId 
INNER JOIN Sites si on si.id = sc.SiteId
INNER JOIN Divisions div on div.id = si.DivisionId
LEFT JOIN StockItems alreadyMatchedStockItems on alreadyMatchedStockItems.Id = s.StockItemId
LEFT JOIN Stockchecks scStockItem on scStockItem.Id = alreadyMatchedStockItems.StockCheckId

WHERE sc.Id <> @StockCheckId
AND div.DealerGroupId = @dealerGroupId
AND (s.StockItemId IS NULL OR scStockItem.Id = @StockCheckId)
AND s.IsDuplicate = 0
AND
(
	(@IntraGroupMatchingRange > 0 AND CONVERT(date, sc.Date) BETWEEN @DateFrom AND @DateTo)
    OR
    (@IntraGroupMatchingRange = 0 AND CONVERT(date, sc.Date) = @stockcheckDate)
)

ORDER BY s.Id


END

GO

