﻿using Dapper;

using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Threading.Tasks;

namespace StockPulse.Loader
{
    public interface IDapper : IDisposable
    {
        DbConnection GetDbconnection(bool isUS = false);

        Task<DataTable> GetDataTableAsync(string query, DynamicParameters parms, CommandType commandType = CommandType.StoredProcedure, bool isUS = false);
        T Get<T>(string sp, DynamicParameters parms, CommandType commandType = CommandType.StoredProcedure, bool isUS = false);
        Task<T> GetAsync<T>(string sp, DynamicParameters parms, CommandType commandType = CommandType.StoredProcedure, bool isUS = false);

        Task<IEnumerable<T>> GetAllAsync<T>(string sp, DynamicParameters parms, CommandType commandType = CommandType.StoredProcedure, bool isUS = false);
        Task<int> ExecuteAsync(string sp, DynamicParameters parms, CommandType commandType = CommandType.StoredProcedure, bool isUS = false);
        T Insert<T>(string sp, DynamicParameters parms, CommandType commandType = CommandType.StoredProcedure, bool isUS = false);
        Task<T> InsertAsync<T>(string sp, DynamicParameters parms, IDbTransaction tran, IDbConnection db, CommandType commandType = CommandType.StoredProcedure);
        T Update<T>(string sp, DynamicParameters parms, CommandType commandType = CommandType.StoredProcedure, bool isUS = false);
        IEnumerable<T> GetAll<T>(string sp, DynamicParameters parms, CommandType commandType = CommandType.StoredProcedure, bool isUS = false);

        //Task<SearchResult> GetMultipleAsync(string sp, DynamicParameters parms, CommandType commandType = CommandType.StoredProcedure);
        Task<int> ExecuteWithConnectionAsync(string sp, DynamicParameters parms, CommandType commandType = CommandType.StoredProcedure, bool isUS = false);
    }
}
