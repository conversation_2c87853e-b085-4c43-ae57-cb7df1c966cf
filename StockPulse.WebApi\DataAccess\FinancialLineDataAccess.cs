﻿using Dapper;
using StockPulse.Model.Import;
using StockPulse.WebApi.Dapper;
using StockPulse.WebApi.Service;
using StockPulse.WebApi.ViewModel;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;

namespace StockPulse.WebApi.DataAccess
{
    public interface IFinancialLineDataAccess
    {
        Task<IEnumerable<FinancialLineVM>> GetFinancialLines(int stockcheckId, int userId);
        Task UpdateLine(int stockCheckId, int financialLineId, string description, string notes, decimal balance, int userId);
        Task DeleteLine(int financialLineId, int stockCheckId, int userId);
        Task PostFinancialLines(List<FinancialLineVM> financialLines, int stockCheckId, int userId, int fileImportId);
        Task DeleteAllLines(int stockCheckId, int userId);
    }

    public class FinancialLineDataAccess : IFinancialLineDataAccess
    {
        private readonly IDapper dapper;
        private readonly ISiteService siteService;
        private readonly IStockCheckDataAccess stockCheckDataAccess;

        public FinancialLineDataAccess(IDapper dapper, IStockCheckDataAccess stockCheckDataAccess, ISiteService siteService)
        {
            this.dapper = dapper;
            this.stockCheckDataAccess = stockCheckDataAccess;
            this.siteService = siteService;
        }

        public async Task<IEnumerable<FinancialLineVM>> GetFinancialLines(int stockcheckId, int userId)
        {
            var paramList = new DynamicParameters();
            paramList.Add("StockCheckId", stockcheckId);
            paramList.Add("UserId", userId);

            return await dapper.GetAllAsync<FinancialLineVM>("dbo.GET_FinancialLines",paramList, System.Data.CommandType.StoredProcedure);

        }


        public async Task PostFinancialLines(List<FinancialLineVM> financialLines, int stockCheckId, int userId, int fileImportId)
        {
            var paramList = new DynamicParameters();
            paramList.Add("StockCheckId", stockCheckId);
            paramList.Add("UserId", userId);

            DataTable dt = new DataTable();

            dt.Columns.Add("Code");
            dt.Columns.Add("AccountDescription");
            dt.Columns.Add("Notes");
            dt.Columns.Add("IsExplanation");
            dt.Columns.Add("Balance");
            dt.Columns.Add("StockCheckId");
            dt.Columns.Add("FileImportId");

            List<string> sitesWithoutActiveStockChecks = new List<string>();

            try
            {
                bool isMultiSite = financialLines.Any(x => x.site != null && x.site != string.Empty);

                // Items need to be assigned to the stockcheck for their respective sites
                if (isMultiSite)
                {
                    // Get a list of site names that user has access to & a list of all sites for dealergroup
                    IEnumerable<ViewModel.SiteVM> sitesForUser = await siteService.GetSites(userId);
                    IEnumerable<ViewModel.SiteVM> sitesForDealerGroup = await siteService.GetAllSites(userId);
                    IEnumerable<SiteDescriptionDictionary> sitesForDealerGroupFromDictionary = await siteService.GetAllSitesFromDictionary(userId);

                    List<string> siteNamesForUser = sitesForUser.Select(x => x.Description.ToUpper()).ToList();
                    List<string> siteNamesForDealerGroup = sitesForDealerGroup.Select(x => x.Description.ToUpper()).ToList();

                    IEnumerable<StockCheckVM> stockchecks = await stockCheckDataAccess.GetStockChecksOverview(userId, true);

                    foreach (var item in financialLines)
                    {

                        int stockCheckIdForSite;

                        if (item.site != null) { item.site = item.site.TrimEnd(' ', '\r'); };

                        if (item.site == null)
                        {
                            throw new Exception($"Error: Item with Account Code {item.accountCode} does not contain a site name.");
                        }
                        else if (!siteNamesForDealerGroup.Contains(item.site.ToUpper()) && !sitesForDealerGroupFromDictionary.Select(x => x.Description).Contains(item.site.ToUpper()))
                        {
                            throw new Exception($"Error: {item.site} is not a valid site.");
                        }
                        else if (!siteNamesForUser.Contains(item.site.ToUpper()) && (siteNamesForDealerGroup.Contains(item.site.ToUpper()) && sitesForDealerGroupFromDictionary.Select(x => x.Description).Contains(item.site.ToUpper())))
                        {
                            throw new Exception($"Error: User does not have access to {item.site}.");
                        }
                        else
                        {
                            int siteIdToUse;

                            var siteToUse = sitesForDealerGroup.FirstOrDefault(x => x.Description.ToUpper() == item.site.ToUpper());
                            if (siteToUse != null)
                            {
                                siteIdToUse = siteToUse.Id;
                            }
                            else
                            {
                                siteIdToUse = sitesForDealerGroupFromDictionary.FirstOrDefault(x => x.Description == item.site.ToUpper()).SiteId;
                            }

                            var stockCheckForSite = stockchecks.Where(x => x.SiteId == siteIdToUse).FirstOrDefault();

                            if (stockCheckForSite == null)
                            {
                                // Instead of throwing exception immediately, add to list of sites without stock checks
                                sitesWithoutActiveStockChecks.Add(item.site);
                                // Use 0 as a placeholder, we'll check this list before executing the stored procedure
                                stockCheckIdForSite = 0;
                            }
                            else
                            {
                                stockCheckIdForSite = stockCheckForSite.Id;
                            }
                        }

                        dt.Rows.Add(
                            item.accountCode,
                            item.description,
                            item.notes,
                            item.isReconcilingAdj,
                            item.balance,
                            stockCheckIdForSite,
                            fileImportId
                            );
                    }
                }
                else
                {

                    foreach (var item in financialLines)
                    {
                        dt.Rows.Add(
                            item.accountCode,
                            item.description,
                            item.notes,
                            item.isReconcilingAdj,
                            item.balance,
                            stockCheckId,
                            fileImportId
                            );
                    }
                }
            }
            catch (Exception e)
            {
                throw new Exception(e.Message);
            }

            if (sitesWithoutActiveStockChecks.Count > 0)
            {
                throw new Exception($"No active stock check for the following sites: {string.Join(", ", sitesWithoutActiveStockChecks.Distinct())}.");
            }

            paramList.Add("FinancialLine", dt, DbType.Object);

            await dapper.ExecuteAsync("dbo.INSERT_FinancialLine", paramList, System.Data.CommandType.StoredProcedure);
            
        }

        public async Task UpdateLine(int stockCheckId, int financialLineId, string description, string notes, decimal balance, int userId)
        {
            var paramList = new DynamicParameters();
            paramList.Add("StockCheckId", stockCheckId);
            paramList.Add("FinancialLineId", financialLineId);
            paramList.Add("Description", description);
            paramList.Add("Notes", notes);
            paramList.Add("Balance", balance);
            paramList.Add("UserId", userId);

            await dapper.ExecuteAsync("dbo.UPDATE_FinancialLine", paramList, System.Data.CommandType.StoredProcedure);
            
        }

        public async Task DeleteLine(int financialLineId, int stockCheckId, int userId)
        {
            var paramList = new DynamicParameters();
            paramList.Add("FinancialLineId", financialLineId);
            paramList.Add("UserId", userId);
            paramList.Add("StockCheckid", stockCheckId);

            await dapper.ExecuteAsync("dbo.DELETE_FinancialLine", paramList, System.Data.CommandType.StoredProcedure); // to do
        }

        public async Task DeleteAllLines(int stockCheckId, int userId)
        {
            var paramList = new DynamicParameters();
            paramList.Add("UserId", userId);
            paramList.Add("StockCheckid", stockCheckId);

            await dapper.ExecuteAsync("dbo.DELETE_FinancialAllLines", paramList, System.Data.CommandType.StoredProcedure); // to do
        }
    }
}
