

-----------STEP 1 -------------------------
-----------------------------------------

BEGIN TRAN

;WITH Duplicates AS (
    SELECT
		Id,
        UserId,
        ScanDateTime,
        Reg,
        Vin,
        ROW_NUMBER() OVER (PARTITION BY UserId, ScanDateTime, Reg, Vin ORDER BY ScanDateTime) AS RowNum
    FROM 
        Scans
)


UPDATE Scans
SET ScanDateTime = DATEADD(SECOND, Duplicates.RowNum - 1, Scans.ScanDateTime)
FROM Scans
JOIN Duplicates 
ON Scans.Id = Duplicates.Id
WHERE Duplicates.RowNum > 1;

--COMMIT








