.d-inline-block.dropdown {
    width: 100%;
}

.dropdown {
    input {
        margin: 1em;
        background-color: transparent;
        border: 1px solid #FFFFFF;
        color: #FFFFFF;
        width: calc(100% - 2em);

        &::placeholder {
            color: #FFFFFF;
        }
    }

    .dropdown-item {
        padding: 0.25rem 1em;
    }
}

table {
    width: 100%;

    td {
        padding: 0.3em 0;
    }

    td:nth-of-type(2) {
        text-align: right;
        color: red;

        fa-icon {
            cursor: pointer;
        }
    }

    input {
        width: 50%;
        padding: 0.5em 1em;
    }
}

#inputAndSaveContainer {
    display: flex;

    button {
        background-color: var(--success);
        color: #FFFFFF;
        border: none;
        padding: 0.5em 1em;
    }
}