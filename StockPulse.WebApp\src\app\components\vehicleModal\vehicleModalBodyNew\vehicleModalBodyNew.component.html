<div [ngClass]="{'compact':showCompact}" class="d-flex justify-content-between">


  <!-- ############################################### -->
  <!-- Left card - DMS details -->
  <!-- ############################################### -->
  <div class="cph-card vehicle-modal-card left">
    <div class="cph-card-header">
      <span class="cph-card-header-title">
        <span *ngIf="item.stockItem?.state == states.Duplicate || item.scan?.scanState == states.Duplicate; else generic">
          Duplicate Scan Details
        </span>
        <ng-template #generic>
          <span *ngIf="item.scan && !item.stockItem">Resolution Details</span>
          <span *ngIf="item.stockItem">DMS Details</span>
        </ng-template>
      </span>
    </div>
    <div class="cph-card-body">
      <ng-container *ngIf="item.stockItem">
        <table [ngClass]="{'compact':showCompact}" id="dms-details-table">
          <tbody>
            <tr>
              <td>Id</td>
              <td>{{ item.stockItem?.stockItemId }}</td>
            </tr>
            <tr class="topBorder">
              <td>Description</td>
              <td>{{ item.stockItem?.description }}</td>
            </tr>
            <tr *ngIf="shouldShowReg()" class="topBorder">
              <td>Registration </td>
              <td>
                <div class="regPlate" [ngClass]="{ 'strikeThrough': !item.stockItem?.reg }">
                  <span *ngIf="item.stockItem?.reg">{{ item.stockItem.reg | cph:'numberPlate':0 }}</span>
                </div>
              </td>
            </tr>
            <tr class="topBorder">
              <td>VIN</td>
              <td>
                <div class="chassis" [ngClass]="{ 'strikeThrough': !item.stockItem?.vin }">
                  <span *ngIf="item.stockItem?.vin">{{ item.stockItem?.vin | cph:'chassis':0 }}</span>
                  <span *ngIf="!item.stockItem?.vin" style="opacity: 0;">00000000</span>
                </div>
              </td>
            </tr>
            <tr class="topBorder">
              <td>Stock Site</td>
              <td>{{ item.stockItem?.site ? item.stockItem?.site : item.otherSiteName }}</td>
            </tr>
            <tr class="topBorder">
              <td>Branch</td>
              <td>{{ item.stockItem?.branch }}</td>
            </tr>
            <tr class="topBorder">
              <td>Stock Type</td>
              <td>{{ item.stockItem?.stockType }}</td>
            </tr>
            <tr class="topBorder">
              <td>Notes</td>
              <td>{{ item.stockItem?.comment }}</td>
            </tr>
            <tr class="topBorder">
              <td>Reference</td>
              <td>{{ item.stockItem?.reference }}</td>
            </tr>
            <tr class="topBorder">
              <td>Days In Stock</td>
              <td>{{ item.stockItem?.dis }} ({{ item.stockItem?.groupDIS }})</td>
            </tr>
            <tr class="topBorder">
              <td>Stock Value</td>
              <td>{{ item.stockItem?.stockValue | cph:'currency':2 }}</td>
            </tr>
            <tr *ngIf="constants.currencySymbol == '$'" class="topBorder">
              <td>Flooring Balance</td>
              <td>{{ item.stockItem?.flooring | cph:'currency':2 }}</td>
            </tr>
          </tbody>
        </table>
      </ng-container>

      <app-explanation *ngIf="!item.stockItem" [item]="item" [showCompact]="showCompact"></app-explanation>
    </div>
  </div>

  <!-- ############################################### -->
  <!-- Right card - Scan details -->
  <!-- ############################################### -->
  <div class="cph-card vehicle-modal-card right">
    <div class="cph-card-header">
      <span class="cph-card-header-title">
        <span *ngIf="item.scan">Scan Details</span>
        <span *ngIf="!item.scan">Resolution Details</span>
      </span>
    </div>
    <div class="cph-card-body">

      <!-- ############################################### -->
      <!-- The scan and details area-->
      <!-- ############################################### -->
      <div *ngIf="item.scan" class="card-row">




        <!-- ############################################### -->
        <!-- The scan -->
        <!-- ############################################### -->
        <div id="regAndVinScan">
        <div id="regScanAndInput">
          <!-- REG STUFF -->
          <div id="regImage" class="subtleBoxShadow">
            <img [src]="item.scan?.regImageLargeUrl" class="popoverImage"
              (click)="amPreviewingVin = false; modalImage = item.scan?.regImageLargeUrl">
          </div>

          <!-- Reg box -->
          <div *ngIf="shouldShowReg()" class="plateContainer reg">
            <span *ngIf="item.isRegEditedOnDevice || item.isRegEditedOnWeb && !service.amEditingReg" class="scanEditedInfo" [ngbPopover]="popContent" triggers="mouseenter:mouseleave"
              (mouseenter)="hoveringVinInfo = false">
              <fa-icon [icon]="icon.faCircleInfo"></fa-icon>
            </span>

            <!-- If not editing reg -->
            <div *ngIf="!service.amEditingReg" class="plateBox regBox"
              [ngClass]="{ 'wide': selections.stockCheck?.statusId <= 3, 'strikeThrough': !item.scan?.scanReg }">
              <span *ngIf="item.scan?.scanReg">{{ item.scan?.scanReg | cph:'numberPlate':0 }}</span>
            </div>

            <button *ngIf="selections.stockCheck && selections.stockCheck?.statusId <= 3 && !service.amEditingReg && !showCompact && !selections.userIsGeneralManager && !selections.userIsReadOnly"
              class="btn btn-primary btnEditPlate" id="editRegButton" (click)="editReg()">
              <fa-icon [icon]="icon.faPencil"></fa-icon>
            </button>

            <!-- If editing reg -->
            <ng-container *ngIf="service.amEditingReg">

              <div class="position-relative d-flex align-items-center">
                <input id="regEditBox" #newRegBox spellcheck="false" style="text-transform:uppercase"
                  (keydown.enter)="saveNewReg()" [(ngModel)]="newReg" maxlength="7" />

                <!-- Saving spinner -->
                <fa-icon class="spinnerIcon fa-spin" id="savingSpinner" *ngIf="amSavingReg" [icon]="icon.faCircleNotch">
                </fa-icon>
              </div>

              <!-- Save -->
              <button id="saveRegButton" class="btn btn-success btnEditPlate" (click)="saveNewReg()">
                <fa-icon class="saveInput" [icon]="icon.faSave"></fa-icon>
              </button>
              <!-- Cancel -->
              <button class="btn btn-danger btnEditPlate" id="cancelRegButton" (click)="cancelEditingReg()">
                <fa-icon class="cancelInput" [icon]="icon.faTimesCircle"></fa-icon>
              </button>


            </ng-container>
          </div>
        </div>
        </div>


        <!-- ############################################### -->
        <!-- The scan Details table and vin image-->
        <!-- ############################################### -->
        <div id="scanDetails">
          <table [ngClass]="{'compact':showCompact}" id="scan-table">
            <tbody>
              <tr>
                <td>Scan Id</td>
                <td>
                  <span>{{ item.scan?.scanId }}</span>
                  <!-- <span *ngIf="!!stockItem?.scanId">{{ stockItem?.scanId }}</span> -->
                </td>
              </tr>
              <tr class="topBorder">
                <td>Scanned at site</td>
                <td>{{item.otherSiteName}}</td>
              </tr>
              <tr class="topBorder">
                <td>Scan Location</td>
                <td>
                  <div ngbDropdown container="body" placement="bottom-right" class="d-inline-block">
                    <button class="btn btn-primary" ngbDropdownToggle
                      [disabled]="!selections.stockCheck || selections.stockCheck?.statusId > 3 || showCompact || selections.userIsGeneralManager || selections.userIsReadOnly">
                        {{ item.scan?.locationDescription }}
                    </button>
                    <div ngbDropdownMenu class="dropdown-menu-left" aria-labelledby="dropdownBasic1">
                        <button *ngFor="let location of constants.Locations" [ngClass]="{ 'active': location.description === item.scan?.locationDescription }"
                          ngbDropdownItem (click)="saveNewLocation(location)">
                            {{ location.description }}
                        </button>
                    </div>
                </div>
                  <!-- <div *ngIf="stockItem" class="locationChip">
                        {{ item.stockItem?.locationDescription }}
                      </div> -->
                </td>
              </tr>
              <tr class="topBorder">
                <td>Scanned by</td>
                <td>
                  <span>{{ item.scan?.scannerName }}</span>
                  <!-- <span *ngIf="!!stockItem?.scanId">{{ item.stockItem?.scannedBy }}</span> -->
                </td>
              </tr>
              <tr class="topBorder">
                <td>Scanned day</td>
                <td>
                  <span>{{ item.scan?.scanDateTime | cph:'day':0 }}</span>
                  <!-- <span *ngIf="!!stockItem?.scanId">{{ item.stockItem?.scanDateTime |cph:'day':0 }}</span> -->
                </td>
              </tr>
              <tr class="topBorder">
                <td>Scanned time</td>
                <td>
                  <span>{{ item.scan?.scanDateTime | cph:'time':0 }}</span>
                  <!-- <span *ngIf="!!stockItem?.scanId">{{ item.stockItem?.scanDateTime | cph:'time':0 }}</span> -->
                </td>
              </tr>
              <tr *ngIf="constants.currency !== 'USD'" class="topBorder">
                <td>Reg Confidence</td>
                <td>
                  <div class="confidence">
                    {{ item.scan && item.scan.regConfidence ? (item.scan?.regConfidence | cph:'regConfidence':0) : 'N/A' }}
                  </div>
                </td>
              </tr>
              <tr class="topBorder">
                <td>VIN Confidence</td>
                <td>
                  <div class="confidence">
                     {{ item.scan && item.scan.vinConfidence ? (item.scan?.vinConfidence | cph:'vinConfidence':0) : 'N/A' }}
                  </div>
                </td>
              </tr>
              <tr class="topBorder">
                <td>Notes</td>
                <td>
                  <div class="comment">
                    <span>{{ item.scan?.scanComment }}</span>
                    <!-- <span *ngIf="!!stockItem?.scanId">{{ item.stockItem?.scanComment }}</span> -->
                  </div>
                </td>
              </tr>
              <tr class="topBorder">
                <td>Latitude / Longitude</td>
                <td>
                  <div class="comment">
                    <span *ngIf="item.scan?.latitude">{{ item.scan?.latitude }}, {{ item.scan?.longitude }}</span>
                    <span *ngIf="!item.scan?.latitude">Unknown</span>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <div *ngIf="item.scan" class="card-row">




        <!-- ############################################### -->
        <!-- The scan -->
        <!-- ############################################### -->
        <div id="regAndVinScan">

        <div id="vinScanAndInput">
          <!-- VIN image -->
          <div id="vinImage">
            <div *ngIf="!item.scan?.vinImageUrl">No VIN scan</div>
            <img *ngIf="item.scan?.vinImageUrl" [src]="item.scan?.vinImageUrl" class="popoverImage"
              (click)="amPreviewingVin = true; modalImage = item.scan?.vinImageUrl">
            <!-- <img *ngIf="stockItem?.vinImageUrl" [src]="item.stockItem?.vinImageUrl" class="popoverImage"
                  (click)="zoomIn(item.stockItem?.vinImageUrl)"> -->
          </div>


          <!-- Vin box -->
          <div class="plateContainer">

            <span *ngIf="item.isVinEditedOnDevice || item.isVinEditedOnWeb && !service.amEditingReg" class="scanEditedInfo" [ngbPopover]="popContent" triggers="mouseenter:mouseleave"
              (mouseenter)="hoveringVinInfo = true">
              <fa-icon [icon]="icon.faCircleInfo"></fa-icon>
            </span>

            <!-- If not editing vin -->
            <div *ngIf="!service.amEditingVin" class="plateBox vinBox"
              [ngClass]="{ 'wide': selections.stockCheck?.statusId <= 3, 'strikeThrough': !item.scan?.scanVin }">
              <span *ngIf="item.scan?.scanVin">{{ item.scan?.scanVin | cph:'numberPlate':0 }}</span>
            </div>

            <button *ngIf="selections.stockCheck && selections.stockCheck?.statusId <= 3 && !service.amEditingVin && !showCompact && !selections.userIsGeneralManager && !selections.userIsReadOnly"
              class="btn btn-primary btnEditPlate" id="editVinButton" (click)="editVin()">
              <fa-icon [icon]="icon.faPencil"></fa-icon>
            </button>

            <!-- If editing vin -->
            <div *ngIf="service.amEditingVin">
              <span *ngIf="newVin && newVin.length < 8" class="text-danger">* VIN must be 8 characters</span>

              <div class="d-flex">
                <div class="position-relative d-flex align-items-center">
                  <input id="vinEditBox" #newVinBox spellcheck="false" (keydown.enter)="saveNewVin()"
                    [(ngModel)]="newVin" maxlength="8" />

                  <!-- Saving spinner -->
                  <fa-icon class="spinnerIcon fa-spin" id="savingSpinnerVin" *ngIf="amSavingVin"
                    [icon]="icon.faCircleNotch">
                  </fa-icon>
                </div>

                <!-- Save -->
                <button id="saveVinButton" class="btn btn-success btnEditPlate" (click)="saveNewVin()">
                  <fa-icon class="saveInput" [icon]="icon.faSave"></fa-icon>
                </button>
                <!-- Cancel -->
                <button class="btn btn-danger btnEditPlate" id="cancelVinButton" (click)="cancelEditingVin()">
                  <fa-icon class="cancelInput" [icon]="icon.faTimesCircle"></fa-icon>
                </button>
              </div>

            </div>


          </div>
        </div>
        </div>


        <!-- ############################################### -->
        <!-- The scan Details table and vin image-->
        <!-- ############################################### -->
        <div id="scanDetails">

          <!-- ############################################### -->
          <!-- The map -->
          <!-- ############################################### -->
          <div *ngIf="!showCompact && item.scan" class="card-row">
            <div id="scanMap" class="subtleBoxShadow" [ngClass]="{ 'noMap': item.scan.latitude === 0 && item.scan.longitude === 0 }">
              <div *ngIf="item.scan.latitude === 0 && item.scan.longitude === 0" class="p-2">
                GPS coordinates not available
              </div>
              <google-map *ngIf="item.scan.latitude !== 0 && item.scan.longitude !== 0" width="100%" height="100%" [options]="mapOptions">
                <map-marker [position]="{ lat: item.scan.latitude, lng: item.scan.longitude }"></map-marker>
              </google-map>
            </div>
          </div>
        </div>
      </div>

      <app-explanation *ngIf="!item.scan" [item]="item" [showCompact]="showCompact"></app-explanation>
    </div>
  </div>

  <!-- Change location modal -->
  <ng-template #changeLocationModal let-modal>
    <div class="modal-header">
      <h4 class="modal-title" id="modal-basic-title">
        Update Location
      </h4>
      <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    <div class="modal-body newLocationChoice">
      <button *ngFor="let location of constants.Locations" class="btn btn-primary"
        [ngClass]="{ 'active': location.description === chosenLocation }" (click)="chooseLocation(location) ">
        {{ location.description }}
      </button>
    </div>
    <div class="modal-footer">
      <button type="button" class="btn btn-primary" (click)="modal.close('Ok')">OK</button>
      <button type="button" class="btn btn-primary" (click)="modal.dismiss('Cancelled')">Cancel</button>
    </div>
  </ng-template>

  <!-- Enlarged image -->
  <div *ngIf="modalImage" class="enlarged-image-overlay">
    <div class="image-backdrop"
      [ngStyle]="{ 'background-image': 'linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), url(' + modalImage + ')' }"
      (click)="modalImage = null">
    </div>
    <div class="image-container" (click)="maybeCloseImage($event)">
      <button class="image-close" (click)="modalImage = null">&times;</button>
      <img [src]="modalImage" alt="Stock check scan" [ngClass]="{ 'isVin': amPreviewingVin }">

      <div *ngIf="amPreviewingVin" id="vinEditorInPreview">
        <div *ngIf="!service.amEditingVin" class="nonEditMode">
          <div class="plateBox vinBox"
            [ngClass]="{ 'wide': selections.stockCheck?.statusId <= 3 }">
            <span *ngIf="item.scan">{{ item.scan?.scanVin | cph:'numberPlate':0 }}</span>
          </div>
          
          <button *ngIf="selections.stockCheck && selections.stockCheck?.statusId <= 3 && !service.amEditingVin && !showCompact && !selections.userIsGeneralManager && !selections.userIsReadOnly"
            class="btn btn-primary btnEditPlate" id="editVinButton" (click)="editVin()">
            <fa-icon [icon]="icon.faPencil"></fa-icon>  
          </button>
        </div>

        <div *ngIf="service.amEditingVin" class="w-100">
          <span *ngIf="newVin && newVin.length < 8" class="text-danger">* VIN must be 8 characters</span>

          <div class="d-flex w-100 justify-content-center">
            <div class="position-relative d-flex align-items-center">
              <input id="vinEditBox" #newVinBox spellcheck="false" (keydown.enter)="saveNewVin()"
                [(ngModel)]="newVin" maxlength="8" />

              <!-- Saving spinner -->
              <fa-icon class="spinnerIcon fa-spin" id="savingSpinnerVin" *ngIf="amSavingVin"
                [icon]="icon.faCircleNotch">
              </fa-icon>
            </div>

            <!-- Save -->
            <button id="saveVinButton" class="btn btn-success btnEditPlate" (click)="saveNewVin()">
              <fa-icon class="saveInput" [icon]="icon.faSave"></fa-icon>
            </button>
            <!-- Cancel -->
            <button class="btn btn-danger btnEditPlate" id="cancelVinButton" (click)="cancelEditingVin()">
              <fa-icon class="cancelInput" [icon]="icon.faTimesCircle"></fa-icon>
            </button>
          </div>

        </div>
      </div>
    </div>
  </div>

  <ng-template #popContent>
    <span *ngIf="hoveringVinInfo" class="text-nowrap">
      VIN has been edited on <span>{{ item.isVinEditedOnDevice ? 'mobile' : 'web' }}</span> app<br>
      Original interpretation: {{ item.interpretedVin ? item.interpretedVin : 'None' }}
    </span>
    <span *ngIf="!hoveringVinInfo" class="text-nowrap">
      Reg has been edited on <span>{{ item.isVinEditedOnDevice ? 'mobile' : 'web' }}</span> app<br>
      Original interpretation: {{ item.interpretedReg ? item.interpretedReg : 'None' }}
    </span>
  </ng-template>