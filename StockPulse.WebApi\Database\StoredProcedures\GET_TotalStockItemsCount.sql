﻿
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO




CREATE OR ALTER PROCEDURE [dbo].[GET_TotalStockItemsCount]
(
    @StockCheckIds varchar(max),
    @UserId INT
)
AS
BEGIN

SET NOCOUNT ON;


IF (dbo.[AuthenticateUserMultiStockChecks](@UserId, @StockCheckIds) = 0)
BEGIN 
    RETURN
END


SELECT COUNT(1) FROM dbo.StockItems
WHERE StockCheckId IN (SELECT * FROM STRING_SPLIT(@StockCheckIds,','))

	
END

GO


