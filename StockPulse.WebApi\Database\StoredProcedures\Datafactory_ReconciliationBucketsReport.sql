﻿
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
  
CREATE OR ALTER   PROC [dbo].[Datafactory_ReconciliationBucketsReport]  
@StockCheckId int  
AS  
BEGIN  
  
SET NOCOUNT ON  
  
DECLARE @UserId INT  
DECLARE @DMSStock INT  
DECLARE @OtherStocks INT  
DECLARE @StocksMatched INT  
DECLARE @OtherScanned INT  
DECLARE @TotalScanned INT  
DECLARE @WaterfallMatched bit  
  
--Get a user for this stockstock  
SET @UserId = (select top 1 U.Id from StockChecks sc inner join Sites as s on sc.SiteId = s.Id  
inner join Divisions as d on s.DivisionId = d.Id inner join Users as U on d.DealerGroupId = u.DealerGroupId  
where sc.IsActive = 1  
and sc.id = @StockCheckId )  
  
  
  
CREATE TABLE #ReconciliationBucketsData(  
Description nvarchar (250) not null  
,VehicleCount int not null
,InStockValue int null
,IsFullHeight int not null  
,[Order] int not null  
,Is<PERSON><PERSON>ble<PERSON> int not null  
,<PERSON>S<PERSON> int not null  
,<PERSON><PERSON>can int not null  
,ReconciliationTypeId int null  
,ReconciliationState nvarchar (250) not null  
)   
   
   
--set identity_insert #ReconciliationBucketsData on  
insert into #ReconciliationBucketsData  
  (  
Description  
,VehicleCount
,InStockValue
,IsFullHeight   
,[Order]   
,IsProblem  
,IsStock  
,IsScan  
,ReconciliationtypeId
,ReconciliationState
  )  
exec [dbo].[GET_ReconciliationBuckets] @StockCheckId,@UserId  
--select * from #ReconciliationBucketsData  
  
SET @DMSStock = (SELECT ISNULL(VehicleCount,0) from #ReconciliationBucketsData WHERE Description = 'Stock')  
SET @OtherStocks = (SELECT SUM(ISNULL(VehicleCount,0)) from #ReconciliationBucketsData WHERE Description NOT IN ('Stock', 'In Stock and Scanned') AND IsStock = 1)  
SET @StocksMatched = (SELECT ISNULL(VehicleCount,0) from #ReconciliationBucketsData WHERE Description = 'In Stock and Scanned')  
SET @OtherScanned = (SELECT SUM(ISNULL(VehicleCount,0)) from #ReconciliationBucketsData WHERE Description NOT IN ('Scanned') AND IsStock = 0)  
SET @TotalScanned = (SELECT ISNULL(VehicleCount,0) from #ReconciliationBucketsData WHERE Description = 'Scanned')  
  
SET @DMSStock = ISNULL(@DMSStock,0)  
SET @OtherStocks = ISNULL(@OtherStocks, 0)  
SET @StocksMatched = ISNULL(@StocksMatched,0)  
SET @OtherScanned = ISNULL(@OtherScanned,0)  
SET @TotalScanned = ISNULL(@TotalScanned,0)  
  
  
  
SET @WaterfallMatched = 0  
  
IF ((@DMSStock - @OtherStocks) = @StocksMatched) AND ((@StocksMatched + @OtherScanned) = @TotalScanned)  
BEGIN   
 SET @WaterfallMatched = 1  
END  
   
  
  
  
insert into ReconciliationBucketsReport(  
 StockCheckId   
,DMSStock   
,OtherStocks   
,StocksMatched   
,OtherScanned  
,TotalScanned  
,WaterfallMatched  
,ReportDate   
)  
select @StockCheckId, @DMSStock, @OtherStocks, @StocksMatched, @OtherScanned, @TotalScanned, @WaterfallMatched, Convert(date,GetDATE())  
  
--select * from #ReconciliationBucketsReport  
DROP TABLE #ReconciliationBucketsData  
END  
  

