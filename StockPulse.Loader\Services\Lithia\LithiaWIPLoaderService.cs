﻿using Microsoft.EntityFrameworkCore;
using StockPulse.Loader.Stockpulse.ViewModel;
using StockPulse.Loader.ViewModel;
using StockPulse.Model;
using StockPulse.Model.Import;
using StockPulse.Repository.Database;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;

namespace StockPulse.Loader.Services
{


    public class LithiaWIPLoaderService : GenericLoaderJobServiceParams
    {

        //constructor
        public LithiaWIPLoaderService()
        {

        }


        public JobParams GetMatchingFilesAndImportParams(string incomingRoot)
        {
            string customer = "lithia";
            string filePattern = "*Service_OpenROs*.csv";

            JobParams parms = new JobParams()
            {
                jobType = LoaderJob.LithiaWIP,
                customerFolder = customer,
                filename = filePattern,
                importSPName = null,
                loadingTableName = "ReconcilingItems",
                jobName = "LithiaWIP",
                pulse = PulsesService.STK_LithiaWIP,
                fileType = FileType.csv,
                regexPattern = "((?<=\")[^\"]*(?=\"(,|$)+)|(?<=,|^)[^,\"]*(?=,|$))",
                headerFailColumn = null,
                headerDefinitions = null,
                errorCount = 0,
                dealerGroupId = 10,
                allMatchingFiles = Directory.GetFiles(incomingRoot.Replace("{customer}", customer), filePattern),
                reconcilingItemTypeIdsToInclude = "101",
                isUS = true
            };

            return parms;
        }


        public DataTable InterpretFile(JobParams parms, RowsAndHeadersResult rowsAndHeaders, LogMessage logMessage)
        {

            List<Model.Input.ReconcilingItem> incomingLines = new List<Model.Input.ReconcilingItem>();// ExtractIncoming(parms, ref parms.errorCount, logMessage, allRows, headerLookup);
            int incomingProcessCount = 0;
            incomingLines = new List<Model.Input.ReconcilingItem>(10000);  //preset the list size (slightly quicker than growing it each time)

            Dictionary<string, int> missingSitesDictionary = new Dictionary<string, int>();

            using (var db = new StockpulseContext(true))
            {
                IEnumerable<SiteDescriptionDictionary> siteDescriptionDictionary = 
                                                            db.SiteDescriptionDictionary.Where(x => x.DealerGroupId == parms.dealerGroupId).AsNoTracking().AsEnumerable();

                // For this loader we want both primary and secondary sites
                var siteDictionaryLookup = siteDescriptionDictionary.ToLookup(x => x.Description);

                int total = rowsAndHeaders.rowsAndCells.Count;

                foreach (var rowCols in rowsAndHeaders.rowsAndCells)
                {
                    incomingProcessCount++;

                    System.Console.WriteLine($"Count {incomingProcessCount} / {total}");

                   // if (incomingProcessCount > 500) { continue; }

                    try
                    {
                        string siteName = rowCols[1].Trim().ToString();

                        if(SharedLoaderService.SkipSiteForLithiaUS(siteName))
                        {
                            continue;
                        }

                        // Now use the lookup for fast lookups
                        var siteDictionaries = siteDictionaryLookup[siteName].ToList();

                        if (siteDictionaries == null)
                        {
                           parms.errorCount++;

                           if (siteName == "" || siteName == null)
                           {
                              siteName = "[BLANK NAME]";
                           }

                           if (!missingSitesDictionary.ContainsKey(siteName))
                           {
                              missingSitesDictionary[siteName] = 1;
                           }
                           else
                           {
                              missingSitesDictionary[siteName] += 1;
                           }

                           continue;
                        }

                        // Loop over the relevant sites
                        foreach(var site in siteDictionaries)
                        {
                            // Found site in dictionary - get SiteId (will go to catch if not found)
                            int siteId = site.SiteId;

                            Model.Input.ReconcilingItem incomingLine = new Model.Input.ReconcilingItem()
                            {
                                SiteId = siteId,
                                Reg = null, //
                                Description = rowCols[9].ToString(),
                                Comment = rowCols[3].ToString() + " " + rowCols[4].ToString(),
                                Reference = rowCols[2].ToString(), //
                                FileImportId = parms.fileImportId,
                                ReconcilingItemTypeId = 101,
                                SourceReportId = 1,
                                Vin = rowCols[5].ToString(), //
                                DealerGroupId = parms.dealerGroupId
                            };

                            incomingLines.Add(incomingLine);
                        }

                        // Limit for now
                        //if(incomingProcessCount == 6000) { return incomingLines;  }

                    }

                    catch (Exception err)
                    {
                        logMessage.FailNotes = logMessage.FailNotes + $" failed on adding item, Item: {incomingProcessCount} {err.ToString()} <br>";
                        parms.errorCount++;
                        continue;
                    }
                }
            }

            missingSitesDictionary = missingSitesDictionary
             .OrderBy(kvp => kvp.Key) // Sort by siteName
             .ToDictionary(kvp => kvp.Key, kvp => kvp.Value);

            foreach (var item in missingSitesDictionary)
            {
               logMessage.FailNotes += $"Unable to find site: {item.Key} ({item.Value}) <br>";
            }

            DataTable result = incomingLines.ToDataTable();
            result.Columns.Remove("Sites");
            result.Columns.Remove("FileImport");
            result.Columns.Remove("DealerGroup");
            result.Columns.Remove("ReconcilingItemType");
            result.Columns.Remove("SourceReports");
            return result;
        }


 

    }
}
