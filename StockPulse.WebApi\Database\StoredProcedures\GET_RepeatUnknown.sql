﻿/****** Object:  StoredProcedure [dbo].[GET_RepeatUnknown]    Script Date: 22/3/21 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].GET_RepeatUnknown
(
    @StockCheckId INT = NULL,
    @UserId INT = NULL
)
AS
BEGIN

SET NOCOUNT ON;

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END


SELECT [Id]
        ,[ScanId]
        ,[ReconcilingItemId]
        ,[MissingResolutionId]
        ,[StockCheckId]
        ,[SourceReportId]
        ,[Reg]
        ,[Vin]
        ,[Description]
        ,[DIS]
        ,[GroupDIS]
        ,[Branch]
        ,[Comment]
        ,[StockType]
        ,[Reference]
        ,[StockValue]
FROM [dbo].[StockItems] 
WHERE StockCheckId = @StockCheckId 	


END

GO




--To use this run 
--exec [GET_RepeatUnknown] @StockCheckId = 1,  @UserId = 1