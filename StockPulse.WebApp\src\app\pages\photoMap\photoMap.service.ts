import { Injectable } from '@angular/core';
import { PhotoMapItemGroup } from 'src/app/model/PhotoMapItemGroup';
import { PhotoMapReportType } from 'src/app/model/PhotoMapReportType';
import { ApiAccessService } from 'src/app/services/apiAccess.service';
import { GetDataService } from 'src/app/services/getData.service';
import { ToastService } from 'src/app/services/newToast.service';
import { ConstantsService } from '../../services/constants.service';
import { SelectionsService } from '../../services/selections.service';

@Injectable({
  providedIn: 'root'
})

export class PhotoMapService {


  showPhotosOnEachItem: boolean = false;
  itemGroupings: PhotoMapItemGroup[];
  itemGroupingsMaster: PhotoMapItemGroup[];

  reportTypes = PhotoMapReportType;
  chosenReportType: PhotoMapReportType;
  chosenScanner: any;
  scansForExecs: any[];
  totalScanCount: number;

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public api: ApiAccessService,
    public getDataService: GetDataService,
    public toastService: ToastService
  ) { }

  initParams() {
    this.chosenScanner = null;
    if (!this.chosenReportType) {
      this.chosenReportType = this.reportTypes.allScans
      }
     this.getData();

  }

  getData() {
    this.getDataService.getPhotoMapItemGroups(this.selections.stockCheck.id, this.chosenReportType).subscribe((res: PhotoMapItemGroup[]) => {
      let scansForExecs: any[] = [];
      let totalScanCount: number = 0;

      //add pic strings
      res.map(group => {
        group.items.map(item => {
          if (item.isScan) {
            item.scanImageLarge = this.constants.makeLargeImageUrl(item.id)
            item.scanImageThumbnail = this.constants.makeThumbnailImageUrl(item.id)
          }

          if (scansForExecs.find(x => x.scannerName === item.scannerName)) {
            scansForExecs.find(x => x.scannerName === item.scannerName).scans += 1;
          } else {
            scansForExecs.push({
              scannerName: item.scannerName,
              scans: 1
            })
          }

          totalScanCount += 1;
        })
      })

      this.totalScanCount = totalScanCount;
      this.scansForExecs = scansForExecs.sort((a,b) => a.scannerName.localeCompare(b.scannerName));
      this.scansForExecs.push({
        scannerName: 'All Scanners',
        scans: totalScanCount
      })
      this.itemGroupingsMaster = res;
      this.itemGroupings = res;

      this.toastService.destroyToast();
    })
  }


  getReportLabel(report:string){
    if(this.reportTypes[report]  === this.reportTypes.allScans){return 'All Scans'}
    if(this.reportTypes[report]  === this.reportTypes.unknownsByLocation){return 'Unknown vehicles by location'}
    if(this.reportTypes[report]  === this.reportTypes.unknownsByResolutionType){return 'Unknown vehicles by resolution'}
    if(this.reportTypes[report]  === this.reportTypes.missingsByStockType){return 'Missings vehicles by stock type'}
    if(this.reportTypes[report]  === this.reportTypes.missingsByResolutionType){return 'Missing vehicles by resolution'}
    if(this.reportTypes[report]  === this.reportTypes.allScansByScanner){return 'All scans by scanner'}
    return 'else'

  }
 
  getChosenReportLabel() {
    let report: string = this.reportTypes[this.chosenReportType];
    return this.getReportLabel(report);
  }

  filterData() {
    let filteredData: PhotoMapItemGroup[] = structuredClone(this.itemGroupingsMaster);

    if (this.chosenScanner.scannerName === 'All Scanners') {
      this.itemGroupings = filteredData;
      return;
    }

    // Loop each group and remove any scans that aren't for selected user
    filteredData.map(group => {
      group.items.map(item => {
        if (item.scannerName !== this.chosenScanner.scannerName) {
          group.items = group.items.filter(x => x.id !== item.id);
        }
      })
    })

    // Then, loop again and check for any empty groups and remove
    filteredData.forEach(group => {
      if (group.items.length < 1) {
        filteredData = filteredData.filter(x => x.label !== group.label);
      }
    })

    this.itemGroupings = filteredData;
  }
}
