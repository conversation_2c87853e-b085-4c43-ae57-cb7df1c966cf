﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].UPDATE_SaveReconciliationResults
(
    @duplicatedScans MatchedItem readonly,
    @scansMatchedToStockItems MatchedItem readonly,
    @scansMatchedToOtherSiteStockItems MatchedItem readonly,
    @scansMatchedToRecItems MatchedItem readonly,
    @remainingUnmatchedScans MatchedItem readonly,
    
    @duplicatedStockItems MatchedItem readonly,
    @stockItemsMatchedToScans MatchedItem readonly,
    @stockItemsMatchedToOtherSiteScans MatchedItem readonly,
    @stockItemsMatchedToRecItems MatchedItem readonly,
	@remainingUnmatchedStockItems MatchedItem readonly,
	
    @stockCheckId INT,
	@userId int
)
AS
BEGIN

SET NOCOUNT ON;



--Only proceed if allowed
IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0 OR dbo.[CanAmmendStockCheck](@StockCheckId)  = 0
BEGIN 
    RETURN 
END


-------------------
--Scans
-------------------
--Update duplicatedScans
CREATE TABLE #duplicatedScans  ([ItemId] INT NOT NULL,[MatchItemId] INT NULL);	INSERT INTO #duplicatedScans SELECT ItemId, MatchItemId from @duplicatedScans
UPDATE Scans
SET Scans.IsDuplicate = IIF(#duplicatedScans.ItemId IS NOT NULL,1,0) --this will adjust all of them within the current stockcheck
FROM Scans LEFT JOIN #duplicatedScans on #duplicatedScans.ItemId = Scans.Id
WHERE Scans.StockCheckId = @stockCheckId
--also now release any scanIds they have against them
UPDATE Scans
SET StockItemId = NULL, UnknownResolutionId = NULL, ReconcilingItemId = NULL
FROM Scans INNER JOIN #duplicatedScans on #duplicatedScans.ItemId = Scans.Id
WHERE Scans.StockCheckId = @stockCheckId


--Update scansMatchedToStockItems
CREATE TABLE #scansMatchedToStockItems  ([ItemId] INT NOT NULL,[MatchItemId] INT NULL);	INSERT INTO #scansMatchedToStockItems SELECT ItemId, MatchItemId from @scansMatchedToStockItems
UPDATE Scans
SET Scans.StockItemId = #scansMatchedToStockItems.MatchItemId, Scans.ReconcilingItemId = NULL
FROM Scans INNER JOIN #scansMatchedToStockItems on #scansMatchedToStockItems.ItemId = Scans.Id
WHERE Scans.StockCheckId = @stockCheckId

--Update scansMatchedToOtherSiteStockItems
CREATE TABLE #scansMatchedToOtherSiteStockItems  ([ItemId] INT NOT NULL,[MatchItemId] INT NULL);	INSERT INTO #scansMatchedToOtherSiteStockItems SELECT ItemId, MatchItemId from @scansMatchedToOtherSiteStockItems
UPDATE Scans
SET Scans.StockItemId = #scansMatchedToOtherSiteStockItems.MatchItemId, Scans.ReconcilingItemId = NULL
FROM Scans INNER JOIN #scansMatchedToOtherSiteStockItems on #scansMatchedToOtherSiteStockItems.ItemId = Scans.Id
WHERE Scans.StockCheckId = @stockCheckId

--         --Update scansMatchedToRecItems
CREATE TABLE #scansMatchedToRecItems  ([ItemId] INT NOT NULL,[MatchItemId] INT NULL);	INSERT INTO #scansMatchedToRecItems SELECT ItemId, MatchItemId from @scansMatchedToRecItems
UPDATE Scans
SET Scans.ReconcilingItemId = #scansMatchedToRecItems.MatchItemId, Scans.StockItemId = NULL
FROM Scans INNER JOIN #scansMatchedToRecItems on #scansMatchedToRecItems.ItemId = Scans.Id
WHERE Scans.StockCheckId = @stockCheckId

--         --Update remainingUnmatchedScans
CREATE TABLE #remainingUnmatchedScans  ([ItemId] INT NOT NULL,[MatchItemId] INT NULL);	INSERT INTO #remainingUnmatchedScans SELECT ItemId, MatchItemId from @remainingUnmatchedScans
UPDATE Scans
SET Scans.ReconcilingItemId = NULL, Scans.StockItemId = NULL
FROM Scans INNER JOIN #remainingUnmatchedScans on #remainingUnmatchedScans.ItemId = Scans.Id
WHERE Scans.StockCheckId = @stockCheckId


-------------------
--StockItems
-------------------
--Update duplicatedStockItems
CREATE TABLE #duplicatedStockItems  ([ItemId] INT NOT NULL,[MatchItemId] INT NULL);	INSERT INTO #duplicatedStockItems SELECT ItemId, MatchItemId from @duplicatedStockItems
UPDATE StockItems
SET StockItems.IsDuplicate = IIF(#duplicatedStockItems.ItemId IS NOT NULL,1,0) -- will adjust them all
FROM StockItems LEFT JOIN #duplicatedStockItems on #duplicatedStockItems.ItemId = StockItems.Id
WHERE StockItems.StockCheckId = @stockCheckId
--also now release any scanIds they have against them
UPDATE StockItems
SET ScanId = NULL, MissingResolutionId = NULL, ReconcilingItemId = NULL
FROM StockItems INNER JOIN #duplicatedStockItems on #duplicatedStockItems.ItemId = StockItems.Id
WHERE StockItems.StockCheckId = @stockCheckId


--Update stockItemsMatchedToScans
CREATE TABLE #stockItemsMatchedToScans  ([ItemId] INT NOT NULL,[MatchItemId] INT NULL);	INSERT INTO #stockItemsMatchedToScans SELECT ItemId, MatchItemId from @stockItemsMatchedToScans
UPDATE StockItems
SET StockItems.ScanId = #stockItemsMatchedToScans.MatchItemId, StockItems.ReconcilingItemId = null
FROM StockItems INNER JOIN #stockItemsMatchedToScans on #stockItemsMatchedToScans.ItemId = StockItems.Id
WHERE StockItems.StockCheckId = @stockCheckId

--Update stockItemsMatchedToOtherSiteScans
CREATE TABLE #stockItemsMatchedToOtherSiteScans  ([ItemId] INT NOT NULL,[MatchItemId] INT NULL);	INSERT INTO #stockItemsMatchedToOtherSiteScans SELECT ItemId, MatchItemId from @stockItemsMatchedToOtherSiteScans
UPDATE StockItems
SET StockItems.ScanId = #stockItemsMatchedToOtherSiteScans.MatchItemId, StockItems.ReconcilingItemId = null
FROM StockItems INNER JOIN #stockItemsMatchedToOtherSiteScans on #stockItemsMatchedToOtherSiteScans.ItemId = StockItems.Id
WHERE StockItems.StockCheckId = @stockCheckId

--Update stockItemsMatchedToRecItems
CREATE TABLE #stockItemsMatchedToRecItems  ([ItemId] INT NOT NULL,[MatchItemId] INT NULL);	INSERT INTO #stockItemsMatchedToRecItems SELECT ItemId, MatchItemId from @stockItemsMatchedToRecItems
UPDATE StockItems
SET StockItems.ReconcilingItemId = #stockItemsMatchedToRecItems.MatchItemId, StockItems.ScanId = null
FROM StockItems INNER JOIN #stockItemsMatchedToRecItems on #stockItemsMatchedToRecItems.ItemId = StockItems.Id
WHERE StockItems.StockCheckId = @stockCheckId

--Update remainingUnmatchedStockItems
CREATE TABLE #remainingUnmatchedStockItems  ([ItemId] INT NOT NULL,[MatchItemId] INT NULL);	INSERT INTO #remainingUnmatchedStockItems SELECT ItemId, MatchItemId from @remainingUnmatchedStockItems
UPDATE StockItems
SET StockItems.ScanId = NULL, StockItems.ReconcilingItemId = NULL
FROM StockItems INNER JOIN #remainingUnmatchedStockItems on #remainingUnmatchedStockItems.ItemId = StockItems.Id
WHERE StockItems.StockCheckId = @stockCheckId



--         -------------------
--         --CleanUp
--         -------------------
DROP TABLE #duplicatedScans
DROP TABLE #scansMatchedToStockItems
DROP TABLE #scansMatchedToOtherSiteStockItems
DROP TABLE #scansMatchedToRecItems
DROP TABLE #remainingUnmatchedScans
            
DROP TABLE #duplicatedStockItems
DROP TABLE #stockItemsMatchedToScans
DROP TABLE #stockItemsMatchedToOtherSiteScans
DROP TABLE #stockItemsMatchedToRecItems
DROP TABLE #remainingUnmatchedStockItems




END

GO

