/*
Add/Update the Email address of the CPHI user. Format is "Name|Email"
*/

DECLARE @DealerGroupId INT, @Name NVARCHAR(255), @Email NVARCHAR(255)
DECLARE @Emails TABLE (Name NVARCHAR(255), Email NVARCHAR(255))
DECLARE @DealerGroups TABLE (Id INT)

-- Insert names and emails into a table variable
INSERT INTO @Emails (Name, Email)
SELECT LEFT(value, CHARINDEX('|', value) - 1), RIGHT(value, LEN(value) - CHARINDEX('|', value))
FROM STRING_SPLIT('<PERSON><PERSON>h <PERSON>|<EMAIL>,<PERSON>|<EMAIL>', ',')

-- Insert DealerGroup Ids into a table variable
INSERT INTO @DealerGroups (Id)
SELECT Id FROM DealerGroup 

-- Cursor for Dealer Groups
DECLARE DealerGroupCursor CURSOR FOR SELECT Id FROM @DealerGroups
OPEN DealerGroupCursor

FETCH NEXT FROM DealerGroupCursor INTO @DealerGroupId
WHILE @@FETCH_STATUS = 0
BEGIN
    -- Cursor for Emails
    DECLARE EmailCursor CURSOR FOR SELECT Name, Email FROM @Emails
    OPEN EmailCursor

    FETCH NEXT FROM EmailCursor INTO @Name, @Email
    WHILE @@FETCH_STATUS = 0
    BEGIN
        DECLARE @UserId NVARCHAR(MAX)
        DECLARE @LinkedPersonId INT
        
       
        BEGIN
		--SELECT @DealerGroupId, @Email


            -- Check if the user already exists in AspNetUsers
            SELECT @UserId = Id, @LinkedPersonId = LinkedPersonId 
            FROM AspNetUsers 
            WHERE Email = @Email AND DealerGroupId = @DealerGroupId

			--SELECT @UserId, @LinkedPersonId
            
            IF @UserId IS NULL
            BEGIN
                -- Insert into Users table and get the LinkedPersonId
                INSERT INTO Users (DealerGroupId, [Name]) 
                VALUES (@DealerGroupId, @Name) 
                SET @LinkedPersonId = SCOPE_IDENTITY()

				INSERT INTO UserSites(UserId, SiteId, IsDefault)
				SELECT @LinkedPersonId, s.Id,0 from Sites s inner join Divisions d on d.Id = s.DivisionId where d.DealerGroupId = @DealerGroupId
                
                -- Insert into AspNetUsers
                SET @UserId = NEWID()
                INSERT INTO AspNetUsers (Id, Discriminator, LinkedPersonId, UserName, NormalizedUserName, Email, NormalizedEmail, EmailConfirmed, LockoutEnabled, DealerGroupId, PhoneNumberConfirmed, TwoFactorEnabled, AccessFailedCount, SecurityStamp)
                VALUES (@UserId, 'ApplicationUser', @LinkedPersonId, @Email, UPPER(@Email), @Email, UPPER(@Email), 1, 1, @DealerGroupId, 0, 0, 0, NEWID())

                -- Insert into AspNetUserRoles
                INSERT INTO AspNetUserRoles (UserId, RoleId)
                VALUES (@UserId, '91e9e4aa-206e-411a-90c4-26908ee9711f')

				
            END

			 SET @UserId = NULL
			SET	@LinkedPersonId = NULL
        END

        FETCH NEXT FROM EmailCursor INTO @Name, @Email
    END

    CLOSE EmailCursor
    DEALLOCATE EmailCursor

    FETCH NEXT FROM DealerGroupCursor INTO @DealerGroupId
END

CLOSE DealerGroupCursor
DEALLOCATE DealerGroupCursor


