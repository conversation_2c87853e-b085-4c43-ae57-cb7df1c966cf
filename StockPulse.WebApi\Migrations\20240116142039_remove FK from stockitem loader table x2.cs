﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace StockPulse.WebApi.Migrations
{
    /// <inheritdoc />
    public partial class removeFKfromstockitemloadertablex2 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_FileImport_Loads_Users_LoadedByUserId",
                schema: "import",
                table: "FileImport_Loads");

            migrationBuilder.DropForeignKey(
                name: "FK_StockItem_Loads_Sites_SiteId",
                schema: "import",
                table: "StockItem_Loads");

            migrationBuilder.DropForeignKey(
                name: "FK_StockItem_Loads_SourceReports_SourceReportId",
                schema: "import",
                table: "StockItem_Loads");

            migrationBuilder.DropIndex(
                name: "IX_StockItem_Loads_SiteId",
                schema: "import",
                table: "StockItem_Loads");

            migrationBuilder.DropIndex(
                name: "IX_StockItem_Loads_SourceReportId",
                schema: "import",
                table: "StockItem_Loads");

            migrationBuilder.RenameTable(
                name: "StockItem_Loads",
                schema: "import",
                newName: "StockItems",
                newSchema: "import");

            migrationBuilder.RenameTable(
                name: "FileImport_Loads",
                schema: "import",
                newName: "FileImports",
                newSchema: "import");

            migrationBuilder.RenameIndex(
                name: "IX_FileImport_Loads_LoadedByUserId",
                schema: "import",
                table: "FileImports",
                newName: "IX_FileImports_LoadedByUserId");

            migrationBuilder.AddColumn<int>(
                name: "FileImportId",
                schema: "import",
                table: "StockItems",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddForeignKey(
                name: "FK_FileImports_Users_LoadedByUserId",
                schema: "import",
                table: "FileImports",
                column: "LoadedByUserId",
                principalSchema: "dbo",
                principalTable: "Users",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_FileImports_Users_LoadedByUserId",
                schema: "import",
                table: "FileImports");

            migrationBuilder.DropColumn(
                name: "FileImportId",
                schema: "import",
                table: "StockItems");

            migrationBuilder.RenameTable(
                name: "StockItems",
                schema: "import",
                newName: "StockItem_Loads",
                newSchema: "import");

            migrationBuilder.RenameTable(
                name: "FileImports",
                schema: "import",
                newName: "FileImport_Loads",
                newSchema: "import");

            migrationBuilder.RenameIndex(
                name: "IX_FileImports_LoadedByUserId",
                schema: "import",
                table: "FileImport_Loads",
                newName: "IX_FileImport_Loads_LoadedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_StockItem_Loads_SiteId",
                schema: "import",
                table: "StockItem_Loads",
                column: "SiteId");

            migrationBuilder.CreateIndex(
                name: "IX_StockItem_Loads_SourceReportId",
                schema: "import",
                table: "StockItem_Loads",
                column: "SourceReportId");

            migrationBuilder.AddForeignKey(
                name: "FK_FileImport_Loads_Users_LoadedByUserId",
                schema: "import",
                table: "FileImport_Loads",
                column: "LoadedByUserId",
                principalSchema: "dbo",
                principalTable: "Users",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_StockItem_Loads_Sites_SiteId",
                schema: "import",
                table: "StockItem_Loads",
                column: "SiteId",
                principalSchema: "dbo",
                principalTable: "Sites",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_StockItem_Loads_SourceReports_SourceReportId",
                schema: "import",
                table: "StockItem_Loads",
                column: "SourceReportId",
                principalSchema: "dbo",
                principalTable: "SourceReports",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
