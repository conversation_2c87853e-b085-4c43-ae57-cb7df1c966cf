﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace StockPulse.Model
{
    public class User
    {
        [Key]
        public int Id { get; set; }

        public int DealerGroupId { get; set; }
        [ForeignKey("DealerGroupId")]
        public virtual DealerGroup DealerGroup { get; set; }


        public string Name { get; set; }

        public string EmployeeNumber { get; set; }
        public string CurrentDevice { get; set; }
    }
}