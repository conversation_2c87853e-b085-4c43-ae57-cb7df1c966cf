h4 {
    width: 100%;

    .reconcilingReasonChip {
        padding: 0em 2em;
        margin: 0px;
        height: 2em;
    }

    .reconcilingReasonChip.bad {
        background-color: var(--danger);
    }
}

#regBox {
    width: 200px;
}

#resolutionImageHolder {
    display: flex;
    flex-wrap: wrap;

    .backupAndButton {
        position: relative;
        width: 30%;
        height: 75px;
        margin-bottom: 1em;
        margin-right: 3.3333333333%;

        button {
            position: absolute;
            top: 0;
            right: 0;
            padding: 0.25em 0.75em;
        }

        img {
            width: 100%;
            height: 100%;
            object-fit: contain;
            background-color: var(--grey95);
            margin-bottom: 1em;
            cursor: pointer;
        }
    }
}

.modal-body {
    max-height: 80vh;
}

.vehicle-modal-card {
    position: relative;

    .cph-card-body {
        display: flex;
        flex-direction: column;
        height: calc(75vh - 2rem);
        max-height: calc(75vh - 2rem);
        overflow-y: auto;
        padding: 0.5em 0;
        scrollbar-gutter: stable both-edges;
        scrollbar-width: thin;

        .card-row {
            display: flex;
            justify-content: space-between;
            flex: 1;
        }

        .card-row:nth-of-type(1) {
            margin-bottom: 1em;
        }
    }

    &.left {
        width: calc(40% - 1em);
    }

    &.right {
        width: calc(60% - 1em);
    }
}

.compact .vehicle-modal-card .cph-card-body {
    height: 26em;
}


#dms-details-table,
.explanationTable,
#scan-table {
    width: 100%;
    //table-layout: fixed;

    thead tr {
        background-color: var(--primaryLighter);
        color: var(--bodyColour);

        th {
            padding: 0.75em;
        }
    }

    tbody tr td {
        padding: 0.75em;
    }

    tbody tr.topBorder td {
        border-top: 1px solid var(--grey95);
    }

    // tbody tr:nth-of-type(1) td {
    //     border-top: 0px solid white!important; //to cancel the border-top on the first one
    // }


}

table.compact tbody tr td {
    padding: 0.2em 1em !important;
}

#scanDetails,
#regAndVinScan {
    width: calc(50% - 0.5em);
    display: flex;
    justify-content: space-between;
    flex-direction: column;
}

.plateContainer {
    display: flex;
    position: relative;
    width: 100%;
    margin-top: 0.5em;
    justify-content: center;
    align-items: center;

    &.reg {
        position: absolute;
        padding: 1em;
        bottom: 0;
        width: 100%;
        background-color: rgba(0, 0, 0, 0.5);
    }

    .plateBox {
        // padding: 0.25em 1em;
        font-weight: bold;
        text-align: center;
        // height: 26px;
        white-space: nowrap;
        display: flex;
        align-items: center;
        justify-content: center;
        letter-spacing: 1px;
        height: 2em;
        border-radius: 0.25em;
        position: relative;
        overflow: hidden;
    }

    .regBox {
        font-family: 'NumberPlate';
        background-color: var(--numberPlate);
        color: #000000;
        width: 6em;
        // border: 1px solid var(--grey80);
    }

    .vinBox {
        font-family: 'VIN';
        background-color: black;
        color: var(--grey80);
        // border: 1px solid var(--grey20)
        letter-spacing: 2px;
        border-radius: 0;
        width: 7em;
    }

    input {
        // padding: 0.25em 1em;
        font-weight: bold;
        text-align: center;
        min-width: 6.5em;
        height: 2em;
        letter-spacing: 1px;
        border-radius: 0.25em;
        // border: 1px solid var(--grey20);
    }

    input#regEditBox {
        font-family: 'NumberPlate';
        background-color: var(--numberPlate);
        color: #000000;
        width: 6em;
    }

    input#vinEditBox {
        font-family: 'VIN';
        background-color: black;
        color: var(--grey80);
        letter-spacing: 2px;
        border-radius: 0;
        width: 7em;
        border: none;
    }


    //when editing
    .btnEditPlate {
        height: 32px;
        margin-left: 0.25em;

        .ng-fa-icon {
            margin: 0;
        }
    }

    #savingSpinner {
        position: absolute;
        right: 0.6em;
        color: #000000;
    }

    #savingSpinnerVin {
        position: absolute;
        right: 0.6em;
        color: white;
    }
}

#backupContainer {
    position: relative;

    #instructionPopoverHolder {
        position: absolute;
        top: 0em;
        left: 0em;
    }
}


#regScanAndInput {
    position: relative;
    width: 100%;

    #regImage {
        width: 100%;

        img {
            width: 100%;
            display: block;
            margin-left: auto;
            margin-right: auto;
        }
    }
}

.compact #regScanAndInput #regImage {
    width: 50%;
}

#vinScanAndInput {
    width: 100%;

    #vinImage {
        width: 100%;
        text-align: center;

        div {
            background-color: #000000;
            font-family: 'VIN';
            padding: 0.5em;
            font-weight: bold;
            color: var(--grey80);
            letter-spacing: 2px;
        }

        img {
            width: 100%;
            display: block;
            margin-left: auto;
            margin-right: auto;
        }
    }

    #vinContainer {
        margin-top: 0.5em;
        width: 100%;
        display: flex;
        justify-content: center;
    }

    .vin,
    .vinEditable {
        min-width: 30%;
        background-color: var(--grey20);
        color: var(--bodyColour);
        font-family: 'VIN';
        padding: 0.25em 1em;
        font-weight: bold;
        text-align: center;
        position: relative;
        border: 1px solid var(--grey20);

        .editContainer {
            color: var(--bodyColour);
        }

        input {
            color: var(--bodyColour);
            width: 100%;
            background: transparent;
            border: none;

            &:focus {
                box-shadow: none !important;
            }
        }
    }

    .vin.wide {
        min-width: 60%;
    }
}

.editContainer {
    cursor: pointer;
    position: absolute;
    right: 10px;
    top: 0;
    bottom: 0;
    display: flex;
    align-items: center;
}

#scanMap {
    width: 100%;
    height: 100%;
    min-height: 150px;

    agm-map {
        height: 100%;
    }

    &.noMap {
        display: flex;
        justify-content: center;
        align-items: center;
        background: #f1f3f4;
        box-shadow: none;
        border: 1px solid var(--grey80);
    }
}

.addButtonArea {
    margin-top: 2em;
    float: right;
}

.holdingNote {
    display: flex;
    align-items: center;
    flex-direction: column;
    //margin-bottom: 4em;

    fa-icon {
        opacity: 0.7;
    }
}

.compact .holdingNote fa-icon {
}


.holdingNoteGood fa-icon {
    color: var(--success);
}

.holdingNoteBad fa-icon {
    color: var(--danger);
}

.holdingNoteMeh fa-icon {
    color: var(--warning);
}

.popoverImage {
    cursor: zoom-in;
}

#fileDropArea {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    width: 100%;
    border: 1px dashed;
    padding: 1em;
    caret-color: transparent;

    &:focus-visible {
        outline: none !important;
    }
}

#filesList {
    padding: 1em;

    fa-icon.fileIcon {
        margin-right: 1em;
    }

    fa-icon.deleteIcon {
        color: var(--danger);
        cursor: pointer;
    }

    .singleFile {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        margin-bottom: 0.5em;

        .fileThumbnail {
            width: 20%;
            margin-right: 0.5em;
        }

        .info {
            max-width: 80%;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    }
}

.notes {
    border: none;
    width: 100%;
}


.okCancelButtons {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    border-top: 1px solid var(--grey80);
    padding-top: 7px;

    .btn {
        margin-left: 0.4em;
    }
}

.cph-card-footer.resolutionButtons {
    position: absolute;
    bottom: 1px;
    left: 1px;
    padding: 0.5em;
    background: #FFFFFF;
    right: 1px;
    border-radius: 0 0 6px 6px;
    height: 50px;
    display: flex;
    align-items: center;
}

.showResolutionButtons {
    padding-bottom: 50px;
}

.enlarged-image-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.7);
    z-index: 1;

    .image-backdrop {
        position: absolute;
        height: 100vh !important;
        width: 100vw;
        filter: blur(10px);
        -webkit-filter: blur(10px);
        height: 100%;
        background-position: center;
        background-repeat: no-repeat;
        background-size: cover;
        zoom: 1.5;
    }

    .image-container {
        height: 90vh;
        width: 90vw;
        z-index: 1;
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;

        .image-close {
            position: absolute;
            top: 0;
            right: 0;
            line-height: 0.5em;
            padding: 0.5em;
            color: #FFFFFF;
            background-color: transparent;
            border: none;
        }

        img {
            object-fit: contain;
            height: 100%;
            max-height: 100%;

            &.isVin {
                width: 100%;
                height: unset;
            }
        }
    }
}

td:nth-of-type(1) {
    width: 30%;
} 

#scan-table {
    td:nth-of-type(1) {
        width: 40%;
    } 
}

.scanEditedInfo {
    position: absolute;
    left: -32px;
    cursor: pointer;
    color: var(--infoDarker);
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

#vinEditorInPreview {
    display: flex;
    position: relative;
    width: 100%;
    margin-top: 1em;
    justify-content: center;
    align-items: center;
    z-index: 2;

    .nonEditMode {
        width: 100%;
        display: flex;
        justify-content: center;
    }

    .plateBox {
        padding: 0.25em 1em;
        font-weight: bold;
        text-align: center;
        width: 200px;
        height: 32px;
    }

    .regBox {
        font-family: 'NumberPlate';
        background-color: var(--numberPlate);
        color: #000000;
        border: 1px solid var(--grey80);
    }

    .vinBox {
        font-family: 'VIN';
        background-color: var(--grey20);
        color: var(--bodyColour);
        border: 1px solid var(--grey20)
    }

    input {
        padding: 0.25em 1em;
        font-weight: bold;
        text-align: center;
        width: 200px;
        height: 32px;
        border: 1px solid var(--grey20);
    }

    input#regEditBox {
        font-family: 'NumberPlate';
        background-color: var(--numberPlate);
        color: #000000;
        width: 6em;
    }

    input#vinEditBox {
        font-family: 'VIN';
        background-color: black;
        color: var(--grey80);
        letter-spacing: 2px;
        border-radius: 0;
        width: 7em;
    }


    //when editing
    .btnEditPlate {
        height: 32px;
        margin-left: 0.25em;

        .ng-fa-icon {
            margin: 0;
        }
    }

    #savingSpinner {
        position: absolute;
        right: 0.6em;
        color: #000000;
    }

    #savingSpinnerVin {
        position: absolute;
        right: 0.6em;
        color: white;
    }
}

// .chassis,
// .regPlate {
//     width: fit-content;
// }