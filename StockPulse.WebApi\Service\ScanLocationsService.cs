﻿using StockPulse.WebApi.DataAccess;
using StockPulse.Model;
using StockPulse.WebApi.ViewModel;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace StockPulse.WebApi.Service
{
    public interface IScanLocationsService
    {
        Task<IEnumerable<Location>> GetScanLocationsForSiteId(int siteId);
        Task SaveScanLocationForSiteId(SaveScanLocationParams parms);
        Task DeleteScanLocationForSiteId(DeleteScanLocationParams parms);
    }

    public class ScanLocationsService : IScanLocationsService
    {
        private readonly IScanLocationsDataAccess scanLocationsDataAccess;

        public ScanLocationsService(IScanLocationsDataAccess scanLocationsDataAccess)
        {
            this.scanLocationsDataAccess = scanLocationsDataAccess;
        }

        public async Task<IEnumerable<Location>> GetScanLocationsForSiteId(int siteId)
        {
            return await scanLocationsDataAccess.GetScanLocationsForSiteId(siteId);
        }

        public async Task SaveScanLocationForSiteId(SaveScanLocationParams parms)
        {
            await scanLocationsDataAccess.SaveScanLocationForSiteId(parms);
        }

        public async Task DeleteScanLocationForSiteId(DeleteScanLocationParams parms)
        {
            await scanLocationsDataAccess.DeleteScanLocationForSiteId(parms);
        }
    }
}
