﻿
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO




CREATE OR ALTER PROCEDURE [dbo].[DELETE_UnknownResolutionImage]
(
    @StockCheckId INT,
	@UserId INT,
	@UnknownResolutionId INT,
	@ImageId INT
)
AS
BEGIN

SET NOCOUNT ON;

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END


DELETE [dbo].[UnknownResolutionImages]
WHERE Id = @ImageId AND UnknownResolutionId = @UnknownResolutionId

EXEC [dbo].[UPDATE_StockCheckLastUpdate] @StockCheckId, @UserId



END
	
  
	


GO


