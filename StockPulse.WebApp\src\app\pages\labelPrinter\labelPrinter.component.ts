import { Component, OnInit } from "@angular/core";
import { NgbModal, NgbModalRef } from "@ng-bootstrap/ng-bootstrap";
import * as XLSX from "xlsx";
import { SiteVMWithSelected } from "src/app/model/SiteVMWithSelected";
import { ConstantsService } from "src/app/services/constants.service";
import { IconService } from "src/app/services/icon.service";
import { Label, LabelPrinterService } from "./labelPrinter.service";
import { LabelPrinterModalComponent } from "./modal/modal.component";
import { ToastService } from "src/app/services/newToast.service";

@Component({
  selector: "app-labelPrinter",
  templateUrl: "./labelPrinter.component.html",
  styleUrls: ["./labelPrinter.component.scss"],
})
export class LabelPrinterComponent implements OnInit {
  file: any;
  fileName: string;
  sourceData: any;

  constructor(
    public service: LabelPrinterService,
    public constantsService: ConstantsService,
    public modalService: NgbModal,
    public icon: IconService,
    public toastService: ToastService
  ) {}

  ngOnInit(): void {
    this.service.initialise();
  }

  searchList() {
    let sitesCopy: SiteVMWithSelected[] = JSON.parse(
      JSON.stringify(this.constantsService.Sites)
    );
    this.service.sitesCopy = sitesCopy.filter((site) =>
      site.description
        .toLocaleLowerCase()
        .includes(this.service.searchString.toLocaleLowerCase())
    );
  }

  openManualPrintModal() {
    const modal: NgbModalRef = this.modalService.open(
      LabelPrinterModalComponent,
      { size: "xs" }
    );

    modal.result.then((res) => {
      if (res) {
        this.service.labelsFromModal = res.labels;
        this.service.copies = res.copies;
        this.service.generateLabels();
      }
    });
  }

  closeIframe() {
    this.service.showIframe = false;
    this.file = null;
  }

  validateCopies() {
    if (this.service.copies === 0) {
      return (this.service.copies = 1);
    }
    if (this.service.copies > 3) {
      return (this.service.copies = 3);
    }
    return;
  }

  onFileChange(evt: any) {
    const target: DataTransfer = <DataTransfer>evt.target;

    if (target.files.length !== 1) {
      return alert("Please select only 1 file");
    }

    if (evt.target.value) {
      this.fileName = /[^\\]*$/.exec(evt.target.value)[0];
    }

    this.file = target.files[0];

    const reader: FileReader = new FileReader();
    reader.onload = (e: any) => {
      try {
        /* read workbook */
        this.file = e.target.result;
        this.sourceData = this.openExcelFile(this.file);
        this.convertInputFile(this.sourceData);
      } catch (error) {
        this.toastService.errorToast("Problem loading file");
      }
    };

    setTimeout(() => {
      reader.readAsBinaryString(target.files[0]);
    }, 50);
  }

  openExcelFile(file: string): any[][] {
    const wb: XLSX.WorkBook = XLSX.read(file, { type: "binary" });

    const wsname: string = wb.SheetNames[0];
    const ws: XLSX.WorkSheet = wb.Sheets[wsname];

    return <any[][]>(
      XLSX.utils.sheet_to_json(ws, { header: 1, blankrows: true })
    );
  }

  convertInputFile(rows: any[]) {
    // Verify file format
    const expectedHeaders: string[] = [
      "Red/Black",
      "Carfax Has\nReport",
      "Carfax Has\nManufacturer\nRecall",
      "Carfax Has\nWarnings",
      "Carfax Has\nProblems",
      "Photos",
      "Video",
      "Autowriter\nDescription",
      "Vehicle",
      "Stock #",
      "VIN",
      "Class",
      "Certified",
      "Deleted Date",
      "Status",
      "Recall Status",
      "Age",
      "Body",
      "Color",
      "Disp",
      "Price",
      "ProfitTime\nAlignment",
      "Default %\nof Market",
      "Last $ Change",
      "Book",
      "Cost",
      "Water",
      "Markup",
      "Odometer",
      "Overall",
      "Like Mine",
      "Price Rank\nDescription",
      "vRank Description",
    ];

    const headerRowIndex: number = 0;
    const headerRow: Object = rows[headerRowIndex];

    const actualHeaders = Object.keys(headerRow)
      .filter((key) => key !== "RowNo")
      .map((key) => headerRow[key]);

    let badColumns: string[] = [];

    for (let i = 0; i < expectedHeaders.length; i++) {
      if (expectedHeaders[i] !== actualHeaders[i]) {
        badColumns.push(
          `Cell ${this.numberToLetter(i)}${headerRowIndex + 1} expected "${
            expectedHeaders[i]
          }" but got "${actualHeaders[i]}".`
        );
      }
    }

    if (badColumns.length > 0) {
      this.file = null;
      this.toastService.errorToast(badColumns.join("\n"));
      return;
    }

    let labels: Label[] = [];

    for (let i = 1; i < rows.length; i++) {
      labels.push({
        vin: rows[i][10] ? rows[i][10].substring(0, 17) : "",
        stockNumber: rows[i][9] ? rows[i][9].substring(0, 13) : "",
        description: rows[i][8] ? rows[i][8].substring(0, 26) : "",
        colour: rows[i][18] ? rows[i][18].substring(0, 26) : "",
      });
    }

    this.service.labelsFromModal = labels;
  }

  private numberToLetter(number) {
    if (number <= 25) {
      return String.fromCharCode(65 + number); // 65 is A
    } else {
      return `A${String.fromCharCode(65 + number - 26)}`;
    }
  }
}
