﻿--1. Find the dependent SPs for this type via SSMS Object Explorer (right click -> View dependencies)
--2. Drop the dependent SPs
--3. Drop the type
--4. Recreate the type
--5. Recreate the dependent SPs

--Deleting Dependent SPs
DROP PROC [dbo].[CREATE_ReconcilingItems]
GO

--Deleting Type
DROP TYPE [dbo].[ReconcilingItemSaveType]
GO


--Creating Type
CREATE TYPE [dbo].[ReconcilingItemSaveType] AS TABLE(
	[Vin] [nvarchar](20),
	[Reg] [nvarchar](20),
	[Description] [nvarchar](250),
	[Comment] [nvarchar](500),
	[Reference] [nvarchar](50),
	[StockCheckId] [int],
	[SourceReportId] [int],
	[ReconcilingItemTypeId] [int],
	[FileImportId] [int]
)
GO

--Create the dependent SPs 
--(best would be to run all_sps.sql)

