#menuTrigger {
    position: absolute;
    top: 35px;
    left: 0;
    bottom: 0;
    width: 10px;
    background-color: var(--primaryDark);
    z-index: 1;
}

#menuToggle {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 9999;
    width: 40px;
    height: 35px;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

#sidenav {
    position: absolute;
    display: flex;
    flex-direction: column;
    top: 0;
    left: 0;
    bottom: 0;
    width: 0;
    background-color: var(--primaryDark);
    color: var(--bodyColour);
    margin: 0;
    padding: 0;
    z-index: 1;
    transition: ease all 0.1s;

    @media (max-width: 1680px) {
        width: 30px;
        top: 30px;
    }

    @media (max-width: 1280px) {
        width: 25px;
        top: 25px;
    }

    .button-group {

        button {
            display: flex;
            align-items: center;
            width: 100%;
            height: 35px;
            border: none;
            padding: 0;
            border-radius: 0;

            @media (max-width: 1680px) {
                height: 30px;
            }
            
            @media (max-width: 1280px) {
                height: 25px;
            }

            .icon-holder {
                width: 35px;

                @media (max-width: 1680px) {
                    width: 30px;
                }
                
                @media (max-width: 1280px) {
                    width: 25px;
                }
            }

            &:disabled {
                background-color: var(--primaryLight);
            }

            &.active {
                background-color: var(--primaryLight);
                color: var(--bodyColour);
                box-shadow: inset 3px 0px 0 0px var(--secondary) !important
            }

            &:hover:not(.active) {
                background-color: var(--primaryLight);
                color: var(--bodyColour);
                box-shadow: inset 3px 0px 0 0px var(--grey) !important
            }
        }
    }

    #sidenav-footer {
        position: absolute;
        left: 0;
        right: 0;
        bottom: 20px;
        width: 100%;
        display: flex;
        justify-content: space-evenly;
        align-items: center;

        img {
            transition: ease all 0.1s;
            width: 30%;
            opacity: 0.7;
        }
    }
}

.sectionLabel {
    height: 35px;
    white-space: nowrap;
    color: var(--bodyColour);
    display: flex;
    align-items: center;
    margin: 3em 0em 0em 0.6em;

    @media (max-width: 1680px) {
        height: 30px;
    }
    
    @media (max-width: 1280px) {
        height: 25px;
    }
}


#sidenav.wide {
    width: 220px;

    @media (max-width: 1680px) {
        width: 190px;
    }
    
    @media (max-width: 1280px) {
        width: 160px;
    }
}

@media screen and (max-width: 1679px) {
    .sectionLabel {
        margin: 2em 0em 0em 0.6em;
    }
}

#fixMenuButtonContainer {
    position: absolute;
    top: 0;
    right: 0;
    height: 35px;
    display: flex;
    align-items: center;

    #fixMenuButton {
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.5s;

        div {
            transition: all 0.5s;

            &.rotate {
                transform: rotate(180deg);
            }
        }
    }
}