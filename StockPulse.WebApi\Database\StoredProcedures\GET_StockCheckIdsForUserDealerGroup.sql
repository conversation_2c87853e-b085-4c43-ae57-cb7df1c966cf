SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].[GET_StockCheckIdsForUserDealerGroup]
(
    @UserId INT = NULL
)
AS
BEGIN

SET NOCOUNT ON;

DECLARE @userDealerGroup int = (SELECT DealerGroupId FROM Users WHERE Id = @userId)

SELECT sc.Id
FROM StockChecks sc
INNER JOIN Sites s on s.Id = sc.SiteId
INNER JOIN Divisions d on d.Id = s.DivisionId
INNER JOIN DealerGroup dg on dg.Id = d.DealerGroupId
WHERE
dg.Id = @userDealerGroup
AND sc.IsActive = 1

END
