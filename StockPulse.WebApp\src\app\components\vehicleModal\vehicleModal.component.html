<ng-template #vehicleModalRef let-modal>

  <div class="modal-header">
    <h4 class="modal-title" id="modal-basic-title">
      <!-- <span *ngIf="!isStockItem">{{item.scan?.scanDescription }}</span>
      <span *ngIf="isStockItem && item.stockItem?.description">{{ item.stockItem?.description }}</span> -->
      <span>{{ fallbackTitle }}</span>
    </h4>
    <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click');">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>

  <div class="modal-body">

    

      <vehicleModalBodyNew *ngIf="item"  [item]="item"></vehicleModalBodyNew>

     
    

  </div>
  <div class="modal-footer">
    <div class="d-flex align-items-center">
      <instructionRow [message]="'Use the up and down keyboard arrows or these buttons to cycle through vehicle records'" [noMargin]="true"></instructionRow>

      <button class="btn btn-primary ms-5" [disabled]="!canGoPreviousItem()" (click)="goToItem(true)">Previous record</button>
      <button class="btn btn-primary" [disabled]="!canGoNextItem()" (click)="goToItem(false)">Next record</button>

      <span class="ms-5">Record {{ currentVehicleIndex+1 }} of {{ idsToNavigateBetween.length }}</span>
    </div>

    <button type="button" class="btn btn-primary" (click)="modal.dismiss('Cancelled');">Close</button>
  </div>


</ng-template>




