﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace StockPulse.Model
{
    public class Site
    {
       

        [Key]
        //[DatabaseGenerated(DatabaseGeneratedOption.None)]
        public int Id { get; set; }
        public string Description { get; set; }
        public bool IsActive { get; set; }
        public decimal Longitude { get; set; }  //watch out on model creating, needs lots of decimal places
        public decimal Latitude { get; set; }  //watch out on model creating, needs lots of decimal places

        //foreign keys "many in this table to one in the other table"

        //division
        public int DivisionId { get; set; }
        [ForeignKey("DivisionId")]
        public virtual Division Divisions { get; set; }
        public bool OverrideLongLat { get; set; }


    }

}