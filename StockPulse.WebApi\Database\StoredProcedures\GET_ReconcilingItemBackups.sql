SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].[GET_ReconcilingItemBackups]
(
    @StockCheckId INT,
	@ReconcilingItemTypeId INT,
	@UserId INT
)
AS
BEGIN

SET NOCOUNT ON;

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END

SELECT * FROM [dbo].[ReconcilingItemBackups]
WHERE StockCheckId = @StockCheckId AND ReconcilingItemTypeId = @ReconcilingItemTypeId

END

GO
