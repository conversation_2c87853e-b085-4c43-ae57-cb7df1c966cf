table,
input {
    width: 100%;
}

.copiesToggleContainer {
    display: flex;
    align-items: center;
    margin-right: 0.3em;
}

.toggleCopiesButton {
    align-self: stretch;
    width: 25px;
    min-width: 25px !important;
    margin: 0 !important;
    padding: 0;
}

.toggleCopiesInput {
    align-self: stretch;
    text-align: center;
}

.modal-footer {
    justify-content: unset;
}

.modalFooterInner {
    display: flex;
    justify-content: space-between;
    width: 100%;
}

.modal-body {
    max-height: 50vh;
    overflow-y: auto;
}

.headerWithIcon {
    position: relative;

    button {
        position: absolute;
        right: 0;
        background-color: transparent;
        border: none;
        color: #FF0000;
    }
}