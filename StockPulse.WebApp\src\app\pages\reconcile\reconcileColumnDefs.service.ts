import { Injectable } from "@angular/core";
import { ColDef } from "ag-grid-community";
import { CphPipe } from "src/app/cph.pipe";
import { BarNew } from "src/app/model/BarNew";
import { ReconciliationState } from "src/app/model/ReconciliationState";
import { ConstantsService } from "src/app/services/constants.service";
import { BackupImageComponent } from "src/app/_cellRenderers/backup-image/backup-image.component";
import { CarNotesComponent } from "src/app/_cellRenderers/carNotes.component";
import { CarResolutionComponent } from "src/app/_cellRenderers/carResolution.component";
import { ChassisComponent } from "src/app/_cellRenderers/chassis.component";
import { ConfidenceComponent } from "src/app/_cellRenderers/confidence.component";
import { ImageComponent } from "src/app/_cellRenderers/image.component";
import { IndexComponent } from "src/app/_cellRenderers/index.component";
import { RegPlateComponent } from "src/app/_cellRenderers/regPlate.component";

@Injectable({
  providedIn: 'root'
})
export class ColumnDefsService {


  constructor(
    private cphPipe: CphPipe,
    public constants: ConstantsService
  ) {

  }

  provideColDefs(bar: BarNew) {
    if (bar.isStock) {
      if (bar.reconciliationState === ReconciliationState.AllItems) { return this.getStockItemsColDefs() }
      if (bar.reconciliationState === ReconciliationState.Duplicate) { return this.getDuplicateStockItemsColDefs() }
      if (bar.reconciliationState === ReconciliationState.MatchedToOtherSite) { return this.getStockItemMatchedToOtherSiteScanColDefs() }
      if (bar.reconciliationState === ReconciliationState.MatchedToReport) { return this.getStockItemsMatchedToRecItemColDefs() }
      if (bar.reconciliationState === ReconciliationState.MatchedToStockOrScan) { return this.getStockItemsWithScanColDefs() }
      if (bar.reconciliationState === ReconciliationState.OutstandingIssue) { return this.getStockItemsWithResolutionColDefs() }
      if (bar.reconciliationState === ReconciliationState.Resolved) { return this.getStockItemsWithResolutionColDefs(true) }
    }
    else {
      if (bar.reconciliationState === ReconciliationState.AllItems) { return this.getScansColDefs() }
      if (bar.reconciliationState === ReconciliationState.Duplicate) { return this.getDuplicateScansColDefs() }
      if (bar.reconciliationState === ReconciliationState.MatchedToOtherSite) { return this.getScanMatchedToOtherSiteStockItemColDefs() }
      if (bar.reconciliationState === ReconciliationState.MatchedToReport) { return this.getScansMatchedToRecItemColDefs() }
      if (bar.reconciliationState === ReconciliationState.MatchedToStockOrScan) { return this.getStockItemsWithScanColDefs() }
      if (bar.reconciliationState === ReconciliationState.OutstandingIssue) { return this.getScansWithResolutionColDefs() }
      if (bar.reconciliationState === ReconciliationState.Resolved) { return this.getScansWithResolutionColDefs(true) }
    }

  }


  //--------------------------
  // StockItems
  //--------------------------
  private getStockItemsColDefs(): ColDef[] {  //ok
    return [
      { headerName: "#", sortable: true,  cellClass: 'indexCell', field: 'index', autoHeight: true, type: 'labelColumn', suppressSizeToFit: true },
      { headerName: "Reg", field: "reg", maxWidth: 100, width: 100, cellRenderer: RegPlateComponent, autoHeight: false, type: 'regVinColumn', hide: this.constants.GlobalParams.find(x => x.name === 'NeverScanReg').boolValue },
      { headerName: "VIN", field: "vin", maxWidth: 100, width: 100, cellRenderer: ChassisComponent, type: 'regVinColumn' },
      { headerName: "Stock Site", field: "siteName", width: 100, type: "labelColumn" },
      { headerName: "Branch", field: "branchName", width: 100, type: "labelColumn" },
      { headerName: "Description", field: "description", width: 250, type: "labelColumn" },
      { headerName: "DIS", field: "dis", sortable: true, width: 50,maxWidth:50, type: 'numberColumn', cellClass: 'agAlignCentre' },
      { headerName: "Group DIS", sortable: true, field: "groupDIS", width: 50,maxWidth:50, type: 'numberColumn', cellClass: 'agAlignCentre' },
      { headerName: "Stock Type", sortable: true, field: "stockType", width: 100, type: 'labelColumn' },
      { headerName: "Notes", sortable: true, field: "comment", width: 250, type: 'labelColumn' },
      { headerName: "Reference", sortable: true, field: "reference", width: 120, type: 'labelColumn' },
      { headerName: "Stock Value", field: "stockValue", width: 100, type: 'currencyColumn' },
      { headerName: "Flooring Balance", field: "flooring", width: 100, type: 'currencyColumn', hide: this.constants.currencySymbol != "$" },
    ];
  }


  private getDuplicateStockItemsColDefs() {  //ok ok
    return this.getStockItemsColDefs().concat([
      { headerName: "Original Id", field: "originalStockItemId", width: 100, type: 'labelColumn' },
      { headerName: "Original Reference", field: "originalReference", width: 150, type: 'labelColumn' },
      { headerName: "Original Notes", field: "originalComment", width: 150, type: 'labelColumn' },
    ]);
  }

  private getStockItemsMatchedToRecItemColDefs() {  //ok ok
    return this.getStockItemsColDefs().concat([
      { headerName: "Matching Type", sortable: true, field: "matchingItemTypeDesc", width: 200, type: 'labelColumn' },
      { headerName: "Matching Item", sortable: true, field: "matchingItemDesc", width: 250, type: 'labelColumn' },
      { headerName: "Matching Ref", sortable: true, field: "matchingItemRef", width: 200, type: 'labelColumn' },
      { headerName: "Matching Notes", sortable: true, field: "matchingItemComment", width: 200, type: 'labelColumn' },
    ]);
  }



  private getStockItemMatchedToOtherSiteScanColDefs() {    //ok ok
    return this.getStockItemsColDefs().concat([
      { headerName: "Photo", field: "regImageThumbnailUrl", width: 50, cellRenderer: ImageComponent, autoHeight: true, filter: null, type: 'labelColumn', cellClass: 'agAlignCentre' },
      { headerName: "Other Site Name", field: "otherSiteName", width: 150, type: "labelColumn" },
      { headerName: "Matching Scan Id", sortable: true, field: "scanId", width: 100, type: 'labelColumn', cellClass: 'agAlignCentre' },
      { headerName: "Matching Location", sortable: true, field: "scanLocation", width: 150, type: 'labelColumn' },
      { headerName: "Matching Scanned By", sortable: true, field: "scannedBy", width: 150, type: 'labelColumn' },
    ]);
  }

  private getStockItemsWithResolutionColDefs(resolved?: boolean) {  //ok ok
    const allCols = this.getStockItemsColDefs().concat([
      { headerName: "Resolved By", sortable: true, field: "resolvedBy", cellRenderer: CarResolutionComponent, width: 100, type: 'labelColumn' },
      { headerName: "When", valueGetter: (params) => this.timeDateGetter(params.data.resolutionDate), width: 120, type: "labelColumn", comparator: this.dateComparator },
      { headerName: "Resolution", sortable: true, field: "resolutionTypeDesc", cellRenderer: CarResolutionComponent, width: 150, type: 'labelColumn' },
      { headerName: "Resolution Detail", sortable: true, field: "resolutionNotes", cellRenderer: CarNotesComponent, autoHeight: true, width: 250, type: 'labelColumn' }
    ]);

    if (resolved) {
      allCols.push(
        { headerName: "Backup", sortable: true, field: "resolutionImages", cellRenderer: BackupImageComponent, autoHeight: true, width: 100, type: 'labelColumn' }
      )
    }

    return allCols;
  }

  private getStockItemsWithScanColDefs() {   //ok  ok
    return this.getStockItemsColDefs().concat([
      { headerName: "Photo", field: "regImageThumbnailUrl", width: 50, cellRenderer: ImageComponent, autoHeight: true, filter: null, type: 'labelColumn', cellClass: 'agAlignCentre' },
      { headerName: "Scan Id", sortable: true, field: "scanId", width: 100, type: 'labelColumn', cellClass: 'agAlignCentre' },
      { headerName: "Scanned By", sortable: true, field: "scannedBy", width: 100, type: 'labelColumn' },
      { headerName: "When", valueGetter: (params) => this.timeDateGetter(params.data.scannedDate), width: 120, type: "labelColumn", comparator: this.dateComparator },
      { headerName: "Location", sortable: true, field: "scanLocation", width: 100, type: 'labelColumn' },
      { headerName: "Distance", sortable: true, field: "distance", valueGetter: (params) => params.data.latitude === 0 && params.data.longitude === 0 ? 'Unknown' : params.data.distance, width: 100, type: 'milesColumn' },
      { headerName: "Longitude", sortable: true, field: "longitude", width: 100, type: 'longlat' },
      { headerName: "Latitude", sortable: true, field: "latitude", width: 100, type: 'longlat' }
    ]);
  }



  //--------------------------
  // Scans
  //--------------------------
  private getScansColDefs():ColDef[] {  //ok
    return [
      { headerName: "#", sortable: true,  cellClass: 'indexCell', field: 'index', autoHeight: true, type: 'labelColumn', suppressSizeToFit: true },
      { headerName: "Reg", field: "reg", maxWidth: 100, width: 150, cellRenderer: RegPlateComponent, autoHeight: false, type: 'regVinColumn', hide: this.constants.GlobalParams.find(x => x.name === 'NeverScanReg').boolValue },
      { headerName: "VIN", field: "vin", maxWidth: 100, width: 150, cellRenderer: ChassisComponent, type: 'regVinColumn' },
      { headerName: "Scan Site", field: "siteName", width: 100, type: "labelColumn" },
      { headerName: "Description", field: "description", width: 250, type: "labelColumn" },
      { headerName: "Notes", field: "comment", width: 250, type: 'labelColumn' },
      { headerName: "Photo",  width: 50, cellRenderer: ImageComponent, autoHeight: true, filter: null, type: 'labelColumn', cellClass: 'agAlignCentre' },
      { headerName: "Scan Id", field: "scanId", cellClass: 'agAlignCentre', autoHeight: true, width: 100, type: 'labelColumn' },
      { headerName: "Reg Confidence", 
        field: "regConfidence", 
        width: 100, 
        cellRenderer: ConfidenceComponent, 
        type: "numberColumn", 
        cellStyle: { 
          display: 'flex', 
          alignItems: 'center',
          height: '100%'
        },
        hide: this.constants.GlobalParams.find(x => x.name === 'NeverScanReg').boolValue 
      },
      { headerName: "VIN Confidence", 
        field: "vinConfidence", 
        width: 100, 
        cellRenderer: ConfidenceComponent, 
        cellStyle: { 
          display: 'flex', 
          alignItems: 'center',
          height: '100%'
        },
        type: "numberColumn" 
      },
      { headerName: "Scanned By", sortable: true, field: "scannedBy", width: 150, type: "labelColumn" },
      { headerName: "When", valueGetter: (params) => this.timeDateGetter(params.data.scannedDate), width: 120, type: "labelColumn", comparator: this.dateComparator },
      { headerName: "Location", field: "scanLocation", width: 100, type: "labelColumn" },
      { headerName: "Distance", sortable: true, field: "distance", valueGetter: (params) => params.data.latitude === 0 && params.data.longitude === 0 ? 'Unknown' : params.data.distance, width: 100, type: 'milesColumn' },
      { headerName: "Longitude", sortable: true, field: "longitude", width: 100, type: 'longlat' },
      { headerName: "Latitude", sortable: true, field: "latitude", width: 100, type: 'longlat' }
    ];
  }


  private getScansMatchedToRecItemColDefs() {  //ok ok
    const allCols = this.getScansColDefs().concat([
      { headerName: "Matching Type", sortable: true, field: "matchingItemTypeDesc", width: 200, type: 'labelColumn' },
      { headerName: "Matching Item", sortable: true, field: "matchingItemDesc", width: 200, type: 'labelColumn' },
      { headerName: "Matching Ref", sortable: true, field: "matchingItemRef", width: 225, type: 'labelColumn' },
      { headerName: "Matching Notes", sortable: true, field: "matchingItemComment", width: 225, type: 'labelColumn' },
    ]);
    return allCols.filter(x=>x.headerName!=='Confidence');
  }

  private getScanMatchedToOtherSiteStockItemColDefs() { //ok ok
    const allCols = this.getScansColDefs().concat([
      { headerName: "Other Site", cellClass: 'matchingSiteDescription', field: "otherSiteName", width: 175, type: "labelColumn" },
      { headerName: "Other Site Description", sortable: true, field: "matchingItemDesc", width: 175, type: 'labelColumn' },
      { headerName: "Other Site Ref", sortable: true, field: "matchingItemRef", width: 100, type: 'labelColumn' },
    ]);
    return allCols.filter(x=>x.headerName!=='Confidence');
  }

  private getDuplicateScansColDefs() { //ok ok
    const allCols = this.getScansColDefs().concat([

      { headerName: "Original Scanned By", sortable: true, field: "originalScannedBy", width: 100, type: 'labelColumn' },
      { headerName: "Original Location", sortable: true, field: "originalLocationDescription", width: 100, type: 'labelColumn' },
      { headerName: "Original Scan Id", sortable: true, field: "originalId", width: 100, type: 'labelColumn', cellClass: 'agAlignCentre' },
      { headerName: "Original Scanned", sortable: true, cellRenderer: (params) => { return this.timeDateGetter(params.data.originalScannedDate) }, width: 100, type: 'labelColumn' }
    ]);
    return allCols.filter(x=>x.headerName!=='Confidence');
  }

  private getScansWithResolutionColDefs(resolved?: boolean) {  //ok ok
    const allCols = this.getScansColDefs().concat([
      { headerName: "Resolved By", sortable: true, field: "resolvedBy", cellRenderer: CarResolutionComponent, width: 100, type: 'labelColumn' },
      { headerName: "Resolution", sortable: true, field: "resolutionTypeDesc", cellRenderer: CarResolutionComponent, width: 100, type: 'labelColumn' },
      { headerName: "Resolution Detail", sortable: true, field: "resolutionNotes", cellRenderer: CarNotesComponent, autoHeight: true, width: 250, type: 'labelColumn' }
    ]);
    
    if (resolved) {
      allCols.push(
        { headerName: "Backup", sortable: true, field: "resolutionImages", cellRenderer: BackupImageComponent, autoHeight: true, width: 100, type: 'labelColumn' }
      )
    }

    return allCols.filter(x=>x.headerName!=='Confidence');
  }

 



  private timeDateGetter(val:Date | null) {
    if(!val){return ''}
    return this.cphPipe.transform(val, 'shortDate', 0) + ' ' + this.cphPipe.transform(val, 'shortTime', 0)
  }


  private dateComparator(date1, date2) {
    var date1Number = date1 && new Date(date1).getTime();
    var date2Number = date2 && new Date(date2).getTime();

    if (date1Number == null && date2Number == null) {
      return 0;
    }

    if (date1Number == null) {
      return -1;
    } else if (date2Number == null) {
      return 1;
    }

    return date1Number - date2Number;
  }


}