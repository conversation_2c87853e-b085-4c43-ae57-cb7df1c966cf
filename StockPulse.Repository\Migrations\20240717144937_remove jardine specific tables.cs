﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace StockPulse.Repository.Migrations
{
    /// <inheritdoc />
    public partial class removejardinespecifictables : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "Jardine_Stocks",
                schema: "import");

            migrationBuilder.DropTable(
                name: "Jardine_Stocks_Backup",
                schema: "import");

            migrationBuilder.DropTable(
                name: "Jardine_TB",
                schema: "import");

            migrationBuilder.DropTable(
                name: "Jardine_TB_Backup",
                schema: "import");

            migrationBuilder.DropTable(
                name: "Jardine_WIP",
                schema: "import");

            migrationBuilder.DropTable(
                name: "Jardine_WIP_Backup",
                schema: "import");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "J<PERSON>ine_Stocks",
                schema: "import",
                columns: table => new
                {
                    Branch = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Comment = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    DIS = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Description = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    GroupDIS = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Reference = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Reg = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    StockCheckSite = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    StockType = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    StockValue = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Vin = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                });

            migrationBuilder.CreateTable(
                name: "Jardine_Stocks_Backup",
                schema: "import",
                columns: table => new
                {
                    BackupDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    Branch = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Comment = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    DIS = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Description = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    GroupDIS = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Reference = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Reg = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    StockCheckSite = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    StockType = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    StockValue = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    UniqueId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Vin = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                });

            migrationBuilder.CreateTable(
                name: "Jardine_TB",
                schema: "import",
                columns: table => new
                {
                    Balance = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Code = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Description = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    StockCheckSite = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                });

            migrationBuilder.CreateTable(
                name: "Jardine_TB_Backup",
                schema: "import",
                columns: table => new
                {
                    BackupDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    Balance = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Code = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Description = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    StockCheckSite = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    UniqueId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
                },
                constraints: table =>
                {
                });

            migrationBuilder.CreateTable(
                name: "Jardine_WIP",
                schema: "import",
                columns: table => new
                {
                    Comment = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Description = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ReconcilingBucket = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Reference = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Reg = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    StockCheckSite = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Vin = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                });

            migrationBuilder.CreateTable(
                name: "Jardine_WIP_Backup",
                schema: "import",
                columns: table => new
                {
                    BackupDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    Comment = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Description = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ReconcilingBucket = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Reference = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Reg = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    StockCheckSite = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    UniqueId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Vin = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                });
        }
    }
}
