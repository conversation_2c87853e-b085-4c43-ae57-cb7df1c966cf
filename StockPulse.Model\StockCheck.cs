﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System;

namespace StockPulse.Model
{
    public class StockCheck
    {
        [Key]
        public int Id { get; set; }


        public int SiteId { get; set; }
        [ForeignKey("SiteId")]
        public virtual Site Sites { get; set; }

        public int StatusId { get; set; }
        [ForeignKey("StatusId")]
        public virtual Status Statuses { get; set; }

        public int UserId { get; set; }
        [ForeignKey("UserId")]
        public virtual User User { get; set; }

        public int? ApprovedByAccountantId { get; set; }
        [ForeignKey("ApprovedByAccountantId")]
        public virtual User ApprovedByAccountant { get; set; }

        public int? ApprovedByGMId { get; set; }
        [ForeignKey("ApprovedByGMId")]
        public virtual User ApprovedByGM { get; set; }

        public int? ApprovedById { get; set; }
        [ForeignKey("ApprovedById")]
        public virtual User ApprovedBy { get; set; }


        [Column(TypeName = "datetime")]
        public DateTime Date { get; set; }

        [Column(TypeName = "datetime")]
        public DateTime LastUpdated { get; set; }

        public bool IsActive { get; set; }

        public bool HasSignoffImage { get; set; }

        public bool IsRegional { get; set; }

        public bool IsTotal { get; set; }

        public DateTime ReconciliationCompletedDate { get; set; }
        public DateTime ReconciliationApprovedDate { get; set; }

        public int ScannedInStock { get; set; }
        public int Unknowns { get; set; }
        public int UnknownOs { get; set; }
        public int Missings { get; set; }
        public int MissingOs { get; set; }

    }

}