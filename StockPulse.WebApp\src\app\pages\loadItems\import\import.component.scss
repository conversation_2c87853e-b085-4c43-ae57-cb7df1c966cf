#inputMaskChoose {
  margin: 0em 1em;
}

#headerRowPicker {
  margin: 1em 0;

  .label {
    margin-right: 0.75em;
    color: var(--bodyColour);
  }

  button:nth-of-type(1) {
    border-radius: 0.3em 0 0 0.3em;
  }
  
  button:nth-of-type(2) {
    border-radius: 0 0.3em 0.3em 0;
  }

  input {
    width: 4em;
    text-align: center;
    padding: 0.375em 0.5em;
  }
}

#topSetupBit{
  margin: 0em 0em 2em;
}

#excelTableHolderHolder {
  position: relative;
  overflow: hidden;
  max-height: 40%;
  margin-bottom: 1em;

  #excelTableHolder {
    max-width: 100%;
    margin: 0em auto;
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    overflow-y: hidden;
    overflow-x: auto;
  }

  #excelMockTable {

    .headerLetterHolder {
      position: relative;

      .headerFilterIndicator {
        position: absolute;
        right: 0px;
        top: 0px;
        color: white;
        background: var(--primary);
        padding: 0 4px;
        border-radius: 2px;
      }
    }

    tr.lettersRow th {
      padding: 0.3em;
      background: var(--grey80);
      text-align: center;
      // border-left: 1px solid var(--grey60);
      // border-right: 1px solid var(--grey60);
      font-weight: 400;
      cursor: pointer;
    }

    tr.lettersRow th:hover {
      background: var(--secondaryDark) !important;
    }

    tr.headingRow th {
      padding: 0.3em 1em;
      background: var(--grey90);
      text-align: center;
      text-align: center;
      font-weight: 400;
      // border: 1px solid var(--grey60);
      white-space: nowrap;
      height: 2.2em;
    }

    td {
      background: white;
      // border: 1px solid var(--grey60);
      padding: 0.3em 1em;
      line-height: 1.2em;
      white-space: nowrap;
    }

    th.highlight,
    td.highlight {
      background: var(--secondaryLightest) !important
    }
  }
}

#excelMockTable,
#excelMockTable td {
  box-shadow: inset 1px -1px var(--grey80);
}
    
#excelMockTable th {
  box-shadow: inset 1px 1px var(--grey60), 0 1px var(--grey60);
}

#tableHolder {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow-y: hidden;
  margin-top: 2em;
  display: flex;
  flex-direction: column;
}

table.saveTable {
  width: 90%;
  margin: 2em auto;

  svg {
    color: var(--secondary)
  }

  tr {
    cursor: pointer;
  }
}

table#importFilters {
  width: 100%;
  margin: 2em auto;

  td {
    padding: 0.5em 0;
  }
}

.mapName {
  width: 100%;
  margin: 1em auto;
  line-height: 2em;
}

.topRowButtons {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5em;
}

.uploadFileWrapper {
  display: flex;
  align-items: center;

  .chooseFileInput {
    width: 0.1px;
    height: 0.1px;
    opacity: 0;
    overflow: hidden;
    position: absolute;
    z-index: -1;
  }

  .chooseFileInput+label {
    background-color: #323130;
    color: #ffffff;
    padding: 0.375rem 0.75rem;
    display: inline-block;
    font-weight: 400;
    text-align: center;
    vertical-align: middle;
    margin-bottom: 0;
    transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out;
    cursor: pointer;

    fa-icon {
      margin-right: 0.75em;
    }
  }

  .chooseFileInput:focus+label,
  .chooseFileInput+label:hover {
    border: 1px solid transparent;
    background-color: var(--secondaryLight);
    color: #323130;
  }

  .fileName {
    margin-right: 0.75em;
    color: var(--bodyColour);
  }
}

#saveMask {
  border: none;
  // border-radius: 0;

  fa-icon {
    margin-right: 0.75em;
  }
}

.importVehicleDetails {
  // border-radius: 0;
  border: none;
  margin-right: 1em;

  fa-icon {
    margin-right: 0.75em;
  }
}

.tablesHolder {
  display: flex;
  flex-grow: 1;
  flex: 1 1 auto;
  overflow: hidden;
  flex-direction: column;
}

.importVehicleDetails.disable {
  cursor: default;
  opacity: 0.65;
}

.requiredColumns {
  height: 20px;
  color: var(--danger);
}

$primary: #323130;
$secondary: #FFBF00;

.btn.highlight {
  background: $secondary !important;
  margin-left: 10px;
}

.btn.noHighlight {
  // background: $primary !important;
  color: white !important;
  margin-left: 10px;
}

importtable {
  flex: 1;
  overflow-y: auto;
}

.maskSubHeader {
  color: white;
  text-align: left;
  padding: 0 1em;
  font-weight: bold;
}

#sitesDropdownMenu {
  min-width: 250px;
  max-height: 90vh;
  overflow-y: auto;
  background-color: var(--primaryLighter);
  padding: 0;
}

#sitesMenuItems button,
#selectAll button {
  height: 30px;
  width: 100%;
  display: block;
}

#sitesMenuItems {
  max-height: 30vh;
  overflow-y: auto;
}

@media (min-width: 1440px) {
  #sitesMenuItems {
      max-height: 40vh;
      overflow-y: auto;
  }
}

.dropdown-body {
  max-height: 45vh;
  overflow-y: auto;
}

.dropdown-footer {
  background: #035359;
}

#sitesMenuItems button {
  text-align: left;
}

#selectAll button:nth-of-type(1) {
  border-top: 1px solid #444444;
}

#confirmCancelButtons {
  display: flex;
  border-top: 1px solid #444444;

  button {
      width: 100%;
  }
}

#confirmCancelButtons button {
  text-align: center;
}

.checkboxIcon {
  color: var(--secondary);
}

#sitesMenuItems button:focus {
  background-color: var(--primaryLight);
  color: var(--bodyColour);
}

#sitesMenuItems button:hover .checkboxIcon {
  color: #000000;
}

.toggleNoCaret::after {
  content: none;
}

#bottomInstructionRowContainer {
  width: calc(100% - 10em);
}

.sheetName {
  margin-right: 0.75em;
  color: var(--bodyColour);
}

.lettersRow {
  height: 2.2em;
  position: sticky;
  top: 0;
}

.headingRow:first-of-type {
  position: sticky;
}

#excelMockTableContainer {
  flex: 1;
  overflow: auto;
}