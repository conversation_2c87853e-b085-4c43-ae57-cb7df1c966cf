﻿using OfficeOpenXml;

using System;

using System.Linq;
using System.Text;
using System.IO;
using ExcelDataReader;
using System.Data;
using System.Collections.Generic;

namespace StockPulse.Loader.Services
{

    public static class GetDataFromFilesService
    {

        public static string[] GetRowsCsv(string filePath)
        {
            string[] result = File.ReadAllLines(filePath);
            return result;
        }



        public static string[] GetRowsCsvANSI(string filePath)
        {
            string[] res = File.ReadAllLines(filePath, Encoding.UTF7);

            return res;
        }


        public static HeadersAndRows GetExcelFileContents(string filePath, int headerRowNumber, int headerSkip)
        {


            DataRowCollection rowsFromHelper = GetRowsExcelNew(filePath);
            List<DataRow> rowsIn = rowsFromHelper.OfType<DataRow>().ToList();

            DataRow headerRow = rowsIn.Skip(headerRowNumber).First();
            List<string> headers = headerRow.ItemArray.ToList().Select(x => x.ToString().Trim()).ToList();

            List<List<string>> rows = new List<List<string>>();

            int i = 0;

            foreach (var rowIn in rowsIn)
            {
                //List<string> cells = new List<string>()
                if (i >= headerSkip)
                {
                    List<string> row = rowIn.ItemArray.ToList().Select(x => x.ToString()).ToList();
                    rows.Add(row);
                }
                i++;

            }

            HeadersAndRows result = new HeadersAndRows()
            {
                headers = headers,
                rows = rows
            };

            return result;

        }


        public static DataRowCollection GetRowsExcelNew(string filePath, int? tableNumber = null)
        {
            try
            {

                System.Text.Encoding.RegisterProvider(System.Text.CodePagesEncodingProvider.Instance);

                using (FileStream stream = File.Open(filePath, FileMode.Open, FileAccess.Read))
                {
                    // Auto-detect format, supports:
                    //  - Binary Excel files (2.0-2003 format; *.xls)
                    //  - OpenXml Excel files (2007 format; *.xlsx)
                    using (var reader = ExcelReaderFactory.CreateReader(stream))
                    {
                        // Choose one of either 1 or 2:

                        // 1. Use the reader methods
                        do
                        {
                            while (reader.Read())
                            {
                                // reader.GetDouble(0);
                            }
                        } while (reader.NextResult());

                        // 2. Use the AsDataSet extension method
                        // The result of each spreadsheet is in result.Tables
                        var result = reader.AsDataSet(new ExcelDataSetConfiguration()
                        {
                            ConfigureDataTable = (tableReader) => new ExcelDataTableConfiguration()
                            {
                                FilterRow = (rowReader) =>
                                {
                                    int progress = (int)Math.Ceiling((decimal)rowReader.Depth / (decimal)rowReader.RowCount * (decimal)100);
                                // progress is in the range 0..100
                                return true;
                                }
                            }
                        });

                        var rows = tableNumber == null ? result.Tables[0].Rows : result.Tables[(int) tableNumber].Rows;

                        return rows;

                    }
                }
            }
            catch  (Exception ex)
            {
                { }
                throw new Exception("failed GetRowsExcelNew");
            }

        }

        // This version is outdated 
        public static string[] GetRowsExcel(string filePath)
        {

            using (ExcelPackage xlPackage = new ExcelPackage(new FileInfo(filePath)))
            {
                var myWorksheet = xlPackage.Workbook.Worksheets.First(); //select sheet here
                var totalRows = myWorksheet.Dimension.End.Row;
                var totalColumns = myWorksheet.Dimension.End.Column;

                var headers = myWorksheet.Cells[1, 1, 1, totalColumns].Select(c => c.Value == null ? string.Empty : c.Value.ToString());
                // Logger.Debug("Headers : " + string.Join(",", headers));

                var sb = new StringBuilder(); //this is your data
                for (int rowNum = 1; rowNum <= totalRows; rowNum++) //select starting row here
                {
                    var row = myWorksheet.Cells[rowNum, 1, rowNum, totalColumns].Select(c => c.Value == null ? string.Empty : c.Value.ToString());
                    sb.AppendLine(string.Join("|", row));
                }

                return sb.ToString().Split(Environment.NewLine.ToCharArray());

            }
        }


    }



}
