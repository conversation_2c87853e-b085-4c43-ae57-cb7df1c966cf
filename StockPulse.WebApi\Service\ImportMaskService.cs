﻿using StockPulse.WebApi.DataAccess;
using StockPulse.Model;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace StockPulse.WebApi.Service
{
    public interface IImportMaskService
    {
        Task<IEnumerable<ImportMaskWithCreatedBy>> GetImportMasks(int userId);
        Task SaveExistingImportMask(ImportMaskSaveWithId item, int userId);
        Task SaveNewImportMask(ImportMaskSave item, int userId);
        Task UpdateImportMasks(ImportMaskUpdateParams parms);
        Task DeleteImportMask(int importMaskId);
    }

    public class ImportMaskService : IImportMaskService
    {
        //properties of the service
        private readonly IImportMaskDataAccess importMaskDataAccess;

        //constructor
        public ImportMaskService(IImportMaskDataAccess importMaskDataAccess)
        {
            this.importMaskDataAccess = importMaskDataAccess;
        }


        //methods of the service
        public async Task<IEnumerable<ImportMaskWithCreatedBy>> GetImportMasks(int userId)
        {
            return await importMaskDataAccess.GetImportMasks(userId);
        }

        public async Task SaveExistingImportMask(ImportMaskSaveWithId item, int userId)
        {
            await importMaskDataAccess.SaveExistingImportMask(item, userId);
        }

        public async Task SaveNewImportMask(ImportMaskSave item, int userId)
        {
            await importMaskDataAccess.SaveNewImportMask(item, userId);
        }

        public async Task UpdateImportMasks(ImportMaskUpdateParams parms)
        {
            await importMaskDataAccess.UpdateImportMasks(parms);
        }

        public async Task DeleteImportMask(int importMaskId)
        {
            await importMaskDataAccess.DeleteImportMask(importMaskId);
        }
    }
}
