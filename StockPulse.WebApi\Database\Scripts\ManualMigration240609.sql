IF OBJECT_ID(N'[__EFMigrationsHistory]') IS NULL
BEGIN
    CREATE TABLE [__EFMigrationsHistory] (
        [MigrationId] nvarchar(150) NOT NULL,
        [ProductVersion] nvarchar(32) NOT NULL,
        CONSTRAINT [PK___EFMigrationsHistory] PRIMARY KEY ([MigrationId])
    );
END;
GO

BEGIN TRANSACTION;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20230425130832_InitialCreate'
)
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20230425130832_InitialCreate', N'8.0.4');
END;
GO

COMMIT;
GO

BEGIN TRANSACTION;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20230530112058_Stockcheck - rename - Reconciliation col'
)
BEGIN
    EXEC sp_rename N'[dbo].[StockChecks].[ReconcilliationCompletedDate]', N'ReconciliationCompletedDate', N'COLUMN';
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20230530112058_Stockcheck - rename - Reconciliation col'
)
BEGIN
    EXEC sp_rename N'[dbo].[StockChecks].[ReconcilliationApprovedDate]', N'ReconciliationApprovedDate', N'COLUMN';
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20230530112058_Stockcheck - rename - Reconciliation col'
)
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20230530112058_Stockcheck - rename - Reconciliation col', N'8.0.4');
END;
GO

COMMIT;
GO

BEGIN TRANSACTION;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20230619122110_OverrideLongLatBoolToSites'
)
BEGIN
    ALTER TABLE [dbo].[Sites] ADD [OverrideLongLat] bit NOT NULL DEFAULT CAST(0 AS bit);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20230619122110_OverrideLongLatBoolToSites'
)
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20230619122110_OverrideLongLatBoolToSites', N'8.0.4');
END;
GO

COMMIT;
GO

BEGIN TRANSACTION;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20230720121516_Add LogMessages table'
)
BEGIN
    CREATE TABLE [dbo].[LogMessages] (
        [Id] int NOT NULL IDENTITY,
        [Job] nvarchar(50) NULL,
        [SourceDate] datetime2 NOT NULL,
        [FinishDate] datetime2 NULL,
        [StartCount] int NOT NULL,
        [ProcessedCount] int NOT NULL,
        [AddedCount] int NOT NULL,
        [RemovedCount] int NOT NULL,
        [ChangedCount] int NOT NULL,
        [FinishCount] int NOT NULL,
        [ErrorCount] int NOT NULL,
        [IsCompleted] bit NOT NULL,
        [FailNotes] nvarchar(50) NULL,
        [InterpretFileSeconds] int NOT NULL,
        [UpdateDbSeconds] int NOT NULL,
        [FinalPartsSeconds] int NOT NULL,
        CONSTRAINT [PK_LogMessages] PRIMARY KEY ([Id])
    );
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20230720121516_Add LogMessages table'
)
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20230720121516_Add LogMessages table', N'8.0.4');
END;
GO

COMMIT;
GO

BEGIN TRANSACTION;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20230728145439_Update ResolutionType Description to 5000 chars'
)
BEGIN
    DECLARE @var0 sysname;
    SELECT @var0 = [d].[name]
    FROM [sys].[default_constraints] [d]
    INNER JOIN [sys].[columns] [c] ON [d].[parent_column_id] = [c].[column_id] AND [d].[parent_object_id] = [c].[object_id]
    WHERE ([d].[parent_object_id] = OBJECT_ID(N'[dbo].[ResolutionTypes]') AND [c].[name] = N'Description');
    IF @var0 IS NOT NULL EXEC(N'ALTER TABLE [dbo].[ResolutionTypes] DROP CONSTRAINT [' + @var0 + '];');
    ALTER TABLE [dbo].[ResolutionTypes] ALTER COLUMN [Description] nvarchar(max) NULL;
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20230728145439_Update ResolutionType Description to 5000 chars'
)
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20230728145439_Update ResolutionType Description to 5000 chars', N'8.0.4');
END;
GO

COMMIT;
GO

BEGIN TRANSACTION;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20230817161250_added new cols to scan'
)
BEGIN
    ALTER TABLE [dbo].[Scans] ADD [InterpretedReg] nvarchar(50) NULL;
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20230817161250_added new cols to scan'
)
BEGIN
    ALTER TABLE [dbo].[Scans] ADD [InterpretedVin] nvarchar(50) NULL;
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20230817161250_added new cols to scan'
)
BEGIN
    ALTER TABLE [dbo].[Scans] ADD [IsRegEditedOnDevice] bit NOT NULL DEFAULT CAST(0 AS bit);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20230817161250_added new cols to scan'
)
BEGIN
    ALTER TABLE [dbo].[Scans] ADD [IsVinEdited] bit NOT NULL DEFAULT CAST(0 AS bit);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20230817161250_added new cols to scan'
)
BEGIN
    ALTER TABLE [dbo].[Scans] ADD [IsVinEditedOnDevice] bit NOT NULL DEFAULT CAST(0 AS bit);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20230817161250_added new cols to scan'
)
BEGIN
    ALTER TABLE [dbo].[Scans] ADD [VinConfidence] decimal(15,3) NOT NULL DEFAULT 0.0;
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20230817161250_added new cols to scan'
)
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20230817161250_added new cols to scan', N'8.0.4');
END;
GO

COMMIT;
GO

BEGIN TRANSACTION;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20230817162114_renamed isRegEdited field in scans'
)
BEGIN
    EXEC sp_rename N'[dbo].[Scans].[IsVinEdited]', N'IsVinEditedOnWeb', N'COLUMN';
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20230817162114_renamed isRegEdited field in scans'
)
BEGIN
    EXEC sp_rename N'[dbo].[Scans].[IsEdited]', N'IsRegEditedOnWeb', N'COLUMN';
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20230817162114_renamed isRegEdited field in scans'
)
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20230817162114_renamed isRegEdited field in scans', N'8.0.4');
END;
GO

COMMIT;
GO

BEGIN TRANSACTION;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20230818092052_Update ResolutionType BackupRequired column length'
)
BEGIN
    DECLARE @var1 sysname;
    SELECT @var1 = [d].[name]
    FROM [sys].[default_constraints] [d]
    INNER JOIN [sys].[columns] [c] ON [d].[parent_column_id] = [c].[column_id] AND [d].[parent_object_id] = [c].[object_id]
    WHERE ([d].[parent_object_id] = OBJECT_ID(N'[dbo].[ResolutionTypes]') AND [c].[name] = N'Description');
    IF @var1 IS NOT NULL EXEC(N'ALTER TABLE [dbo].[ResolutionTypes] DROP CONSTRAINT [' + @var1 + '];');
    ALTER TABLE [dbo].[ResolutionTypes] ALTER COLUMN [Description] nvarchar(250) NULL;
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20230818092052_Update ResolutionType BackupRequired column length'
)
BEGIN
    DECLARE @var2 sysname;
    SELECT @var2 = [d].[name]
    FROM [sys].[default_constraints] [d]
    INNER JOIN [sys].[columns] [c] ON [d].[parent_column_id] = [c].[column_id] AND [d].[parent_object_id] = [c].[object_id]
    WHERE ([d].[parent_object_id] = OBJECT_ID(N'[dbo].[ResolutionTypes]') AND [c].[name] = N'BackupRequired');
    IF @var2 IS NOT NULL EXEC(N'ALTER TABLE [dbo].[ResolutionTypes] DROP CONSTRAINT [' + @var2 + '];');
    ALTER TABLE [dbo].[ResolutionTypes] ALTER COLUMN [BackupRequired] nvarchar(max) NULL;
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20230818092052_Update ResolutionType BackupRequired column length'
)
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20230818092052_Update ResolutionType BackupRequired column length', N'8.0.4');
END;
GO

COMMIT;
GO

BEGIN TRANSACTION;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20230822154253_migration to divide reg % scores by 10'
)
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20230822154253_migration to divide reg % scores by 10', N'8.0.4');
END;
GO

COMMIT;
GO

BEGIN TRANSACTION;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20230905111219_add IsAgencyStock to stockitem 2'
)
BEGIN
    ALTER TABLE [dbo].[StockItems] ADD [IsAgencyStock] bit NOT NULL DEFAULT CAST(0 AS bit);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20230905111219_add IsAgencyStock to stockitem 2'
)
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20230905111219_add IsAgencyStock to stockitem 2', N'8.0.4');
END;
GO

COMMIT;
GO

BEGIN TRANSACTION;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116121212_add stock item and file item loader tables'
)
BEGIN
    CREATE TABLE [import].[FileImport_Loads] (
        [Id] int NOT NULL,
        [FileName] nvarchar(50) NULL,
        [FileDate] datetime2 NOT NULL,
        [LoadDate] datetime2 NOT NULL,
        [LoadedByUserId] int NOT NULL,
        CONSTRAINT [FK_FileImport_Loads_Users_LoadedByUserId] FOREIGN KEY ([LoadedByUserId]) REFERENCES [dbo].[Users] ([Id]) ON DELETE CASCADE
    );
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116121212_add stock item and file item loader tables'
)
BEGIN
    CREATE TABLE [import].[StockItem_Loads] (
        [Id] int NOT NULL,
        [SourceReportId] int NOT NULL,
        [Reg] nvarchar(50) NULL,
        [Vin] nvarchar(50) NULL,
        [Description] nvarchar(250) NULL,
        [DIS] int NULL,
        [GroupDIS] int NULL,
        [Branch] nvarchar(50) NULL,
        [Comment] nvarchar(500) NULL,
        [StockType] nvarchar(50) NULL,
        [Reference] nvarchar(50) NULL,
        [StockValue] decimal(15,3) NOT NULL,
        [SiteId] int NOT NULL,
        CONSTRAINT [FK_StockItem_Loads_Sites_SiteId] FOREIGN KEY ([SiteId]) REFERENCES [dbo].[Sites] ([Id]) ON DELETE CASCADE,
        CONSTRAINT [FK_StockItem_Loads_SourceReports_SourceReportId] FOREIGN KEY ([SourceReportId]) REFERENCES [dbo].[SourceReports] ([Id]) ON DELETE CASCADE
    );
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116121212_add stock item and file item loader tables'
)
BEGIN
    CREATE INDEX [IX_FileImport_Loads_LoadedByUserId] ON [import].[FileImport_Loads] ([LoadedByUserId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116121212_add stock item and file item loader tables'
)
BEGIN
    CREATE INDEX [IX_StockItem_Loads_SiteId] ON [import].[StockItem_Loads] ([SiteId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116121212_add stock item and file item loader tables'
)
BEGIN
    CREATE INDEX [IX_StockItem_Loads_SourceReportId] ON [import].[StockItem_Loads] ([SourceReportId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116121212_add stock item and file item loader tables'
)
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20240116121212_add stock item and file item loader tables', N'8.0.4');
END;
GO

COMMIT;
GO

BEGIN TRANSACTION;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116122229_add file item (non-import)'
)
BEGIN
    CREATE TABLE [dbo].[FileImports] (
        [Id] int NOT NULL IDENTITY,
        [FileName] nvarchar(50) NULL,
        [FileDate] datetime2 NOT NULL,
        [LoadDate] datetime2 NOT NULL,
        [LoadedByUserId] int NOT NULL,
        CONSTRAINT [PK_FileImports] PRIMARY KEY ([Id]),
        CONSTRAINT [FK_FileImports_Users_LoadedByUserId] FOREIGN KEY ([LoadedByUserId]) REFERENCES [dbo].[Users] ([Id]) ON DELETE CASCADE
    );
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116122229_add file item (non-import)'
)
BEGIN
    CREATE INDEX [IX_FileImports_LoadedByUserId] ON [dbo].[FileImports] ([LoadedByUserId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116122229_add file item (non-import)'
)
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20240116122229_add file item (non-import)', N'8.0.4');
END;
GO

COMMIT;
GO

BEGIN TRANSACTION;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116122537_add FK to file import'
)
BEGIN
    ALTER TABLE [import].[StockItem_Loads] ADD [FileImportId] int NOT NULL DEFAULT 0;
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116122537_add FK to file import'
)
BEGIN
    CREATE INDEX [IX_StockItem_Loads_FileImportId] ON [import].[StockItem_Loads] ([FileImportId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116122537_add FK to file import'
)
BEGIN
    ALTER TABLE [import].[StockItem_Loads] ADD CONSTRAINT [FK_StockItem_Loads_FileImports_FileImportId] FOREIGN KEY ([FileImportId]) REFERENCES [dbo].[FileImports] ([Id]) ON DELETE CASCADE;
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116122537_add FK to file import'
)
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20240116122537_add FK to file import', N'8.0.4');
END;
GO

COMMIT;
GO

BEGIN TRANSACTION;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116140839_delete main file import table and change table names'
)
BEGIN
    ALTER TABLE [import].[StockItem_Loads] DROP CONSTRAINT [FK_StockItem_Loads_FileImports_FileImportId];
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116140839_delete main file import table and change table names'
)
BEGIN
    DROP TABLE [dbo].[FileImports];
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116140839_delete main file import table and change table names'
)
BEGIN
    DROP INDEX [IX_StockItem_Loads_FileImportId] ON [import].[StockItem_Loads];
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116140839_delete main file import table and change table names'
)
BEGIN
    DECLARE @var3 sysname;
    SELECT @var3 = [d].[name]
    FROM [sys].[default_constraints] [d]
    INNER JOIN [sys].[columns] [c] ON [d].[parent_column_id] = [c].[column_id] AND [d].[parent_object_id] = [c].[object_id]
    WHERE ([d].[parent_object_id] = OBJECT_ID(N'[import].[StockItem_Loads]') AND [c].[name] = N'FileImportId');
    IF @var3 IS NOT NULL EXEC(N'ALTER TABLE [import].[StockItem_Loads] DROP CONSTRAINT [' + @var3 + '];');
    ALTER TABLE [import].[StockItem_Loads] DROP COLUMN [FileImportId];
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116140839_delete main file import table and change table names'
)
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20240116140839_delete main file import table and change table names', N'8.0.4');
END;
GO

COMMIT;
GO

BEGIN TRANSACTION;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116142039_remove FK from stockitem loader table x2'
)
BEGIN
    ALTER TABLE [import].[FileImport_Loads] DROP CONSTRAINT [FK_FileImport_Loads_Users_LoadedByUserId];
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116142039_remove FK from stockitem loader table x2'
)
BEGIN
    ALTER TABLE [import].[StockItem_Loads] DROP CONSTRAINT [FK_StockItem_Loads_Sites_SiteId];
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116142039_remove FK from stockitem loader table x2'
)
BEGIN
    ALTER TABLE [import].[StockItem_Loads] DROP CONSTRAINT [FK_StockItem_Loads_SourceReports_SourceReportId];
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116142039_remove FK from stockitem loader table x2'
)
BEGIN
    DROP INDEX [IX_StockItem_Loads_SiteId] ON [import].[StockItem_Loads];
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116142039_remove FK from stockitem loader table x2'
)
BEGIN
    DROP INDEX [IX_StockItem_Loads_SourceReportId] ON [import].[StockItem_Loads];
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116142039_remove FK from stockitem loader table x2'
)
BEGIN
    EXEC sp_rename N'[import].[StockItem_Loads]', N'StockItems';
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116142039_remove FK from stockitem loader table x2'
)
BEGIN
    EXEC sp_rename N'[import].[FileImport_Loads]', N'FileImports';
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116142039_remove FK from stockitem loader table x2'
)
BEGIN
    EXEC sp_rename N'[import].[FileImports].[IX_FileImport_Loads_LoadedByUserId]', N'IX_FileImports_LoadedByUserId', N'INDEX';
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116142039_remove FK from stockitem loader table x2'
)
BEGIN
    ALTER TABLE [import].[StockItems] ADD [FileImportId] int NOT NULL DEFAULT 0;
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116142039_remove FK from stockitem loader table x2'
)
BEGIN
    ALTER TABLE [import].[FileImports] ADD CONSTRAINT [FK_FileImports_Users_LoadedByUserId] FOREIGN KEY ([LoadedByUserId]) REFERENCES [dbo].[Users] ([Id]) ON DELETE CASCADE;
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116142039_remove FK from stockitem loader table x2'
)
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20240116142039_remove FK from stockitem loader table x2', N'8.0.4');
END;
GO

COMMIT;
GO

BEGIN TRANSACTION;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116145122_fix issue with file import table v2'
)
BEGIN
    DECLARE @var4 sysname;
    SELECT @var4 = [d].[name]
    FROM [sys].[default_constraints] [d]
    INNER JOIN [sys].[columns] [c] ON [d].[parent_column_id] = [c].[column_id] AND [d].[parent_object_id] = [c].[object_id]
    WHERE ([d].[parent_object_id] = OBJECT_ID(N'[import].[FileImports]') AND [c].[name] = N'Id');
    IF @var4 IS NOT NULL EXEC(N'ALTER TABLE [import].[FileImports] DROP CONSTRAINT [' + @var4 + '];');
    ALTER TABLE [import].[FileImports] DROP COLUMN [Id];
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116145122_fix issue with file import table v2'
)
BEGIN
    DECLARE @var5 sysname;
    SELECT @var5 = [d].[name]
    FROM [sys].[default_constraints] [d]
    INNER JOIN [sys].[columns] [c] ON [d].[parent_column_id] = [c].[column_id] AND [d].[parent_object_id] = [c].[object_id]
    WHERE ([d].[parent_object_id] = OBJECT_ID(N'[import].[StockItems]') AND [c].[name] = N'FileImportId');
    IF @var5 IS NOT NULL EXEC(N'ALTER TABLE [import].[StockItems] DROP CONSTRAINT [' + @var5 + '];');
    ALTER TABLE [import].[StockItems] ALTER COLUMN [FileImportId] int NULL;
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116145122_fix issue with file import table v2'
)
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20240116145122_fix issue with file import table v2', N'8.0.4');
END;
GO

COMMIT;
GO

BEGIN TRANSACTION;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116145210_fix issue with file import table v3'
)
BEGIN
    ALTER TABLE [import].[FileImports] ADD [Id] int NOT NULL IDENTITY;
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116145210_fix issue with file import table v3'
)
BEGIN
    ALTER TABLE [import].[FileImports] ADD CONSTRAINT [PK_FileImports] PRIMARY KEY ([Id]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116145210_fix issue with file import table v3'
)
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20240116145210_fix issue with file import table v3', N'8.0.4');
END;
GO

COMMIT;
GO

BEGIN TRANSACTION;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116145434_add FK to file import v2'
)
BEGIN
    ALTER TABLE [dbo].[StockItems] ADD [FileImportId] int NULL;
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116145434_add FK to file import v2'
)
BEGIN
    CREATE INDEX [IX_StockItems_FileImportId] ON [dbo].[StockItems] ([FileImportId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116145434_add FK to file import v2'
)
BEGIN
    ALTER TABLE [dbo].[StockItems] ADD CONSTRAINT [FK_StockItems_FileImports_FileImportId] FOREIGN KEY ([FileImportId]) REFERENCES [import].[FileImports] ([Id]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116145434_add FK to file import v2'
)
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20240116145434_add FK to file import v2', N'8.0.4');
END;
GO

COMMIT;
GO

BEGIN TRANSACTION;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116150420_add FK to file import for recon and fl'
)
BEGIN
    ALTER TABLE [dbo].[ReconcilingItems] ADD [FileImportId] int NULL;
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116150420_add FK to file import for recon and fl'
)
BEGIN
    ALTER TABLE [dbo].[FinancialLines] ADD [FileImportId] int NULL;
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116150420_add FK to file import for recon and fl'
)
BEGIN
    CREATE INDEX [IX_ReconcilingItems_FileImportId] ON [dbo].[ReconcilingItems] ([FileImportId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116150420_add FK to file import for recon and fl'
)
BEGIN
    CREATE INDEX [IX_FinancialLines_FileImportId] ON [dbo].[FinancialLines] ([FileImportId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116150420_add FK to file import for recon and fl'
)
BEGIN
    ALTER TABLE [dbo].[FinancialLines] ADD CONSTRAINT [FK_FinancialLines_FileImports_FileImportId] FOREIGN KEY ([FileImportId]) REFERENCES [import].[FileImports] ([Id]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116150420_add FK to file import for recon and fl'
)
BEGIN
    ALTER TABLE [dbo].[ReconcilingItems] ADD CONSTRAINT [FK_ReconcilingItems_FileImports_FileImportId] FOREIGN KEY ([FileImportId]) REFERENCES [import].[FileImports] ([Id]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116150420_add FK to file import for recon and fl'
)
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20240116150420_add FK to file import for recon and fl', N'8.0.4');
END;
GO

COMMIT;
GO

BEGIN TRANSACTION;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116153020_add reonciling items to import'
)
BEGIN
    CREATE TABLE [import].[ReconcilingItems] (
        [Id] int NOT NULL,
        [ReconcilingItemTypeId] int NOT NULL,
        [Reg] nvarchar(50) NULL,
        [Vin] nvarchar(50) NULL,
        [Description] nvarchar(250) NULL,
        [Comment] nvarchar(500) NULL,
        [Reference] nvarchar(50) NULL,
        [SourceReportId] int NOT NULL,
        [SiteId] int NOT NULL,
        [FileImportId] int NULL,
        CONSTRAINT [FK_ReconcilingItems_ReconcilingItemTypes_ReconcilingItemTypeId] FOREIGN KEY ([ReconcilingItemTypeId]) REFERENCES [dbo].[ReconcilingItemTypes] ([Id]) ON DELETE CASCADE,
        CONSTRAINT [FK_ReconcilingItems_Sites_SiteId] FOREIGN KEY ([SiteId]) REFERENCES [dbo].[Sites] ([Id]) ON DELETE CASCADE,
        CONSTRAINT [FK_ReconcilingItems_SourceReports_SourceReportId] FOREIGN KEY ([SourceReportId]) REFERENCES [dbo].[SourceReports] ([Id]) ON DELETE CASCADE
    );
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116153020_add reonciling items to import'
)
BEGIN
    CREATE INDEX [IX_ReconcilingItems_ReconcilingItemTypeId] ON [import].[ReconcilingItems] ([ReconcilingItemTypeId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116153020_add reonciling items to import'
)
BEGIN
    CREATE INDEX [IX_ReconcilingItems_SiteId] ON [import].[ReconcilingItems] ([SiteId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116153020_add reonciling items to import'
)
BEGIN
    CREATE INDEX [IX_ReconcilingItems_SourceReportId] ON [import].[ReconcilingItems] ([SourceReportId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116153020_add reonciling items to import'
)
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20240116153020_add reonciling items to import', N'8.0.4');
END;
GO

COMMIT;
GO

BEGIN TRANSACTION;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116155636_add FL to import'
)
BEGIN
    CREATE TABLE [import].[FinancialLines] (
        [Id] int NOT NULL,
        [Code] int NULL,
        [AccountDescription] nvarchar(250) NULL,
        [Balance] decimal(15,3) NOT NULL,
        [FileImportId] int NULL,
        CONSTRAINT [FK_FinancialLines_FileImports_FileImportId] FOREIGN KEY ([FileImportId]) REFERENCES [import].[FileImports] ([Id])
    );
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116155636_add FL to import'
)
BEGIN
    CREATE INDEX [IX_FinancialLines_FileImportId] ON [import].[FinancialLines] ([FileImportId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116155636_add FL to import'
)
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20240116155636_add FL to import', N'8.0.4');
END;
GO

COMMIT;
GO

BEGIN TRANSACTION;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116155929_add site id to import fl'
)
BEGIN
    ALTER TABLE [import].[FinancialLines] ADD [SiteId] int NOT NULL DEFAULT 0;
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116155929_add site id to import fl'
)
BEGIN
    CREATE INDEX [IX_FinancialLines_SiteId] ON [import].[FinancialLines] ([SiteId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116155929_add site id to import fl'
)
BEGIN
    ALTER TABLE [import].[FinancialLines] ADD CONSTRAINT [FK_FinancialLines_Sites_SiteId] FOREIGN KEY ([SiteId]) REFERENCES [dbo].[Sites] ([Id]) ON DELETE CASCADE;
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116155929_add site id to import fl'
)
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20240116155929_add site id to import fl', N'8.0.4');
END;
GO

COMMIT;
GO

BEGIN TRANSACTION;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116160520_add SiteDescriptionDictionary'
)
BEGIN
    CREATE TABLE [import].[SiteDescriptionDictionary] (
        [Id] int NOT NULL,
        [Description] nvarchar(250) NULL,
        [DealerGroupId] int NOT NULL,
        [SiteId] int NOT NULL,
        CONSTRAINT [FK_SiteDescriptionDictionary_DealerGroup_DealerGroupId] FOREIGN KEY ([DealerGroupId]) REFERENCES [dbo].[DealerGroup] ([Id]) ON DELETE CASCADE,
        CONSTRAINT [FK_SiteDescriptionDictionary_Sites_SiteId] FOREIGN KEY ([SiteId]) REFERENCES [dbo].[Sites] ([Id]) ON DELETE CASCADE
    );
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116160520_add SiteDescriptionDictionary'
)
BEGIN
    CREATE INDEX [IX_SiteDescriptionDictionary_DealerGroupId] ON [import].[SiteDescriptionDictionary] ([DealerGroupId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116160520_add SiteDescriptionDictionary'
)
BEGIN
    CREATE INDEX [IX_SiteDescriptionDictionary_SiteId] ON [import].[SiteDescriptionDictionary] ([SiteId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116160520_add SiteDescriptionDictionary'
)
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20240116160520_add SiteDescriptionDictionary', N'8.0.4');
END;
GO

COMMIT;
GO

BEGIN TRANSACTION;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116161100_add SiteDescrip'
)
BEGIN
    DECLARE @var6 sysname;
    SELECT @var6 = [d].[name]
    FROM [sys].[default_constraints] [d]
    INNER JOIN [sys].[columns] [c] ON [d].[parent_column_id] = [c].[column_id] AND [d].[parent_object_id] = [c].[object_id]
    WHERE ([d].[parent_object_id] = OBJECT_ID(N'[import].[SiteDescriptionDictionary]') AND [c].[name] = N'Id');
    IF @var6 IS NOT NULL EXEC(N'ALTER TABLE [import].[SiteDescriptionDictionary] DROP CONSTRAINT [' + @var6 + '];');
    ALTER TABLE [import].[SiteDescriptionDictionary] DROP COLUMN [Id];
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116161100_add SiteDescrip'
)
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20240116161100_add SiteDescrip', N'8.0.4');
END;
GO

COMMIT;
GO

BEGIN TRANSACTION;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116161227_add key for SiteDescrip'
)
BEGIN
    ALTER TABLE [import].[SiteDescriptionDictionary] ADD [Id] int NOT NULL IDENTITY;
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116161227_add key for SiteDescrip'
)
BEGIN
    ALTER TABLE [import].[SiteDescriptionDictionary] ADD CONSTRAINT [PK_SiteDescriptionDictionary] PRIMARY KEY ([Id]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116161227_add key for SiteDescrip'
)
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20240116161227_add key for SiteDescrip', N'8.0.4');
END;
GO

COMMIT;
GO

BEGIN TRANSACTION;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116164452_add back in FKs for import.StockItems'
)
BEGIN
    CREATE INDEX [IX_StockItems_SiteId] ON [import].[StockItems] ([SiteId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116164452_add back in FKs for import.StockItems'
)
BEGIN
    CREATE INDEX [IX_StockItems_SourceReportId] ON [import].[StockItems] ([SourceReportId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116164452_add back in FKs for import.StockItems'
)
BEGIN
    ALTER TABLE [import].[StockItems] ADD CONSTRAINT [FK_StockItems_SourceReports_SourceReportId] FOREIGN KEY ([SourceReportId]) REFERENCES [dbo].[SourceReports] ([Id]) ON DELETE CASCADE;
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116164452_add back in FKs for import.StockItems'
)
BEGIN
    ALTER TABLE [import].[StockItems] ADD CONSTRAINT [FK_StockItems_Sites_SiteId] FOREIGN KEY ([SiteId]) REFERENCES [dbo].[Sites] ([Id]) ON DELETE CASCADE;
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116164452_add back in FKs for import.StockItems'
)
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20240116164452_add back in FKs for import.StockItems', N'8.0.4');
END;
GO

COMMIT;
GO

BEGIN TRANSACTION;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116164738_add back in FKs x2'
)
BEGIN
    CREATE INDEX [IX_StockItems_FileImportId] ON [import].[StockItems] ([FileImportId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116164738_add back in FKs x2'
)
BEGIN
    CREATE INDEX [IX_ReconcilingItems_FileImportId] ON [import].[ReconcilingItems] ([FileImportId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116164738_add back in FKs x2'
)
BEGIN
    ALTER TABLE [import].[ReconcilingItems] ADD CONSTRAINT [FK_ReconcilingItems_FileImports_FileImportId] FOREIGN KEY ([FileImportId]) REFERENCES [import].[FileImports] ([Id]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240116164738_add back in FKs x2'
)
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20240116164738_add back in FKs x2', N'8.0.4');
END;
GO

COMMIT;
GO

BEGIN TRANSACTION;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240117140631_amend code on FL to string'
)
BEGIN
    DECLARE @var7 sysname;
    SELECT @var7 = [d].[name]
    FROM [sys].[default_constraints] [d]
    INNER JOIN [sys].[columns] [c] ON [d].[parent_column_id] = [c].[column_id] AND [d].[parent_object_id] = [c].[object_id]
    WHERE ([d].[parent_object_id] = OBJECT_ID(N'[import].[FinancialLines]') AND [c].[name] = N'Code');
    IF @var7 IS NOT NULL EXEC(N'ALTER TABLE [import].[FinancialLines] DROP CONSTRAINT [' + @var7 + '];');
    ALTER TABLE [import].[FinancialLines] ALTER COLUMN [Code] nvarchar(50) NULL;
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240117140631_amend code on FL to string'
)
BEGIN
    DECLARE @var8 sysname;
    SELECT @var8 = [d].[name]
    FROM [sys].[default_constraints] [d]
    INNER JOIN [sys].[columns] [c] ON [d].[parent_column_id] = [c].[column_id] AND [d].[parent_object_id] = [c].[object_id]
    WHERE ([d].[parent_object_id] = OBJECT_ID(N'[dbo].[FinancialLines]') AND [c].[name] = N'Code');
    IF @var8 IS NOT NULL EXEC(N'ALTER TABLE [dbo].[FinancialLines] DROP CONSTRAINT [' + @var8 + '];');
    ALTER TABLE [dbo].[FinancialLines] ALTER COLUMN [Code] nvarchar(50) NULL;
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240117140631_amend code on FL to string'
)
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20240117140631_amend code on FL to string', N'8.0.4');
END;
GO

COMMIT;
GO

BEGIN TRANSACTION;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240117165232_increase length of filename field v2'
)
BEGIN
    DECLARE @var9 sysname;
    SELECT @var9 = [d].[name]
    FROM [sys].[default_constraints] [d]
    INNER JOIN [sys].[columns] [c] ON [d].[parent_column_id] = [c].[column_id] AND [d].[parent_object_id] = [c].[object_id]
    WHERE ([d].[parent_object_id] = OBJECT_ID(N'[dbo].[UnknownResolutionImages]') AND [c].[name] = N'FileName');
    IF @var9 IS NOT NULL EXEC(N'ALTER TABLE [dbo].[UnknownResolutionImages] DROP CONSTRAINT [' + @var9 + '];');
    ALTER TABLE [dbo].[UnknownResolutionImages] ALTER COLUMN [FileName] nvarchar(250) NULL;
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240117165232_increase length of filename field v2'
)
BEGIN
    DECLARE @var10 sysname;
    SELECT @var10 = [d].[name]
    FROM [sys].[default_constraints] [d]
    INNER JOIN [sys].[columns] [c] ON [d].[parent_column_id] = [c].[column_id] AND [d].[parent_object_id] = [c].[object_id]
    WHERE ([d].[parent_object_id] = OBJECT_ID(N'[dbo].[MissingResolutionImages]') AND [c].[name] = N'FileName');
    IF @var10 IS NOT NULL EXEC(N'ALTER TABLE [dbo].[MissingResolutionImages] DROP CONSTRAINT [' + @var10 + '];');
    ALTER TABLE [dbo].[MissingResolutionImages] ALTER COLUMN [FileName] nvarchar(250) NULL;
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240117165232_increase length of filename field v2'
)
BEGIN
    DECLARE @var11 sysname;
    SELECT @var11 = [d].[name]
    FROM [sys].[default_constraints] [d]
    INNER JOIN [sys].[columns] [c] ON [d].[parent_column_id] = [c].[column_id] AND [d].[parent_object_id] = [c].[object_id]
    WHERE ([d].[parent_object_id] = OBJECT_ID(N'[import].[FileImports]') AND [c].[name] = N'FileName');
    IF @var11 IS NOT NULL EXEC(N'ALTER TABLE [import].[FileImports] DROP CONSTRAINT [' + @var11 + '];');
    ALTER TABLE [import].[FileImports] ALTER COLUMN [FileName] nvarchar(250) NULL;
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240117165232_increase length of filename field v2'
)
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20240117165232_increase length of filename field v2', N'8.0.4');
END;
GO

COMMIT;
GO

BEGIN TRANSACTION;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240119131623_change tables to be DG specific v2'
)
BEGIN
    ALTER TABLE [dbo].[FinancialLines] DROP CONSTRAINT [FK_FinancialLines_FileImports_FileImportId];
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240119131623_change tables to be DG specific v2'
)
BEGIN
    ALTER TABLE [dbo].[ReconcilingItems] DROP CONSTRAINT [FK_ReconcilingItems_FileImports_FileImportId];
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240119131623_change tables to be DG specific v2'
)
BEGIN
    ALTER TABLE [dbo].[ReconcilingItems] DROP CONSTRAINT [FK_ReconcilingItems_ReconcilingItemTypes_ReconcilingItemTypeId];
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240119131623_change tables to be DG specific v2'
)
BEGIN
    ALTER TABLE [dbo].[ReconcilingItems] DROP CONSTRAINT [FK_ReconcilingItems_SourceReports_SourceReportId];
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240119131623_change tables to be DG specific v2'
)
BEGIN
    ALTER TABLE [dbo].[StockItems] DROP CONSTRAINT [FK_StockItems_FileImports_FileImportId];
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240119131623_change tables to be DG specific v2'
)
BEGIN
    ALTER TABLE [dbo].[StockItems] DROP CONSTRAINT [FK_StockItems_SourceReports_SourceReportId];
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240119131623_change tables to be DG specific v2'
)
BEGIN
    DROP TABLE [import].[FinancialLines];
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240119131623_change tables to be DG specific v2'
)
BEGIN
    DROP TABLE [import].[ReconcilingItems];
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240119131623_change tables to be DG specific v2'
)
BEGIN
    DROP TABLE [import].[StockItems];
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240119131623_change tables to be DG specific v2'
)
BEGIN
    CREATE TABLE [import].[MMGFinancialLines] (
        [Id] int NOT NULL,
        [Code] nvarchar(50) NULL,
        [AccountDescription] nvarchar(250) NULL,
        [Balance] decimal(15,3) NOT NULL,
        [SiteId] int NOT NULL,
        [FileImportId] int NULL,
        CONSTRAINT [FK_MMGFinancialLines_FileImports_FileImportId] FOREIGN KEY ([FileImportId]) REFERENCES [import].[FileImports] ([Id]),
        CONSTRAINT [FK_MMGFinancialLines_Sites_SiteId] FOREIGN KEY ([SiteId]) REFERENCES [dbo].[Sites] ([Id]) ON DELETE CASCADE
    );
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240119131623_change tables to be DG specific v2'
)
BEGIN
    CREATE TABLE [import].[MMGReconcilingItems] (
        [Id] int NOT NULL,
        [ReconcilingItemTypeId] int NOT NULL,
        [Reg] nvarchar(50) NULL,
        [Vin] nvarchar(50) NULL,
        [Description] nvarchar(250) NULL,
        [Comment] nvarchar(500) NULL,
        [Reference] nvarchar(50) NULL,
        [SourceReportId] int NOT NULL,
        [SiteId] int NOT NULL,
        [FileImportId] int NULL,
        CONSTRAINT [FK_MMGReconcilingItems_FileImports_FileImportId] FOREIGN KEY ([FileImportId]) REFERENCES [import].[FileImports] ([Id]),
        CONSTRAINT [FK_MMGReconcilingItems_ReconcilingItemTypes_ReconcilingItemTypeId] FOREIGN KEY ([ReconcilingItemTypeId]) REFERENCES [dbo].[ReconcilingItemTypes] ([Id]) ON DELETE CASCADE,
        CONSTRAINT [FK_MMGReconcilingItems_Sites_SiteId] FOREIGN KEY ([SiteId]) REFERENCES [dbo].[Sites] ([Id]) ON DELETE CASCADE,
        CONSTRAINT [FK_MMGReconcilingItems_SourceReports_SourceReportId] FOREIGN KEY ([SourceReportId]) REFERENCES [dbo].[SourceReports] ([Id]) ON DELETE CASCADE
    );
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240119131623_change tables to be DG specific v2'
)
BEGIN
    CREATE TABLE [import].[MMGStockItems] (
        [Id] int NOT NULL,
        [SourceReportId] int NOT NULL,
        [Reg] nvarchar(50) NULL,
        [Vin] nvarchar(50) NULL,
        [Description] nvarchar(250) NULL,
        [DIS] int NULL,
        [GroupDIS] int NULL,
        [Branch] nvarchar(50) NULL,
        [Comment] nvarchar(500) NULL,
        [StockType] nvarchar(50) NULL,
        [Reference] nvarchar(50) NULL,
        [StockValue] decimal(15,3) NOT NULL,
        [SiteId] int NOT NULL,
        [FileImportId] int NULL,
        CONSTRAINT [FK_MMGStockItems_FileImports_FileImportId] FOREIGN KEY ([FileImportId]) REFERENCES [import].[FileImports] ([Id]),
        CONSTRAINT [FK_MMGStockItems_Sites_SiteId] FOREIGN KEY ([SiteId]) REFERENCES [dbo].[Sites] ([Id]) ON DELETE CASCADE,
        CONSTRAINT [FK_MMGStockItems_SourceReports_SourceReportId] FOREIGN KEY ([SourceReportId]) REFERENCES [dbo].[SourceReports] ([Id]) ON DELETE CASCADE
    );
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240119131623_change tables to be DG specific v2'
)
BEGIN
    CREATE INDEX [IX_MMGFinancialLines_FileImportId] ON [import].[MMGFinancialLines] ([FileImportId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240119131623_change tables to be DG specific v2'
)
BEGIN
    CREATE INDEX [IX_MMGFinancialLines_SiteId] ON [import].[MMGFinancialLines] ([SiteId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240119131623_change tables to be DG specific v2'
)
BEGIN
    CREATE INDEX [IX_MMGReconcilingItems_FileImportId] ON [import].[MMGReconcilingItems] ([FileImportId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240119131623_change tables to be DG specific v2'
)
BEGIN
    CREATE INDEX [IX_MMGReconcilingItems_ReconcilingItemTypeId] ON [import].[MMGReconcilingItems] ([ReconcilingItemTypeId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240119131623_change tables to be DG specific v2'
)
BEGIN
    CREATE INDEX [IX_MMGReconcilingItems_SiteId] ON [import].[MMGReconcilingItems] ([SiteId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240119131623_change tables to be DG specific v2'
)
BEGIN
    CREATE INDEX [IX_MMGReconcilingItems_SourceReportId] ON [import].[MMGReconcilingItems] ([SourceReportId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240119131623_change tables to be DG specific v2'
)
BEGIN
    CREATE INDEX [IX_MMGStockItems_FileImportId] ON [import].[MMGStockItems] ([FileImportId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240119131623_change tables to be DG specific v2'
)
BEGIN
    CREATE INDEX [IX_MMGStockItems_SiteId] ON [import].[MMGStockItems] ([SiteId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240119131623_change tables to be DG specific v2'
)
BEGIN
    CREATE INDEX [IX_MMGStockItems_SourceReportId] ON [import].[MMGStockItems] ([SourceReportId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240119131623_change tables to be DG specific v2'
)
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20240119131623_change tables to be DG specific v2', N'8.0.4');
END;
GO

COMMIT;
GO

BEGIN TRANSACTION;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240122124103_change tables to be DG specific x2'
)
BEGIN
    DROP TABLE [import].[MMGReconcilingItems];
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240122124103_change tables to be DG specific x2'
)
BEGIN
    CREATE TABLE [import].[MMGAtAuctions] (
        [Id] int NOT NULL,
        [ReconcilingItemTypeId] int NOT NULL,
        [Reg] nvarchar(50) NULL,
        [Vin] nvarchar(50) NULL,
        [Description] nvarchar(250) NULL,
        [Comment] nvarchar(500) NULL,
        [Reference] nvarchar(50) NULL,
        [SourceReportId] int NOT NULL,
        [SiteId] int NOT NULL,
        [FileImportId] int NULL,
        CONSTRAINT [FK_MMGAtAuctions_FileImports_FileImportId] FOREIGN KEY ([FileImportId]) REFERENCES [import].[FileImports] ([Id]),
        CONSTRAINT [FK_MMGAtAuctions_ReconcilingItemTypes_ReconcilingItemTypeId] FOREIGN KEY ([ReconcilingItemTypeId]) REFERENCES [dbo].[ReconcilingItemTypes] ([Id]) ON DELETE CASCADE,
        CONSTRAINT [FK_MMGAtAuctions_Sites_SiteId] FOREIGN KEY ([SiteId]) REFERENCES [dbo].[Sites] ([Id]) ON DELETE CASCADE,
        CONSTRAINT [FK_MMGAtAuctions_SourceReports_SourceReportId] FOREIGN KEY ([SourceReportId]) REFERENCES [dbo].[SourceReports] ([Id]) ON DELETE CASCADE
    );
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240122124103_change tables to be DG specific x2'
)
BEGIN
    CREATE TABLE [import].[MMGStockOnLoans] (
        [Id] int NOT NULL,
        [ReconcilingItemTypeId] int NOT NULL,
        [Reg] nvarchar(50) NULL,
        [Vin] nvarchar(50) NULL,
        [Description] nvarchar(250) NULL,
        [Comment] nvarchar(500) NULL,
        [Reference] nvarchar(50) NULL,
        [SourceReportId] int NOT NULL,
        [SiteId] int NOT NULL,
        [FileImportId] int NULL,
        CONSTRAINT [FK_MMGStockOnLoans_FileImports_FileImportId] FOREIGN KEY ([FileImportId]) REFERENCES [import].[FileImports] ([Id]),
        CONSTRAINT [FK_MMGStockOnLoans_ReconcilingItemTypes_ReconcilingItemTypeId] FOREIGN KEY ([ReconcilingItemTypeId]) REFERENCES [dbo].[ReconcilingItemTypes] ([Id]) ON DELETE CASCADE,
        CONSTRAINT [FK_MMGStockOnLoans_Sites_SiteId] FOREIGN KEY ([SiteId]) REFERENCES [dbo].[Sites] ([Id]) ON DELETE CASCADE,
        CONSTRAINT [FK_MMGStockOnLoans_SourceReports_SourceReportId] FOREIGN KEY ([SourceReportId]) REFERENCES [dbo].[SourceReports] ([Id]) ON DELETE CASCADE
    );
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240122124103_change tables to be DG specific x2'
)
BEGIN
    CREATE TABLE [import].[MMGWIPs] (
        [Id] int NOT NULL,
        [ReconcilingItemTypeId] int NOT NULL,
        [Reg] nvarchar(max) NULL,
        [Vin] nvarchar(max) NULL,
        [Description] nvarchar(max) NULL,
        [Comment] nvarchar(max) NULL,
        [Reference] nvarchar(max) NULL,
        [SourceReportId] int NOT NULL,
        [SiteId] int NOT NULL,
        [FileImportId] int NULL,
        CONSTRAINT [FK_MMGWIPs_FileImports_FileImportId] FOREIGN KEY ([FileImportId]) REFERENCES [import].[FileImports] ([Id]),
        CONSTRAINT [FK_MMGWIPs_ReconcilingItemTypes_ReconcilingItemTypeId] FOREIGN KEY ([ReconcilingItemTypeId]) REFERENCES [dbo].[ReconcilingItemTypes] ([Id]) ON DELETE CASCADE,
        CONSTRAINT [FK_MMGWIPs_Sites_SiteId] FOREIGN KEY ([SiteId]) REFERENCES [dbo].[Sites] ([Id]) ON DELETE CASCADE,
        CONSTRAINT [FK_MMGWIPs_SourceReports_SourceReportId] FOREIGN KEY ([SourceReportId]) REFERENCES [dbo].[SourceReports] ([Id]) ON DELETE CASCADE
    );
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240122124103_change tables to be DG specific x2'
)
BEGIN
    CREATE INDEX [IX_MMGAtAuctions_FileImportId] ON [import].[MMGAtAuctions] ([FileImportId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240122124103_change tables to be DG specific x2'
)
BEGIN
    CREATE INDEX [IX_MMGAtAuctions_ReconcilingItemTypeId] ON [import].[MMGAtAuctions] ([ReconcilingItemTypeId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240122124103_change tables to be DG specific x2'
)
BEGIN
    CREATE INDEX [IX_MMGAtAuctions_SiteId] ON [import].[MMGAtAuctions] ([SiteId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240122124103_change tables to be DG specific x2'
)
BEGIN
    CREATE INDEX [IX_MMGAtAuctions_SourceReportId] ON [import].[MMGAtAuctions] ([SourceReportId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240122124103_change tables to be DG specific x2'
)
BEGIN
    CREATE INDEX [IX_MMGStockOnLoans_FileImportId] ON [import].[MMGStockOnLoans] ([FileImportId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240122124103_change tables to be DG specific x2'
)
BEGIN
    CREATE INDEX [IX_MMGStockOnLoans_ReconcilingItemTypeId] ON [import].[MMGStockOnLoans] ([ReconcilingItemTypeId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240122124103_change tables to be DG specific x2'
)
BEGIN
    CREATE INDEX [IX_MMGStockOnLoans_SiteId] ON [import].[MMGStockOnLoans] ([SiteId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240122124103_change tables to be DG specific x2'
)
BEGIN
    CREATE INDEX [IX_MMGStockOnLoans_SourceReportId] ON [import].[MMGStockOnLoans] ([SourceReportId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240122124103_change tables to be DG specific x2'
)
BEGIN
    CREATE INDEX [IX_MMGWIPs_FileImportId] ON [import].[MMGWIPs] ([FileImportId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240122124103_change tables to be DG specific x2'
)
BEGIN
    CREATE INDEX [IX_MMGWIPs_ReconcilingItemTypeId] ON [import].[MMGWIPs] ([ReconcilingItemTypeId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240122124103_change tables to be DG specific x2'
)
BEGIN
    CREATE INDEX [IX_MMGWIPs_SiteId] ON [import].[MMGWIPs] ([SiteId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240122124103_change tables to be DG specific x2'
)
BEGIN
    CREATE INDEX [IX_MMGWIPs_SourceReportId] ON [import].[MMGWIPs] ([SourceReportId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240122124103_change tables to be DG specific x2'
)
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20240122124103_change tables to be DG specific x2', N'8.0.4');
END;
GO

COMMIT;
GO

BEGIN TRANSACTION;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240123142545_increase failnotes length'
)
BEGIN
    DECLARE @var12 sysname;
    SELECT @var12 = [d].[name]
    FROM [sys].[default_constraints] [d]
    INNER JOIN [sys].[columns] [c] ON [d].[parent_column_id] = [c].[column_id] AND [d].[parent_object_id] = [c].[object_id]
    WHERE ([d].[parent_object_id] = OBJECT_ID(N'[dbo].[LogMessages]') AND [c].[name] = N'FailNotes');
    IF @var12 IS NOT NULL EXEC(N'ALTER TABLE [dbo].[LogMessages] DROP CONSTRAINT [' + @var12 + '];');
    ALTER TABLE [dbo].[LogMessages] ALTER COLUMN [FailNotes] nvarchar(max) NULL;
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240123142545_increase failnotes length'
)
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20240123142545_increase failnotes length', N'8.0.4');
END;
GO

COMMIT;
GO

BEGIN TRANSACTION;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240301105834_add IsPrimarySiteId to SiteDescDic'
)
BEGIN
    ALTER TABLE [import].[SiteDescriptionDictionary] ADD [IsPrimarySiteId] bit NOT NULL DEFAULT CAST(0 AS bit);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240301105834_add IsPrimarySiteId to SiteDescDic'
)
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20240301105834_add IsPrimarySiteId to SiteDescDic', N'8.0.4');
END;
GO

COMMIT;
GO

BEGIN TRANSACTION;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240415154452_add lithiafinancialines'
)
BEGIN
    CREATE TABLE [import].[LithiaFinancialLines] (
        [Id] int NOT NULL,
        [Code] nvarchar(50) NULL,
        [AccountDescription] nvarchar(250) NULL,
        [Balance] decimal(15,3) NOT NULL,
        [SiteId] int NOT NULL,
        [FileImportId] int NULL,
        CONSTRAINT [FK_LithiaFinancialLines_FileImports_FileImportId] FOREIGN KEY ([FileImportId]) REFERENCES [import].[FileImports] ([Id]),
        CONSTRAINT [FK_LithiaFinancialLines_Sites_SiteId] FOREIGN KEY ([SiteId]) REFERENCES [dbo].[Sites] ([Id]) ON DELETE CASCADE
    );
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240415154452_add lithiafinancialines'
)
BEGIN
    CREATE INDEX [IX_LithiaFinancialLines_FileImportId] ON [import].[LithiaFinancialLines] ([FileImportId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240415154452_add lithiafinancialines'
)
BEGIN
    CREATE INDEX [IX_LithiaFinancialLines_SiteId] ON [import].[LithiaFinancialLines] ([SiteId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240415154452_add lithiafinancialines'
)
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20240415154452_add lithiafinancialines', N'8.0.4');
END;
GO

COMMIT;
GO

BEGIN TRANSACTION;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240416122305_add lithiaWIPs'
)
BEGIN
    CREATE TABLE [import].[LithiaWIPs] (
        [Id] int NOT NULL,
        [ReconcilingItemTypeId] int NOT NULL,
        [Reg] nvarchar(50) NULL,
        [Vin] nvarchar(50) NULL,
        [Description] nvarchar(250) NULL,
        [Comment] nvarchar(500) NULL,
        [Reference] nvarchar(50) NULL,
        [SourceReportId] int NOT NULL,
        [SiteId] int NOT NULL,
        [FileImportId] int NULL,
        CONSTRAINT [FK_LithiaWIPs_FileImports_FileImportId] FOREIGN KEY ([FileImportId]) REFERENCES [import].[FileImports] ([Id]),
        CONSTRAINT [FK_LithiaWIPs_ReconcilingItemTypes_ReconcilingItemTypeId] FOREIGN KEY ([ReconcilingItemTypeId]) REFERENCES [dbo].[ReconcilingItemTypes] ([Id]) ON DELETE CASCADE,
        CONSTRAINT [FK_LithiaWIPs_Sites_SiteId] FOREIGN KEY ([SiteId]) REFERENCES [dbo].[Sites] ([Id]) ON DELETE CASCADE,
        CONSTRAINT [FK_LithiaWIPs_SourceReports_SourceReportId] FOREIGN KEY ([SourceReportId]) REFERENCES [dbo].[SourceReports] ([Id]) ON DELETE CASCADE
    );
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240416122305_add lithiaWIPs'
)
BEGIN
    CREATE INDEX [IX_LithiaWIPs_FileImportId] ON [import].[LithiaWIPs] ([FileImportId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240416122305_add lithiaWIPs'
)
BEGIN
    CREATE INDEX [IX_LithiaWIPs_ReconcilingItemTypeId] ON [import].[LithiaWIPs] ([ReconcilingItemTypeId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240416122305_add lithiaWIPs'
)
BEGIN
    CREATE INDEX [IX_LithiaWIPs_SiteId] ON [import].[LithiaWIPs] ([SiteId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240416122305_add lithiaWIPs'
)
BEGIN
    CREATE INDEX [IX_LithiaWIPs_SourceReportId] ON [import].[LithiaWIPs] ([SourceReportId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240416122305_add lithiaWIPs'
)
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20240416122305_add lithiaWIPs', N'8.0.4');
END;
GO

COMMIT;
GO

BEGIN TRANSACTION;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240416132152_add lithia stockitems'
)
BEGIN
    CREATE TABLE [import].[LithiaStockItems] (
        [Id] int NOT NULL,
        [SourceReportId] int NOT NULL,
        [Reg] nvarchar(50) NULL,
        [Vin] nvarchar(50) NULL,
        [Description] nvarchar(250) NULL,
        [DIS] int NULL,
        [GroupDIS] int NULL,
        [Branch] nvarchar(50) NULL,
        [Comment] nvarchar(500) NULL,
        [StockType] nvarchar(50) NULL,
        [Reference] nvarchar(50) NULL,
        [StockValue] decimal(15,3) NOT NULL,
        [SiteId] int NOT NULL,
        [FileImportId] int NULL,
        CONSTRAINT [FK_LithiaStockItems_FileImports_FileImportId] FOREIGN KEY ([FileImportId]) REFERENCES [import].[FileImports] ([Id]),
        CONSTRAINT [FK_LithiaStockItems_Sites_SiteId] FOREIGN KEY ([SiteId]) REFERENCES [dbo].[Sites] ([Id]) ON DELETE CASCADE,
        CONSTRAINT [FK_LithiaStockItems_SourceReports_SourceReportId] FOREIGN KEY ([SourceReportId]) REFERENCES [dbo].[SourceReports] ([Id]) ON DELETE CASCADE
    );
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240416132152_add lithia stockitems'
)
BEGIN
    CREATE INDEX [IX_LithiaStockItems_FileImportId] ON [import].[LithiaStockItems] ([FileImportId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240416132152_add lithia stockitems'
)
BEGIN
    CREATE INDEX [IX_LithiaStockItems_SiteId] ON [import].[LithiaStockItems] ([SiteId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240416132152_add lithia stockitems'
)
BEGIN
    CREATE INDEX [IX_LithiaStockItems_SourceReportId] ON [import].[LithiaStockItems] ([SourceReportId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240416132152_add lithia stockitems'
)
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20240416132152_add lithia stockitems', N'8.0.4');
END;
GO

COMMIT;
GO

BEGIN TRANSACTION;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240418094106_add new import tables'
)
BEGIN
    CREATE TABLE [import].[FinancialLines] (
        [Id] int NOT NULL,
        [Code] nvarchar(50) NULL,
        [AccountDescription] nvarchar(250) NULL,
        [Balance] decimal(15,3) NOT NULL,
        [SiteId] int NOT NULL,
        [FileImportId] int NULL,
        [DealerGroupId] int NOT NULL,
        CONSTRAINT [FK_FinancialLines_DealerGroup_DealerGroupId] FOREIGN KEY ([DealerGroupId]) REFERENCES [dbo].[DealerGroup] ([Id]) ON DELETE CASCADE,
        CONSTRAINT [FK_FinancialLines_FileImports_FileImportId] FOREIGN KEY ([FileImportId]) REFERENCES [import].[FileImports] ([Id]),
        CONSTRAINT [FK_FinancialLines_Sites_SiteId] FOREIGN KEY ([SiteId]) REFERENCES [dbo].[Sites] ([Id]) ON DELETE CASCADE
    );
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240418094106_add new import tables'
)
BEGIN
    CREATE TABLE [import].[ReconcilingItems] (
        [Id] int NOT NULL,
        [ReconcilingItemTypeId] int NOT NULL,
        [Reg] nvarchar(50) NULL,
        [Vin] nvarchar(50) NULL,
        [Description] nvarchar(250) NULL,
        [Comment] nvarchar(500) NULL,
        [Reference] nvarchar(50) NULL,
        [SourceReportId] int NOT NULL,
        [SiteId] int NOT NULL,
        [FileImportId] int NULL,
        [DealerGroupId] int NOT NULL,
        CONSTRAINT [FK_ReconcilingItems_DealerGroup_DealerGroupId] FOREIGN KEY ([DealerGroupId]) REFERENCES [dbo].[DealerGroup] ([Id]) ON DELETE CASCADE,
        CONSTRAINT [FK_ReconcilingItems_FileImports_FileImportId] FOREIGN KEY ([FileImportId]) REFERENCES [import].[FileImports] ([Id]),
        CONSTRAINT [FK_ReconcilingItems_ReconcilingItemTypes_ReconcilingItemTypeId] FOREIGN KEY ([ReconcilingItemTypeId]) REFERENCES [dbo].[ReconcilingItemTypes] ([Id]) ON DELETE CASCADE,
        CONSTRAINT [FK_ReconcilingItems_Sites_SiteId] FOREIGN KEY ([SiteId]) REFERENCES [dbo].[Sites] ([Id]) ON DELETE CASCADE,
        CONSTRAINT [FK_ReconcilingItems_SourceReports_SourceReportId] FOREIGN KEY ([SourceReportId]) REFERENCES [dbo].[SourceReports] ([Id]) ON DELETE CASCADE
    );
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240418094106_add new import tables'
)
BEGIN
    CREATE TABLE [import].[StockItems] (
        [Id] int NOT NULL,
        [SourceReportId] int NOT NULL,
        [Reg] nvarchar(50) NULL,
        [Vin] nvarchar(50) NULL,
        [Description] nvarchar(250) NULL,
        [DIS] int NULL,
        [GroupDIS] int NULL,
        [Branch] nvarchar(50) NULL,
        [Comment] nvarchar(500) NULL,
        [StockType] nvarchar(50) NULL,
        [Reference] nvarchar(50) NULL,
        [StockValue] decimal(15,3) NOT NULL,
        [SiteId] int NOT NULL,
        [FileImportId] int NULL,
        [DealerGroupId] int NOT NULL,
        CONSTRAINT [FK_StockItems_DealerGroup_DealerGroupId] FOREIGN KEY ([DealerGroupId]) REFERENCES [dbo].[DealerGroup] ([Id]) ON DELETE CASCADE,
        CONSTRAINT [FK_StockItems_FileImports_FileImportId] FOREIGN KEY ([FileImportId]) REFERENCES [import].[FileImports] ([Id]),
        CONSTRAINT [FK_StockItems_Sites_SiteId] FOREIGN KEY ([SiteId]) REFERENCES [dbo].[Sites] ([Id]) ON DELETE CASCADE,
        CONSTRAINT [FK_StockItems_SourceReports_SourceReportId] FOREIGN KEY ([SourceReportId]) REFERENCES [dbo].[SourceReports] ([Id]) ON DELETE CASCADE
    );
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240418094106_add new import tables'
)
BEGIN
    CREATE INDEX [IX_FinancialLines_DealerGroupId] ON [import].[FinancialLines] ([DealerGroupId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240418094106_add new import tables'
)
BEGIN
    CREATE INDEX [IX_FinancialLines_FileImportId] ON [import].[FinancialLines] ([FileImportId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240418094106_add new import tables'
)
BEGIN
    CREATE INDEX [IX_FinancialLines_SiteId] ON [import].[FinancialLines] ([SiteId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240418094106_add new import tables'
)
BEGIN
    CREATE INDEX [IX_ReconcilingItems_DealerGroupId] ON [import].[ReconcilingItems] ([DealerGroupId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240418094106_add new import tables'
)
BEGIN
    CREATE INDEX [IX_ReconcilingItems_FileImportId] ON [import].[ReconcilingItems] ([FileImportId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240418094106_add new import tables'
)
BEGIN
    CREATE INDEX [IX_ReconcilingItems_ReconcilingItemTypeId] ON [import].[ReconcilingItems] ([ReconcilingItemTypeId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240418094106_add new import tables'
)
BEGIN
    CREATE INDEX [IX_ReconcilingItems_SiteId] ON [import].[ReconcilingItems] ([SiteId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240418094106_add new import tables'
)
BEGIN
    CREATE INDEX [IX_ReconcilingItems_SourceReportId] ON [import].[ReconcilingItems] ([SourceReportId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240418094106_add new import tables'
)
BEGIN
    CREATE INDEX [IX_StockItems_DealerGroupId] ON [import].[StockItems] ([DealerGroupId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240418094106_add new import tables'
)
BEGIN
    CREATE INDEX [IX_StockItems_FileImportId] ON [import].[StockItems] ([FileImportId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240418094106_add new import tables'
)
BEGIN
    CREATE INDEX [IX_StockItems_SiteId] ON [import].[StockItems] ([SiteId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240418094106_add new import tables'
)
BEGIN
    CREATE INDEX [IX_StockItems_SourceReportId] ON [import].[StockItems] ([SourceReportId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240418094106_add new import tables'
)
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20240418094106_add new import tables', N'8.0.4');
END;
GO

COMMIT;
GO

BEGIN TRANSACTION;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240418101553_add new input tables'
)
BEGIN
    IF SCHEMA_ID(N'input') IS NULL EXEC(N'CREATE SCHEMA [input];');
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240418101553_add new input tables'
)
BEGIN
    CREATE TABLE [input].[FinancialLines] (
        [Id] int NOT NULL,
        [Code] nvarchar(50) NULL,
        [AccountDescription] nvarchar(250) NULL,
        [Balance] decimal(15,3) NOT NULL,
        [SiteId] int NOT NULL,
        [FileImportId] int NULL,
        [DealerGroupId] int NOT NULL,
        CONSTRAINT [FK_FinancialLines_DealerGroup_DealerGroupId] FOREIGN KEY ([DealerGroupId]) REFERENCES [dbo].[DealerGroup] ([Id]) ON DELETE CASCADE,
        CONSTRAINT [FK_FinancialLines_FileImports_FileImportId] FOREIGN KEY ([FileImportId]) REFERENCES [import].[FileImports] ([Id]),
        CONSTRAINT [FK_FinancialLines_Sites_SiteId] FOREIGN KEY ([SiteId]) REFERENCES [dbo].[Sites] ([Id]) ON DELETE CASCADE
    );
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240418101553_add new input tables'
)
BEGIN
    CREATE TABLE [input].[ReconcilingItems] (
        [Id] int NOT NULL,
        [ReconcilingItemTypeId] int NOT NULL,
        [Reg] nvarchar(50) NULL,
        [Vin] nvarchar(50) NULL,
        [Description] nvarchar(250) NULL,
        [Comment] nvarchar(500) NULL,
        [Reference] nvarchar(50) NULL,
        [SourceReportId] int NOT NULL,
        [SiteId] int NOT NULL,
        [FileImportId] int NULL,
        [DealerGroupId] int NOT NULL,
        CONSTRAINT [FK_ReconcilingItems_DealerGroup_DealerGroupId] FOREIGN KEY ([DealerGroupId]) REFERENCES [dbo].[DealerGroup] ([Id]) ON DELETE CASCADE,
        CONSTRAINT [FK_ReconcilingItems_FileImports_FileImportId] FOREIGN KEY ([FileImportId]) REFERENCES [import].[FileImports] ([Id]),
        CONSTRAINT [FK_ReconcilingItems_ReconcilingItemTypes_ReconcilingItemTypeId] FOREIGN KEY ([ReconcilingItemTypeId]) REFERENCES [dbo].[ReconcilingItemTypes] ([Id]) ON DELETE CASCADE,
        CONSTRAINT [FK_ReconcilingItems_Sites_SiteId] FOREIGN KEY ([SiteId]) REFERENCES [dbo].[Sites] ([Id]) ON DELETE CASCADE,
        CONSTRAINT [FK_ReconcilingItems_SourceReports_SourceReportId] FOREIGN KEY ([SourceReportId]) REFERENCES [dbo].[SourceReports] ([Id]) ON DELETE CASCADE
    );
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240418101553_add new input tables'
)
BEGIN
    CREATE TABLE [input].[StockItems] (
        [Id] int NOT NULL,
        [SourceReportId] int NOT NULL,
        [Reg] nvarchar(50) NULL,
        [Vin] nvarchar(50) NULL,
        [Description] nvarchar(250) NULL,
        [DIS] int NULL,
        [GroupDIS] int NULL,
        [Branch] nvarchar(50) NULL,
        [Comment] nvarchar(500) NULL,
        [StockType] nvarchar(50) NULL,
        [Reference] nvarchar(50) NULL,
        [StockValue] decimal(15,3) NOT NULL,
        [SiteId] int NOT NULL,
        [FileImportId] int NULL,
        [DealerGroupId] int NOT NULL,
        CONSTRAINT [FK_StockItems_DealerGroup_DealerGroupId] FOREIGN KEY ([DealerGroupId]) REFERENCES [dbo].[DealerGroup] ([Id]) ON DELETE CASCADE,
        CONSTRAINT [FK_StockItems_FileImports_FileImportId] FOREIGN KEY ([FileImportId]) REFERENCES [import].[FileImports] ([Id]),
        CONSTRAINT [FK_StockItems_Sites_SiteId] FOREIGN KEY ([SiteId]) REFERENCES [dbo].[Sites] ([Id]) ON DELETE CASCADE,
        CONSTRAINT [FK_StockItems_SourceReports_SourceReportId] FOREIGN KEY ([SourceReportId]) REFERENCES [dbo].[SourceReports] ([Id]) ON DELETE CASCADE
    );
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240418101553_add new input tables'
)
BEGIN
    CREATE INDEX [IX_FinancialLines_DealerGroupId] ON [input].[FinancialLines] ([DealerGroupId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240418101553_add new input tables'
)
BEGIN
    CREATE INDEX [IX_FinancialLines_FileImportId] ON [input].[FinancialLines] ([FileImportId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240418101553_add new input tables'
)
BEGIN
    CREATE INDEX [IX_FinancialLines_SiteId] ON [input].[FinancialLines] ([SiteId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240418101553_add new input tables'
)
BEGIN
    CREATE INDEX [IX_ReconcilingItems_DealerGroupId] ON [input].[ReconcilingItems] ([DealerGroupId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240418101553_add new input tables'
)
BEGIN
    CREATE INDEX [IX_ReconcilingItems_FileImportId] ON [input].[ReconcilingItems] ([FileImportId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240418101553_add new input tables'
)
BEGIN
    CREATE INDEX [IX_ReconcilingItems_ReconcilingItemTypeId] ON [input].[ReconcilingItems] ([ReconcilingItemTypeId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240418101553_add new input tables'
)
BEGIN
    CREATE INDEX [IX_ReconcilingItems_SiteId] ON [input].[ReconcilingItems] ([SiteId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240418101553_add new input tables'
)
BEGIN
    CREATE INDEX [IX_ReconcilingItems_SourceReportId] ON [input].[ReconcilingItems] ([SourceReportId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240418101553_add new input tables'
)
BEGIN
    CREATE INDEX [IX_StockItems_DealerGroupId] ON [input].[StockItems] ([DealerGroupId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240418101553_add new input tables'
)
BEGIN
    CREATE INDEX [IX_StockItems_FileImportId] ON [input].[StockItems] ([FileImportId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240418101553_add new input tables'
)
BEGIN
    CREATE INDEX [IX_StockItems_SiteId] ON [input].[StockItems] ([SiteId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240418101553_add new input tables'
)
BEGIN
    CREATE INDEX [IX_StockItems_SourceReportId] ON [input].[StockItems] ([SourceReportId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240418101553_add new input tables'
)
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20240418101553_add new input tables', N'8.0.4');
END;
GO

COMMIT;
GO

BEGIN TRANSACTION;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240422100551_remove import tables'
)
BEGIN
    DROP TABLE [import].[FinancialLines];
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240422100551_remove import tables'
)
BEGIN
    DROP TABLE [import].[ReconcilingItems];
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240422100551_remove import tables'
)
BEGIN
    DROP TABLE [import].[StockItems];
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240422100551_remove import tables'
)
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20240422100551_remove import tables', N'8.0.4');
END;
GO

COMMIT;
GO

BEGIN TRANSACTION;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240422160450_remove import tables mmg'
)
BEGIN
    DROP TABLE [import].[MMGAtAuctions];
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240422160450_remove import tables mmg'
)
BEGIN
    DROP TABLE [import].[MMGFinancialLines];
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240422160450_remove import tables mmg'
)
BEGIN
    DROP TABLE [import].[MMGStockItems];
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240422160450_remove import tables mmg'
)
BEGIN
    DROP TABLE [import].[MMGStockOnLoans];
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240422160450_remove import tables mmg'
)
BEGIN
    DROP TABLE [import].[MMGWIPs];
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240422160450_remove import tables mmg'
)
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20240422160450_remove import tables mmg', N'8.0.4');
END;
GO

COMMIT;
GO

BEGIN TRANSACTION;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240423151849_add unique key constraint to input stockitems'
)
BEGIN
    EXEC(N'CREATE UNIQUE INDEX [IX_StockItems_Reg_Vin_SiteId] ON [input].[StockItems] ([Reg], [Vin], [SiteId]) WHERE [Reg] IS NOT NULL AND [Vin] IS NOT NULL');
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240423151849_add unique key constraint to input stockitems'
)
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20240423151849_add unique key constraint to input stockitems', N'8.0.4');
END;
GO

COMMIT;
GO

BEGIN TRANSACTION;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240423155717_remove redundant lithia import tables'
)
BEGIN
    DROP TABLE [import].[LithiaFinancialLines];
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240423155717_remove redundant lithia import tables'
)
BEGIN
    DROP TABLE [import].[LithiaStockItems];
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240423155717_remove redundant lithia import tables'
)
BEGIN
    DROP TABLE [import].[LithiaWIPs];
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240423155717_remove redundant lithia import tables'
)
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20240423155717_remove redundant lithia import tables', N'8.0.4');
END;
GO

COMMIT;
GO

BEGIN TRANSACTION;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240429121148_PrintLog table'
)
BEGIN
    CREATE TABLE [dbo].[PrintLogs] (
        [Id] int NOT NULL IDENTITY,
        [UserId] int NOT NULL,
        [Vin] nvarchar(50) NULL,
        [Date] datetime2 NOT NULL,
        CONSTRAINT [PK_PrintLogs] PRIMARY KEY ([Id]),
        CONSTRAINT [FK_PrintLogs_Users_UserId] FOREIGN KEY ([UserId]) REFERENCES [dbo].[Users] ([Id]) ON DELETE CASCADE
    );
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240429121148_PrintLog table'
)
BEGIN
    CREATE INDEX [IX_PrintLogs_UserId] ON [dbo].[PrintLogs] ([UserId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240429121148_PrintLog table'
)
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20240429121148_PrintLog table', N'8.0.4');
END;
GO

COMMIT;
GO

BEGIN TRANSACTION;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240516113154_EmployeeNumber to users'
)
BEGIN
    ALTER TABLE [dbo].[Users] ADD [EmployeeNumber] nvarchar(50) NULL;
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240516113154_EmployeeNumber to users'
)
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20240516113154_EmployeeNumber to users', N'8.0.4');
END;
GO

COMMIT;
GO

BEGIN TRANSACTION;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240521155904_Add IsStandard to ImportMasks'
)
BEGIN
    ALTER TABLE [dbo].[ImportMasks] ADD [IsStandard] bit NOT NULL DEFAULT CAST(0 AS bit);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240521155904_Add IsStandard to ImportMasks'
)
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20240521155904_Add IsStandard to ImportMasks', N'8.0.4');
END;
GO

COMMIT;
GO

BEGIN TRANSACTION;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240523101218_Create ReconcilingItemBackups table'
)
BEGIN
    CREATE TABLE [dbo].[ReconcilingItemBackups] (
        [Id] int NOT NULL IDENTITY,
        [StockCheckId] int NOT NULL,
        [ReconcilingItemTypeId] int NOT NULL,
        [FileName] nvarchar(50) NULL,
        CONSTRAINT [PK_ReconcilingItemBackups] PRIMARY KEY ([Id]),
        CONSTRAINT [FK_ReconcilingItemBackups_ReconcilingItemTypes_ReconcilingItemTypeId] FOREIGN KEY ([ReconcilingItemTypeId]) REFERENCES [dbo].[ReconcilingItemTypes] ([Id]) ON DELETE CASCADE,
        CONSTRAINT [FK_ReconcilingItemBackups_StockChecks_StockCheckId] FOREIGN KEY ([StockCheckId]) REFERENCES [dbo].[StockChecks] ([Id]) ON DELETE CASCADE
    );
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240523101218_Create ReconcilingItemBackups table'
)
BEGIN
    CREATE INDEX [IX_ReconcilingItemBackups_ReconcilingItemTypeId] ON [dbo].[ReconcilingItemBackups] ([ReconcilingItemTypeId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240523101218_Create ReconcilingItemBackups table'
)
BEGIN
    CREATE INDEX [IX_ReconcilingItemBackups_StockCheckId] ON [dbo].[ReconcilingItemBackups] ([StockCheckId]);
END;
GO

IF NOT EXISTS (
    SELECT * FROM [__EFMigrationsHistory]
    WHERE [MigrationId] = N'20240523101218_Create ReconcilingItemBackups table'
)
BEGIN
    INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
    VALUES (N'20240523101218_Create ReconcilingItemBackups table', N'8.0.4');
END;
GO

COMMIT;
GO

