﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace StockPulse.Repository.Migrations
{
    /// <inheritdoc />
    public partial class adddealergroupid : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "DealerGroup_Id",
                schema: "dbo",
                table: "LogMessages",
                type: "int",
                nullable: true,
                defaultValue: 0);

            migrationBuilder.CreateIndex(
                name: "IX_LogMessages_DealerGroup_Id",
                schema: "dbo",
                table: "LogMessages",
                column: "DealerGroup_Id");

            migrationBuilder.AddForeignKey(
                name: "FK_LogMessages_DealerGroup_DealerGroup_Id",
                schema: "dbo",
                table: "LogMessages",
                column: "DealerGroup_Id",
                principalSchema: "dbo",
                principalTable: "DealerGroup",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_LogMessages_DealerGroup_DealerGroup_Id",
                schema: "dbo",
                table: "LogMessages");

            migrationBuilder.DropIndex(
                name: "IX_LogMessages_DealerGroup_Id",
                schema: "dbo",
                table: "LogMessages");

            migrationBuilder.DropColumn(
                name: "DealerGroup_Id",
                schema: "dbo",
                table: "LogMessages");
        }
    }
}
