input{
  background: unset;
}
#gridHolder {
  width: 100%;
  margin: 0em auto;
  padding: 0em;
  height: 100%;
}


/* dark background white writing */
:host ::ng-deep {
  ag-grid-angular {
    height: 100%;
  }
  .grouped {
    .ag-row-level-0 {
      background-color: hsla(30, 2%, 19%, 0.9) !important;
      font-weight: 500;
      color: white;
    }
  
  }

  .ag-cell.disabled {
    color: var(--grey50);
  }
  .ag-row.changed .ag-cell {
    background: lightGreen;
  }

  .ag-row.locked .ag-cell {
    background: rgb(235, 161, 112);
  }

  
  .ag-row.deleted .ag-cell {
    text-decoration: line-through;
  }

  .ag-row .cancelButton {
    display: none;
  }
  .ag-row.changed .cancelButton {
    display: block;
  }

  .ag-cell.editable{
    border: 1px solid var(--grey70);
    background: rgba(255,255,255,0);
  }

  .ag-cell.editable:hover{background:var(--secondary)}

  .editableInner {
    width: 80px !important;
    text-align: right;
    height: 1.6em;
    padding-right: 0.5em;

    background: white;
    border: 1px solid var(--grey70);
    display: flex;
    width: 90%;
    justify-content: flex-end;
  }
  .ag-cell-focus .editableInner {
    background: var(--secondaryLightest) !important;
    font-weight: 700;
  }
  .ag-cell.disabled .editableInner {
    background: var(--grey90) !important;
  }
  .ag-cell.noPad {
    padding: 0px !important;
  }

  .ag-cell-focus,.ag-cell-no-focus{
    border: 1px solid transparent !important;
  }
}


.fileInputWrapper {
  display: flex;
  align-items: center;
  margin: 0 0.5em;
}