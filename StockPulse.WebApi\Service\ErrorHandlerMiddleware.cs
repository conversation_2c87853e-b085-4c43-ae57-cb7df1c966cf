﻿using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Sockets;
using System.Threading.Tasks;
using System.Linq;
using Microsoft.AspNetCore.Http.HttpResults;

namespace StockPulse.WebApi.Service
{
    public class ErrorHandlerMiddleware
    {
        private readonly RequestDelegate _next;

        //Web
        private const int MinimumCompatibleWebVersion_Major = 3;
        private const int MinimumCompatibleWebVersion_Minor = 4;
        private const int MinimumCompatibleWebVersion_BugFix = 0;


        //Mobile
        private const int MinimumCompatibleMobileVersion_Major = 4;
        private const int MinimumCompatibleMobileVersion_Minor = 1;
        private const int MinimumCompatibleMobileVersion_BugFix = 0;

        private const string WebKeyName = "WebVersion";
        private const string MobileKeyName = "MobileVersion";

        public ErrorHandlerMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        public async Task Invoke(HttpContext context)
        {
            try
            {
                ValidateVersion(context.Request);
                await _next(context);
            }
            catch (Exception error)
            {
                var response = context.Response;
                response.ContentType = "application/json";
                
                string errorMessage = error?.Message;



                switch (error)
                {
                    case InvalidWebVersionException ve:
                        response.StatusCode = (int)HttpStatusCode.BadRequest;
                        errorMessage = "Please refresh your browser to ensure you are running the latest version";
                        break;
                    case InvalidMobileVersionException ve:
                        response.StatusCode = (int)HttpStatusCode.BadRequest;
                        errorMessage = "Invalid mobile app version in use";
                        break;
                    default:
                        break;
                }
                await response.WriteAsync(errorMessage);
            }
        }


        private static void ValidateVersion(HttpRequest request)
        {
            if (request.Headers.ContainsKey(WebKeyName))
            {
                try
                {
                    var headerValue = request.Headers[WebKeyName].ToString();
                    //Check if its greater than min. 

                    int[] webVersion = headerValue.Split('.').Select(int.Parse).ToArray();

                    int totalOfWebVersion = (webVersion[0] * 1000000) + (webVersion[1] * 1000) + (webVersion[2] * 1);
                    int totalOfMinimumCompatibleWebVersion = (MinimumCompatibleWebVersion_Major * 1000000) + (MinimumCompatibleWebVersion_Minor * 1000) + (MinimumCompatibleWebVersion_BugFix * 1);

                    if (totalOfWebVersion < totalOfMinimumCompatibleWebVersion)
                    {
                        throw new InvalidWebVersionException();
                    }
                }
                catch (Exception e)
                {
                    throw new InvalidWebVersionException();
                }
            }
            else if (request.Headers.ContainsKey(MobileKeyName))
            {
                try
                {
                    var headerValue = request.Headers[MobileKeyName].ToString();
                    //Check if its greater than min. 

                    int[] mobileVersion = headerValue.Split('.').Select(int.Parse).ToArray();

                    int totalOfMobileVersion = (mobileVersion[0] * 1000000) + (mobileVersion[1] * 1000) + (mobileVersion[2] * 1);
                    int totalOfMinimumCompatibleMobileVersion = (MinimumCompatibleMobileVersion_Major * 1000000) + (MinimumCompatibleMobileVersion_Minor * 1000) + (MinimumCompatibleMobileVersion_BugFix * 1);

                    if (totalOfMobileVersion < totalOfMinimumCompatibleMobileVersion)
                    {
                        throw new InvalidMobileVersionException();
                    }
                }
                catch (Exception e)
                {
                    throw new InvalidMobileVersionException();
                }
            }
            else
            {
                throw new InvalidMobileVersionException();
            }

        }
    }
}
