﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace StockPulse.Model
{
    public class ReconcilingItemBackup
    {
        [Key]
        public int Id { get; set; }
        public int StockCheckId { get; set; }
        [ForeignKey("StockCheckId")]
        public virtual StockCheck StockCheck { get; set; }
        public int ReconcilingItemTypeId { get; set; }
        [ForeignKey("ReconcilingItemTypeId")]
        public virtual ReconcilingItemType ReconcilingItemType { get; set; }
        [MaxLength(200)]
        public string FileName { get; set; }
    }
}