import { Component } from "@angular/core";
import { ICellRendererAngularComp } from "ag-grid-angular";
import { IconService } from '../services/icon.service';


@Component({
  selector: 'checkboxIcon-cell',
  template: `
    <fa-icon [icon]="params.value ? icon.faCheckSquare : icon.faTimesSquare"></fa-icon>
  `
})
export class CheckboxIconComponent implements ICellRendererAngularComp {
  params;

  constructor(
    public icon: IconService
  ) { }

  agInit(params: any): void {
    this.params = params;
  }

  refresh(params?: any): boolean {
    return false;
  }
}
