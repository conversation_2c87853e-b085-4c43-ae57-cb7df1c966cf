.dropdown {
    input {
        margin: 1em;
        background-color: transparent;
        border: 1px solid #FFFFFF;
        color: #FFFFFF;
        width: calc(100% - 2em);

        &::placeholder {
            color: #FFFFFF;
        }
    }

    .dropdown-item {
        padding: 0.25rem 1em;
    }
}

#iframeContainer {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 9999;
    padding: 150px;
    background: rgba(0, 0, 0, 0.5);

    button {
        position: absolute;
        right: 166px;
        background: rgb(50, 54, 57);
        border: none;
        font-size: 20px !important;
        padding: 0 5px;
        right: 166px;
        top: 162px;
        color: #FFFFFF;
    }
}


.chooseFileInput+label {
    height: 25px;
    background-color: #323130;
    color: #ffffff;
    padding: 0.375rem 0.75rem;
    display: flex;
    font-weight: 400;
    text-align: center;
    margin-bottom: 0;
    transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out;
    cursor: pointer;
    align-items: center;

    &:hover {
        background-color: var(--secondary);
        color: #000000;
    }

    fa-icon {
        margin-right: 0.5em;
    }
}

