﻿


CREATE OR ALTER PROCEDURE [dbo].[GET_AllUsersAndLogins] (
	@UserId int
)
AS
BEGIN

SELECT
anu.Id as AppUserId,
anu.LinkedPersonId as Code,
u.Name as Name,
u.Name as <PERSON><PERSON>hort,
anu.Username as User<PERSON><PERSON>,
anr.Name as <PERSON><PERSON><PERSON>,
anu.Email as Email,
(SELECT STRING_AGG(SiteId, ',') FROM UserSites WHERE UserId = anu.LinkedPersonId) as Sites,
(SELECT TOP 1(SiteId) FROM UserSites WHERE UserId = anu.LinkedPersonId AND IsDefault = 1) as SiteC<PERSON>,
u.EmployeeN<PERSON>ber,
CASE 
	WHEN DATEDIFF(DAY, GETUTCDATE(), anu.LockoutEnd) > 999 THEN 1
	ELSE 0
END as IsDeleted,
CASE 
	WHEN DATEDIFF(DAY, GETUTCDATE(), anu.LockoutEnd) <= 999 THEN 1
	ELSE 0
END as IsLocked

FROM AspNetUsers anu
INNER JOIN Users u on u.Id = anu.LinkedPersonId
INNER JOIN AspNetUserRoles anur on anur.UserId = anu.Id
INNER JOIN AspNetRoles anr on anr.Id = anur.RoleId
INNER JOIN Users cu on cu.Id = @UserId
WHERE
u.DealerGroupId = cu.DealerGroupId 

END
GO
