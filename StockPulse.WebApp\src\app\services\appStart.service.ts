import { HttpClient } from '@angular/common/http';
import { Injectable, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { forkJoin } from 'rxjs';
import { GlobalParam } from "../model/GlobalParam";
import { ResolutionType } from '../model/ResolutionType';
import { WhoAmIResult } from '../model/WhoAmIResult';
import { StockChecksService } from '../pages/stockChecks/stockChecks.service';
import { ApiAccessService } from './apiAccess.service';
import { ConstantsService } from './constants.service';
import { GetDataService } from './getData.service';
import { SelectionsService } from './selections.service';
import { MsalGuardConfiguration, MsalService, MSAL_GUARD_CONFIG } from '@azure/msal-angular';
import { DealerGroupSelectionModalComponent } from '../components/dealerGroupSelectionModal/dealerGroupSelectionModal.component';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { MultiDealerGroupService } from './multiDealerGroup.service';
import { UserPreferenceService } from './userPreference.service';
import { UserPreference } from '../model/UserPreference';
import { BaseURLVM } from '../model/BaseURLVM';
import { LoginService } from '../pages/login/login.service';


@Injectable({
  providedIn: 'root'
})
export class AppStartService {



  @ViewChild('dealerGroupSelectionModal', { static: true }) dealerGroupSelectionModal: DealerGroupSelectionModalComponent;
  constructor(
    public http: HttpClient,
    public constants: ConstantsService,
    public selections: SelectionsService,
    public data: GetDataService,
    public apiAccessService: ApiAccessService,
    public router: Router,
    private stockChecksService: StockChecksService,
    private authService: MsalService,
    public modalService: NgbModal,
    public multiDealerGroupService: MultiDealerGroupService,
    private userPreferenceService: UserPreferenceService,
    private loginService: LoginService
  ) {

  }


  //The observable that gets all the initial standing data
  initiateAppDataLoad() {


    let requests = [];
    requests = this.multiDealerGroupService.createRequests('');

    //Get Country specific baseURL
    forkJoin(requests).subscribe((data: any) => {
      const baseURLUK: BaseURLVM = data[0];
      const baseURLUS: BaseURLVM = data[1];
      

    this.multiDealerGroupService.checkandSetBaseURL(baseURLUK, baseURLUS);

    const isDealerGroupSelected = localStorage.getItem('isDealerGroupSelected');
   
    if (this.constants.isMultiDealerGroupAccount && isDealerGroupSelected != 'true'){
      this.setDealerGroupAccountAndContinueLoading();
    }
    else {
      this.continueLoading();
    }

  })
    
  }

  setDealerGroupAccountAndContinueLoading(){
      
    let mySubscription = this.selections.dealerGroupSelectionModalEmitter.subscribe(res =>{
      if(res){
        this.continueLoading();
      }
      mySubscription.unsubscribe();
  })

  this.constants.dealerGroupSelectionModal.showModal();
  }

  continueLoading(){
    
    //-------------------------------
    //start with whoAmI
    //-------------------------------
    this.apiAccessService.get('whoami', '').subscribe((whoAmIResult: WhoAmIResult) => {
      try {
        this.processWhoAmIData(whoAmIResult);
      } catch (error) {
        if (error === 'User not authorised for App') {
          this.selections.userId = null;
          localStorage.clear();
          this.loginService.incorrectAccessRights = true;
          this.router.navigateByUrl('/login');
        }
      }

    }, e => {

      //if its auth then it means its redirected from Azure,
      //so wait and do nothing as it will be redirected to home page again.
      //if not from auth then its a first time user and should be redirected to login page
      if (this.router.url.startsWith('/resetpassword')){
        console.log('resetpassword');
      }
      else if (!document.referrer.endsWith('/auth')) {
        this.router.navigateByUrl('/login');
      }

    }, () => {

      //-------------------------------
      //got whoAmI, now do rest
      //-------------------------------

      let requests = [
        this.apiAccessService.get('Sites', 'All'),
      ];

      if (!this.selections.userIsScanAndPrintOnly) {
        requests.push(this.stockChecksService.getAndSetStockChecks(true, false));
      }

      forkJoin(requests).subscribe(res => {
          //----------------------
          //sites
          //----------------------
          const sites = res[0];
          this.constants.Sites = sites.sort((a, b) => a.description.localeCompare(b.description));

          if (this.selections.userIsScanAndPrintOnly) {
            this.router.navigateByUrl('/labelPrinter');
          } else {

          //----------------------
          //stockchecks
          //----------------------
          //we will go ahead and load the first one
          const userHomeSites = this.stockChecksService.stockCheckVMs.filter(x=>x.site===this.selections.userHomeSiteName)
          if(userHomeSites.length>0){
            const mostRecent = userHomeSites.sort((a,b)=>a.id-b.id)[0];
            this.stockChecksService.loadStockCheck(mostRecent,true);
            
            this.router.navigateByUrl('/home');
          }else{
            this.router.navigateByUrl('/stockChecks');

          }




          //----------------------
          //finish
          //----------------------

        }

      })
    })


  }

  processWhoAmIData(userDetails: WhoAmIResult) {
    this.selections.userRole = userDetails.role;// 'SysAdministrator' // userDetails.role;
    this.selections.userId = userDetails.userId;// 1// userDetails.id;
    this.selections.usersName = userDetails.usersName;// 'Nimish Tandon'// userDetails.name;
    this.selections.userUsername = userDetails.userUsername;
    this.selections.userIsApprover = this.selections.userRole === 'Approver' || this.selections.userRole === 'SysAdministrator' || this.selections.userRole === 'GeneralManager';//to do
    this.selections.userIsGeneralManager = this.selections.userRole === 'GeneralManager';
    this.selections.userIsReadOnly = this.selections.userRole === 'ReadOnly' || this.selections.userRole === 'ScanAndView';
    this.selections.userIsScanAndPrintOnly = this.selections.userRole === 'ScanAndPrint';
    this.selections.userIsResolver = this.selections.userRole === 'Resolver';
    this.selections.userSiteIds = userDetails.userSiteIds.split(',').map(x => parseInt(x));
    this.selections.userHomeSiteName = userDetails.userHomeSiteName;
    this.selections.dealerGroups = userDetails.dealerGroupsForSysAdmin;

    if (this.selections.dealerGroups.length > 0) {
      this.selections.userDealerGroup = this.selections.dealerGroups.find(d => d.isSelected == true).description;
    }

    this.constants.ResolutionTypes = userDetails.resolutionTypes;
    this.constants.ResolutionTypesActive = this.constants.ResolutionTypes.filter(x => x.isActive);
    this.constants.GlobalParams = userDetails.globalParams;
    this.constants.ImageKey = this.constants.GlobalParams.find(x => x.name === 'ImageKey').stringValue;
    this.constants.ConfigFilePath = this.constants.GlobalParams.find(x => x.name === 'ConfigFilePath').stringValue;
    this.constants.IsUploadDMSStockEnabled = this.constants.GlobalParams.find(x => x.name === 'IsUploadDMSStockEnabled')?.boolValue;
    this.constants.IsSignoffConsignedEnabled = this.constants.GlobalParams.find(x => x.name === 'IsSignoffConsignedEnabled')?.boolValue;
    this.constants.shouldUploadSignoffImage = this.constants.GlobalParams.find(x => x.name === 'shouldUploadSignoffImage')?.boolValue;
    this.constants.BackgroundImageURL = this.constants.GlobalParams.find(x => x.name === 'backgroundImageURL').stringValue;
    this.constants.requireBackupForMissingResolution = this.constants.GlobalParams.find(x => x.name === 'RequireBackupForMissingResolution')?.boolValue;
    this.constants.requireBackupForUnknownResolution = this.constants.GlobalParams.find(x => x.name === 'RequireBackupForUnknownResolution')?.boolValue;
    this.constants.strictStockCheckStatus = this.constants.GlobalParams.find(x => x.name === 'strictStockCheckStatus')?.boolValue;
    this.constants.strictTBStockCheckStatus = this.constants.GlobalParams.find(x => x.name === 'strictTBStockCheckStatus')?.boolValue;
    this.constants.allowAgencyStockUpload = this.constants.GlobalParams.find(x => x.name === 'allowAgencyStockUpload')?.boolValue || false;;
    this.constants.AutomatedDataImport = this.constants.GlobalParams.find(x => x.name === 'AutomatedDataImport')?.boolValue || false;
    this.constants.neverScanReg = this.constants.GlobalParams.find(x => x.name === 'NeverScanReg')?.boolValue;

    this.constants.currency = this.constants.GlobalParams.find(x => x.name === 'currency').stringValue;
    this.constants.currencySymbol = this.getCurrencySymbol(this.constants.currency);

    let allowableRoles: string[] = ['SysAdministrator', 'Approver', 'Reconciler', 'Resolver', 'GeneralManager', 'ReadOnly', 'ScanAndPrint', 'ScanAndView']
    if (!allowableRoles.includes(this.selections.userRole)) {
      this.selections.userIsNotAuthorisedForApp = true;
      throw ('User not authorised for App')
    }

    this.constants.showLabelPrintFeature = this.constants.GlobalParams.find(x => x.name === 'ShowLabelPrintFeature')?.boolValue || false;

    let lockedReconcilingItemTypeIds: GlobalParam = this.constants.GlobalParams.find(param => param.name === "LockedReconcilingItemTypeIds");

    if (this.selections.userRole === 'SysAdministrator') {
      this.constants.lockedReconcilingItemTypeIds = [];  
    } else {
      if (lockedReconcilingItemTypeIds.stringValue) {
        this.constants.lockedReconcilingItemTypeIds = lockedReconcilingItemTypeIds.stringValue.split(',').map(Number);
      } else {
        this.constants.lockedReconcilingItemTypeIds = [];
      }
    };

    userDetails.userPreferences.forEach(pref => {
      this.userPreferenceService.preferenceStore[pref.preferenceName] = new UserPreference(pref);
    })

    this.constants.whoAmILoaded = true;
  }

  getCurrencySymbol(currency: string) {
    switch(currency){
      case 'GBP':
        return '£';
      case 'USD':
        return '$'
    }  
  }


  getTokensOutOfStorageAndConfirmIfDone() {

    let accessToken = localStorage.getItem('accessToken');
    let refreshToken = localStorage.getItem('refreshToken');
    if (!accessToken || !refreshToken) { 
      return false; 
    }

    this.constants.accessToken = accessToken;
    this.constants.refreshToken = refreshToken;
    return true;
  }






}

