﻿using log4net;
using StockPulse.Loader.Stockpulse.ViewModel;
using StockPulse.Model;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using StockPulse.Model.Import;
using StockPulse.Repository.Database;
using StockPulse.Loader.ViewModel;

namespace StockPulse.Loader.Services
{


   public class SharedLoaderService
   {
      private readonly ILog logger;
      private string fileName;

      //constructor
      public SharedLoaderService(ILog loggerIn)
      {
         logger = loggerIn;
      }


      public async Task<RowsAndHeadersResult> GetRowsAndHeaders(JobParams parms, string fileToProcess, LogMessage logMessage)
      {

         //try opening the file, if fail, return (common problem is loader trying to open file whilst scraper is saving it).   Silent fail if problem, will try again soon
         try { FileStream fs = File.Open(fileToProcess, FileMode.OpenOrCreate, FileAccess.ReadWrite, FileShare.None); fs.Close(); }
         catch (IOException) { return null; }

         //wait if running in dev
         if (ConfigService.isDev && !Environment.CurrentDirectory.Contains("bin\\Debug"))
         {
            System.Threading.Thread.Sleep(1000 * 60 * 5); //5 minute sleep to prevent concurrent load on database with production loader
         }

         try
         {
            logMessage.SourceDate = DateTime.UtcNow;
            logMessage.Job = GetJobName(parms);

            logger.Info($@"[{DateTime.UtcNow}] New file found at {parms.fileSource}.  Starting {fileToProcess}");   //update logger 

            fileName = Path.GetFileName(fileToProcess);
            var fileDate = DateTime.ParseExact(fileName.Substring(0, 15), "yyyyMMdd_HHmmss", null);
            logMessage.SourceDate = fileDate;

            // Get the data
            if (parms.fileType == FileType.csv)
            {
               if (parms.jobType == LoaderJob.MMGWIP)
               {
                  CleanMMGWipsCsvFile(fileToProcess);
               }

               string[] allRows = GetDataFromFilesService.GetRowsCsv(fileToProcess);

               // Clean CSV if applicable
               if (parms.replaceStrings != null && parms.replaceStrings.Count > 0)
               {
                  allRows = allRows.Select(row =>
                  {
                     foreach (var kvp in parms.replaceStrings)
                     {
                        row = row.Replace(kvp.Key, kvp.Value); // Replace dictionary key with its value
                     }
                     return row;
                  }).ToArray();
               }

               Dictionary<string, int> headerLookup = new Dictionary<string, int>();
               string[] headers;

               int rowsToSkip = parms.rowsToSkip == null ? 0 : parms.rowsToSkip.Value;

               // If we are passed headers to get, let's get them
               if (parms.headerDefinitions != null)
               {
                  headers = allRows.Skip(rowsToSkip).First().Replace("\"", "").ToUpper().Split(parms.delimiter);

                  foreach (var header in parms.headerDefinitions)
                  {
                     headerLookup.Add(header.Key, Array.IndexOf(headers, header.Value));
                  }
               }
               // If not, let's take the whole thing
               else
               {
                  headers = null;
               }

               //read rowsandCells
               List<string[]> rowsAndCells = ExtractRowsAndCells(parms, allRows, headers, logMessage);

               return new RowsAndHeadersResult()
               {
                  rowsAndCells = rowsAndCells,
                  headerLookup = headerLookup
               };
            }
            // This will therefore be an Excel
            else
            {
               DataRowCollection allDataRows = GetDataFromFilesService.GetRowsExcelNew(fileToProcess);

               Dictionary<string, int> headerLookup = new Dictionary<string, int>();
               List<string[]> rowsAndCells = new List<string[]>();
               string[] headers;

               int rowsToSkip = parms.rowsToSkip == null ? 0 : parms.rowsToSkip.Value;

               // If we are passed headers to get, let's get them
               if (parms.headerDefinitions != null)
               {
                  headers = allDataRows[rowsToSkip].ItemArray.Select(item => item.ToString()).ToArray();

                  foreach (var header in parms.headerDefinitions)
                  {
                     headerLookup.Add(header.Key, Array.IndexOf(headers, header.Value));
                  }

                  //headers = null;
               }
               // If not, let's take the whole thing
               else
               {
                  headers = null;
               }

               for (int rowCount = rowsToSkip + 1; rowCount < allDataRows.Count; rowCount++)
               {
                  string[] newRow = allDataRows[rowCount].ItemArray.Select(item => item.ToString()).ToArray();
                  rowsAndCells.Add(newRow);
               }

               return new RowsAndHeadersResult()
               {
                  rowsAndCells = rowsAndCells,
                  headerLookup = headerLookup
               };
            }


         }
         catch (Exception ex)
         {
            throw new Exception(ex.Message, ex);
         }
      }

      public async Task FinishImport(JobParams parms, DataTable dataTable, DateTime start, string processingFilePath, LogMessage logMessage)
      {

         DateTime finishedInterpetFile = DateTime.UtcNow;

         // Depending on what job we have - clear respective input table for the type
         if (parms.jobType.ToString().Contains("TB"))
         {
            await HelpersService.ClearInputTBs(parms.dealerGroupId, parms.isUS);
         }
         else if (parms.jobType.ToString().Contains("WIP") || parms.jobType.ToString().Contains("StockAtAuction") || parms.jobType.ToString().Contains("StockOnLoan") || parms.jobType.ToString().Contains("Booked"))
         {
            await HelpersService.ClearInputReconcilingItems(parms.dealerGroupId, parms.reconcilingItemTypeIdsToInclude, parms.reconcilingItemTypeIdsToExclude, parms.isUS);
         }
         else if (parms.jobType.ToString().Contains("StockItems"))
         {
            await HelpersService.ClearInputStocks(parms.dealerGroupId, parms.stockTypes, parms.isUS);
         }

         await HelpersService.BulkCopyTable("input", parms.loadingTableName, dataTable, parms.isUS);

         using (var db = new StockpulseContext(parms.isUS))
         {
            DateTime finishedUpdateDb = DateTime.UtcNow;

            // If we want to do an import as well
            if (parms.importSPName != null)
            {
               await HelpersService.ExecuteSPWithConnectionAsync(parms.importSPName, ConfigService.connectionNameUK, parms.isUS);

               // TODO and take to dbo
               // parms.takeToDbo != null { do }

               try
               {
                  await db.SaveChangesAsync();
                  finishedUpdateDb = DateTime.UtcNow;
               }
               catch (Exception err)
               {
                  logMessage.FailNotes = logMessage.FailNotes + "Failed to save to DB " + err.ToString();
                  parms.errorCount++;
               }
            }

            logMessage.Id = 0; // Should always be zero as new message
            logMessage.FinishDate = DateTime.UtcNow;
            logMessage.ProcessedCount = dataTable.Rows.Count;
            logMessage.IsCompleted = true;
            logMessage.ErrorCount = parms.errorCount;

            MoveFileToProcessed(parms, start, finishedUpdateDb, finishedInterpetFile, processingFilePath, logMessage);
         }

      }

      private void MoveFileToProcessed(JobParams parms, DateTime start, DateTime finishedUpdateDb, DateTime finishedInterpetFile, string fileToProcess, LogMessage logMessage)
      {
         try
         {
            logMessage.Job = GetJobName(parms);
            logMessage.DealerGroup_Id = parms.dealerGroupId;

            if (parms.fileType == FileType.csv)
            {
               File.Move(fileToProcess, fileToProcess.Replace(@"\inbound", @"\processed").Replace("p.csv", $"processed{DateTime.UtcNow.ToString("yyyyMMdd_HHmmss")}.csv"));
            }
            else if (parms.fileType == FileType.xlsx)
            {
               File.Move(fileToProcess, fileToProcess.Replace(@"\inbound", @"\processed").Replace("p.xlsx", $"processed{DateTime.UtcNow.ToString("yyyyMMdd_HHmmss")}.xlsx"));
            }
            if (parms.errorCount > 0)
            {
               //we have errors so use the reporter
               logMessage.FailNotes = $"{parms.errorCount} errors " + "\r\n ------------------------ Errors ----------------------------- \r\n" + logMessage.FailNotes;

               parms.errorCount--;

               CentralLoggingService.ReportError(logMessage, parms.isUS, true);
            }
            else
            {
               logMessage.InterpretFileSeconds = (int)(finishedInterpetFile - start).TotalSeconds;
               logMessage.UpdateDbSeconds = (int)(finishedUpdateDb - finishedInterpetFile).TotalSeconds;
               logMessage.FinalPartsSeconds = (int)(DateTime.UtcNow - finishedUpdateDb).TotalSeconds;

               using (var db = new StockpulseContext(parms.isUS))
               {
                  db.LogMessages.Add(logMessage);
                  db.SaveChanges();
               }

            }
         }
         catch (Exception err)
         {
            logMessage.FailNotes = logMessage.FailNotes + " FAILED moving file and logging to server" + err.ToString();
            logMessage.Job = GetJobName(parms);
            logMessage.DealerGroup_Id = parms.dealerGroupId;
            parms.errorCount++;
            CentralLoggingService.ReportError(logMessage, parms.isUS);
         }
      }

      public static string GetJobName(JobParams parms)
      {
         string jobName;

         if (parms.additionalInfo == null)
         {
            jobName = parms.jobName;
         }
         else
         {
            jobName = parms.jobName + " - " + parms.additionalInfo;
         }

         return jobName;
      }

      private List<string[]> ExtractRowsAndCells(JobParams parms, string[] allRows, string[] headers, LogMessage logMessage)
      {
         // If headers already provided skip first row, if no headers take everything
         var bodyRows = headers != null ? allRows.Skip(1) : allRows;

         List<string[]> rowsAndCells = new List<string[]>();

         foreach (var row in bodyRows)
         {
            if (string.IsNullOrEmpty(row)) { continue; } //skip empties

            string[] rowCols = Regex.Matches(row, parms.regexPattern)
                .Cast<Match>()
                .Select(m => m.Value)
                .ToArray();


            if (headers != null && rowCols.Length != headers.Length)
            {
               logMessage.FailNotes = logMessage.FailNotes + $"ExtractRowsAndCells Error: Skipped rowCol as had {rowCols.Length} rowCols and needed {headers.Length} <br>";
               parms.errorCount++;
               continue;
            }
            else if (parms.knownHeaderLength != null && (rowCols.Length != parms.knownHeaderLength))
            {
               logMessage.FailNotes = logMessage.FailNotes + $"ExtractRowsAndCells Error: Skipped rowCol as had {rowCols.Length} rowCols and needed {parms.knownHeaderLength} <br>";
               parms.errorCount++;
               continue;
            }

            rowsAndCells.Add(rowCols);
         }

         return rowsAndCells;
      }

      // isUS refers to which server we are using
      public async Task<int> AddFileToImports(string filePath, bool isUS = false)
      {
         try
         {
            using (var db = new StockpulseContext(isUS))
            {
               FileImport newFileImport = new FileImport();
               newFileImport.FileName = Path.GetFileName(filePath);
               newFileImport.LoadDate = DateTime.Now;
               newFileImport.FileDate = DateTime.ParseExact(newFileImport.FileName.Substring(0, 15), "yyyyMMdd_HHmmss", null); // Need to interpret this
               newFileImport.LoadedByUserId = isUS ? 1749 : 433; // How are we getting this? Hardcode to Rich Id / System user for now

               db.FileImports.Add(newFileImport);
               await db.SaveChangesAsync();
               return newFileImport.Id;
            }
         }
         catch (Exception ex)
         {
            return 0;
         }
      }

      public static void CleanMMGWipsCsvFile(string filePath)
      {
         string content = File.ReadAllText(filePath);

         // 🔹 Regex to find CRLF (`\r\n`) NOT preceded by a digit (`0-9`)
         string cleanedContent = Regex.Replace(content, @"(?<!\d)\r\n", "");

         File.WriteAllText(filePath, cleanedContent);
      }

      public static bool SkipSiteForLithiaUS(string name)
      {
         var sitesToSkip = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
            {
                "Bend Nissan", "Bennington Ford", "Bennington Honda", "Bennington Hyundai", "Bennington Toyota",
                "Carbone Recon Center", "Concord Wholesale Vehicles", "Denver Bentley", "DFC SPE2",
                "Driveway Finance Corp", "Financial", "Gardena Honda", "Grand Forks CJD", "Grand Forks Ford",
                "Grand Forks Toyota", "Great Falls Honda", "LAD Printing", "Lodi Toyota", "McLaren Distribution Ctr.",
                "Miami Genesis", "Mobile Subaru", "Monroeville Ford", "New Port Richey Genesis",
                "New Port Richey Used Center", "Portland CJD", "Roseville Chevrolet", "Salem VW Wholesale",
                "SCFC SPE", "Temecula Acura", "Utica Body Shop", "Utica Buick Cadillac GMC", "Valencia Audi",
                "Vaughan Singer", "Vienna Mazda", "Vienna Nissan", "Woodbridge Pagani", "Yorkville Chevrolet",
                "Yorkville Honda", "Yorkville Nissan", "Support Services", "Florida Support Services",
                "Finance Department", "IT Department", "HR Legal Department"
            };

         // Skip if it's in the list or contains "LADAR" (case-insensitive)
         return sitesToSkip.Contains(name) || name.IndexOf("LADAR", StringComparison.OrdinalIgnoreCase) >= 0;
      }

   }
}
