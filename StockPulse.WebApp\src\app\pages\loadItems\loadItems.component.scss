.dropdownButtonToPickReportType {
    width: 34em;
    margin-right: 1em;
    margin-left: 1em;

    &.restricted {
        width: 20em;
    }
}

.reconcilingItemTypeButton {
    width: 34em;
}

#buttons {
    button {
        margin-right: 1em;
    }
}

.buttonInner {
    display: inline-block;
    width: 97%;
    display: inline-block;
}

table.newItem {
    width: 50%;
    margin: 2em auto;

    input {
        width: 14em;
        text-align: center;
        line-height: 2em;
    }
}

.search-bar {
    display: flex;
    align-items: center;
}

#importComponent {
    display: flex;
    flex-direction: column;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 1em;
    overflow: hidden;
}

reconcilingItemsTable,
dmsstocktable,
trialBalanceTable {
    flex: 1;
}

.reportDescription {
    text-overflow: ellipsis;
    overflow: hidden;
}

.dropdownSubTitle {
    text-align: left;
    color: #FFFFFF;
    margin-left: 0.9em;
}