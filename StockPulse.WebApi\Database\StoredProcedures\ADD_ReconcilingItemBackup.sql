

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].[ADD_ReconcilingItemBackup]
(
	@FileName nvarchar(200),
    @StockCheckId INT,
	@ReconcilingItemTypeId INT,
	@UserId INT
)
AS
BEGIN

SET NOCOUNT ON;

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END

INSERT INTO [dbo].[ReconcilingItemBackups]
    ([StockCheckId],[ReconcilingItemTypeId],[FileName])
VALUES
    (@StockCheckId, @ReconcilingItemTypeId, @FileName)

	DECLARE @Id INT
	SET @Id = SCOPE_IDENTITY();

	EXEC [dbo].[UPDATE_StockCheckLastUpdate] @StockCheckId, @UserId

	SELECT @Id


END

GO
