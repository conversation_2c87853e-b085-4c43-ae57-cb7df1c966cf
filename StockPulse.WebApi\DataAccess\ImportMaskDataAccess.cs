﻿using Dapper;
using StockPulse.Model;
using StockPulse.WebApi.Dapper;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace StockPulse.WebApi.DataAccess
{
    public interface IImportMaskDataAccess
    {
        Task<IEnumerable<ImportMaskWithCreatedBy>> GetImportMasks(int userId);
        Task SaveExistingImportMask(ImportMaskSaveWithId item, int userId);
        Task SaveNewImportMask(ImportMaskSave item, int userId);
        Task UpdateImportMasks(ImportMaskUpdateParams parms);
        Task DeleteImportMask(int importMaskId);
    }

    public class ImportMaskDataAccess : IImportMaskDataAccess
    {
        private readonly IDapper dapper;

        public ImportMaskDataAccess(IDapper dapper)
        {
            this.dapper = dapper;
        }

        public async Task<IEnumerable<ImportMaskWithCreatedBy>> GetImportMasks(int userId)
        {
            var paramList = new DynamicParameters();
            paramList.Add("UserId", userId);

            return await dapper.GetAllAsync<ImportMaskWithCreatedBy>("dbo.GET_ImportMasks",paramList, System.Data.CommandType.StoredProcedure);

        }

        public async Task SaveExistingImportMask(ImportMaskSaveWithId item, int userId)
        {
            var paramList = new DynamicParameters();
            paramList.Add("UserId", userId);
            paramList.Add("Id", item.Id);
            paramList.Add("Name", item.Name);
            paramList.Add("TopRowsToSkip", item.TopRowsToSkip);
            paramList.Add("ColumnValueEqualsesJSON", item.ColumnValueEqualsesJSON);
            paramList.Add("ColumnValueDifferentFromsJSON", item.ColumnValueDifferentFromsJSON);
            paramList.Add("ColumnValueNotNullsJSON", item.ColumnValueNotNullsJSON);
            paramList.Add("ColumnsWeWantJSON", item.ColumnsWeWantJSON);
            paramList.Add("IsStandard", item.IsStandard);
            paramList.Add("IsMultiSite", item.IsMultiSite);
            paramList.Add("IgnoreZeroValues", item.IgnoreZeroValues);

            await dapper.ExecuteAsync("dbo.UPDATE_ImportMask", paramList, System.Data.CommandType.StoredProcedure);
            
        }



        public async Task SaveNewImportMask(ImportMaskSave item, int userId)
        {
            var paramList = new DynamicParameters();
            paramList.Add("UserId", userId);
            paramList.Add("Name", item.Name);
            paramList.Add("TopRowsToSkip", item.TopRowsToSkip);
            paramList.Add("ColumnValueEqualsesJSON", item.ColumnValueEqualsesJSON);
            paramList.Add("ColumnValueDifferentFromsJSON", item.ColumnValueDifferentFromsJSON);
            paramList.Add("ColumnValueNotNullsJSON", item.ColumnValueNotNullsJSON);
            paramList.Add("ColumnsWeWantJSON", item.ColumnsWeWantJSON);
            paramList.Add("IsStandard", item.IsStandard);
            paramList.Add("IsMultiSite", item.IsMultiSite);
            paramList.Add("IgnoreZeroValues", item.IgnoreZeroValues);

            await dapper.ExecuteAsync("dbo.CREATE_ImportMask", paramList, System.Data.CommandType.StoredProcedure);
            
        }

        public async Task UpdateImportMasks(ImportMaskUpdateParams parms)
        {
            foreach (var item in parms.MasksToUpdate)
            {
                var paramList = new DynamicParameters();
                paramList.Add("Id", item.id);
                paramList.Add("Name", item.newValue);
                paramList.Add("IsStandard", item.isStandard);
                paramList.Add("IsMultiSite", item.IsMultiSite);
                paramList.Add("IgnoreZeroValues", item.IgnoreZeroValues);

                await dapper.ExecuteAsync("dbo.UPDATE_ImportMaskName", paramList, System.Data.CommandType.StoredProcedure);
            }
        }

        public async Task DeleteImportMask(int importMaskId)
        {
            var paramList = new DynamicParameters();
            paramList.Add("Id", importMaskId);
            await dapper.ExecuteAsync("dbo.DELETE_ImportMask", paramList, System.Data.CommandType.StoredProcedure);
        }
    }


}
