import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { PreferenceKey } from 'src/app/model/UserPreference';
import { ConstantsService } from 'src/app/services/constants.service';
import { IconService } from 'src/app/services/icon.service';
import { ToastService } from 'src/app/services/newToast.service';
import { SelectionsService } from 'src/app/services/selections.service';
import { UserPreferenceService } from 'src/app/services/userPreference.service';
import version from './../../../../package.json';

interface MenuItem {
  name: string;
  icon: string;
  link: string;
  isActive: boolean;
  hide?: boolean;
}

interface MenuSection { header: string, items: MenuItem[] }

@Component({
  selector: 'app-sidenav',
  templateUrl: './sidenav.component.html',
  styleUrls: ['./sidenav.component.scss']
})
export class SidenavComponent implements OnInit {
  menuSections: MenuSection[];
  menuIsWide: boolean;
  showMenuLabels: boolean;
  disabledRoutes: boolean;
  version: string;
  amHoveringSideMenu: boolean;
  keepMenuWide: boolean;
  get keepMenuFixed(): boolean{
    return this.userPreferenceService.getPreference(PreferenceKey.KeepMenuFixed);
  }

  constructor(
    public icon: IconService,
    public selections: SelectionsService,
    public currentRoute: ActivatedRoute,
    public router: Router,
    public toastService: ToastService,
    public constants: ConstantsService,
    public userPreferenceService: UserPreferenceService
  ) {
  }

  ngOnInit(): void {
    this.version = version.version;

    if (!this.selections.userIsScanAndPrintOnly) {
      this.menuSections = [
        {
          header: '', items: [
            { name: 'Home', icon: 'faHome', link: '/home', isActive: false },
          ]
        },
        {
          header: 'Load Stock Check', items: [
            { name: 'Load Stock Check', icon: 'faFolderOpen', link: '/stockChecks', isActive: false },
          ]
        },
        {
          header: 'Carry out Stock Check', items: [
            { name: 'Import Data', icon: 'faFileImport', link: '/loadItems', isActive: false },
            { name: 'Reconcile', icon: 'faChartWaterfall', link: '/reconcile', isActive: false },
            { name: 'Sign-off', icon: 'faBadgeCheck', link: '/signoff', isActive: false }
          ]
        },

        {
          header: 'Review Stock Check Results', items: [
            { name: 'Photo Map', icon: 'faCamera', link: '/photoMap', isActive: false },
            { name: 'Reg / VIN Mismatches', icon: 'faTriangleExclamation', link: '/vehiclesWithWrongReg', isActive: false, hide: this.constants.GlobalParams.find(x => x.name === 'NeverScanReg').boolValue },
            { name: 'Recurring Issues', icon: 'faRepeat', link: '/repeatOffenders', isActive: false },
            { name: 'Vehicle Search', icon: 'faMagnifyingGlass', link: '/vehicleSearch', isActive: false }
          ]
        },
        {
          header: 'Administration', items: [
            { name: 'Print Labels', icon: 'faPrint', link: '/labelPrinter', isActive: false, hide: !this.constants.showLabelPrintFeature },
            { name: 'User Maintenance', icon: 'faUsers', link: '/userMaintenance', isActive: false }
          ]
        }
      ]

      if(this.selections.userRole==='SysAdministrator' && (this.selections.userUsername.endsWith('@cphi.co.uk') || this.selections.userUsername.endsWith('@cphinsight.com'))){
        this.menuSections[4].items.push({ name: 'Table Maintenance', icon: 'faTerminal', link: '/tableMaintenance', isActive: false })
      }
      
    } else {
      this.menuSections = [
        {
          header: '', items: [
            { name: 'Print Labels', icon: 'faPrint', link: '/labelPrinter', isActive: true }
          ]
        }
      ]
    }
  }

  makeMenuWide() {
    this.menuIsWide = true;

    setTimeout(() => {
      this.showMenuLabels = true;
      this.showMenuLabels = true;
    }, 50);
  }

  routeIsActive(menuItem: MenuItem) {
    return menuItem.link === this.currentRoute.snapshot['_routerState'].url;
  }

  goTo(newRoute: string) {
    if (this.router.url !== newRoute) {
      this.toastService.loadingToast();
      this.highlightActiveMenu(newRoute);
      this.router.navigateByUrl(newRoute);
    }
  }

  highlightActiveMenu(url: string) {
    this.menuSections.forEach(section => {
      section.items.forEach(menuItem => {
        menuItem.isActive = menuItem.link === url ? true : false;
      })
    })
  }

  enableRoute(route: string) {
    const routesNotRequiringStockcheck: string[] = ['/home', '/stockChecks', '/training', '/userMaintenance', '/labelPrinter', '/tableMaintenance', '/vehicleSearch'];
    return this.selections.stockCheck || routesNotRequiringStockcheck.includes(route)
  }

  getLogo() {
    return this.constants.lightTheme ? '../../../assets/imgs/cph-logo-black.png' : '../../../assets/imgs/cph-logo.png';
  }

  maybeHideMenu(event) {
    this.amHoveringSideMenu = false;
    
    if (this.keepMenuWide || this.userPreferenceService.getPreference(PreferenceKey.KeepMenuFixed) || event.pageX < 200) {
      return;
    }
    this.closeSideMenu();
  }

  fixMenu() {
    const isFixed:boolean = this.userPreferenceService.getPreference(PreferenceKey.KeepMenuFixed);

    if(isFixed){
      this.closeSideMenu();
      this.userPreferenceService.setPreference(PreferenceKey.KeepMenuFixed, false);
    }
    else{
      this.userPreferenceService.setPreference(PreferenceKey.KeepMenuFixed, true);
    }

    setTimeout(() => {
      this.constants.sidenavToggledEmitter.emit();
    }, 500)
  }

  showFixMenuToggle() {
    if (this.userPreferenceService.getPreference(PreferenceKey.KeepMenuFixed)  && this.amHoveringSideMenu) {
      return true;
    } else if (this.userPreferenceService.getPreference(PreferenceKey.KeepMenuFixed) && !this.amHoveringSideMenu) {
      return false;
    } else if (!this.userPreferenceService.getPreference(PreferenceKey.KeepMenuFixed) && this.showMenuLabels) {
      return true;
    } else {
      return false;
    }
  }

  closeSideMenu() {
    if (this.keepMenuFixed) { return; }
    this.showMenuLabels = false;
    this.menuIsWide = false;
  }

  get isMenuWide() {
    if (this.userPreferenceService.getPreference(PreferenceKey.KeepMenuFixed) || this.menuIsWide) {
      return true;
    };
  }
}
