﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace StockPulse.Model.Import
{
    [Table("FileImports", Schema = "import")]
    public class FileImport
    {
        [Key]
        public int Id { get; set; }

        public string FileName { get; set; }

        public DateTime FileDate { get; set; }

        public DateTime LoadDate { get; set; }

        public int LoadedByUserId { get; set; }
        [ForeignKey("LoadedByUserId")]
        public virtual User LoadedByUser { get; set; }

    }

}