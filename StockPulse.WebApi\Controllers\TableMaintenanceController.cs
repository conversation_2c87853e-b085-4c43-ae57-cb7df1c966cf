﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using static StockPulse.WebApi.Attribute.UserLevelAccessAttribute;
using StockPulse.WebApi.Attribute;
using StockPulse.Model;
using StockPulse.WebApi.Service;
using System;


namespace StockPulse.WebApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    [UserLevelAccess(new[] { UserRole.SysAdministrator })]
    public class TableMaintenanceController : ControllerBase, IAttributeValueProvider
    {
        private readonly IMaintenanceTableService maintenanceTableService;
        private readonly string userRole;
        private readonly string userEmail;

        public UserRole attributeUserRole => GetUserRole();
        private UserRole GetUserRole()
        {
            return (UserRole)Enum.Parse(typeof(UserRole), userRole);
        }


        public TableMaintenanceController(IMaintenanceTableService msr, IUserService userService)
        {
            maintenanceTableService = msr;
            userRole = userService.GetUserRole();
            userEmail = userService.GetUserEmailAddressFromToken();
        }




        [HttpGet]
        [Route("[action]")]
        public async Task<IEnumerable<MaintenanceTable>> GetTables()
        {
            if (!userEmail.EndsWith("@cphi.co.uk") && !userEmail.EndsWith("@cphinsight.com")) return null;
                
            return await maintenanceTableService.GetTables();
        }


        [HttpGet]
        [Route("[action]")]
        public async Task<string> GetData(int tableId)
        {
            if (!userEmail.EndsWith("@cphi.co.uk") && !userEmail.EndsWith("@cphinsight.com")) return null;

            return await maintenanceTableService.GetData(tableId);
        }

        [HttpPost]
        [Route("[action]")]
        public async Task<bool> SaveData(MaintenanceTableSaveVM maintenanceTableSaveVM)
        {
            if (!userEmail.EndsWith("@cphi.co.uk") && !userEmail.EndsWith("@cphinsight.com")) return false;

            return await maintenanceTableService.SaveData(maintenanceTableSaveVM);
        }

        [HttpPost]
        [Route("[action]")]
        public async Task<bool> DeleteData(MaintenanceTableDeleteVM maintenanceTableDeleteVM)
        {
            if (!userEmail.EndsWith("@cphi.co.uk") && !userEmail.EndsWith("@cphinsight.com")) return false;

            return await maintenanceTableService.DeleteData(maintenanceTableDeleteVM);
        }


    }
}
