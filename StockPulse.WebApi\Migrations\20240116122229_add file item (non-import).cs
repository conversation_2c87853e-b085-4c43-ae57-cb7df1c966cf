﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace StockPulse.WebApi.Migrations
{
    /// <inheritdoc />
    public partial class addfileitemnonimport : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "FileImports",
                schema: "dbo",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    FileName = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    FileDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    LoadDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    LoadedByUserId = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_FileImports", x => x.Id);
                    table.ForeignKey(
                        name: "FK_FileImports_Users_LoadedByUserId",
                        column: x => x.LoadedByUserId,
                        principalSchema: "dbo",
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_FileImports_LoadedByUserId",
                schema: "dbo",
                table: "FileImports",
                column: "LoadedByUserId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "FileImports",
                schema: "dbo");
        }
    }
}
