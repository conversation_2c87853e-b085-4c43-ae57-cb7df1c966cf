﻿using Microsoft.Extensions.Configuration;
using StockPulse.WebApi.DataAccess;
using StockPulse.WebApi.ViewModel;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;

namespace StockPulse.WebApi.Service
{

    public interface IStockCheckReconciliationService
    {
        Task ReconcileStockCheck(int stockCheckId, int userId);
        //Task ReconcileStockChecks(string stockCheckIds, int userId);
    }

    public class StockCheckReconciliationService : IStockCheckReconciliationService
    {
        //properties of the service
        private readonly IStockCheckDataAccess stockCheckDataAccess;
        private readonly IStockItemDataAccess stockItemDataAccess;
        private readonly IScanDataAccess scanDataAccess;
        private readonly IReconcilingItemDataAccess reconcilingItemDataAccess;


        public StockCheckReconciliationService(IStockCheckDataAccess stockCheckDataAccess, IConfiguration config, IStockItemDataAccess stockItemDataAccess, IScanDataAccess scanDataAccess, IReconcilingItemDataAccess reconcilingItemDataAccess)
        {
            this.stockCheckDataAccess = stockCheckDataAccess;
            //_config = config;
            this.stockItemDataAccess = stockItemDataAccess;
            this.scanDataAccess = scanDataAccess;
            this.reconcilingItemDataAccess = reconcilingItemDataAccess;
        }

        public StockCheckReconciliationService()
        {

        }

        //public async Task ReconcileStockChecks(string stockCheckIds, int userId)
        //{
        //    foreach (var id in stockCheckIds)
        //    {
        //        await ReconcileStockCheck(id, userId);
        //    }
        //}


        public async Task ReconcileStockCheck(int stockCheckId, int userId)
        {
            StockcheckDataToReconcile data = await GetStockcheckData(stockCheckId, userId);
            ReconciliationResults results = DoReconcileStockCheck(data, data.RecProcessMatchRequiresBothRegAndVin);

            //---------------------------
            // Save changes to the db
            //---------------------------

            //IEnumerable<LabelAndValue> testResultBefore = await stockCheckDataAccess.GetStockCheckResultsTest(stockCheckId);

            await stockCheckDataAccess.SaveReconciliationResults(results, stockCheckId, userId);

            //IEnumerable<LabelAndValue> testResultAfter = await stockCheckDataAccess.GetStockCheckResultsTest(stockCheckId);

            //foreach (var item in testResultBefore)
            //{
            //    var matchingInAfter = testResultAfter.First(x => x.Label == item.Label);
            //    if (matchingInAfter.Value != item.Value)
            //    {
            //        { }  //change spotted
            //    }
            //    else
            //    {
            //        { }
            //    }
            //}

            return;
        }


        private async Task<StockcheckDataToReconcile> GetStockcheckData(int stockCheckId, int userId)
        {
            //---------------------------
            //0. Get Data
            //---------------------------

            StockcheckDataToReconcile result = new StockcheckDataToReconcile();

            //Download our scans, our stockItems, our reconcilingItems
            result.OurScans = await scanDataAccess.GetMatchItems(stockCheckId, userId);
            result.OurStockItems = await stockItemDataAccess.GetMatchItems(stockCheckId, userId);
            result.OurReconcilingItemsForMissing = await reconcilingItemDataAccess.GetMissingsMatchItems(stockCheckId, userId);
            result.OurReconcilingItemsForUnknown = await reconcilingItemDataAccess.GetUnknownsMatchItems(stockCheckId, userId);

            //Download other sites' unmatched scans and unmatched stockItems
            result.OtherSitesUnMatchedScans = await scanDataAccess.GetMatchItemsOtherSites(stockCheckId, userId);
            result.OtherSiteUnmatchedStockItems = await stockItemDataAccess.GetMatchItemsOtherSites(stockCheckId, userId);

            result.RecProcessMatchRequiresBothRegAndVin = await stockCheckDataAccess.GetRecProcessMatchChoice(stockCheckId, userId);

            //preprocess scans and stockitems to eliminate any irrelevant regs or vins like " "
            foreach (var item in result.OurScans)
            {
                if (!IsProperItem(item.Reg)) { item.Reg = null; }
                if (!IsProperItem(item.Vin)) { item.Vin = null; }
            }
            foreach (var item in result.OurStockItems)
            {
                if (!IsProperItem(item.Reg)) { item.Reg = null; }
                if (!IsProperItem(item.Vin)) { item.Vin = null; }
            }

            return result;
        }




        public static ReconciliationResults DoReconcileStockCheck(StockcheckDataToReconcile data, bool requireBothRegAndVinMatch)
        {

            ReconciliationResults results = new ReconciliationResults();
            //---------------------------
            //0. DeDupe source items
            //---------------------------

            //deDupe Scans
            List<MatchItem> unDupedScans = DeDupeUsingBothRegAndVinMatch(data.OurScans, results.duplicatedScans);

            //deDupe StockItems
            List<MatchItem> unDupedStockItems = DeDupeUsingBothRegAndVinMatch(data.OurStockItems, results.duplicatedStockItems);


            //---------------------------
            //1. Match up Scans
            //---------------------------

            //match our scans to our stockItems
            List<MatchItem> remainingScansAfterStockItemMatch = requireBothRegAndVinMatch ?
                        MatchUsingBothRegAndVin(unDupedScans, unDupedStockItems, results.scansMatchedToStockItems) :
                        MatchUsingRegOrVin(unDupedScans, unDupedStockItems, results.scansMatchedToStockItems);


            //match our remaining scans against other sites' unmatched stockItems
            List<MatchItem> remainingScansAfterOtherSiteMatch = requireBothRegAndVinMatch ?
                MatchUsingBothRegAndVin(remainingScansAfterStockItemMatch, data.OtherSiteUnmatchedStockItems, results.scansMatchedToOtherSiteStockItems) :
                MatchUsingRegOrVin(remainingScansAfterStockItemMatch, data.OtherSiteUnmatchedStockItems, results.scansMatchedToOtherSiteStockItems);


            //match our remaining scans against our reconciling Items
            results.remainingUnmatchedScans = requireBothRegAndVinMatch ?
                MatchUsingBothRegAndVin(remainingScansAfterOtherSiteMatch, data.OurReconcilingItemsForUnknown, results.scansMatchedToRecItems).ConvertAll(x => new MatchedItem(x)) :
                MatchUsingRegOrVin(remainingScansAfterOtherSiteMatch, data.OurReconcilingItemsForUnknown, results.scansMatchedToRecItems).ConvertAll(x => new MatchedItem(x));

            //---------------------------
            //2.  Match up StockItems
            //---------------------------
            //populate stockItemsMatchedToScans
            foreach (var item in results.scansMatchedToStockItems)
            {
                results.stockItemsMatchedToScans.Add(new MatchedItem() { ItemId = (int)item.MatchItemId, MatchItemId = item.ItemId });
            }
            List<int> stockItemIdsSorted = results.scansMatchedToStockItems.Select(x => (int)x.MatchItemId).ToList();
            List<MatchItem> remainingStockItems = unDupedStockItems.Where(x => !stockItemIdsSorted.Contains(x.ItemId)).ToList();

            //match remaining stock items against other sites spare scans
            List<MatchItem> remainingStockItemsAfterOtherSiteScans = requireBothRegAndVinMatch ?
                MatchUsingBothRegAndVin(remainingStockItems, data.OtherSitesUnMatchedScans, results.stockItemsMatchedToOtherSiteScans) :
                MatchUsingRegOrVin(remainingStockItems, data.OtherSitesUnMatchedScans, results.stockItemsMatchedToOtherSiteScans);

            //match remaining stock items against our reconciling items
            results.remainingUnmatchedStockItems = requireBothRegAndVinMatch ?
                MatchUsingBothRegAndVin(remainingStockItemsAfterOtherSiteScans, data.OurReconcilingItemsForMissing, results.stockItemsMatchedToRecItems).ConvertAll(x => new MatchedItem(x)) :
                MatchUsingRegOrVin(remainingStockItemsAfterOtherSiteScans, data.OurReconcilingItemsForMissing, results.stockItemsMatchedToRecItems).ConvertAll(x => new MatchedItem(x));

            return results;
        }






        private static List<MatchItem> MatchUsingBothRegAndVin(IEnumerable<MatchItem> itemsToMatchToSomething, IEnumerable<MatchItem> itemsToMatchAgainst, List<MatchedItem> itemsMatchedToSomething)
        {
            List<MatchItem> unmatchedItems = new List<MatchItem>();
            List<MatchItem> itemsWeCanMatchAgainst = itemsToMatchAgainst.ToList();

            foreach (var item in itemsToMatchToSomething)
            {
                MatchItem regMatch = null;
                MatchItem vinMatch = null;

                //try match on reg
                if (item.Reg != null)
                {
                    regMatch = itemsWeCanMatchAgainst.FirstOrDefault(x => x.Reg == item.Reg);
                }

                if (regMatch != null)
                {
                    ///we have got a match for the reg

                    ///new! STK-427 if we have a vin, should also look to match on this
                    if (item.Vin != null)
                    {
                        vinMatch = itemsWeCanMatchAgainst.FirstOrDefault(x => x.Vin == item.Vin);
                        if (vinMatch != null)
                        {
                            ///we have a matching vin also so..
                            ProcessAMatch(item, vinMatch, itemsWeCanMatchAgainst, itemsMatchedToSomething);
                        }
                        else
                        {
                            ///didn't find a vin match, so unmatched
                            unmatchedItems.Add(item);
                        }
                    }
                    else
                    {
                        ///no vin, but matched reg, so ok
                        ProcessAMatch(item, regMatch, itemsWeCanMatchAgainst, itemsMatchedToSomething);
                    }

                }
                else  ///didn't find a reg match, or there's no reg
                {
                    ///try match on vin
                    if (item.Vin != null)
                    {
                        vinMatch = itemsWeCanMatchAgainst.FirstOrDefault(x => x.Vin == item.Vin);
                    }
                    if (vinMatch != null)
                    {
                        ///we have a match based on vin
                        ProcessAMatch(item, vinMatch, itemsWeCanMatchAgainst, itemsMatchedToSomething);
                    }
                    else
                    {
                        unmatchedItems.Add(item);
                    }
                }

            }

            return unmatchedItems;
        }

        private static List<MatchItem> MatchUsingRegOrVin(IEnumerable<MatchItem> itemsToMatchToSomething, IEnumerable<MatchItem> itemsToMatchAgainst, List<MatchedItem> itemsMatchedToSomething)
        {
            List<MatchItem> unmatchedItems = new List<MatchItem>();
            List<MatchItem> itemsWeCanMatchAgainst = itemsToMatchAgainst.ToList();

            foreach (var item in itemsToMatchToSomething)
            {
                MatchItem regMatch = null;
                MatchItem vinMatch = null;

                if(item.Reg == "LV72HZG")
                {
                    { }
                }
                //try match on reg
                if (item.Reg != null)
                {
                    regMatch = itemsWeCanMatchAgainst.FirstOrDefault(x => x.Reg == item.Reg);
                }

                if (regMatch != null)
                {
                    ///we matched on reg, 
                    ProcessAMatch(item, regMatch, itemsWeCanMatchAgainst, itemsMatchedToSomething);
                }
                else  ///didn't find a reg match, or there's no reg
                {
                    ///try match on vin
                    if (item.Vin != null)
                    {
                        vinMatch = itemsWeCanMatchAgainst.FirstOrDefault(x => x.Vin == item.Vin);
                    }

                    ///finish up
                    if (vinMatch != null)
                    {
                        ProcessAMatch(item, vinMatch, itemsWeCanMatchAgainst, itemsMatchedToSomething);
                    }
                    else
                    {
                        unmatchedItems.Add(item);
                    }
                }

            }

            return unmatchedItems;
        }

        private static void ProcessAMatch(MatchItem item, MatchItem matchingItem, List<MatchItem> itemsWeCanMatchAgainst, List<MatchedItem> itemsMatchedToSomething)
        {
            itemsWeCanMatchAgainst.Remove(matchingItem);
            itemsMatchedToSomething.Add(new MatchedItem() { ItemId = item.ItemId, MatchItemId = matchingItem.ItemId });
        }

        private static List<MatchItem> DeDupeUsingBothRegAndVinMatch(IEnumerable<MatchItem> itemsIn, List<MatchedItem> duplicatedResultsLocation)
        {
            List<MatchItem> unDuplicatedItems = new List<MatchItem>();

            List<string> seenRegsVinCombination = new List<string>();
            List<string> seenRegs = new List<string>();
            List<string> seenVins = new List<string>();

            foreach (var item in itemsIn)
            {
                string regToUse = item.Reg != null ? item.Reg : string.Empty;
                string vinToUse = item.Vin != null ? item.Vin : string.Empty;

                //early exit if no reg or vin
                if (regToUse == string.Empty && vinToUse == string.Empty)
                {
                    unDuplicatedItems.Add(item);
                    continue;
                }

                string regVinCombination = $"{regToUse}#|#{vinToUse}";

                bool seenBefore = false;
                if (regToUse == string.Empty) { seenBefore = seenVins.Contains(vinToUse); }
                else if (vinToUse == string.Empty) { seenBefore = seenRegs.Contains(regToUse); }
                else { seenBefore = seenRegsVinCombination.Contains(regVinCombination); }

                if (seenBefore)
                {
                    //is a dupe
                    duplicatedResultsLocation.Add(new MatchedItem(item));
                }
                else
                {
                    //not a dupe
                    unDuplicatedItems.Add(item);
                }

                seenRegsVinCombination.Add(regVinCombination);
                seenRegs.Add(regToUse);
                seenVins.Add(vinToUse);
            }

            return unDuplicatedItems;
        }


        private static List<MatchItem> DeDupeUsingRegOrVinMatch(IEnumerable<MatchItem> itemsIn, List<MatchedItem> duplicatedResultsLocation)
        {
            List<MatchItem> unDuplicatedItems = new List<MatchItem>();

            List<string> seenRegs = new List<string>();
            List<string> seenVins = new List<string>();
            foreach (var item in itemsIn)
            {
                //early exit if no reg or vin
                if (item.Reg == null && item.Vin == null)
                {
                    unDuplicatedItems.Add(item);
                    continue;
                }


                string regToUse = item.Reg != null ? item.Reg : string.Empty;
                string vinToUse = item.Vin != null ? item.Vin : string.Empty;


                if (seenRegs.Contains(regToUse) || seenVins.Contains(vinToUse))
                {
                    //is a dupe
                    duplicatedResultsLocation.Add(new MatchedItem(item));
                }
                else
                {
                    //not a dupe
                    unDuplicatedItems.Add(item);
                }

                if (regToUse != string.Empty) { seenRegs.Add(regToUse); };
                if (vinToUse != string.Empty) { seenVins.Add(vinToUse); };
            }

            return unDuplicatedItems;
        }

        private bool IsProperItem(string itemValue)
        {
            return itemValue != null && itemValue != "" && itemValue != " " && itemValue != "INPUT" && itemValue != "TBC";
        }

    }
}
