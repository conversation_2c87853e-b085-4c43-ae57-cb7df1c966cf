﻿using System.Collections.Generic;

using StockPulse.Loader.Services;
using StockPulse.Loader.Stockpulse.ViewModel;
using StockPulse.Loader.Services.Jardine;
using StockPulse.Loader.Services.MMG;
using StockPulse.Loader.Services.Lithia;
using StockPulse.Loader.ViewModel;
using StockPulse.Model;

namespace StockPulse.Loader.Jobs
{
    public static class GenericJobCustomerLookup
    {


        public static List<JobParams> BuildIncomingLoads()
        {
            List<JobParams> loads = new List<JobParams>();

            // Build up for each customer
            //--------------------------------------- UK--------------------------------------------------------------------------
            if (!ConfigService.isUS)
            {

                // JARDINE //
                var jardineTB = new JardineTBLoaderService(); // input.FinancialLines
                loads.Add(jardineTB.GetMatchingFilesAndImportParams(ConfigService.incomingRoot));

                var jardineStocks = new JardineStocksLoaderService(); // input.StockItems
                loads.Add(jardineStocks.GetMatchingFilesAndImportParams(ConfigService.incomingRoot));

                var jardineWIPs = new JardineWIPLoaderService(); // input.ReconcilingItems
                loads.Add(jardineWIPs.GetMatchingFilesAndImportParams(ConfigService.incomingRoot));

                // MMG //
                var mmgTB = new MMGTBLoaderService(); // input.FinancialLines
                loads.Add(mmgTB.GetMatchingFilesAndImportParams(ConfigService.incomingRoot));

                var mmgStock = new MMGStockLoaderService(); // input.StockItems
                loads.Add(mmgStock.GetMatchingFilesAndImportParams(ConfigService.incomingRoot));

                var mmgWIP = new MMGWIPLoaderService(); // input.ReconcilingItems
                loads.Add(mmgWIP.GetMatchingFilesAndImportParams(ConfigService.incomingRoot));

                var mmgAtAuction = new MMGAtAuctionLoaderService(); // input.ReconcilingItems
                loads.Add(mmgAtAuction.GetMatchingFilesAndImportParams(ConfigService.incomingRoot));

                var mmgOnLoan = new MMGStockOnLoanLoaderService(); // input.ReconcilingItems
                loads.Add(mmgOnLoan.GetMatchingFilesAndImportParams(ConfigService.incomingRoot));

            }


            //--------------------------------------- US--------------------------------------------------------------------------
            if (ConfigService.isUS)
            {
                // Lithia //
                var lithiaTB = new LithiaTBLoaderService(); // input.FinancialLines
                loads.Add(lithiaTB.GetMatchingFilesAndImportParams(ConfigService.incomingRoot));

                var lithiaWIP = new LithiaWIPLoaderService(); // input.ReconcilingItems
                loads.Add(lithiaWIP.GetMatchingFilesAndImportParams(ConfigService.incomingRoot));

                var lithiaStock = new LithiaStockLoaderService(); // input.StockItems
                loads.Add(lithiaStock.GetMatchingFilesAndImportParams(ConfigService.incomingRoot));

                var lithiaBook = new LithiaBookedAndPendingLoaderService(); // input.ReconcilingItems
                loads.Add(lithiaBook.GetMatchingFilesAndImportParams(ConfigService.incomingRoot));
            }

            return loads;
        }




        public static GenericLoaderJobServiceParams GetStockJobParams(JobParams parms)
        {
            GenericLoaderJobServiceParams genericStockJobParams = null;

            // JARDINE //
            if(parms.jobType == LoaderJob.JardineStockItems)
            {
                genericStockJobParams = new JardineStocksLoaderService();
            }
            else if (parms.jobType == LoaderJob.JardineTB)
            {
                genericStockJobParams = new JardineTBLoaderService();
            }
            else if (parms.jobType == LoaderJob.JardineWIP)
            {
                genericStockJobParams = new JardineWIPLoaderService();
            }

            // MMG //
            else if (parms.jobType == LoaderJob.MMGTB)
            {
                genericStockJobParams = new MMGTBLoaderService();
            }
            else if (parms.jobType == LoaderJob.MMGStockItems)
            {
                genericStockJobParams = new MMGStockLoaderService();
            }
            else if (parms.jobType == LoaderJob.MMGWIP)
            {
                genericStockJobParams = new MMGWIPLoaderService();
            }
            else if (parms.jobType == LoaderJob.MMGStockAtAuction)
            {
                genericStockJobParams = new MMGAtAuctionLoaderService();
            }
            else if (parms.jobType == LoaderJob.MMGStockOnLoan)
            {
                genericStockJobParams = new MMGStockOnLoanLoaderService();
            }

            // Lithia //
            else if (parms.jobType == LoaderJob.LithiaTB)
            {
                genericStockJobParams = new LithiaTBLoaderService();
            }
            else if (parms.jobType == LoaderJob.LithiaWIP)
            {
                genericStockJobParams = new LithiaWIPLoaderService();
            }
            else if (parms.jobType == LoaderJob.LithiaStockItems)
            {
                genericStockJobParams = new LithiaStockLoaderService();
            }
            else if (parms.jobType == LoaderJob.LithiaBookedAndPending)
            {
                genericStockJobParams = new LithiaBookedAndPendingLoaderService();
            }

            return genericStockJobParams;
        }





    }


}
