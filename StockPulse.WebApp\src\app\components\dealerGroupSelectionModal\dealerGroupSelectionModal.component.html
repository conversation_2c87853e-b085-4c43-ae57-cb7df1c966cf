<ng-template #modalRef let-modal>
    <div class="modal-header">
        <h4 class="modal-title" id="modal-basic-title">{{ modalHeader }}</h4>
        <button class="close" aria-label="Close" (mouseup)="modal.dismiss('Cross click')">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <div class="modal-body alertModalBody lowHeight">
        <div id="country">
            <ng-container  *ngFor="let baseURLVM of getBaseURLVMs">
                <button class="btn btn-primary" (mouseup)="setBaseURL(baseURLVM.baseURL, dealerGroupVM)" *ngFor="let dealerGroupVM of baseURLVM.dealerGroupVMs">
                    <span>{{dealerGroupVM.description}}</span>
                </button>
            </ng-container >
        </div>
    </div>
</ng-template>
