﻿/****** Object:  StoredProcedure [dbo].[GET_ReconcilingItemTypeStats]    Script Date: 18/03/21 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].GET_ReconcilingItemTypeStats
(
    @StockCheckId INT = NULL,
	@UserId INT = NULL
)
AS
BEGIN

SET NOCOUNT ON

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END

	

  
--with @recitemstotal table(id int, vehicleCount int) as
SELECT reconcilingItemTypeId, count(Id) AS 'vehicleCount'  
INTO #recitemstotal
FROM ReconcilingItems WHERE StockCheckId=@StockCheckId group by ReconcilingItemTypeId


SELECT 
recTypes.Id, 
recTypes.Description,
recTypes.ExplainsMissingVehicle as ExplainsMissing,
recTypes.SortOrder,
ISNULL(counts.vehicleCount,0) AS VehicleCount,
recTypes.IsActive
FROM ReconcilingItemTypes recTypes
left join #recitemstotal counts ON counts.reconcilingItemTypeId = rectypes.id
WHERE DealerGroupId = (SELECT DealerGroupId FROM Users WHERE Id = @UserId)
AND (recTypes.IsActive = 1 OR ISNULL(counts.vehicleCount,0) > 0)

DROP TABLE #recitemstotal

	

END

GO




--To use this run 
--exec [GET_ReconcilingItemTypeStats] @StockCheckId = 99, @UserId = 1