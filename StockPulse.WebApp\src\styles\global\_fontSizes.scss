//we strictly only use font-size in this page.

// ---------------------------------+
//how we set body font
// ---------------------------------+

html {
    font-size: 100%;

    body {
        font-size: var(--body-font-size);

        @media (max-width: 1920px) {
            font-size: var(--body-font-size_mediumScreen) !important;
        }

        @media (max-width: 1400px) {
            font-size: var(--body-font-size_smallScreen) !important;
        }
    }
}

// ---------------------------------+
//how we set various headings
// ---------------------------------+
h1,
h2,
h3,
h4,
h5 {
    margin: 0;
    font-weight: 500 !important;
}

h1,
.h1Thin,
.h1 {
    font-size: var(--h1-font-size);

    @media (max-width: 1920px) {
        font-size: var(--h1-font-size_mediumScreen) !important;
    }

    @media (max-width: 1400px) {
        font-size: var(--h1-font-size_smallScreen) !important;
    }
}

h2,
.h2Thin,
.h2 {
    font-size: var(--h2-font-size);

    @media (max-width: 1920px) {
        font-size: var(--h2-font-size_mediumScreen) !important;
    }

    @media (max-width: 1400px) {
        font-size: var(--h2-font-size_smallScreen) !important;
    }
}

h3,
.h3Thin,
.h3 {
    font-size: var(--h3-font-size);

    @media (max-width: 1920px) {
        font-size: var(--h3-font-size_mediumScreen) !important;
    }

    @media (max-width: 1400px) {
        font-size: var(--h3-font-size_smallScreen) !important;
    }
}

h4,
.h4Thin,
.h4 {
    font-size: var(--h4-font-size);

    @media (max-width: 1920px) {
        font-size: var(--h4-font-size_mediumScreen) !important;
    }

    @media (max-width: 1400px) {
        font-size: var(--h4-font-size_smallScreen) !important;
    }
}

h5,
.h5Thin,
.h5 {
    font-size: var(--h5-font-size);

    @media (max-width: 1920px) {
        font-size: var(--h5-font-size_mediumScreen) !important;
    }

    @media (max-width: 1400px) {
        font-size: var(--h5-font-size_smallScreen) !important;
    }
}



// ---------------------------------+
//font sizes for buttons
// ---------------------------------+
.buttonIconOnly {
    font-size: var(--icon-button-font-size) !important;

    @media (max-width: 1920px) {
        font-size: var(--icon-button-font-size_mediumScreen) !important;
    }

    @media (max-width: 1400px) {
        font-size: var(--icon-button-font-size_smallScreen) !important;
    }
}

button,
.btn,
.dropdown-menu,
.form-control {
    font-size: var(--button-font-size) !important;
    font-weight: 400;

    @media (max-width: 1920px) {
        font-size: var(--button-font-size_mediumScreen) !important;
    }

    @media (max-width: 1400px) {
        font-size: var(--button-font-size_smallScreen) !important;
    }
}

.btn.h1 {
    font-size: var(--h1-font-size) !important;

    @media (max-width: 1920px) {
        font-size: var(--h1-font-size_mediumScreen) !important;
    }

    @media (max-width: 1400px) {
        font-size: var(--h1-font-size_smallScreen) !important;
    }
}

.btn.h3 {
    font-size: var(--h3-font-size) !important;

    @media (max-width: 1920px) {
        font-size: var(--h3-font-size_mediumScreen) !important;
    }

    @media (max-width: 1400px) {
        font-size: var(--h3-font-size_smallScreen) !important;
    }
}

.btn.h4 {
    font-size: var(--h4-font-size) !important;

    @media (max-width: 1920px) {
        font-size: var(--h4-font-size_mediumScreen) !important;
    }

    @media (max-width: 1400px) {
        font-size: var(--h4-font-size_smallScreen) !important;
    }
}

.btn.h4Thin {
    font-size: var(--h4-font-size) !important;

    @media (max-width: 1920px) {
        font-size: var(--h4-font-size_mediumScreen) !important;
    }

    @media (max-width: 1400px) {
        font-size: var(--h4-font-size_smallScreen) !important;
    }
}

// ---------------------------------+
//modal
// ---------------------------------+
.modal-dialog {
    .modal-header {
        button.close {
            font-size: 1.688rem !important;
        }
    }
}

// ---------------------------------+
//font size for grids
// ---------------------------------+
.ag-theme-balham {
    font-size: var(--grid-font-size) !important;

    @media (max-width: 1920px) {
        font-size: var(--grid-font-size_mediumScreen) !important;
    }

    //override for small screens
    @media (max-width: 1400px) {
        font-size: var(--grid-font-size_smallScreen) !important;
    }
}

.ag-icon,
.ag-checkbox-input-wrapper {
    font-size: var(--button-font-size);
    line-height: var(--button-font-size);

    @media (max-width: 1920px) {
        font-size: var(--button-font-size);
        line-height: var(--button-font-size);
    }

    @media (max-width: 1400px) {
        font-size: var(--button-font-size);
        line-height: var(--button-font-size);
    }
}

// ---------------------------------+
//font size for popovers
// ---------------------------------+
.popover-header,
.popover {
    font-size: var(--popover-font-size);

    @media (max-width: 1920px) {
        font-size: var(--popover-font-size_mediumScreen) !important;
    }

    @media (max-width: 1400px) {
        font-size: var(--popover-font-size_smallScreen) !important;
    }
}
