﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].[ADD_MissingResolution]
(
    @StockCheckId INT,
	@UserId INT,
	@ResolutionTypeId INT,
	@ResolutionDate datetime,
	@Notes nvarchar(250),
	@IsResolved bit,
	@StockItemId INT
)
AS
BEGIN

SET NOCOUNT ON;

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END


INSERT INTO [dbo].[MissingResolutions]
		([ResolutionTypeId]
		,[ResolutionDate]
		,[Notes]
		,[IsResolved]
		,[UserId]
		,[StockcheckIdAndReference])
	VALUES
		(@ResolutionTypeId
		,@ResolutionDate
		,@Notes
		,@IsResolved
		,@UserId
		,(SELECT LEFT(CONCAT('SCID:',@StockCheckId,'|VIN:',Vin,'|REG:',Reg,'|REF:',Reference), 250) FROM StockItems WHERE Id = @StockItemId )
		)

		DECLARE @Id INT;
		SET @Id = SCOPE_IDENTITY();

		EXEC [dbo].[UPDATE_StockCheckLastUpdate] @StockCheckId, @UserId

		SELECT @Id


END
	
  
	


GO


