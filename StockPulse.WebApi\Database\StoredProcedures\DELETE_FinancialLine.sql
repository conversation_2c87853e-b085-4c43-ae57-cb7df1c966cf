﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


  
CREATE OR ALTER PROCEDURE [dbo].[DELETE_FinancialLine]  
(  
    @StockCheckId INT = NULL,  
    @UserId INT = NULL,
	@FinancialLineId INT = NULL
    
)  
AS  
BEGIN  
  
SET NOCOUNT ON;  
  
IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END

   
DELETE FROM [dbo].[FinancialLines]
WHERE Id = @FinancialLineId


EXEC [dbo].[UPDATE_StockCheckLastUpdate] @StockCheckId, @UserId  
  
  
END  
  
GO


