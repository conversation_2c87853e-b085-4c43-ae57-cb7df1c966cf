﻿using Microsoft.EntityFrameworkCore;
using StockPulse.Loader.Stockpulse.ViewModel;
using StockPulse.Repository.Database;
using StockPulse.Model;
using StockPulse.Model.Import;
using StockPulse.Loader.ViewModel;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;

namespace StockPulse.Loader.Services.MMG
{


    public class MMGAtAuctionLoaderService : GenericLoaderJobServiceParams
    {

        //constructor
        public MMGAtAuctionLoaderService()
        {

        }



        public JobParams GetMatchingFilesAndImportParams(string incomingRoot)
        {
            string customer = "mmg";
            string filePattern = "*StockPulseAtAuction*";

            JobParams parms = new JobParams()
            {
                jobType = LoaderJob.MMGStockAtAuction,
                customerFolder = "mmg",
                filename = filePattern,
                importSPName = null,
                loadingTableName = "ReconcilingItems",
                jobName = "MMGStockAtAuction",
                pulse = PulsesService.STK_MMGAtAuction,
                fileType = FileType.xlsx,
                regexPattern = "((?<=\")[^\"]*(?=\"(,|$)+)|(?<=,|^)[^,\"]*(?=,|$))",
                headerFailColumn = null,
                headerDefinitions = BuildHeaderDictionary(),
                errorCount = 0,
                dealerGroupId = 11,
                allMatchingFiles = Directory.GetFiles(incomingRoot.Replace("{customer}", customer), filePattern),
                reconcilingItemTypeIdsToInclude = "93",
                rowsToSkip = 2
            };

            return parms;
        }


        public DataTable InterpretFile(JobParams parms, RowsAndHeadersResult rowsAndHeaders, LogMessage logMessage)
        {
            List<Model.Input.ReconcilingItem> incomingAtAuctions = new List<Model.Input.ReconcilingItem>();// ExtractIncoming(parms, ref parms.errorCount, logMessage, allRows, headerLookup);
            int incomingProcessCount = 0;
            incomingAtAuctions = new List<Model.Input.ReconcilingItem>(10000);  //preset the list size (slightly quicker than growing it each time)

            using (var db = new StockpulseContext())
            {
                int total = rowsAndHeaders.rowsAndCells.Count;

                IEnumerable<SiteDescriptionDictionary> siteDescriptionDictionary = db.SiteDescriptionDictionary.Where(x => x.DealerGroupId == parms.dealerGroupId)
                                                                                                               .AsNoTracking().AsEnumerable();

                foreach (var rowCols in rowsAndHeaders.rowsAndCells)
                {
                    incomingProcessCount++;

                    try
                    {
                        System.Console.WriteLine($"Count {incomingProcessCount} / {total}");

                        // Get distinct site ids
                        int[] siteIds = siteDescriptionDictionary.Select(x => x.SiteId).Distinct().ToArray();

                        // We need to the row for each site id
                        foreach (int siteId in siteIds)
                        {
                            Model.Input.ReconcilingItem incomingAtAuction = new Model.Input.ReconcilingItem()
                            {
                                SiteId = siteId,
                                Reg = rowCols[rowsAndHeaders.headerLookup["Reg"]].Replace(" ", ""),
                                Description = rowCols[rowsAndHeaders.headerLookup["Description"]],
                                Vin = rowCols[rowsAndHeaders.headerLookup["Chassis"]],
                                Reference = null,
                                FileImportId = parms.fileImportId,
                                ReconcilingItemTypeId = 93,
                                SourceReportId = 1,
                                DealerGroupId = parms.dealerGroupId

                            };

                            incomingAtAuctions.Add(incomingAtAuction);
                        }

                    }

                    catch (Exception err)
                    {
                        logMessage.FailNotes = logMessage.FailNotes + $" failed on adding item, Item: {incomingProcessCount} {err.ToString()}";
                        parms.errorCount++;
                        continue;
                    }
                }
            }

            DataTable result = incomingAtAuctions.ToDataTable();

            result.Columns.Remove("Sites");
            result.Columns.Remove("FileImport");
            result.Columns.Remove("ReconcilingItemType");
            result.Columns.Remove("DealerGroup");
            result.Columns.Remove("SourceReports");

            return result;
        }


        private Dictionary<string, string> BuildHeaderDictionary()
        {
            Dictionary<string, string> headerDefinitions = new Dictionary<string, string>()
                {
                        { "Reg", "Reg" },
                        { "Chassis", "Chassis" },
                        { "Description", "Description" }
                };

            return headerDefinitions;
        }

        private decimal GetDecimal(string raw)
        {
            try
            {
                return decimal.Parse(raw);
            }
            catch
            {
                return 0;
            };
        }

        private int? GetNullableInt(string raw)
        {
            try
            {
                return int.Parse(raw);
            }
            catch
            {
                return null;
            };
        }

        private string GetVinLastEightChars(string raw)
        {
            try
            {
                string vinRaw = raw.ToString().Trim();
                int length = vinRaw.Length;
                return vinRaw.Substring(length - 8);
            }
            catch
            {
                return null;
            }

        }




    }
}
