import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { CphPipe } from 'src/app/cph.pipe';
import { RepeatMissingOffender } from 'src/app/model/RepeatMissingOffender';
import { RepeatUnknownOffender } from 'src/app/model/RepeatUnknownOffender';
import { StockCheck } from 'src/app/model/StockCheck';
import { ApiAccessService } from 'src/app/services/apiAccess.service';
import { ConstantsService } from 'src/app/services/constants.service';
import { IconService } from 'src/app/services/icon.service';
import { ToastService } from 'src/app/services/newToast.service';
import { RepeatOffendersStage } from '../../model/RepeatOffendersStage';
import { SelectionsService } from '../../services/selections.service';
import { GlobalSearchService } from '../globalSearch/globalSearch.service';
import { ScanWithResolution } from 'src/app/model/ScanWithResolution';
import { StockItemWithResolution } from 'src/app/model/StockItemWithResolution';

@Injectable({
  providedIn: 'root'
})
export class RepeatOffendersService {

  stages = RepeatOffendersStage

  stage: RepeatOffendersStage;
  //stockChecks: StockCheckVM[];

  missingVehicles: StockItemWithResolution[];
  unknownVehicles: ScanWithResolution[];
  
  stockChecks: StockCheck[];

  constructor(
    public icon: IconService,
    public selection: SelectionsService,
    public apiService: ApiAccessService,
    public constants: ConstantsService,
    public toastService: ToastService,
    public globalSearchService:GlobalSearchService,
    public selections: SelectionsService,
    public cphPipe: CphPipe,
    public router: Router,

  ) { 

  }

  reset(){
    this.stockChecks = null;
  }
}
