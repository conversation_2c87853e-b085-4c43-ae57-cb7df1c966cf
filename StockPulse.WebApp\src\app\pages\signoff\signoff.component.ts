import { Component, OnInit, ViewChild, ElementRef, EventEmitter } from '@angular/core';
import { ConstantsService } from '../../services/constants.service'
import { GetDataService } from '../../services/getData.service'
import { SelectionsService } from '../../services/selections.service'
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { IconService } from '../../services/icon.service';
import { debounceTime, distinctUntilChanged, forkJoin, Subject, Subscription } from 'rxjs';
import { SaveDataService } from 'src/app/services/saveData.service';
import { ApiAccessService } from 'src/app/services/apiAccess.service';
import { FinancialLineVM } from "src/app/model/FinancialLineVM";
import { Status } from "src/app/model/Status";
import { GlobalParam } from "src/app/model/GlobalParam";
import { ConfirmModalComponent } from 'src/app/components/confirmModal/confirmModal.component';
import { CphPipe } from 'src/app/cph.pipe';
import { ToastService } from 'src/app/services/newToast.service';
import { ReconciliationBucket } from 'src/app/model/ReconciliationBucket';
import { ResolutionBucket } from 'src/app/model/ResolutionBucket';
import { StatusUpdate } from 'src/app/model/StatusUpdate';
import { StockConsignmentVM } from 'src/app/model/StockConsignmentVM';
import { StockLoadReportSummary } from 'src/app/model/StockLoadReportSummary';
import { StockCheck } from 'src/app/model/StockCheck';
import jspdf from "jspdf";
import { StockChecksService } from '../stockChecks/stockChecks.service';
import { StatusChangeLogVM } from 'src/app/model/StatusChangeLogVM';

export interface Totals {
  trialBalance: number;
  adjustedTrialBalance: number;
  loaded: number;
  loadedUnits: number;
  difference: number;
}

@Component({
  selector: 'app-signoff',
  templateUrl: './signoff.component.html',
  styleUrls: ['./signoff.component.scss']
})
export class SignoffComponent implements OnInit {
  @ViewChild('newFinancialLine', { static: true }) newFinancialLine: ElementRef;
  @ViewChild('confirmModal', { static: true }) confirmModal: ConfirmModalComponent;

  newItemForm: UntypedFormGroup;
  editedLine: FinancialLineVM;

  subscription: Subscription;
  subscription1: Subscription;
  statusUpdate: StatusUpdate;
  statusRequested: Status;
  chosenNewStatus: Status;

  financialLines: FinancialLineVM[] = [];
  totalTrialBalance: number;

  financialLinesExplanations: FinancialLineVM[] = [];
  adjustedTrialBalance: number;

  //stockReportsLoaded: FinancialLineVM[] = [];
  stockLoadReportSummary: StockLoadReportSummary[];
  reconcilingItemTypes: ReconciliationBucket[];

  stockConsignment: StockConsignmentVM;

  totals: Totals;
  updateTrigger: EventEmitter<any>
  reconciliationBuckets: ReconciliationBucket[];
  resolutionBuckets: ResolutionBucket[];
  totalMissings: number;
  totalInStockValue: number;
  totalUnknowns: number;
  resolutionBucketsMissingResolved:ResolutionBucket[];
  resolutionBucketsUnknownResolved:ResolutionBucket[];
  
  totalMissingsUnresolved: number;
  totalUnknownsUnResolved: number;

  hoveredStatus:Status;

  pageRefreshSubscription: Subscription;


  description: string;
  notes: string;
  balance: string;
  balanceChanged: Subject<string> = new Subject<string>();
  hideSaveCancelButtons: boolean;

  signOffLogs: StatusChangeLogVM[];

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public modalService: NgbModal,
    public icon: IconService,
    public apiAccess: ApiAccessService,
    public toastService: ToastService,
    public stockChecksService: StockChecksService
  ) {
    this.balanceChanged.pipe(
      debounceTime(10), 
      distinctUntilChanged()
    ).subscribe(balance => {
      this.balance = balance.replace(/[^-^0-9.]/g, '');
    });
  }

  ngOnDestroy() {
    if (this.subscription) this.subscription.unsubscribe();
    if (this.subscription1) this.subscription1.unsubscribe();
    if (this.pageRefreshSubscription) this.pageRefreshSubscription.unsubscribe();
  }

  ngOnInit() {

    this.reloadStockCheck();

 

    this.selections.financialLinesImportedEmitter.subscribe(() => {
      this.loadFinancialLinesData();  
    })

    this.pageRefreshSubscription = this.constants.refreshPage.subscribe((res) => {
      if(res){this.reload();}
    })

  }
  initParams() {
    this.chosenNewStatus = null;
    
    this.statusUpdate = {
      statusId: this.selections.stockCheck.statusId,
      stockCheckId: this.selections.stockCheck.id,
      images:[]
    } as StatusUpdate
  }

  loadData() {
    let requests = [];
    requests.push(this.apiAccess.get('financiallines', this.selections.stockCheck.id.toString())); // 0
    requests.push(this.apiAccess.get('stockItems', 'Report/' + this.selections.stockCheck.id.toString())); // 1
    requests.push(this.apiAccess.get('stockchecks','ReconciliationBuckets',[{ key: 'stockcheckid', value: this.selections.stockCheck.id }])); // 2
    requests.push(this.apiAccess.get('stockchecks','ResolutionBuckets',[{ key: 'stockcheckid', value: this.selections.stockCheck.id }])); // 3
    requests.push(this.apiAccess.get('StatusChangeLogItems', 'GetStatusChangeLogItems', [{ key: 'stockcheckid', value: this.selections.stockCheck.id }])); // 4

    if (this.constants.IsSignoffConsignedEnabled){
      requests.push(this.apiAccess.get('stockItems', 'Consignment/' + this.selections.stockCheck.id.toString())); // 5
    }
    
    forkJoin(requests).subscribe((res: any[]) => {
      this.financialLines = res[0]
      this.stockLoadReportSummary = res[1];
      this.reconciliationBuckets = res[2];
      this.resolutionBuckets = res[3];
      this.signOffLogs = res[4];
      
      if (this.constants.IsSignoffConsignedEnabled) {
        this.stockConsignment = res[5];
        this.calculateTotalLoaded();
      } else {
        this.calculateTotalLoaded();
      }

      this.dealWithFinancialLinesData();
      this.workOutResolutionBuckets(this.resolutionBuckets);
    })
  }

  workOutResolutionBuckets(res: ResolutionBucket[]) {
    this.resolutionBucketsMissingResolved = res.filter(x=>x.explainsMissing && !x.description.includes('not resolved'));
    this.resolutionBucketsUnknownResolved = res.filter(x=>!x.explainsMissing && !x.description.includes('not resolved'));
    this.totalMissings = this.constants.sum(this.resolutionBuckets.filter(x=>x.explainsMissing).map(x=>x.vehicleCount))
    this.totalInStockValue = this.constants.sum(this.resolutionBuckets.filter(x=>x.explainsMissing).map(x=>x.inStockValue));
    this.totalUnknowns = this.constants.sum(this.resolutionBuckets.filter(x=>!x.explainsMissing).map(x=>x.vehicleCount))
    this.totalMissingsUnresolved = this.totalMissings - this.constants.sum(this.resolutionBucketsMissingResolved.map(x=>x.vehicleCount));// this.resolutionBucketsMissingResolved.length;
    this.totalUnknownsUnResolved = this.totalUnknowns - this.constants.sum(this.resolutionBucketsUnknownResolved.map(x=>x.vehicleCount));
  }

  reloadStockCheck() {
    let endpoint: string = `getStockChecks?isActive=${this.stockChecksService.showActive}&stockCheckId=${this.selections.stockCheck.id}`;

    if (!this.stockChecksService.showActive) {
      endpoint += `&fromDate=${this.stockChecksService.fromDate}&toDate=${this.stockChecksService.toDate}`;
    }

    this.apiAccess.get('stockchecks', endpoint).subscribe((data:StockCheck[]) => {
      data.map(x => {
        x.date = new Date(x.date);
        x.lastUpdated = new Date(x.lastUpdated);
      });

      this.selections.stockCheck = data.find(x => x.id === this.selections.stockCheck.id);

      this.initParams();
      this.loadData();
    }, e => {
      console.error('Failed to load stock checks', e);
    })
  }

  

  loadFinancialLinesData(){
    this.apiAccess.get('financiallines', this.selections.stockCheck.id.toString()).subscribe((data: FinancialLineVM[]) => {
      this.financialLines = data;
      this.dealWithFinancialLinesData();
    });

  }

  dealWithFinancialLinesData() {
    this.setFinancialLinesExplanations();
    this.calculateTotalTrialBalance();
    this.calculateAdjustedTrialBalance();
    this.calculateTotalLoaded();
  }

  setFinancialLinesExplanations(){
    this.financialLinesExplanations = this.financialLines.filter(f => f.isReconcilingAdj === true);
    this.financialLines = this.financialLines.filter(f => f.isReconcilingAdj === false);
  }

  calculateTotalTrialBalance() {
    this.totalTrialBalance = this.financialLines.reduce((sum, current) => sum + current.balance, 0);
  }



  calculateAdjustedTrialBalance() {
    this.adjustedTrialBalance = this.financialLines.reduce((sum, current) => sum + current.balance, 0 );
    this.adjustedTrialBalance += this.financialLinesExplanations.reduce((sum, current) => sum + current.balance, 0 );
  }

  calculateTotalLoaded() {
    this.totals = {
      trialBalance: this.totalTrialBalance ? this.totalTrialBalance : 0,
      adjustedTrialBalance: this.adjustedTrialBalance ? this.adjustedTrialBalance : 0,
      loaded: 0,
      loadedUnits: 0,
      difference: 0
    }

    if (this.stockLoadReportSummary) {
      this.stockLoadReportSummary.forEach(s => {
        this.totals.loaded += s.balance;
        this.totals.loadedUnits += +s.units;
      })
    }

    if (this.constants.IsSignoffConsignedEnabled && this.stockConsignment){
      this.totals.loaded -= this.stockConsignment?.balance;
      this.totals.loadedUnits -= this.stockConsignment?.units; 
    }

    this.totals.difference = parseFloat(this.totals.loaded.toFixed(2)) - parseFloat(this.totals.adjustedTrialBalance.toFixed(2));
    this.toastService.destroyToast();
  }

  resetNewItemForm() {
    this.description = '';
    this.notes = '';
    this.balance = '';
  }

  addNewItem() {
    this.resetNewItemForm()
    this.editedLine = null;
    this.modalService.open(this.newFinancialLine, { size: 'xs', keyboard: true, ariaLabelledBy: 'modal-basic-title' }).result.then((result) => {
      //have chosen to 'OK' selections



    }, (reason) => {

      this.resetNewItemForm()

    });
  }

  editItem(line: FinancialLineVM) {
    this.resetNewItemForm();

    this.editedLine = line;
    this.description = line.description;
    this.notes = line.notes;
    this.balance = line.balance.toString();

    this.modalService.open(this.newFinancialLine, { size: 'xs', keyboard: true, ariaLabelledBy: 'modal-basic-title' }).result.then((result) => {
      //have chosen to 'OK' selections

    }, (reason) => {


    });

  }

  deleteExplanationLine(line:FinancialLineVM, isFinancialLine?: boolean){
    this.constants.alertModal.title = `Really delete this ${isFinancialLine ? 'financial line' : 'explanation'} ?`;
    this.constants.alertModal.message = '';

    this.modalService.open(this.constants.alertModal.elementRef, { size: 'sm', keyboard: true, ariaLabelledBy: 'modal-basic-title' }).result.then((result) => {
      // Have chosen to 'OK' deletions.
      this.toastService.loadingToast('Deleting...');

      this.apiAccess.delete('financiallines','financialline',line.id).subscribe(res=>{
        //ok it is gone
        this.financialLinesExplanations.splice(this.financialLines.findIndex(x=>x.id===line.id),1);
        this.loadFinancialLinesData();

        this.toastService.destroyToast();
      })

    }, (reason) => {
      // Have chosen to cancel. 
      return
    });
  }

  submitItem() {
    if (!!this.editedLine) {
      this.saveExistingFinancialLine(this.editedLine.id, this.description, this.notes, Number(this.balance))
    } else {

      let financialLine: FinancialLineVM = {
        accountCode: null,
        description: this.description,
        balance: Number(parseFloat(this.balance)),
        notes: this.notes,
        isReconcilingAdj: true
      }


      this.saveNewFinancialLine(financialLine)

    }

  }


  saveExistingFinancialLine(id: number, description: string, notes: string, balance: number) {
    this.apiAccess.updateFinancialLine(id, description, notes, balance).subscribe(res => {
      //ok it's updated
      this.loadFinancialLinesData();
      this.toastService.successToast('Saved');
      this.modalService.dismissAll();
    }, e => {
      this.toastService.errorToast('Failed to save');
    })
  }

  saveNewFinancialLine(finLine: FinancialLineVM) {
    let finLines: FinancialLineVM[] = [];
    finLines.push(finLine);

    this.apiAccess.post('financiallines', 'FinancialLines', { financialLines: finLines, stockCheckId: this.selections.stockCheck.id }).subscribe(res => {
      this.loadFinancialLinesData();
      this.toastService.successToast('Saved');
      this.modalService.dismissAll();
    }, e => {

    })
  }

  reload() {
    this.toastService.loadingToast('Reloading...');
    
    this.statusUpdate = {
      statusId: this.selections.stockCheck.statusId,
      stockCheckId: this.selections.stockCheck.id,
      images:[]
    } as StatusUpdate

    this.loadData();
  }



  chooseNewStatus(status: Status) {

    this.statusRequested = status;
  
    // ask to confirm if about to put to complete
    if (this.statusUpdate.statusId < 4 && status.id === 4) {
      this.hideSaveCancelButtons = true;
      
      //are setting to complete, which can't be undone, confirm ok?
      this.confirmModal.confirmModalHeader = "Stock check will be set to complete, which can only be undone by a reviewer, continue?"
      
      this.modalService.open(this.confirmModal, { size: 'sm', keyboard: true, ariaLabelledBy: 'modal-basic-title' }).result.then((result) => {
        //'ok'
       this.chosenNewStatus = status
       this.statusUpdate.statusId = status.id;
       this.statusRequested = null;
       this.saveNewStatus();
      }, (reason) => {
        //cancel, don't do anything
        this.statusRequested = null;
        
      }
      );
    } else {
      //not set to completed, just change it
      this.hideSaveCancelButtons = false;
      this.statusUpdate.statusId = status.id;
      this.chosenNewStatus = status

    }
  }


  saveNewStatus(){

    this.apiAccess.post('StockChecks','UpdateStatus',this.statusUpdate).subscribe(res=>{
      //ok it's updated
      if (this.chosenNewStatus)
      {
      this.selections.stockCheck.status = this.chosenNewStatus.description;
      this.selections.stockCheck.statusId = this.chosenNewStatus.id;
      }
     
      this.initParams();
      
      if(this.selections.stockCheck.statusId>3 ){
        if(!this.selections.stockCheck.approvedByAccountant){
          this.selections.stockCheck.approvedByAccountant = this.selections.usersName;
          this.selections.stockCheck.reconciliationCompletedDate = new Date();
        }
      }else{
        this.selections.stockCheck.approvedByAccountant = null;
        this.selections.stockCheck.reconciliationCompletedDate = null;
      }

      if(this.selections.stockCheck.statusId>4){
        this.selections.stockCheck.approvedBy = this.selections.usersName;
        this.selections.stockCheck.reconciliationApprovedDate = new Date();
      }else{
        this.selections.stockCheck.approvedBy = null;
        this.selections.stockCheck.reconciliationApprovedDate = null;
      }

      if(this.selections.stockCheck.statusId<5){
        this.selections.stockCheck.approvedBy = null;
      }

      if(this.selections.stockCheck.statusId<4){
        this.selections.stockCheck.approvedByAccountant = null;
      }


      this.apiAccess.get('StatusChangeLogItems', 'GetStatusChangeLogItems', [{ key: 'stockcheckid', value: this.selections.stockCheck.id }]).subscribe((res: StatusChangeLogVM[]) => {
        this.signOffLogs = res;
      });
      
      this.toastService.successToast('Updated stock check status')
    },e=>{
      //failed to update
      this.toastService.errorToast('Failed to update status');
    })

  }

  cancelNewStatus(){
    let stockCheckStatus = this.constants.Statuses.find(x=>x.description==this.selections.stockCheck.status);
    this.chosenNewStatus = stockCheckStatus;
    this.statusUpdate.statusId = stockCheckStatus.id;

  }
  
  accountCodeToInt(accountCode: string) {
    return accountCode.replace(/['"]+/g, '')
  }

  validateForm() {
    if (this.description === '') return false;
    if (this.notes === '') return false;
    if (this.balance === '' || this.balance === '0') return false;
    return true;
  }

  currencyPipe(value: string) {
    this.balanceChanged.next(value);
  }

  removeNonNumeric(element: any) {
    element.target.value = element.target.value.replace(/[^0-9.]/g, '');
  }

  takeScreenshot() {
    let instructionRow: HTMLElement = document.getElementById('hideOnPrint');
    let stockCheckTitleForPrint: HTMLElement = document.getElementById('stockCheckTitleForPrint');

    instructionRow.classList.add('hidden');
    stockCheckTitleForPrint.classList.remove('hidden');

    let screenshotArea: HTMLElement = document.getElementById('screenshot-area');

    screenshotArea.style.bottom = 'unset';

    let fileName: string = `${this.selections.stockCheckLongName}.pdf`;

    let pdf: jspdf = new jspdf('l', 'px', [screenshotArea.offsetWidth + 40, screenshotArea.offsetHeight + 40]);
    pdf.html(screenshotArea, {
      callback: (doc) => { doc.save(fileName); },
      x: 0,
      y: 0,
      width: screenshotArea.offsetWidth
    });

    setTimeout(() => {
      instructionRow.classList.remove('hidden');
      stockCheckTitleForPrint.classList.add('hidden');
      screenshotArea.style.bottom = '0';
    }, 100)
  }

  allowTrialBalanceUpload() {
    if (this.financialLines.length > 0) { return false; }
    return this.constants.GlobalParams?.find(x => x.name === 'allowTrialBalanceUpload')?.boolValue || this.selections.userRole === 'SysAdministrator';
  }
}


