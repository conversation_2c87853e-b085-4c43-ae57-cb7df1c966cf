SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].ADD_FileImport
(
    @FileName TEXT = NULL,
    @FileDate DATETIME = NULL,
    @LoadDate DATETIME = NULL,
    @LoadedByUserId INT = NULL
)
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @InsertedId INT;

    INSERT INTO import.FileImports (FileName, FileDate, LoadDate, LoadedByUserId)
    VALUES (@FileName, @FileDate, @LoadDate, @LoadedByUserId);

    SET @InsertedId = SCOPE_IDENTITY();

    SELECT @InsertedId AS InsertedId;
END
GO