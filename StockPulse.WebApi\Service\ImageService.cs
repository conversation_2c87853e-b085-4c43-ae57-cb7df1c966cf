﻿using Azure.Storage;
using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using Microsoft.Extensions.Configuration;
using StockPulse.WebApi.Dapper;
using System;
using System.IO;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace StockPulse.WebApi.Service
{
    public interface IImageService
    {
        //string GetSignoffImageURL(string scanId);
        //string GetScanImageURL(string scanId, bool isHiRes, bool isVin);
        string GetMissingImageURL(string scanId);
        string GetUnknownImageURL(string scanId);
        Task<bool> UploadScanImage(Stream stream, int scanId, bool isHiRes, bool isVin);
        Task<bool> UploadMissingResolutionImage(Stream stream, int id, string originalFileName, string contentType);
        Stream convertBase64ToStream(string s);
        Task<bool> DeleteMissingResolutionImage(int imageId);
        Task<bool> UploadUnknownResolutionImage(Stream stream, int id, string originalFileName, string contentType);
        Task<bool> DeleteUnknownResolutionImage(int value);
        Task<bool> UploadSignOffImage(Stream stream, int id, bool replace = false);
        Task<bool> DeleteSignOffImage(int value);
        string GetContentType(string fileBase64);
        string RemoveUnwantedCharsFromFileName(string originalFileName);
        Task<bool> UploadReconcilingItemBackupFile(Stream stream, int id, string originalFileName, string contentType);
        Task<bool> DeleteReconcilingItemBackupFile(int fileId);
        string GetReconcilingItemBackupFileURL(int fileId);
    }

    public class ImageService : IImageService
    {
        private readonly IDapper dapper;

        private readonly IConfiguration config;

        private readonly string configSectionName = "BlobStorage";
        private readonly string configStorageAccount = "StorageAccount";
        private readonly string configStorageAccountKey = "StorageAccountKey";
        private readonly string configFilePath = "FilePath";
        private readonly string configReadOnlyKey = "ReadOnlyKey";

        private readonly string storageAccount;
        private readonly string storageAccountKey;
        private readonly string filePath;
        private readonly string readOnlyKey;


        public ImageService(IDapper dapper, IConfiguration config)
        {
            this.dapper = dapper;
            this.config = config;

            storageAccount = config[$"{configSectionName}:{configStorageAccount}"];
            storageAccountKey = config[$"{configSectionName}:{configStorageAccountKey}"];
            filePath = config[$"{configSectionName}:{configFilePath}"];
            readOnlyKey = config[$"{configSectionName}:{configReadOnlyKey}"];

        }

        private async Task<bool> UploadFileToStorage(Stream fileStream, string fileName, string originalFileName, string contentType, bool replace, bool noCache)
        {
            // Create a URI to the blob
            Uri blobUri = new Uri(filePath + fileName);

            // Create StorageSharedKeyCredentials object by reading
            // the values from the configuration (appsettings.json)
            StorageSharedKeyCredential storageCredentials = new StorageSharedKeyCredential(storageAccount, storageAccountKey);

            // Create the blob client.
            BlobClient blobClient = new BlobClient(blobUri, storageCredentials);

            // Upload the file
            try
            {
                BlobUploadOptions blobUploadOptions = new BlobUploadOptions
                {
                    HttpHeaders = new BlobHttpHeaders
                    {
                        ContentDisposition = $"attachment; fileName= {originalFileName}",
                        ContentType = contentType,
                        CacheControl = noCache ? "max-age=1" : string.Empty //1 second 
                    }
                };

                if (!replace)
                {
                    blobUploadOptions.Conditions = new BlobRequestConditions { IfNoneMatch = new Azure.ETag("*") };
                }
                await blobClient.UploadAsync(fileStream, blobUploadOptions);

                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message, ex);
                //return false;
            }
        }

        /*
               public async Task<object> GetScanImagesToProcess()
               {
                   return await dapper.GetAllAsync<ScanImage>("SELECT TOP 1000 Id, ImageBase64, ImageBase64Sm, VinImagebase64 FROM Scans WHERE ImageInblob = 0", null, System.Data.CommandType.Text);
               }

               public async Task<int> UpdateScanImagesToProcess(int scanId)
               {
                   return await dapper.ExecuteAsync("UPDATE SCANS SET ImageInBlob = 1 WHERE Id = " + scanId, null, System.Data.CommandType.Text);
               }

               public async Task<object> GetSignoffImagesToProcess()
               {
                   return await dapper.GetAllAsync<SignoffImage>("SELECT TOP 1000 Id, SignoffImageBase64 FROM StockChecks WHERE ImageInblob = 0", null, System.Data.CommandType.Text);
               }

               public async Task<int> UpdateSignoffImagesToProcess(int id)
               {
                   return await dapper.ExecuteAsync("UPDATE StockChecks SET ImageInBlob = 1 WHERE Id = " + id, null, System.Data.CommandType.Text);
               }

               public async Task<object> GetProblemCarImagesToProcess()
               {
                   return await dapper.GetAllAsync<ProblemCarImage>("SELECT TOP 1000 Id, DocumentImageBase64 FROM ProblemCarUpdates WHERE ImageInblob = 0", null, System.Data.CommandType.Text);
               }

               public async Task<int> UpdateProblemCarImagesToProcess(int id)
               {
                   return await dapper.ExecuteAsync("UPDATE ProblemCarUpdates SET ImageInBlob = 1 WHERE Id = " + id, null, System.Data.CommandType.Text);
               }

               public async Task<object> GetProblemScanImagesToProcess()
               {
                   return await dapper.GetAllAsync<ProblemScanImage>("SELECT TOP 1000 Id, DocumentImageBase64 FROM ProblemScanUpdates WHERE ImageInblob = 0", null, System.Data.CommandType.Text);
               }

               public async Task<int> UpdateProblemScanImagesToProcess(int id)
               {
                   return await dapper.ExecuteAsync("UPDATE ProblemScanUpdates SET ImageInBlob = 1 WHERE Id = " + id, null, System.Data.CommandType.Text);
               }
               */

        //public string GetScanImageURL(string scanId, bool isHiRes, bool isVin)
        //{
        //    string fileName = GenerateScanImageFileName(scanId, isHiRes, isVin);
        //    return GetBlobURL(fileName);
        //}

        //public string GetSignoffImageURL(string scanId)
        //{
        //    string fileName = GenerateSignOffImageFileName(scanId);
        //    return GetBlobURL(fileName);
        //}
        public string GetMissingImageURL(string scanId)
        {
            string fileName = GenerateMissingResolutionImageFileName(scanId);
            return GetBlobURL(fileName);
        }

        public string GetUnknownImageURL(string scanId)
        {
            string fileName = GenerateUnknownResolutionImageFileName(scanId);
            return GetBlobURL(fileName);
        }


        private string GetBlobURL(string fileName)
        {
            return $"{filePath}{fileName}?{readOnlyKey}";
        }

        private string GenerateScanImageFileName(string scanId, bool isHiRes, bool isVin)
        {
            if (isHiRes)
            {
                return scanId + "L.jpg";
            }
            else if (isVin)
            {
                return scanId + "V.jpg";
            }
            else if (!isHiRes && !isVin)
            {
                return scanId + "T.jpg";
            }
            else
            {
                return string.Empty;
            }
        }
        private string GenerateMissingResolutionImageFileName(string id)
        {
            return id + "M";
        }
        private string GenerateUnknownResolutionImageFileName(string id)
        {
            return id + "U";
        }
        private string GenerateSignOffImageFileName(string id)
        {
            return id + "S";
        }

        public async Task<bool> UploadScanImage(Stream stream, int scanId, bool isHiRes, bool isVin)
        {
            string fileName = GenerateScanImageFileName(scanId.ToString(), isHiRes, isVin);
            return await UploadFileToStorage(stream, fileName, fileName, "application/octet-stream", false, false);
        }

        public async Task<bool> UploadMissingResolutionImage(Stream stream, int id, string originalFileName, string contentType)
        {
            string fileName = GenerateMissingResolutionImageFileName(id.ToString());
            return await UploadFileToStorage(stream, fileName, originalFileName, contentType, false, false);
        }

        public async Task<bool> UploadSignOffImage(Stream stream, int id, bool replace = false)
        {
            string fileName = GenerateSignOffImageFileName(id.ToString());
            return await UploadFileToStorage(stream, fileName, fileName, "application/octet-stream", replace, true);
        }

        public async Task<bool> UploadUnknownResolutionImage(Stream stream, int id, string originalFileName, string contentType)
        {
            string fileName = GenerateUnknownResolutionImageFileName(id.ToString());
            return await UploadFileToStorage(stream, fileName, originalFileName, contentType, false, false);
        }

        public Stream convertBase64ToStream(string s)
        {
            if (s == "data:") return new MemoryStream(); // File is empty;

            byte[] bytes = Convert.FromBase64String(s.Split(";base64,")[1]);

            return new MemoryStream(bytes);
        }

        public async Task<bool> DeleteMissingResolutionImage(int imageId)
        {
            string fileName = GenerateMissingResolutionImageFileName(imageId.ToString());
            return await DeleteFileFromStorage(fileName);
        }

        public async Task<bool> DeleteSignOffImage(int imageId)
        {
            string fileName = GenerateSignOffImageFileName(imageId.ToString());
            return await DeleteFileFromStorage(fileName);
        }

        private async Task<bool> DeleteFileFromStorage(string fileName)
        {
            try
            {

                // Create a URI to the blob
                Uri blobUri = new Uri(filePath + fileName);

                // Create StorageSharedKeyCredentials object by reading
                // the values from the configuration (appsettings.json)
                StorageSharedKeyCredential storageCredentials = new StorageSharedKeyCredential(storageAccount, storageAccountKey);

                // Create the blob client.
                BlobClient blobClient = new BlobClient(blobUri, storageCredentials);

                BlobContainerClient blobContainerClient = new BlobContainerClient(blobUri, storageCredentials);
                return await blobContainerClient.DeleteBlobIfExistsAsync(fileName);

            }
            catch
            {
                return false;
            }
        }



        public async Task<bool> DeleteUnknownResolutionImage(int value)
        {
            string fileName = GenerateUnknownResolutionImageFileName(value.ToString());
            return await DeleteFileFromStorage(fileName);
        }

        public string GetContentType(string fileBase64)
        {
            if (fileBase64 == "data:") return string.Empty; // File is empty;

            return fileBase64.Split(";base64,")[0];
        }



        public string RemoveUnwantedCharsFromFileName(string originalFileName)
        {
            return Regex.Replace(originalFileName, "[,;]", "");
        }

        public string GetReconcilingItemBackupFileURL(int id)
        {
            return GetBlobURL(id.ToString());
        }

        public async Task<bool> UploadReconcilingItemBackupFile(Stream stream, int id, string originalFileName, string contentType)
        {
            return await UploadFileToStorage(stream, id.ToString(), originalFileName, contentType, false, false);
        }

        public async Task<bool> DeleteReconcilingItemBackupFile(int fileId)
        {
            return await DeleteFileFromStorage(fileId.ToString());
        }
    }
}
