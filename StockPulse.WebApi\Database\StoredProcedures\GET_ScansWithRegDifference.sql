﻿/****** Object:  StoredProcedure [dbo].[GET_ScansWithRegDifference]    Script Date: 31/3/21 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].GET_ScansWithRegDifference
(
    @StockCheckId INT = NULL,
    @UserId INT = NULL
)
AS
BEGIN

SET NOCOUNT ON;

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END



DECLARE @isRegional INT;
SET @isRegional = (SELECT IsRegional FROM StockChecks
                    WHERE StockChecks.Id = @StockCheckId)

DECLARE @isTotal INT;
SET @isTotal = (SELECT IsTotal FROM StockChecks
                    WHERE StockChecks.Id = @StockCheckId)

IF @isRegional = 0 AND @isTotal = 0

    BEGIN

        SELECT 
        Scans.Id AS ScanId, 
        Scans.Reg AS RegPerScan, 
        StockItems.Reg AS RegPerStockList, 
        Scans.Vin AS VinPerScan, 
        StockItems.Vin AS VinPerStockList,
		u.Name as ScannerName,
		loc.Description as LocationDescription,
		scans.ScanDateTime,
        CASE WHEN Scans.Reg = StockItems.Reg THEN 1 ELSE 0 END AS RegMatches
        FROM StockItems
        INNER JOIN Scans ON Scans.Id = StockItems.ScanId
        INNER JOIN Users u on u.Id = scans.UserId
		INNER JOIN Locations loc on loc.Id = scans.LocationId
        WHERE StockItems.StockCheckId = @StockCheckId AND
        ((Scans.Reg != StockItems.Reg OR StockItems.Vin != Scans.Vin))
		AND
		Scans.Reg != '' AND StockItems.Reg != '' AND Scans.Vin != '' AND StockItems.Vin != '';

    END

IF @isRegional = 1 AND @isTotal = 0

    BEGIN

    DECLARE @DivisionId INT;
    SET @DivisionId = (SELECT DivisionId FROM StockChecks
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id
                        WHERE StockChecks.Id = @StockCheckId AND StockChecks.IsRegional = 1)

    SELECT 
    Scans.Id AS ScanId, 
    Scans.Reg AS RegPerScan, 
    StockItems.Reg AS RegPerStockList, 
    Scans.Vin AS VinPerScan, 
    StockItems.Vin AS VinPerStockList,
	u.Name as ScannerName,
		loc.Description as LocationDescription,
		scans.ScanDateTime,
    CASE WHEN Scans.Reg = StockItems.Reg THEN 1 ELSE 0 END AS RegMatches
    FROM StockItems
    INNER JOIN Scans ON Scans.Id = StockItems.ScanId
	INNER JOIN Users u on u.Id = scans.UserId
		INNER JOIN Locations loc on loc.Id = scans.LocationId
    INNER JOIN StockChecks ON StockChecks.Id=StockItems.StockCheckId
    INNER JOIN Sites ON Sites.Id=StockChecks.SiteId
    INNER JOIN Divisions ON Divisions.Id=Sites.DivisionId
    WHERE Divisions.Id = @DivisionId
    AND
    ((Scans.Reg != StockItems.Reg) OR (StockItems.Vin != Scans.Vin))
	AND
	Scans.Reg != '' AND StockItems.Reg != '' AND Scans.Vin != '' AND StockItems.Vin != '';


    END

IF @isTotal = 1 AND @isRegional = 0

    BEGIN

    DECLARE @DealerGroupId INT;
    SET @DealerGroupId = (SELECT DealerGroupId FROM StockChecks
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id
                        WHERE StockChecks.Id = @StockCheckId)

    SELECT 
	Scans.Id AS ScanId, 
	Scans.Reg AS RegPerScan, 
	StockItems.Reg AS RegPerStockList, 
    Scans.Vin AS VinPerScan, 
	u.Name as ScannerName,
		loc.Description as LocationDescription,
		scans.ScanDateTime,
	StockItems.Vin AS VinPerStockList,
    CASE WHEN Scans.Reg = StockItems.Reg THEN 1 ELSE 0 END AS RegMatches
    FROM StockItems
    INNER JOIN Scans ON Scans.Id = StockItems.ScanId
	INNER JOIN Users u on u.Id = scans.UserId
		INNER JOIN Locations loc on loc.Id = scans.LocationId
    INNER JOIN StockChecks ON StockChecks.Id=[StockItems].StockCheckId
    INNER JOIN Sites ON Sites.Id=StockChecks.SiteId
    INNER JOIN Divisions ON Divisions.Id=Sites.DivisionId
    INNER JOIN DealerGroup ON Divisions.DealerGroupId=DealerGroup.Id
    WHERE DealerGroup.Id = @DealerGroupId
    AND
    ((Scans.Reg != StockItems.Reg OR StockItems.Vin != Scans.Vin))
	AND
	Scans.Reg != '' AND StockItems.Reg != '' AND Scans.Vin != '' AND StockItems.Vin != '';

    END

END

GO



--To use this run 
--exec [GET_ScansWithRegDifference] 8495, 389