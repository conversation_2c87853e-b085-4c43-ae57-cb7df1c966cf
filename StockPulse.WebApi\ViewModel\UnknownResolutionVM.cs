﻿using System;
using System.Collections.Generic;

namespace StockPulse.WebApi.ViewModel
{
    public class UnknownResolutionVM
    {
        public int Id { get; set; }

        public int StockCheckId { get; set; }
        public int ScanId { get; set; }

        public int? ResolutionTypeId { get; set; }
        public string ResolutionTypeDescription { get; set; }

        public DateTime? ResolutionDateTime { get; set; }

       // public int UserId { get; set; }
        public string UsersName { get; set; }

        public string Notes { get; set; }

        public bool IsResolved { get; set; }
        public List<ImageToUpdate> Images { get; set; }
        
    }
}
