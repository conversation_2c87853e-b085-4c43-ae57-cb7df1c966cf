import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';

@Component({
  selector: 'sliderSwitch',
  templateUrl: './sliderSwitch.component.html',
  styleUrls: ['./sliderSwitch.component.scss']
})
export class SliderSwitchComponent implements OnInit {
  @Input() defaultValue!: boolean;
  @Input() text!: string;
  @Output() toggle: EventEmitter<void> = new EventEmitter<void>();

  constructor() { }

  ngOnInit() {
    if (this.defaultValue) {
      const input = document.getElementById("checkbox") as HTMLInputElement;
      if (input !== null) input.checked = true;
    }
  }

  toggleDefaultValue(): void {
    this.toggle.emit();
  }
}
