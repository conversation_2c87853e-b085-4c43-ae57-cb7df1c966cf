import {Component} from "@angular/core";
import {ICellRendererAngularComp} from "ag-grid-angular";


// both this and the parent component could be folded into one component as they're both simple, but it illustrates how
// a fuller example could work
@Component({
    selector: 'reg-cell',
    template:        `
    
    <div class="regPlate" [ngClass]="{ 'strikeThrough': !params }">
    <span *ngIf="params">{{params|cph:'numberPlate':0}}</span>
    
  </div>`
    ,
    styles: []
})
export class RegPlateComponent implements ICellRendererAngularComp {
    
    params: any;

    agInit(params: any): void {
    
    this.params = params.value;
     

    }

    refresh(): boolean {
        return false;
    }
}


