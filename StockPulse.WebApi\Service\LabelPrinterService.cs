﻿using StockPulse.WebApi.DataAccess;
using System.Threading.Tasks;

namespace StockPulse.WebApi.Service
{
    public interface ILabelPrinterService
    {
        Task SavePrintLog(string vin, int userId);
    }

    public class LabelPrinterService : ILabelPrinterService
    {
        //properties of the service
        private readonly ILabelPrinterDataAccess labelPrinterDataAccess;

        //constructor
        public LabelPrinterService(ILabelPrinterDataAccess labelPrinterDataAccess)
        {
            this.labelPrinterDataAccess = labelPrinterDataAccess;
        }

        public async Task SavePrintLog(string vin, int userId)
        {
            await labelPrinterDataAccess.SavePrintLog(vin, userId);
        }
    }
}
