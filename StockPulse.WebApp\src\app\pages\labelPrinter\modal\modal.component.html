<div class="modal-header">
    <h4 class="modal-title">Manual Print</h4>
    <button type="button" class="close" aria-label="Close" (click)="onCancelButtonClick()">
        <span aria-hidden="true">&times;</span>
    </button>
</div>

<div class="modal-body">
    <table *ngFor="let label of labels; index as i;">
        <thead>
            <tr>
                <th class="headerWithIcon" colspan="2">
                    <span>Label {{ i+1 }}</span>
                    <button *ngIf="i > 0 && i+1 === labels.length" (click)="deleteLabel()">
                        <fa-icon [icon]="icon.faTimesCircle"></fa-icon>
                    </button>
                </th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>VIN</td>
                <td>
                    <input type="text" [(ngModel)]="label.vin" maxlength="17" placeholder="Maximum 17 characters">
                </td>
            </tr>
            <tr>
                <td>Stock Number</td>
                <td>
                    <input type="text" [(ngModel)]="label.stockNumber" maxlength="13" placeholder="Maximum 13 characters">
                </td>
            </tr>
            <tr>
                <td>Description</td>
                <td>
                    <input type="text" [(ngModel)]="label.description" maxlength="26" placeholder="Maximum 26 characters">
                </td>
            </tr>
            <tr>
                <td>Colour</td>
                <td>
                    <input type="text" [(ngModel)]="label.colour" maxlength="26" placeholder="Maximum 26 characters">
                </td>
            </tr>
        </tbody>
    </table>
</div>

<div class="modal-footer">
    <div class="modalFooterInner">
        <div>
            <button type="button" class="btn btn-primary" (click)="addLabel()">Add Label</button>
        </div>

        <div class="d-flex">
            <div class="copiesToggleContainer">
                <span class="me-2">Copies</span>
                <button class="btn btn-primary toggleCopiesButton" [disabled]="!copies || copies <= 1" (click)="copies = copies - 1">
                    -
                </button>
                <input class="toggleCopiesInput" type="number" min="1" max="3" [(ngModel)]="copies" (ngModelChange)="validateCopies()" />
                <button class="btn btn-primary toggleCopiesButton" (click)="copies = copies + 1">+</button>
            </div>
            
            <button type="button" class="btn btn-success" (click)="onSaveButtonClick()">
                Generate
            </button>
            <button type="button" class="btn btn-primary" (click)="onCancelButtonClick()">Close</button>
        </div>
    </div>
</div>