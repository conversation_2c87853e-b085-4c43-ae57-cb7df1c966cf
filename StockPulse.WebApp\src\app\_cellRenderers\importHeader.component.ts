import { Component } from '@angular/core';
import { LoadItemsService } from '../pages/loadItems/loadItems.service';
import { IconService } from '../services/icon.service';
import { SelectionsService } from '../services/selections.service';

@Component({
    selector: 'importHeader',
    template: `
        
        <!-- If not index -->
        <div class="" id="holder" *ngIf="params?.column?.colDef?.field !== 'index'" >
             
            <div  placement="left" container="body"    class="customHeaderLabel">{{params.displayName}}</div> 
            <input  
        [class]="{ 'hidden': params?.column?.colDef?.headerName === 'Count' }"
        (focus)="onFocus($event)"
        [ngModel]="loadItemsService.chosenImportMask.columnsWeWantAsLetters[fieldName]"
        (ngModelChange)="onChange($event)"
        />

        </div>

  



    `,
    styles: [
        `
        #holder{position:relative;display:flex;width:100%;flex-direction:column}
        .customHeaderLabel{padding-left: 0px;padding-right:0px;width:100%;text-align:center;transition: ease all 0.1s;text-overflow:ellipsis;overflow:hidden;}
        #holder input{width:100%!important;}
        input{text-align:center;margin-top: 0.3em;}
        input.hidden {
            pointer-events: none;
            visibility: hidden;
        }
        

        
    `
    ]
})
export class ImportHeaderComponent {
    public params: any;
    fieldName: any;


    constructor(
        public icon: IconService,
        public selections: SelectionsService,
        public loadItemsService: LoadItemsService,
    ){}
    sortType: string;

    agInit(params): void {
        this.params = params;
        this.fieldName = params.column.userProvidedColDef.field
    }


    onChange(newValue:string ){
        newValue = newValue.toUpperCase();
        this.loadItemsService.chosenNewHeaderLettersEmitter.next({fieldName: this.params.column.colDef.field, letters:newValue});
        
    }
    
    onFocus(event:any){
        
        //highlights result table column
        this.params.context.thisComponent.columnHeaderToHighlight.next(this.params.column.colId) 

        //triggers excel table source to highlight
        this.loadItemsService.focusedOnHeaderLettersEmitter.next({fieldName: this.params.column.colDef.field, letters:event.srcElement.value});

    }


   

}
