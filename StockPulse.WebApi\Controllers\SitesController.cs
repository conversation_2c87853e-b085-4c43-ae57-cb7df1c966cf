﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using StockPulse.Model;
using StockPulse.WebApi.Attribute;
using StockPulse.WebApi.Service;
using StockPulse.WebApi.ViewModel;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using static StockPulse.WebApi.Attribute.UserLevelAccessAttribute;

namespace StockPulse.WebApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    [UserLevelAccess(new[] { UserRole.SysAdministrator, UserRole.Reconciler, UserRole.Resolver, UserRole.Scanner, UserRole.Approver, UserRole.GeneralManager, UserRole.ReadOnly, UserRole.ScanAndPrint, UserRole.ScanAndView })]
    public class SitesController : ControllerBase, IAttributeValueProvider
    {

        private readonly ISiteService siteService;
        private readonly int userId;
        private readonly string userRole;

        public UserRole attributeUserRole => GetUserRole();
        private UserRole GetUserRole()
        {
            return (UserRole)Enum.Parse(typeof(UserRole), userRole);
        }

        public SitesController(ISiteService siteService, IUserService userService)
        {
            this.siteService = siteService;
            userId = userService.GetUserId();
            userRole = userService.GetUserRole();
        }

        [HttpGet]
        [Route("All")]
        public async Task<IEnumerable<SiteVM>> GetSites()
        {
            return await siteService.GetSites(userId);
        }

        [HttpGet]
        [Route("GetSiteNameLookups")]
        public async Task<IEnumerable<SiteNameLookup>> GetSiteNameLookups()
        {
            return await siteService.GetSiteNameLookups(userId);
        }

        [HttpPut]
        [Route("UpdateSiteNameLookups")]
        public async Task UpdateSiteNameLookups(UpdateSiteNameLookupsParams parms)
        {
            await siteService.UpdateSiteNameLookups(parms, userId);
        }

        [HttpDelete]
        [Route("DeleteSiteNameLookup")]
        public async Task DeleteSiteNameLookup(int id)
        {
            await siteService.DeleteSiteNameLookup(id, userId);
        }

        //[HttpGet]
        //[Route("GetLocationsForStockCheck")]
        //public async Task<IEnumerable<Location>> GetLocationsForStockCheck(int stockCheckId)
        //{
        //    return await siteService.GetLocationsForStockCheck(stockCheckId, userId);
        //}



        //[HttpGet]
        //[Route("GetSitesForDivision")]
        //public async Task<IEnumerable<Site>> GetSitesForDivision(int divisionId)
        //{
        //    return await siteService.GetSitesForDivision(divisionId, userId);
        //}

        //[HttpGet]
        //[Route("GetLongAndLat")]
        //public async Task<SiteLongLat> GetLongAndLat(int stockcheckId)
        //{
        //    return await siteService.GetLongAndLat(stockcheckId, userId);
        //}

    }
}
