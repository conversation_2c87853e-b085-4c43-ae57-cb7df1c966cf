﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace StockPulse.WebApi.Migrations
{
    /// <inheritdoc />
    public partial class removeredundantlithiaimporttables : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "LithiaFinancialLines",
                schema: "import");

            migrationBuilder.DropTable(
                name: "LithiaStockItems",
                schema: "import");

            migrationBuilder.DropTable(
                name: "LithiaWIPs",
                schema: "import");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "LithiaFinancialLines",
                schema: "import",
                columns: table => new
                {
                    FileImportId = table.Column<int>(type: "int", nullable: true),
                    SiteId = table.Column<int>(type: "int", nullable: false),
                    AccountDescription = table.Column<string>(type: "nvarchar(250)", maxLength: 250, nullable: true),
                    Balance = table.Column<decimal>(type: "decimal(15,3)", nullable: false),
                    Code = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Id = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.ForeignKey(
                        name: "FK_LithiaFinancialLines_FileImports_FileImportId",
                        column: x => x.FileImportId,
                        principalSchema: "import",
                        principalTable: "FileImports",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_LithiaFinancialLines_Sites_SiteId",
                        column: x => x.SiteId,
                        principalSchema: "dbo",
                        principalTable: "Sites",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "LithiaStockItems",
                schema: "import",
                columns: table => new
                {
                    FileImportId = table.Column<int>(type: "int", nullable: true),
                    SiteId = table.Column<int>(type: "int", nullable: false),
                    SourceReportId = table.Column<int>(type: "int", nullable: false),
                    Branch = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Comment = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    DIS = table.Column<int>(type: "int", nullable: true),
                    Description = table.Column<string>(type: "nvarchar(250)", maxLength: 250, nullable: true),
                    GroupDIS = table.Column<int>(type: "int", nullable: true),
                    Id = table.Column<int>(type: "int", nullable: false),
                    Reference = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Reg = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    StockType = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    StockValue = table.Column<decimal>(type: "decimal(15,3)", nullable: false),
                    Vin = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.ForeignKey(
                        name: "FK_LithiaStockItems_FileImports_FileImportId",
                        column: x => x.FileImportId,
                        principalSchema: "import",
                        principalTable: "FileImports",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_LithiaStockItems_Sites_SiteId",
                        column: x => x.SiteId,
                        principalSchema: "dbo",
                        principalTable: "Sites",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_LithiaStockItems_SourceReports_SourceReportId",
                        column: x => x.SourceReportId,
                        principalSchema: "dbo",
                        principalTable: "SourceReports",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "LithiaWIPs",
                schema: "import",
                columns: table => new
                {
                    FileImportId = table.Column<int>(type: "int", nullable: true),
                    ReconcilingItemTypeId = table.Column<int>(type: "int", nullable: false),
                    SiteId = table.Column<int>(type: "int", nullable: false),
                    SourceReportId = table.Column<int>(type: "int", nullable: false),
                    Comment = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    Description = table.Column<string>(type: "nvarchar(250)", maxLength: 250, nullable: true),
                    Id = table.Column<int>(type: "int", nullable: false),
                    Reference = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Reg = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Vin = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.ForeignKey(
                        name: "FK_LithiaWIPs_FileImports_FileImportId",
                        column: x => x.FileImportId,
                        principalSchema: "import",
                        principalTable: "FileImports",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_LithiaWIPs_ReconcilingItemTypes_ReconcilingItemTypeId",
                        column: x => x.ReconcilingItemTypeId,
                        principalSchema: "dbo",
                        principalTable: "ReconcilingItemTypes",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_LithiaWIPs_Sites_SiteId",
                        column: x => x.SiteId,
                        principalSchema: "dbo",
                        principalTable: "Sites",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_LithiaWIPs_SourceReports_SourceReportId",
                        column: x => x.SourceReportId,
                        principalSchema: "dbo",
                        principalTable: "SourceReports",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_LithiaFinancialLines_FileImportId",
                schema: "import",
                table: "LithiaFinancialLines",
                column: "FileImportId");

            migrationBuilder.CreateIndex(
                name: "IX_LithiaFinancialLines_SiteId",
                schema: "import",
                table: "LithiaFinancialLines",
                column: "SiteId");

            migrationBuilder.CreateIndex(
                name: "IX_LithiaStockItems_FileImportId",
                schema: "import",
                table: "LithiaStockItems",
                column: "FileImportId");

            migrationBuilder.CreateIndex(
                name: "IX_LithiaStockItems_SiteId",
                schema: "import",
                table: "LithiaStockItems",
                column: "SiteId");

            migrationBuilder.CreateIndex(
                name: "IX_LithiaStockItems_SourceReportId",
                schema: "import",
                table: "LithiaStockItems",
                column: "SourceReportId");

            migrationBuilder.CreateIndex(
                name: "IX_LithiaWIPs_FileImportId",
                schema: "import",
                table: "LithiaWIPs",
                column: "FileImportId");

            migrationBuilder.CreateIndex(
                name: "IX_LithiaWIPs_ReconcilingItemTypeId",
                schema: "import",
                table: "LithiaWIPs",
                column: "ReconcilingItemTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_LithiaWIPs_SiteId",
                schema: "import",
                table: "LithiaWIPs",
                column: "SiteId");

            migrationBuilder.CreateIndex(
                name: "IX_LithiaWIPs_SourceReportId",
                schema: "import",
                table: "LithiaWIPs",
                column: "SourceReportId");
        }
    }
}
