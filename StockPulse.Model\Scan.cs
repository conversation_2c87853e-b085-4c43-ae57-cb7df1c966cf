﻿using System;

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace StockPulse.Model
{
    public class Scan
    {
        [Key]
        public int Id { get; set; }


        // Made this nullable, as otherwise DB doesn't create.
        public int UserId { get; set; }
        [ForeignKey("UserId")]
        public virtual User User { get; set; }

        public int StockCheckId { get; set; }
        [ForeignKey("StockCheckId")]
        public virtual StockCheck StockChecks { get; set; }

        public int? LastEditedById { get; set; }
        [ForeignKey("LastEditedById")]
        public virtual User LastEditedBy { get; set; }

        public int LocationId { get; set; }
        [ForeignKey("LocationId")]
        public virtual Location Locations { get; set; }

        public int? UnknownResolutionId { get; set; }
        [ForeignKey("UnknownResolutionId")]
        public virtual UnknownResolution UnknownResolutions { get; set; }

        public int? StockItemId { get; set; }
        [ForeignKey("StockItemId")]
        public virtual StockItem StockItems { get; set; }

        public int? ReconcilingItemId { get; set; }
        [ForeignKey("ReconcilingItemId")]
        public virtual ReconcilingItem ReconcilingItems { get; set; }

        public bool IsDuplicate { get; set; }

        [Column(TypeName = "datetime")]
        public DateTime? LastEditedDateTime { get; set; }

        public decimal RegConfidence { get; set; }

        public bool IsRegEditedOnWeb { get; set; }
        public decimal VinConfidence { get; set; }
        public bool IsVinEditedOnWeb { get; set; }
        public bool IsRegEditedOnDevice { get; set; }
        public bool IsVinEditedOnDevice { get; set; }

        public decimal Longitude { get; set; }

        public decimal Latitude { get; set; }

        [Column(TypeName = "datetime")]
        public DateTime ScanDateTime { get; set; }

        public string Comment { get; set; }

        public string Reg { get; set; }
        public string Vin { get; set; }

        public string InterpretedReg { get; set; }
        public string InterpretedVin { get; set; }

        public string Description { get; set; }

        public string CoordinatesJSON { get; set; }

        public bool HasVinImage { get; set; }
        public DateTime? SaveDate { get; set; }
    }

}