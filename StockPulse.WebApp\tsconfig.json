{
  "compileOnSave": false,
  "compilerOptions": {
    "baseUrl": "./",
    "outDir": "./dist/0.9.6",
    "sourceMap": true,
    "declaration": false,
    "module": "esnext",
    "resolveJsonModule":true,
    "moduleResolution": "node",
    "experimentalDecorators": true,
    "paths":  {
      "exceljs": [
        "node_modules/exceljs/dist/exceljs.min.js"
        ]
    },
    "target": "es2022",
    "typeRoots": [
      "node_modules/@types"
    ],
    "lib": [
      "es2018",
      "dom"
    ],
    "downlevelIteration": true,
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true
  },
  "angularCompilerOptions": {
    "strictTemplates": false,
    //"enableIvy": false,
    "fullTemplateTypeCheck": false,
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true
  }
}
