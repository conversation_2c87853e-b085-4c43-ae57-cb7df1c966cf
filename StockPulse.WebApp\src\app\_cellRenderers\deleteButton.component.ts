import {Component} from "@angular/core";
import {ICellRendererAngularComp} from "ag-grid-angular";
import {IconService} from '../services/icon.service'
import { SelectionsService } from "../services/selections.service";
import { ConstantsService } from "../services/constants.service";
import { LoadItemsService } from "../pages/loadItems/loadItems.service";


@Component({
    selector: 'deleteButton-cell',
    template:    `
    
    <button (click)="onClick($event)" class="btn btn-danger"  [disabled]="disable()">
    
    <fa-icon [icon]="icon.faTrash"></fa-icon>
    </button>
  `
    ,
    styles: []
})
export class DeleteButtonComponent implements ICellRendererAngularComp {
    params;

    constructor(
      public icon: IconService,
      public selections: SelectionsService,
      public constants: ConstantsService,
      public loadItemsService: LoadItemsService
    ) { }


    agInit(params: any): void {
     this.params = params;
    
    }

    disable(): boolean {
      // Disable if stock check beyond scans completed
      if (this.selections.stockCheck.statusId > 3) { return true; }
      // Disable if user role in this list
      if (this.selections.userIsGeneralManager || this.selections.userIsReadOnly || this.selections.userIsResolver) { return true; }

      const chosenRec: string = this.loadItemsService.chosenReconcilingItemType.description;
      // If DMS or Agency, GlobalParam must be set OR SysAdmin level user to enable
      if (chosenRec === 'DMS Stock' && this.constants.IsUploadDMSStockEnabled || this.selections.userRole === 'SysAdministrator') { return false; }
      if (chosenRec === 'Agency Stock' && this.constants.allowAgencyStockUpload || this.selections.userRole === 'SysAdministrator') { return false; }
      // If other rec type, must not appear in GlobalParam locked rec type Ids to enable
      if (chosenRec !== 'DMS Stock' && chosenRec !== 'Agency Stock' && this.selections.userRole != 'SysAdministrator' && !this.constants.lockedReconcilingItemTypeIds.includes(this.loadItemsService.chosenReconcilingItemType.id)) { return false; }
      return true;
    }

    refresh(params?:any): boolean {
        return false;
    }

    onClick($event) {
        if (this.params.onClick instanceof Function) {
          // put anything into params u want pass into parents component
          const params = {
            event: $event,
            rowData: this.params.node.data
            // ...something
          }
          this.params.onClick(params.rowData);
    
        }
      }
}


