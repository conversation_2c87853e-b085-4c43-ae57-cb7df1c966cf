import { Component, OnInit, Input, EventEmitter, HostListener } from "@angular/core";
import { ImportHeaderComponent } from '../../../../_cellRenderers/importHeader.component';
import { GridApi, GridOptions } from 'ag-grid-community';
import { CphPipe } from '../../../../cph.pipe';
import { Subscription } from 'rxjs';
import {  LoadItemsService } from "../../loadItems.service";
import { ConstantsService } from "src/app/services/constants.service";
import { StockItem } from "src/app/model/StockItem";
import { ReconcilingItemVM } from "src/app/model/ReconcilingItemVM";
import { IndexComponent } from "src/app/_cellRenderers/index.component";
import { FinancialLineVM } from "src/app/model/FinancialLineVM";

type StockItemOrRecItemOrFinLine = StockItem | ReconcilingItemVM | FinancialLineVM

@Component({
  selector: 'importTable',
  templateUrl: './importTable.component.html',
  styleUrls: ['./importTable.component.scss']
  
})




export class ImportTableComponent implements OnInit {
  @Input() rowData: StockItemOrRecItemOrFinLine[];

  @HostListener('window:resize', [])
  private onresize(event) {
    if (this.gridApi) this.gridApi.sizeColumnsToFit();
  }

  mainTableGridOptions: GridOptions;
  public gridApi: GridApi;
  columnToHighlight: string;
  subscription: Subscription;
  columnHeaderToHighlight: EventEmitter<string>;
  sidenavToggledSubscription: Subscription;

  constructor(
    public constants: ConstantsService,
    public cphPipe: CphPipe,
    public loadItemsService: LoadItemsService,
  ) { }

  ngOnDestroy() {
    if (this.subscription) this.subscription.unsubscribe();
    if (this.sidenavToggledSubscription) { this.sidenavToggledSubscription.unsubscribe(); }
  }

  ngOnInit(): void {
    this.initParams()
  }

  initParams() {
    this.loadItemsService.filter.valueChanges.subscribe(result => this.search(result))

    this.mainTableGridOptions = {
      // domLayout: 'autoHeight',
      animateRows: false,
      rowBuffer: 0,
      defaultColDef: { cellClass: (params) => this.cellHighlight(params), },
      frameworkComponents: { agColumnHeader: ImportHeaderComponent },
      headerHeight: 50,
      getRowHeight: () => 50,
      columnTypes: {
        "numberColumn": { filter: 'agNumberColumnFilter' },
        "labelColumn": { cellClass: 'agAlignCentre', filter: 'agTextColumnFilter', sortable: true },
        "number": { filter: 'agTextColumnFilter', valueFormatter: (params) => params.value ? this.cphPipe.transform(params.value, 'number', 0) : 0 },
        "currency": { filter: 'agTextColumnFilter', valueFormatter: (params) => params.value ? this.cphPipe.transform(params.value, 'currency', 2) : 0 },
        "numberNoFilter": { cellClass: 'agAlignLeft', floatingFilter: false, filter: false }
      },
      context: { thisComponent: this },
      columnDefs: [

      ]
    }

    this.columnHeaderToHighlight = new EventEmitter();

    if (this.loadItemsService.chosenReconcilingItemType.description==='DMS Stock') {
      this.mainTableGridOptions.columnDefs = [
        { headerName: "Count", cellRendererFramework: IndexComponent, width: 50, type: 'numberNoFilter' },
        { headerName: "Reg", field: "reg", width: 80, type: 'labelColumn', hide: this.constants.currencySymbol === "$" },
        { headerName: "VIN", field: "vin", width: 80, type: 'labelColumn' },
        { headerName: "Description", field: "description", width: 500, type: "labelColumn" },
        { headerName: "DIS", field: "dis", width: 70, type: "number" },
        { headerName: "Group DIS", field: "groupDIS", width: 70, type: "number" },
        { headerName: "Branch", field: "branch", width: 100, type: 'labelColumn' },
        { headerName: "Stock Type", field: "stockType", width: 150, type: 'labelColumn' },
        { headerName: "Notes", field: "comment", width: 250, type: 'labelColumn' },
        { headerName: "Reference", field: "reference", width: 100, type: 'labelColumn' },
        { headerName: "Stock Value", field: "stockValue", width: 100, type: 'currency' },
        { headerName: "Flooring Balance", field: "flooring", width: 100, type: 'currency', hide: this.constants.currencySymbol != "$" },
        { headerName: "Site", field: "site", width: 100, type: 'labelColumn', },
      ]
    } else if (this.loadItemsService.chosenReconcilingItemType.description  === 'Trial Balance') {
      this.mainTableGridOptions.columnDefs = [
        { headerName: "Count", cellRendererFramework: IndexComponent, autoHeight: true, width: 50, type: 'numberNoFilter' },
        { headerName: "Account Code", field: "accountCode", width: 100, type: "labelColumn" },
        { headerName: "Account Name", field: "description", width: 300, type: 'labelColumn' },
        { headerName: "Value", field: "balance", width: 100, type: 'numberColumn' },
        { headerName: "Site", field: "site", width: 100, type: 'labelColumn', },
      ]
    }else {
      this.mainTableGridOptions.columnDefs = [
        { headerName: "Count", cellRendererFramework: IndexComponent, width: 50, type: 'numberNoFilter' },
        { headerName: "Reg", field: "reg", width: 200,  type: 'labelColumn', hide: this.constants.currencySymbol === "$" },
        { headerName: "VIN", field: "vin", width: 200, type: 'labelColumn' },
        { headerName: "Description", field: "description", width: 400, type: "labelColumn" },
        { headerName: "Notes", field: "comment", width: 300, type: 'labelColumn' },
        { headerName: "Reference", field: "reference", width: 200, type: 'labelColumn' },
        { headerName: "Site", field: "site", width: 100, type: 'labelColumn', },
      ]

    }

    this.sidenavToggledSubscription = this.constants.sidenavToggledEmitter.subscribe(() => {
      if (this.gridApi) {
        this.gridApi.sizeColumnsToFit();
      }
    })
  }

  onGridReady(params) {

    this.gridApi = params.api;

    //subscribe to updates
    this.subscription = this.loadItemsService.updateImportTableEmitter.subscribe((update: StockItem[]) => {
      if (this.gridApi) {
        this.gridApi.setRowData(update);
      } else {
        this.rowData = update;
      }
    })

    this.subscription = this.loadItemsService.updateMultiSiteEmitter.subscribe((update: boolean) => {
      this.mainTableGridOptions.columnApi.setColumnVisible('site', update);
      this.gridApi.redrawRows();
      this.gridApi.sizeColumnsToFit();
    })

    this.columnHeaderToHighlight.subscribe((updatedColumn: string) => {
      this.columnToHighlight = updatedColumn
      if (this.gridApi) this.gridApi.redrawRows();
    })
  }

  cellHighlight(params) {
    let result = ['index', 'stockValue'].includes(params.colDef.field) ? 'agAlignRight' : 'agAlignCentre'
    this.columnToHighlight && this.columnToHighlight == params.colDef.field ? result += ' highlight' : ''
    return result
  }

  search(text: string): StockItem[] {
    return []
  }

  accountCodeToInt(data: any) {
    if (!data.accountCode) return '';
    return data.accountCode.replace(/['"]+/g, '');
  }
}


