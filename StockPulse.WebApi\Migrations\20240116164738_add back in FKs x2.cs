﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace StockPulse.WebApi.Migrations
{
    /// <inheritdoc />
    public partial class addbackinFKsx2 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateIndex(
                name: "IX_StockItems_FileImportId",
                schema: "import",
                table: "StockItems",
                column: "FileImportId");

            migrationBuilder.CreateIndex(
                name: "IX_ReconcilingItems_FileImportId",
                schema: "import",
                table: "ReconcilingItems",
                column: "FileImportId");

            migrationBuilder.AddForeignKey(
                name: "FK_ReconcilingItems_FileImports_FileImportId",
                schema: "import",
                table: "ReconcilingItems",
                column: "FileImportId",
                principalSchema: "import",
                principalTable: "FileImports",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_ReconcilingItems_FileImports_FileImportId",
                schema: "import",
                table: "ReconcilingItems");

            migrationBuilder.DropIndex(
                name: "IX_StockItems_FileImportId",
                schema: "import",
                table: "StockItems");

            migrationBuilder.DropIndex(
                name: "IX_ReconcilingItems_FileImportId",
                schema: "import",
                table: "ReconcilingItems");
        }
    }
}
