﻿
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROC [dbo].[GET_UserSiteRoleToCache]
AS
BEGIN 
SET NOCOUNT ON

	  SELECT 
	  us.UserId,
	  div.Description
	  INTO #userDivs
	  FROM UserSites us
	  INNER JOIN Sites s on s.id = us.SiteId
	  INNER JOIN Divisions div on div.id = s.DivisionId
	  GROUP BY UserId,div.Description
	  
	  SELECT 
	  UserId, STRING_AGG(Description,',') as DivisionsList
	  INTO #userDivList
	  FROM #UserDivs
	  GROUP BY UserId
	 
	 

  SELECT  
  D.Description as DealerGroupName,
  U.DealerGroupId, 
  U.Id AS UserId,
  aspU.Id as AspNetUserId,
  aspU.UserName,
  aspU.Email, 
  U.Name,
  COALESCE(R.Name,'Missing Role') as RoleName,  
  STRING_AGG(si.Id,',') as SiteIds,
  STRING_AGG(CAST(si.Description AS NVARCHAR(MAX)),',') as SiteN<PERSON><PERSON>, 
  sitesDefault.Description as DefaultSiteName,
  udl.DivisionsList as DivisionNames
 
  FROM [AspNetUsers] as aspU
  LEFT JOIN AspNetUserRoles UR ON aspU.Id = UR.UserId
  LEFT JOIN AspNetRoles R on UR.RoleId = R.Id
  INNER JOIN Users as U on aspU.LinkedPersonId = u.Id
  INNER JOIN DealerGroup d on d.Id = U.DealerGroupId
  LEFT JOIN UserSites defaultSite on defaultSite.IsDefault = 1 AND defaultSite.UserId = u.id
  LEFT JOIN Sites sitesDefault on sitesDefault.Id = defaultSite.SiteId
  LEFT JOIN UserSites US ON U.Id = US.UserId
  LEFT JOIN Sites si on si.Id = us.SiteId
  LEFT JOIN #userDivList udl on udl.UserId = U.Id
  GROUP BY 
  D.Description,
  U.DealerGroupId, 
  U.Id, 
  aspU.Id,
  aspU.UserName,
  aspU.Email, 
  U.Name,
  R.Name,
  udl.DivisionsList,
  sitesDefault.Description


   DROP TABLE #userDivList
	  DROP TABLE #userDivs

END
GO



