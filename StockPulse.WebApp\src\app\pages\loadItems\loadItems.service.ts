import { EventEmitter, Injectable } from '@angular/core';
import { UntypedFormControl } from '@angular/forms';
import { FinancialLineVM } from 'src/app/model/FinancialLineVM';
import { ImportMask } from "src/app/model/ImportMask";
import { ReconcilingItemTypeStat } from 'src/app/model/ReconcilingItemTypeStat';
import { ReconcilingItemVM } from "src/app/model/ReconcilingItemVM";

@Injectable({
  providedIn: 'root'
})
export class LoadItemsService {
  importFile: string;

  updateImportTableEmitter: EventEmitter<ReconcilingItemVM[] | FinancialLineVM[]>;
  updateMultiSiteEmitter: EventEmitter<boolean>;
  chosenNewHeaderLettersEmitter: EventEmitter<{ fieldName: string, letters: string }>;
  focusedOnHeaderLettersEmitter: EventEmitter<{ fieldName: string, letters: string }>;

  chosenImportMask: ImportMask;
  updateMainTable: EventEmitter<any>;
  showImportProcess: boolean = false;
  chosenReconcilingItemType: ReconcilingItemTypeStat;
  filter: UntypedFormControl;
  multiSite: boolean = false;
  bulkItemsLoaded: EventEmitter<boolean> = new EventEmitter();

  selectedRowsParams: {
    controller: string,
    route: string,
    ids: number[]
  }

  filteredOutDueToBlanks: number = 0;

  constructor() {}
}
