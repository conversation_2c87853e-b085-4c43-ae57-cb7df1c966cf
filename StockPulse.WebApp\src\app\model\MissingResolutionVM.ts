import { ImageToUpdate } from "./ImageToUpdate";





export interface MissingResolutionVM {
  id: number;
  stockCheckid: number;
  stockItemId: number;

  resolutionTypeId: number | null;
  resolutionTypeDescription: string;

  resolutionDate: Date | string | null;

  //userId: number;
  //usersName: string;
  notes: string;

  isResolved: boolean;
  images: Array<ImageToUpdate>;

  requiredBackup?: string;

  stockcheckIdAndReference: string;
}
