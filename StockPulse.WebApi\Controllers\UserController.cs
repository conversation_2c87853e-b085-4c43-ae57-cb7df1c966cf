﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using StockPulse.Model;
using StockPulse.WebApi.Attribute;
using StockPulse.WebApi.Service;
using StockPulse.WebApi.ViewModel;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using static StockPulse.WebApi.Attribute.UserLevelAccessAttribute;

namespace StockPulse.WebApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class UserController : ControllerBase, IAttributeValueProvider
    {
        private readonly IUserService userService;
        private readonly int userId;
        private readonly string userRole;

        public UserRole attributeUserRole => GetUserRole();
        private UserRole GetUserRole()
        {
            return (UserRole)Enum.Parse(typeof(UserRole), userRole);
        }
        public UserController(IUserService userService)
        {
            this.userService = userService;
            userId = userService.GetUserId();
            userRole = userService.GetUserRole();
        }




        [HttpGet]
        [Route("UserAndLogin")]
        public async Task<object> Get()
        {
            return await userService.GetAllUsersAndLoginsNew(userId);
        }


        [HttpPatch]
        [Route("UserAndLogin")]
        [UserLevelAccess(new[] { UserRole.SysAdministrator })]
        public async Task Put(UserAndLogin userAndLogin)
        {
            await userService.UpdateUsersAndLogins(userId, userAndLogin);
        }

        [HttpPatch]
        [Route("UserAndLogin/Restore")]
        [UserLevelAccess(new[] { UserRole.SysAdministrator })]
        public async Task Restore(string id)
        {
            await userService.RestoreUsersAndLogins(userId, id);
        }

        [HttpPost]
        [Route("UserAndLogin")]
        [UserLevelAccess(new[] { UserRole.SysAdministrator })]
        public async Task<object> Post(UserAndLogin userAndLogin)
        {
            try
            {
                return await userService.AddUsersAndLogins(userId, userAndLogin);
            }
            catch(Exception ex)
            {
                return BadRequest(new { message = ex.Message});
            }
        }

        [HttpPost]
        [Route("UserAndLogin/Upload")]
        [UserLevelAccess(new[] { UserRole.SysAdministrator })]
        public async Task<IActionResult> Upload(IFormFile file)
        {
            var result = await userService.UploadUsersAndLogins(userId, file);

            if (result.StartsWith("Error"))
            {
                return BadRequest(result.ToString().Remove(0, 5));
            }
            else
            {
                return Ok(new { message = result.ToString() });
            }
           
        }


        [HttpDelete]
        [Route("UserAndLogin")]
        [UserLevelAccess(new[] { UserRole.SysAdministrator })]
        public async Task Delete(string id)
        {
            await userService.DeleteUsersAndLogins(userId, id);
        }

        //[HttpPost]
        //[Route("UpdateDealerGroup")]
        //[UserLevelAccess(new[] { UserRole.SysAdministrator })]
        //public async Task UpdateDealerGroup(DealerGroupVM dealerGroupVM)
        //{
        //    //string emailAddress = userService.GetUserEmailAddressFromToken();
        //    await userService.UpdateDealerGroup(userId,  dealerGroupVM.Id);
        //}

        // GET all possible roles
        [HttpGet]
        [Route("Roles")]
        public async Task<List<IdentityRole>> GetAllRoles()
        {
            return await userService.GetAllRoles();
        }

        [HttpGet]
        [Route("ReloadCache")]
        [UserLevelAccess(new[] { UserRole.SysAdministrator })]
        public async Task ReLoadUserSiteRoleCache()
        {
            await userService.ReLoadUserSiteRoleCache();
        }

        [HttpPost]
        [Route("SaveUserPreference")]
        public async Task SaveUserPreference(UserPreference userPreference)
        {
            await userService.SaveUserPreference(userPreference, userId);
        }
        [HttpPost]
        [Route("LogDeviceInfo")]
        public async Task LogDeviceInfo(LogDeviceInfoParams parms)
        {
            await userService.LogDeviceInfo(userId, parms.deviceInfo);
        }
    }
}
