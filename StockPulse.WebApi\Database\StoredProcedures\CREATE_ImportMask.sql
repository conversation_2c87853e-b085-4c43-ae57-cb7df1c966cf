﻿/****** Object:  StoredProcedure [dbo].[CREATE_ImportMask]    Script Date: 9/3/21 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].CREATE_ImportMask
(
    @Name varchar(30) = NULL	,
	@UserId int = NULL,
	@TopRowsToSkip int = NULL	,
	@ColumnValueEqualsesJSON varchar(500) = NULL	,
	@ColumnValueDifferentFromsJSON varchar(500) = NULL	,
	@ColumnValueNotNullsJSON varchar(500) = NULL	,
	@ColumnsWeWantJSON varchar(500) = NULL	,
	@IsStandard bit = 0,
	@IsMultiSite bit = 0,
	@IgnoreZeroValues bit = 0
)
AS
BEGIN

SET NOCOUNT ON

INSERT 
INTO ImportMasks (Name,UserId,TopRowsToSkip,ColumnValueEqualsesJSON,ColumnValueDifferentFromsJSON,ColumnValueNotNullsJSON,ColumnsWeWantJSON,IsStandard,IsMultiSite,IgnoreZeroValues)
 values (@Name,@UserId,@TopRowsToSkip,@ColumnValueEqualsesJSON,@ColumnValueDifferentFromsJSON,@ColumnValueNotNullsJSON,@ColumnsWeWantJSON,@IsStandard,@IsMultiSite,@IgnoreZeroValues);

 SELECT SCOPE_IDENTITY();
END

GO



--To use this run 
--exec [CREATE_ImportMask] @Name = 'Bob', @DealerGroup = 1