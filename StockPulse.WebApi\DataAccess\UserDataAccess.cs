﻿using Dapper;
using Microsoft.AspNetCore.Identity;
using StockPulse.Model;
using StockPulse.WebApi.Auth.Model;
using StockPulse.WebApi.Dapper;
using StockPulse.WebApi.ViewModel;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace StockPulse.WebApi.DataAccess
{
   public interface IUserDataAccess
   {
      Task<int> CreateUser(int userId, User user, int dealergroup);
      //Task<IEnumerable<User>> GetUsers(int stockcheckId); //*         * Not getting used
      Task SaveUserSites(int userId, int siteId, string sitesString, int newUserId);
      Task<string> getUsersName(int linkedPersonId);
      Task<IEnumerable<UserWithSites>> GetAllUserWithSites(int linkedPersonId);
      Task UpdateUser(int userId, UserAndLogin user);
      Task<int> CheckLinkedPersonExists(int linkedPersonId, object dealerGroupId);
      Task<int> GetDealerGroupId(int userId);
      Task<IEnumerable<string>> GetUserSitesName(int userId);
      Task<string> GetUsersDealerGroupName(int userId);
      Task<string> GetNominatedUserName(int userId);
      Task<IEnumerable<UserAndLogin>> GetAllUsersAndLogins(int userId);
      Task<string> GetAllSiteIdsForUser(int userId);
      Task<IEnumerable<UserParamSet>> UserSiteRoleToCache();
      Task<IEnumerable<DealerGroupVM>> GetAllDealerGroups();
      Task UpdateDealerGroup(int userId, int dealerGroupId);
      Task UpdateLinkedPersonId(int userId, string emailAddress, int newLinkedPersonId);
      Task<string> GetNominatedUserEmail(int userId);
      Task SaveUserPreference(UserPreference userPreference, int userId);
      Task<IEnumerable<UserPreference>> GetUserPreferences(int userId);
      Task LogDeviceInfo(int userId, string deviceInfo);
      Task<int> UpdateUserPasswordAcrossAllDealerGroups(string passwordHash, string email);
      Task RemoveFromRoleAsync(string currentUserById, string v);
      Task AddToRoleAsync(string id, string roleIdToAdd);
      Task<string> GetAspnetUserIdDirectFromDb(string emailAddress, int dealerGroupId);
      Task UpdateUsernameToEmail(string aspnetUserId);
   }

   public class UserDataAccess : IUserDataAccess
   {
      private readonly IDapper dapper;

      public UserDataAccess(IDapper dapper)
      {
         this.dapper = dapper;
      }

      /*
       * Not getting used
      public async Task<IEnumerable<User>> GetUsers(int stockcheckId)
      {
          var paramList = new DynamicParameters();
          paramList.Add("StockCheckId", stockcheckId);

          //IEnumerable<User> returnValues = await dapper.QueryAsync<CommissionItem>("dbo.GET_ProductsSold", paramList, commandTimeout: 70, commandType: System.Data.CommandType.StoredProcedure);

          return await dapper.GetAllAsync<User>("dbo.GET_Users",paramList, System.Data.CommandType.StoredProcedure);

      }
      */

      public async Task<int> CreateUser(int userId, User user, int dealergroup)
      {
         var paramList = new DynamicParameters();
         paramList.Add("UserId", userId);
         paramList.Add("Name", user.Name);
         paramList.Add("DealerGroup", dealergroup);
         paramList.Add("EmployeeNumber", user.EmployeeNumber);
         return await dapper.GetAsync<int>("dbo.Create_User", paramList, System.Data.CommandType.StoredProcedure);
      }

      public async Task SaveUserSites(int userId, int siteId, string sitesString, int newUserId)
      {
         var paramList = new DynamicParameters();
         paramList.Add("UserId", userId);
         paramList.Add("NewUserId", newUserId);
         paramList.Add("SiteId", siteId);
         paramList.Add("SiteString", sitesString);
         await dapper.ExecuteAsync("Create_UserSites", paramList, System.Data.CommandType.StoredProcedure);
      }

      public async Task<string> getUsersName(int linkedPersonId)
      {
         return await dapper.GetAsync<string>($"SELECT Name FROM Users WHERE Id = {linkedPersonId}", null, System.Data.CommandType.Text);
      }


      public async Task<IEnumerable<UserWithSites>> GetAllUserWithSites(int linkedPersonId)
      {
         var paramList = new DynamicParameters();
         paramList.Add("UserId", linkedPersonId);
         return await dapper.GetAllAsync<UserWithSites>("dbo.GET_AllUsersWithSites", paramList, System.Data.CommandType.StoredProcedure);
      }

      public async Task UpdateUser(int userId, UserAndLogin user)
      {
         var paramList = new DynamicParameters();
         paramList.Add("UserId", userId);
         paramList.Add("UserToUpdateId", user.Code);
         paramList.Add("Name", user.Name);
         paramList.Add("DefaultSiteId", user.SiteCode);
         paramList.Add("NewEmail", user.Email);
         paramList.Add("NewEmployeeNumber", user.EmployeeNumber);
         await dapper.ExecuteAsync("dbo.UPDATE_User", paramList, System.Data.CommandType.StoredProcedure);
      }

      public async Task<int> CheckLinkedPersonExists(int linkedPersonId, object dealerGroupId)
      {
         var query = $"SELECT Count(1) FROM [Users] WHERE Id = {linkedPersonId} AND DealerGroupId = {dealerGroupId}";
         return await dapper.GetAsync<int>(query, null, System.Data.CommandType.Text);
      }

      public async Task<int> GetDealerGroupId(int userId)
      {
         var query = $"SELECT DealerGroupId FROM [Users] WHERE Id = {userId}";
         return await dapper.GetAsync<int>(query, null, System.Data.CommandType.Text);
      }

      public async Task<IEnumerable<string>> GetUserSitesName(int userId)
      {
         var query = $"SELECT S.Description FROM UserSites AS US INNER JOIN Sites AS S ON US.SiteId = S.Id WHERE US.UserId = {userId}";
         return await dapper.GetAllAsync<string>(query, null, System.Data.CommandType.Text);

      }

      public async Task<string> GetUsersDealerGroupName(int userId)
      {
         var query = $"SELECT D.Description FROM Users AS U INNER JOIN DealerGroup AS D ON U.DealerGroupId = D.Id WHERE U.Id = {userId}";
         return await dapper.GetAsync<string>(query, null, System.Data.CommandType.Text);
      }

      public async Task<string> GetNominatedUserName(int userId)
      {
         var query = $"SELECT NU.Name FROM Users AS U INNER JOIN DealerGroup AS D ON U.DealerGroupId = D.Id INNER JOIN Users AS NU ON NU.Id = D.NominatedUserId WHERE U.Id = {userId}";
         return await dapper.GetAsync<string>(query, null, System.Data.CommandType.Text);
      }

      public async Task<string> GetNominatedUserEmail(int userId)
      {
         var query = $@"SELECT anu.email
FROM Users AS U 
INNER JOIN DealerGroup AS D ON U.DealerGroupId = D.Id 
INNER JOIN AspNetUsers anu on anu.LinkedPersonId = d.NominatedUserId
WHERE U.Id =  {userId}";
         return await dapper.GetAsync<string>(query, null, System.Data.CommandType.Text);
      }


      public async Task<IEnumerable<UserAndLogin>> GetAllUsersAndLogins(int userId)
      {
         var paramList = new DynamicParameters();
         paramList.Add("UserId", userId);

         return await dapper.GetAllAsync<UserAndLogin>("dbo.GET_AllUsersAndLogins", paramList, System.Data.CommandType.StoredProcedure);
      }

      public async Task<string> GetAllSiteIdsForUser(int userId)
      {
         var query = $"SELECT STRING_AGG(SiteId, ',') FROM UserSites WHERE UserId = {userId}";
         return await dapper.GetAsync<string>(query, null, System.Data.CommandType.Text);
      }

      public async Task<IEnumerable<UserParamSet>> UserSiteRoleToCache()
      {
         return await dapper.GetAllAsync<UserParamSet>("dbo.Get_UserSiteRoleToCache", null);
      }

      public async Task<IEnumerable<DealerGroupVM>> GetAllDealerGroups()
      {
         var query = $"SELECT Id, Description FROM DealerGroup";
         return await dapper.GetAllAsync<DealerGroupVM>(query, null, System.Data.CommandType.Text);
      }

      public async Task UpdateDealerGroup(int userId, int dealerGroupId)
      {
         var query = $"Update Users Set DealerGroupId = {dealerGroupId} where Id = {userId}";
         await dapper.ExecuteAsync(query, null, System.Data.CommandType.Text);
      }

      public async Task UpdateLinkedPersonId(int userId, string emailAddress, int newLinkedPersonId)
      {
         var query = $"Update AspNetUsers Set LinkedPersonId = {newLinkedPersonId} WHERE Email = '{emailAddress}' AND LinkedPersonId = {userId}";
         await dapper.ExecuteAsync(query, null, System.Data.CommandType.Text);
      }

      public async Task UpdateUsernameToEmail(string aspnetUserId)
      {
         var query = $"Update AspNetUsers Set UserName = Email, NormalizedUserName = NormalizedEmail WHERE Id = '{aspnetUserId}'";
         await dapper.ExecuteAsync(query, null, System.Data.CommandType.Text);
      }

      public async Task SaveUserPreference(UserPreference userPreference, int userId)
      {
         var paramList = new DynamicParameters();
         paramList.Add("UserId", userId);
         paramList.Add("PreferenceName", userPreference.PreferenceName);
         paramList.Add("Preference", userPreference.Preference);
         await dapper.ExecuteAsync("dbo.UPDATE_UserPreference", paramList, System.Data.CommandType.StoredProcedure);
      }

      public async Task<IEnumerable<UserPreference>> GetUserPreferences(int userId)
      {
         var query = $"SELECT * FROM UserPreferences WHERE Person_Id = {userId}";
         return await dapper.GetAllAsync<UserPreference>(query, null, System.Data.CommandType.Text);
      }
      public async Task LogDeviceInfo(int userId, string deviceInfo)
      {
         var query = $"UPDATE Users SET CurrentDevice = '{deviceInfo}' WHERE Id = {userId}";
         await dapper.ExecuteAsync(query, null, System.Data.CommandType.Text);
      }

      public async Task<int> UpdateUserPasswordAcrossAllDealerGroups(string passwordHash, string email)
      {
         var updatePasswordSQL = $"UPDATE AspNetUsers Set PasswordHash = '{passwordHash}', LockoutEnd = NULL WHERE Email = '{email}'";
         return await dapper.ExecuteAsync(updatePasswordSQL, null, System.Data.CommandType.Text);
      }

      public async Task RemoveFromRoleAsync(string userId, string roleId)
      {
         var roleRoleSQL = $"DELETE FROM AspNetUserRoles where UserId = '{userId}' and RoleId = '{roleId}'";
         await dapper.ExecuteAsync(roleRoleSQL, null, System.Data.CommandType.Text);
      }

      public async Task AddToRoleAsync(string userId, string roleIdToAdd)
      {
         var insertUserRoleSQL = $"INSERT INTO AspNetUserRoles (UserId, RoleId) VALUES ('{userId}','{roleIdToAdd}')";
         await dapper.ExecuteAsync(insertUserRoleSQL, null, System.Data.CommandType.Text);
      }

      public async Task<string> GetAspnetUserIdDirectFromDb(string emailAddress, int dealerGroupId)
      {
         var query = $"SELECT Id FROM AspNetUsers where Email = '{emailAddress}' and DealerGroupId = '{dealerGroupId}'";
         return await dapper.GetAsync<string>(query, null, System.Data.CommandType.Text);
      }
   }
}
