﻿using StockPulse.WebApi.ViewModel;
using System.Collections.Generic;
using System.Runtime.Serialization;

namespace PlateRecognizer
{

    [DataContract]
    public class PlateReaderResult
    {

        [DataMember(Name = "processing_time")]
        public decimal ProcessingTime { get; set; }

        [DataMember(Name = "version")]
        public int Version { get; set; }

        [DataMember(Name = "results")]
        public IList<PlateResult> Results { get; set; }

        [DataMember(Name = "filename")]
        public string Filename { get; set; }

        [DataMember(Name = "didSucceeed")]
        public bool DidSucceeed { get; set; }
        [DataMember(Name = "seenBefore")]
        public ScanSeenBefore SeenBefore { get; set; }
    }




}