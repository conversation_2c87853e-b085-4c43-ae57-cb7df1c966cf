﻿using StockPulse.Model.Import;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace StockPulse.Model.Input
{
    [Table("FinancialLines", Schema = "input")]
    public class FinancialLine
    {
        [Key]
        public int Id { get; set; }


        public string Code { get; set; }
        public string AccountDescription { get; set; }
        public decimal Balance { get; set; }


        public int SiteId { get; set; }
        [ForeignKey("SiteId")]
        public virtual Site Sites { get; set; }


        public int? FileImportId { get; set; }
        [ForeignKey("FileImportId")]
        public virtual FileImport FileImport { get; set; }


        public int DealerGroupId { get; set; }
        [ForeignKey("DealerGroupId")]
        public virtual DealerGroup DealerGroup { get; set; }

    }

}