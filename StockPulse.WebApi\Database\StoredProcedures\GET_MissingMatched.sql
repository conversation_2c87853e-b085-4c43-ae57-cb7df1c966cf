﻿/****** Object:  StoredProcedure [dbo].[MissingMatched]    Script Date: 01/04/21 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].GET_MissingMatched
(
   	@UserId INT = NULL,
    @StockCheckId INT = NULL	
)
AS
BEGIN

SET NOCOUNT ON;

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END


DECLARE @isRegional INT;
SET @isRegional = (SELECT IsRegional FROM StockChecks
                    WHERE StockChecks.Id = @StockCheckId)

DECLARE @isTotal INT;
SET @isTotal = (SELECT IsTotal FROM StockChecks
                    WHERE StockChecks.Id = @StockCheckId)


IF @isRegional = 0 AND @isTotal = 0
   
    BEGIN
	
    SELECT [Id]
          ,[ReconcilingItemId]
          ,[MissingResolutionId]
          ,StockItems.[Description]
          ,[DIS]
          ,[GroupDIS]
          ,[Branch]
          ,[StockType]
          ,[StockValue]
    FROM [dbo].[StockItems] 
    WHERE StockItems.StockCheckId = @StockCheckId 	

    END
	
IF @isRegional = 1 AND @isTotal = 0

    BEGIN

    DECLARE @DivisionId INT;

    SET @DivisionId = (SELECT DivisionId FROM StockChecks
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id
                        WHERE StockChecks.Id = @StockCheckId AND StockChecks.IsRegional = 1)
	
    SELECT StockItems.[Id]
          ,[ReconcilingItemId]
          ,[MissingResolutionId]
          ,StockItems.[Description]
          ,[DIS]
          ,[GroupDIS]
          ,[Branch]
          ,[StockType]
          ,[StockValue]
    FROM [dbo].[StockItems]
    INNER JOIN StockChecks ON StockChecks.Id=StockItems.StockCheckId
    INNER JOIN Sites ON Sites.Id=StockChecks.SiteId
    INNER JOIN Divisions ON Divisions.Id=Sites.DivisionId
    WHERE Divisions.Id = @DivisionId	

    END

IF @isRegional = 0 AND @isTotal = 1

    BEGIN 

    DECLARE @DealerGroupId INT;
    
    SET @DealerGroupId = (SELECT DealerGroupId FROM StockChecks
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id
                        WHERE StockChecks.Id = @StockCheckId)

    SELECT StockItems.[Id]
          ,[ReconcilingItemId]
          ,[MissingResolutionId]
          ,StockItems.[Description]
          ,[DIS]
          ,[GroupDIS]
          ,[Branch]
          ,[StockType]
          ,[StockValue]
    FROM [dbo].[StockItems]
    INNER JOIN StockChecks ON StockChecks.Id=StockItems.StockCheckId
    INNER JOIN Sites ON Sites.Id=StockChecks.SiteId
    INNER JOIN Divisions ON Divisions.Id=Sites.DivisionId
    INNER JOIN DealerGroup ON Divisions.DealerGroupId=DealerGroup.Id
    WHERE DealerGroup.Id = @DealerGroupId

    END

END

GO



-- To use this run 
--exec [GET_MissingMatched] @StockCheckId = 1, @UserId = 104