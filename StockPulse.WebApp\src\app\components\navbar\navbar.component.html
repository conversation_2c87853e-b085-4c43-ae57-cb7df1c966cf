<div id="overlay" class="overlay" (click)="accountP.close();" *ngIf="accountP.isOpen()"></div>
<div id="overlay" class="overlay" (click)="settingsP.close();" *ngIf="settingsP.isOpen()"></div>
<nav id="navbar-new" [ngClass]="navbarClass">
    <div id="logo-container">
        <img id="navbarLogo" class="doNotPrint" [src]="getLogo()" >
    </div>
    <div id="top-right-buttons-container">
        
        <!-- Toggle theme -->
        <!-- <button class="btn btn-primary" (click)="changeTheme()">
            <fa-icon *ngIf="constants.lightTheme" [icon]="icon.faMoon"></fa-icon>
            <fa-icon *ngIf="!constants.lightTheme" [icon]="icon.faSunBright"></fa-icon>
        </button> -->

        <!-- Help -->
        <a
            href="mailto:<EMAIL>"
            id="help"
            class="buttonIconOnly"
            ngbPopover="Help"
            placement="bottom-right"
            triggers="mouseenter:mouseleave"
            container="body">
            <fa-icon [icon]="icon.faMessageQuestion"></fa-icon>
        </a>
        
        <!-- Download training slides -->
        <button
            id="training-slides-download"
            class="buttonIconOnly"
            [ngbPopover]="trainingSlidesPopover"
            popoverClass="training-slides-popover"
            placement="bottom-right"
            triggers="manual"
            container="body"
            #slidesP="ngbPopover"
            (click)="slidesP.open()">
            <fa-icon [icon]="icon.faFilePowerpoint"></fa-icon>
        </button>
        
        <!-- Refresh page -->
        <button
            *ngIf="pageNeedsRefresh()"
            id="app-refresh"
            class="buttonIconOnly"
            [ngClass]="{ 'animate': animateRefreshIcon }"
            ngbPopover="Refresh Data"
            placement="bottom-right"
            triggers="mouseenter:mouseleave"
            container="body"
            (click)="emitRefresh()">
            <fa-icon [icon]="icon.faSyncAlt"></fa-icon>
        </button>

        <!-- Settings -->
        <button
            id="settings"
            class="buttonIconOnly"
            placement="bottom-right"
			[ngbPopover]="settingsPopover"
            popoverClass="settings-popover"
            [autoClose]="false"
            triggers="manual"
            #settingsP="ngbPopover"
            (click)="settingsP.open();"
			container="body">
            <fa-icon [icon]="icon.faCog"></fa-icon>
        </button>
        
        <!-- Account options -->
        <button
            id="account-options"
            class="buttonIconOnly"
            placement="bottom-right"
			[ngbPopover]="accountPopover"
            popoverClass="account-popover"
            [autoClose]="false"
            triggers="manual"
            #accountP="ngbPopover"
            (click)="accountP.open();"
			container="body">
            <fa-icon [icon]="icon.faUser"></fa-icon>
        </button>
    </div>
</nav>

<ng-template #accountPopover>
    <div id="accountPopover">
        <div class="popover-header">
            User Details
        </div>
        <div class="popover-body-inner">
            <table>
                <tr>
                    <td>Name</td>
                    <td>{{ selections.usersName }}</td>
                </tr>
                <tr>
                    <td>Role</td>
                    <td>{{ selections.userRole }}</td>
                </tr>
                <tr>
                    <td>Email</td>
                    <td>{{ selections.userUsername }}</td>
                </tr>
                <tr *ngIf="selections.userDealerGroup">
                    <td>Dealer Group</td>
                    <td>

                        <!-- <div ngbDropdown class="d-inline-block"> -->
                            <button type="button" class="btn btn-primary" (click)="switchDealerGroup();">
                                {{selections.userDealerGroup}}
                            </button>
                            <!-- <div ngbDropdownMenu aria-labelledby="dropdownBasic1" >
                                <button  ngbDropdownItem *ngFor="let dealerGroup of selections.dealerGroups;" (click)="accountP.close(); selectDealerGroup(dealerGroup)">{{dealerGroup.description}}</button>
                            </div> 
                        </div>-->
                    </td>
                </tr>
                <tr></tr>
            </table>
        </div>
        <div class="popover-footer">
            <button class="btn btn-primary" (click)="accountP.close(); logout();">
                Log out
            </button>
        </div>
    </div> 
     
</ng-template>

<ng-template #trainingSlidesPopover>
    <div id="trainingSlidesPopover">
        <div class="popover-header">
            Training Slides
        </div>
        <div class="popover-body-inner">
            <a
                class="btn btn-primary w-100"
                href="https://stockpulseimages.blob.core.windows.net/stockpulse-training/StockPulse%20User%20Guide%20v3.2.pdf?sp=r&st=2023-08-23T12:51:09Z&se=2030-08-23T20:51:09Z&spr=https&sv=2022-11-02&sr=b&sig=bolRTgS%2F4PpAFyTCqp8v7WkBrm42VGmrgYA4fddqYOw%3D"
                target="_blank"
                download="TrainingSlides.pdf">
                Download
            </a>
        </div>
    </div> 
</ng-template>

<ng-template #settingsPopover>
    <div id="settingsPopover">
        <div class="popover-header">
            Settings
        </div>
        <div class="popover-body-inner">
            <button class="btn btn-primary mb-2" (click)="openScanLocationsModal(); settingsP.close();">
                Manage site scan locations
            </button>
            <button class="btn btn-primary mb-2" (click)="openSiteNameLookupsModal(); settingsP.close();">
                Manage site name lookups
            </button>
            <button *ngIf="!selections.userIsScanAndPrintOnly" class="btn btn-primary" (click)="openReviewImportMapsModal(); settingsP.close();">
                Review import maps
            </button>

        </div>
    </div> 
</ng-template>