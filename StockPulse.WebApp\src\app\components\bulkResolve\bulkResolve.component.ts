import { Component } from '@angular/core';
import { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { CphPipe } from 'src/app/cph.pipe';
import { ImageToUpdate } from 'src/app/model/ImageToUpdate';
import { Resolution } from 'src/app/model/Resolution';
import { ResolutionType } from 'src/app/model/ResolutionType';
import { WaterfallBarDetailItem } from 'src/app/model/WaterfallBarDetailItem';
import { ApiAccessService } from 'src/app/services/apiAccess.service';
import { ConstantsService } from 'src/app/services/constants.service';
import { IconService } from 'src/app/services/icon.service';
import { ToastService } from 'src/app/services/newToast.service';
import { SelectionsService } from 'src/app/services/selections.service';
import { FileWithBase64 } from '../vehicleModal/vehicleModalBodyNew/explanation/explanation.component';

@Component({
  selector: 'bulkResolve',
  templateUrl: './bulkResolve.component.html',
  styleUrls: ['./bulkResolve.component.scss']
})
export class BulkResolveComponent {
  isStock: boolean;
  itemsToResolve: WaterfallBarDetailItem[];
  resolution: ResolutionType;
  resolutionNotes: string;
  resolutionResolved: boolean;
  fileSizeExceeded: boolean;
  resolutionFiles: FileWithBase64[] = [];
  resolutionsSaved: number = 0;

  constructor(
    public modalService: NgbModal,
    public activeModal: NgbActiveModal,
    public constantsService: ConstantsService,
    public iconService: IconService,
    public toastService: ToastService,
    public selectionsService: SelectionsService,
    public apiAccessService: ApiAccessService,
    public cphPipe: CphPipe
  ) {

  }

  public onSaveButtonClick() {
    const savingToast = this.toastService.loadingToast('Saving...');
    
    this.itemsToResolve.forEach(item => {
      this.saveResolution(item, savingToast);
    })
  
    this.activeModal.close();
  }

  public onCancelButtonClick() {
    this.activeModal.dismiss();
  }

  getResolutionTypes() {
    return this.isStock ? this.resolutionTypesForMissing() : this.resolutionTypesForUnknowns();
  }

  resolutionTypesForMissing() {
    return this.constantsService.ResolutionTypesActive.filter(e => e.explainsMissingVehicle).sort((a, b) => a.description.localeCompare(b.description));
  }

  resolutionTypesForUnknowns() {
    return this.constantsService.ResolutionTypesActive.filter(e => !e.explainsMissingVehicle).sort((a, b) => a.description.localeCompare(b.description));
  }

  chooseResolution(problemResolution: ResolutionType) {
    this.resolution = problemResolution;
  }

  backupRequired() {
    if (this.isStock) {
      if (this.constantsService.requireBackupForMissingResolution) { return true; }
    } else {
      if (this.constantsService.requireBackupForUnknownResolution) { return true; }
    }
  }

  onFileDropped(event: File[]) {
    this.prepareFilesList(event);
  }

  onFilePaste(event: ClipboardEvent) {
    event.preventDefault();
    let file: File = event.clipboardData.items[0].getAsFile();
    this.prepareFilesList([file]);
  }

  fileBrowseHandler(event: any) {
    const files: File[] = event.target.files;
    this.prepareFilesList(files);
  }

  prepareFilesList(files: File[]) {
    for (let item of files) {
      if (item.size > 10000000) { // 10mb limit
        this.fileSizeExceeded = true;
      } else {
        let reader: FileReader = new FileReader();
        reader.onload = (e) => {
          let base64: string = e.target.result as string;
          let object: FileWithBase64 = Object.assign(item, { base64: base64 });
          this.resolutionFiles.push(object);
        }
        reader.readAsDataURL(item);
      }
    }
  }

  formatBytes(bytes: number, decimals: number = 2) {
    if (bytes === 0) { return "0 Bytes"; }

    const k: number = 1024;
    const dm: number = decimals <= 0 ? 0 : decimals;
    const sizes: string[] = ["Bytes", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];
    const i: number = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + " " + sizes[i];
  }

  deleteFile(index: number) {
    this.resolutionFiles.splice(index, 1);
    if (this.resolutionFiles.length === 0) { this.resolutionResolved = false; }
  }

  convertFilesToImages(): ImageToUpdate[] {
    const images: ImageToUpdate[] = [];
    this.resolutionFiles.forEach(file => {
      images.push({ status: "ADD", fileBase64: file.base64, id: null, url: '', fileName: file.name });
    })

    return images;
  }

  thereAreFiles() {
    if (this.resolutionFiles && this.resolutionFiles.length > 0) { return true; }
    return false;
  }

  instructionRowMessage() {
    return `Add files with the button below (10mb or less in size) or by dragging and dropping a file in this area. You can also use the screen snipping tool by pressing windows key + shift + s, 
    dragging around an area on screen then pressing ctrl + v in this area to attach a screen snip. The StockPulse mobile app allows you to add backup using your device's camera. 
    ** Please take care to obscure customer personal data from uploads ** `
  }

  getThumbnail(file: any) {
    if (file.base64.includes('data:image')) {
      return file.base64;
    } else {
      return this.imageForFileExtensions(file.name);
    }
  }

  imageForFileExtensions(nameOrSrc: string) {
    if (nameOrSrc.includes('.pdf')) {
      return './assets/imgs/backup-pdf.png';
    } else if (nameOrSrc.includes('.docx')) {
      return './assets/imgs/backup-word.png';
    } else if (nameOrSrc.includes('.xl') || nameOrSrc.includes('.csv')) {
      return './assets/imgs/backup-excel.png';
    } else {
      return './assets/imgs/backup-file.png';
    }
  }

  saveResolution(itemIn: WaterfallBarDetailItem, savingToast) {
    const controllerName = this.isStock ? 'StockItems' : 'Scans';
    const methodName = this.isStock ? 'SaveMissingResolution' : 'SaveUnknownResolution';
    const images: ImageToUpdate[] = this.convertFilesToImages();

    const payload: Resolution = {
      resolutionId: 0,
      stockCheckId: this.selectionsService.stockCheck.id,
      originalItemId: this.isStock ? itemIn.stockItemId : itemIn.scanId,
      isResolved: true,
      resolutionTypeId: this.constantsService.ResolutionTypes.find(x => x.id === this.resolution.id && x.explainsMissingVehicle === this.isStock).id,
      notes: `${this.resolutionNotes} (Bulk resolved by ${ this.selectionsService.usersName } on ${ this.cphPipe.transform(new Date(), 'dateMed', 0) })`,
      images: images
    }

    this.apiAccessService.post(controllerName, methodName, payload).subscribe((data: any) => {
      this.resolutionsSaved++;

      if (this.itemsToResolve.length - 1 === this.resolutionsSaved) {
        this.toastService.successToast('Explanations Saved');
        savingToast.close();

        setTimeout(() => {
          this.selectionsService.stockCheckItemChanged.emit(true);
        }, 500)
      }
    }, error => {
      console.error('Failed to save resolution', error);
    });
  }

  preventSave() {
    if (!this.resolution || !this.resolutionNotes) { return true; }
    if (this.backupRequired() && this.resolutionFiles.length === 0) { return true; }
    return false;
  }

  showBackupRow() {
    const doShowBackupSetting = this.constantsService.GlobalParams.find(x => x.name === 'doShowBackupRequiredRow');
    return this.resolution && doShowBackupSetting && doShowBackupSetting.boolValue;
  }
}
