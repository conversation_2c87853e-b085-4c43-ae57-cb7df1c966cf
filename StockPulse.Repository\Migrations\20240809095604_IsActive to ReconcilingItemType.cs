﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace StockPulse.Repository.Migrations
{
    /// <inheritdoc />
    public partial class IsActivetoReconcilingItemType : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "IsActive",
                schema: "dbo",
                table: "ReconcilingItemTypes",
                type: "bit",
                nullable: false,
                defaultValue: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsActive",
                schema: "dbo",
                table: "ReconcilingItemTypes");
        }
    }
}
