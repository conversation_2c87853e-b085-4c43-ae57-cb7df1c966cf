﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace StockPulse.Model
{
    public class ResolutionType
    {
        [Key]
        public int Id { get; set; }

        public int DealerGroupId { get; set; }
        [ForeignKey("DealerGroupId")]
        public virtual DealerGroup DealerGroup { get; set; }

        public string Description { get; set; }

        public string BackupRequired { get; set; }

        public bool ExplainsMissingVehicle { get; set; }
        public bool IsActive { get; set; }

    }

}