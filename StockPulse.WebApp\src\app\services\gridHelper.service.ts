

import { Injectable } from '@angular/core';
import { SelectionsService } from './selections.service';
import { ColumnApi, GridApi, RowNode } from 'ag-grid-community';
import { GetDataService } from './getData.service';
import { CphPipe } from '../cph.pipe';
import { ConstantsService } from './constants.service';

@Injectable({
  providedIn: 'root'
})


export class GridHelperService {
  constructor(
    public selections: SelectionsService,
    public data: GetDataService,
    public cphPipe: CphPipe,
    public constants: ConstantsService,
  ) { }

  autoSizeAll(gridApi: GridApi, gridColumnApi: ColumnApi, autoColWidth:number, setColSizesFirst?:boolean) {
    if (!gridColumnApi) { return }
    let allColIds = gridColumnApi.getAllGridColumns().map(x => x.getColId());

    if(setColSizesFirst){
      let colsToSize = allColIds.filter(x => x !== 'ag-Grid-AutoColumn')
      gridColumnApi.autoSizeColumns(colsToSize);
    }
    gridColumnApi.setColumnWidth('ag-Grid-AutoColumn', autoColWidth);
    gridApi.sizeColumnsToFit();
  }
}