﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using StockPulse.WebApi.Service;
using StockPulse.WebApi.ViewModel;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace StockPulse.WebApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class AppStartController : ControllerBase
    {
        private readonly IConfiguration config;
        private readonly IHttpContextAccessor httpContextAccessor;
        private readonly IUserService userService;

        public AppStartController(IConfiguration config, IHttpContextAccessor httpContextAccessor, IUserService userService)
        {
            this.httpContextAccessor = httpContextAccessor;
            this.config = config;
            this.userService = userService;
        }

        //[HttpGet]
        //public string Test()
        //{
        //    //var userId = this.userService.GetUserId();
        //    return JsonConvert.SerializeObject("YES: Yes");
        //}


        [HttpGet]
        [Route("GetBaseURL/")]
        [Route("GetBaseURL/{userEmail}")]
        [AllowAnonymous]
        public async Task<string> GetBaseURL(string userEmail)
        {
            var env = config[$"WebApp:Env"].ToUpper();
            var country = config[$"WebApp:Country"].ToUpper();
            string baseURL = string.Empty;


            if (string.IsNullOrEmpty(userEmail))
            {
                userEmail = string.Empty;

                //Get its from token - This is will happen for AAD login
                var emailClaim = httpContextAccessor.HttpContext.User.Claims.Where(c => c.Type == "preferred_username").FirstOrDefault();

                if (emailClaim != null && emailClaim.Value != null)
                {
                    userEmail = emailClaim.Value;
                }

            }

            if (string.IsNullOrEmpty(userEmail))
            {
                return string.Empty;
            }


            //string userDomain = userEmail.Contains('@')? userEmail.Split('@')[1] : "no email";
            bool userExists = await CheckUserExists(userEmail);

            if (userExists)
            {
                baseURL = GetBaseURL(env, country);
            }

            return baseURL;

        }



        [HttpGet]
        [Route("GetBaseURLNew/")]
        [Route("GetBaseURLNew/{userEmail}")]
        [AllowAnonymous]
        public async Task<BaseURLVM> GetBaseURLNew(string userEmail)
        {
            var env = config[$"WebApp:Env"].ToUpper();
            var country = config[$"WebApp:Country"].ToUpper();
            //string baseURL = string.Empty;
            BaseURLVM baseURLVM = new BaseURLVM();


            if (string.IsNullOrEmpty(userEmail))
            {
                userEmail = string.Empty;
                
                //Get its from token - This is will happen for AAD login
                var emailClaim = httpContextAccessor.HttpContext.User.Claims.Where(c => c.Type == "preferred_username").FirstOrDefault();

                if (emailClaim != null && emailClaim.Value != null)
                {
                    userEmail =  emailClaim.Value;
                }

            }

            if (string.IsNullOrEmpty(userEmail))
            {
                return new BaseURLVM();
            }


            //string userDomain = userEmail.Contains('@')? userEmail.Split('@')[1] : "no email";
            bool userExists = await CheckUserExists(userEmail);

            if (userExists)
            {
                baseURLVM.BaseURL = GetBaseURL(env, country);
                baseURLVM.dealerGroupVMs = await userService.GetDealerGroupsForSpecificEmailAddress(userEmail);
            }

            return baseURLVM;

        }

        private async Task<bool> CheckUserExists(string emailAddress)
        {
            var userSiteRoleDictionary = await userService.GetUserSiteRoleDictionary();
            UserParamSet paramSet = userSiteRoleDictionary.FirstOrDefault(u => u.Key.StartsWith(emailAddress.ToUpper())).Value;

            if (paramSet != null)
            {
                return true;
            }

            return false;
        }

        private string GetBaseURL(string env, string country)
        {
            string baseURL = string.Empty;
            string envInURL = string.Empty;

            env = env.ToUpper();
            country = country.ToUpper();

            if (env == "TEST") envInURL = "test";
            if (env == "DEV") envInURL = "dev";
            if (country == "UK" && env == "PROD") envInURL = "prod";





            if (env == "PROD" || env == "TEST" || env == "DEV")
            {
                switch (country)
                {
                    case "UK":
                        baseURL = $"https://stockpulseapi{envInURL}.cphi.co.uk";
                        break;
                    case "US":
                        baseURL = $"https://stockpulseapius{envInURL}.cphi.co.uk";
                        break;
                    default:
                        baseURL = "";
                        break;
                }
            }
            else if (env == "LOCAL")
            {
                baseURL = "https://localhost:44322";
            }
            

            return baseURL;
        }

        private string GenerateBaseURL(string env, string userDomain, string userEmail)
        {
            string baseURL = string.Empty;
            string envInURL = string.Empty;

            env = env.ToUpper();
            userDomain = userDomain.ToUpper();

            if (env == "TEST") envInURL = "test";
            if (env == "DEV") envInURL = "dev";




            if (env == "PROD" || env == "TEST" || env == "DEV")
            {
                switch (userDomain)
                {
                    case "CPHI.CO.UK":
                        baseURL = (userEmail == "<EMAIL>") ? $"https://us{envInURL}.stockpulse.app" : $"https://stockpulseapi.cphi.co.uk";
                        break;
                    case "VERTU.COM":
                        baseURL = $"https://stockpulseapi.cphi.co.uk";
                        break;
                    case "LITHIA.COM":
                        baseURL = $"https://us{envInURL}.stockpulse.app";
                        break;
                    default:
                        //baseURL = $"https://uk{envInURL}.stockpulse.app";
                        baseURL = "https://stockpulseapi.cphi.co.uk";
                        break;
                }
            }
            else if (env == "LOCAL")
            {
                baseURL = "https://localhost:44322";
            }
            else
            {
                baseURL = "https://uk.stockpulse.app";
            }

            return baseURL;
        }

        
    }

    
}
