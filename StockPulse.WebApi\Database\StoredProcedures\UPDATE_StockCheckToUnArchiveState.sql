﻿
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].[UPDATE_StockCheckToUnArchiveState]
(
    @StockCheckIds varchar(max),
    @UserId INT
)
AS
BEGIN

SET NOCOUNT ON;


IF (dbo.[AuthenticateUserMultiStockChecks](@UserId, @StockCheckIds) = 0)
BEGIN 
    RETURN
END


--check if no other stockcheck for the same site is active.
SELECT * INTO #InActiveStockcheckIds FROM STRING_SPLIT(@StockCheckIds,',')

SELECT SiteId INTO #SiteIds FROM StockChecks WHERE Id IN (SELECT * FROM #InActiveStockcheckIds)

IF EXISTS (
SELECT SiteId FROM #SiteIds GROUP BY SiteId HAVING COUNT(SiteId) > 1
)
BEGIN 
	SELECT 0;
	RETURN;
END


IF EXISTS (
	SELECT Id FROM StockChecks 
	WHERE 
	Id NOT IN (SELECT * FROM #InActiveStockcheckIds) AND
	SiteId IN (SELECT * FROM #SiteIds) AND
	IsActive = 1
)
BEGIN
	SELECT 0;
	RETURN;
END



UPDATE StockChecks
SET IsActive = 1
WHERE Id IN (SELECT * FROM STRING_SPLIT(@StockCheckIds,','))

SELECT 1;
	
END

GO


