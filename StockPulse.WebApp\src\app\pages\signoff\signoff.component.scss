#trial-balance-card {
    width: calc(40% - 0.5em);
    background-color: #FFFFFF;
    margin-right: 1em;
    padding: 2em;
    overflow-y: auto;
    overflow-x: hidden;

    table {
        table-layout: fixed;

        thead tr th:nth-of-type(4),
        tbody tr td:nth-of-type(4) {
            width: 50px;
        }

        thead tr th:nth-of-type(2),
        tbody tr td:nth-of-type(2) {
            width: 40%;
            word-break: break-all;
        }
    }
}

#reconciliation-signoff-card {
    width: calc(60% - 0.5em);
    position: relative;

    #reconciliation {
        height: calc(100% - 250px - 1em);
        max-height: calc(100% - 250px - 1em);
        background-color: #FFFFFF;
        padding: 2em;
        overflow-y: auto;
    }

    table tbody tr {
        td:nth-of-type(1) {
            width: 50%;
        }

        td:nth-of-type(2) {
            text-align: right;
            position: relative;
        }
    }
}

table {
    width: 100%;
    background-color: #FFFFFF;

    thead {
        background-color: var(--primaryLighter);
        color: var(--bodyColour);

        th {
            font-weight: 400;
            padding: 0.5em 1em;

            &:nth-of-type(3) {
                text-align: right;
            }
        }
    }

    tbody tr {
        td {
            padding: 0.25em 1em;

            button.deleteIcon,
            button.editIcon {
                padding: 0;
                cursor: pointer;
            }

            &:nth-of-type(3),
            &:nth-of-type(4) {
                text-align: right;
            }
        }

        &.sub-header {
            background-color: var(--primaryLighter);
            color: var(--bodyColour);

            td {
                padding: 0.5em 1em;
            }
        }
    }
}

.card-section {
    width: 50%;
}

#status {
    width: 100%;
    display: flex;

    #current-status {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
    }
}

#signoff {
    margin-top: 1em;
    background-color: #FFFFFF;
    padding: 2em;
    height: 250px;
    display: flex;

    #signoffInteractions {
        width: calc(60% - 1em);
        margin-right: 1em;

        table {
            table-layout: fixed;

            td {
                padding: 0;
            }

            .signature-box {
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                width: calc(100% - 0.5em);
                border: 1px solid black;
                height: 80px;
                margin-top: 3em;
                position: relative;
                padding: 0.5em;

                &.left {
                    margin-right: 0.5em;
                }

                &.right {
                    margin-left: 0.5em;
                }

                .signature-box-header {
                    position: absolute;
                    top: -2em;
                    left: 0;
                }

                .signature {
                    font-family: NumberPlate;
                    text-align: center;
                }
            }
        }
    }

    #signoffLogs {
        margin-left: 1em;
        max-height: 250px;
        overflow: auto;
    }
}

@media print {
    .content-new {
        display: block;
        position: unset;

        .print-stacked {
            display: block !important;
    
            #trial-balance-card,
            #reconciliation-signoff-card {
                width: 100%;
            }
    
            #trial-balance-card {
                page-break-after: always;
            }

            #signoff {
                display: block !important;
            }
        }
    }
}

#newFinancialLineTable {
    textarea {
        background-color: transparent;
        border: 1px solid var(--grey80);
    }

    td:nth-of-type(2) {
        text-align: right;
    }

    input.newBalance {
        width: 50%;
        text-align: right;
    }
}

.hidden {
    display: none;
}

#stockCheckTitleForPrint {
    padding: 0.5em;
    margin-bottom: 1em;
}

.brace {
    position: absolute;
    width: 10px;
    right: 0;
    width:7px;

    span {
        transform: translate(21px, 33%);
        display: inline-block;
    }
}

.brace-top {
    top: 5%;
    bottom: 0;
    border-top: 2px solid var(--grey80);
    border-right: 2px solid var(--grey80);
    border-radius: 0 5px 0 0;
}

.brace-bottom {
    top: 0;
    bottom: 5%;
    border-right: 2px solid var(--grey80);
    border-bottom: 2px solid var(--grey80);
    border-radius: 0 0 5px 0;
}

.placeholder {
    vertical-align: unset;
    height: 1.5em;
}

.placeholder-lg {
    height: 2.2em;
}

.placeholder-xl {
    height: 5em;
}


#cards-container {
    display: flex;
    flex: 1;
    overflow: auto;
}