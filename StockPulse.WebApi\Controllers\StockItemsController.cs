﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using StockPulse.WebApi.ViewModel;
using StockPulse.WebApi.Service;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using static StockPulse.WebApi.Attribute.UserLevelAccessAttribute;
using StockPulse.WebApi.Attribute;

namespace StockPulse.WebApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    [UserLevelAccess(new[] { UserRole.SysAdministrator, UserRole.Reconciler, UserRole.Resolver, UserRole.Approver, UserRole.GeneralManager, UserRole.ReadOnly, UserRole.ScanAndPrint, UserRole.ScanAndView })]
    public class StockItemsController : ControllerBase, IAttributeValueProvider
    {

        private readonly IStockItemService stockItemService;
        private readonly int userId;
        private readonly string userRole;

        public UserRole attributeUserRole => GetUserRole();
        private UserRole GetUserRole()
        {
            return (UserRole)Enum.Parse(typeof(UserRole), userRole);
        }

        public StockItemsController(IStockItemService stockItemService, IUserService userService)
        {
            this.stockItemService = stockItemService;
            this.userId = userService.GetUserId();
            userRole = userService.GetUserRole(); 
        }

        // Get DMS stockitems for a stockcheck
        [HttpGet]
        [Route("GetDMSStockItems")]
        [UserLevelAccess(new[] { UserRole.SysAdministrator, UserRole.Reconciler, UserRole.Resolver, UserRole.Approver, UserRole.GeneralManager, UserRole.ReadOnly, UserRole.ScanAndView })]
        public async Task<IEnumerable<ViewModel.StockItem>> GetDMSStockItems(int stockcheckId)
        {
            return await stockItemService.GetDMSStockItems(stockcheckId, userId);
        }

        // Get AgencyStockItems
        [HttpGet]
        [Route("GetAgencyStockItems")]
        [UserLevelAccess(new[] { UserRole.SysAdministrator, UserRole.Reconciler, UserRole.Resolver, UserRole.Approver, UserRole.GeneralManager, UserRole.ReadOnly, UserRole.ScanAndView })]
        public async Task<IEnumerable<ViewModel.StockItem>> GetAgencyStockItems(int stockcheckId)
        {
            return await stockItemService.GetAgencyStockItems(stockcheckId, userId);
        }

        [HttpGet]
        [Route("GetItemFullDetail")]
        [UserLevelAccess(new[] { UserRole.SysAdministrator, UserRole.Reconciler, UserRole.Resolver, UserRole.Approver, UserRole.GeneralManager, UserRole.ReadOnly, UserRole.ScanAndView })]
        public async Task<ItemFullDetail> GetItemFullDetail(int? stockItemId,int? scanId, int? stockCheckId)
        {
            return await stockItemService.GetItemFullDetail(stockItemId, scanId, stockCheckId, userId);
        }

        [HttpGet]
        [Route("TotalItems")]
        [UserLevelAccess(new[] { UserRole.SysAdministrator, UserRole.Reconciler, UserRole.Resolver, UserRole.Approver, UserRole.GeneralManager, UserRole.ReadOnly, UserRole.ScanAndView })]
        public async Task<int> GetTotalItems(int stockCheckId)
        {
            return await stockItemService.GetTotalItems(stockCheckId, userId);
        }

        [HttpGet]
        [Route("AgencyStock")]
        [UserLevelAccess(new[] { UserRole.SysAdministrator, UserRole.Reconciler, UserRole.Resolver, UserRole.Approver, UserRole.GeneralManager, UserRole.ReadOnly, UserRole.ScanAndView })]
        public async Task<int> GetAgencyStock(int stockCheckId)
        {
            return await stockItemService.GetAgencyStock(stockCheckId, userId);
        }


        [HttpGet]
        [Route("Report/{stockcheckId}")]
        [UserLevelAccess(new[] { UserRole.SysAdministrator, UserRole.Reconciler, UserRole.Resolver, UserRole.Approver, UserRole.GeneralManager, UserRole.ReadOnly, UserRole.ScanAndView })]
        public async Task<IEnumerable<StockLoadReportSummary>> GetStockReport(int stockcheckId)
        {
            return await stockItemService.GetStockReport(stockcheckId, userId);
        }

        [HttpGet]
        [Route("Consignment/{stockcheckId}")]
        [UserLevelAccess(new[] { UserRole.SysAdministrator, UserRole.Reconciler, UserRole.Resolver, UserRole.Approver, UserRole.GeneralManager, UserRole.ReadOnly, UserRole.ScanAndView })]
        public async Task<StockConsignmentVM> GetStockConsignment(int stockcheckId)
        {
            return await stockItemService.GetStockConsignment(stockcheckId, userId);
        }

        [HttpGet]
        [Route("GetStockItemsWithResolution")]
        [UserLevelAccess(new[] { UserRole.SysAdministrator, UserRole.Reconciler, UserRole.Resolver, UserRole.Approver, UserRole.GeneralManager, UserRole.ReadOnly, UserRole.ScanAndView })]
        public async Task<IEnumerable<StockItemWithResolution>> GetStockItemsWithResolution(int stockcheckId)
        {
            return await stockItemService.GetStockItemsWithResolution(stockcheckId, userId);
        }


        [HttpGet]
        [Route("GetRepeatMissings")]
        [UserLevelAccess(new[] { UserRole.SysAdministrator, UserRole.Reconciler, UserRole.Resolver, UserRole.Approver, UserRole.GeneralManager, UserRole.ReadOnly, UserRole.ScanAndView })]
        public List<StockItemWithResolution> GetRepeatMissings(string stockcheckIds)
        {
            List<int> ids = stockcheckIds.Split(',').Select(int.Parse).ToList();
            return stockItemService.GetRepeatMissings(ids, userId);
        }

        [HttpGet]
        [Route("GetGlobalSearchResults")]
        [UserLevelAccess(new[] { UserRole.SysAdministrator, UserRole.Reconciler, UserRole.Resolver, UserRole.Approver, UserRole.GeneralManager, UserRole.ReadOnly, UserRole.ScanAndView })]
        public async Task<IEnumerable<GlobalSearchResultItem>> GetGlobalSearchResults(string reg, string vin, bool requireAndMatch)
        {
            if (String.IsNullOrEmpty(reg) && string.IsNullOrEmpty(vin))
            {
                return new List<GlobalSearchResultItem>();
            }
            else
            {
                return await stockItemService.GetGlobalSearchResults(userId, reg, vin, requireAndMatch);
            }
        }

        [HttpGet]
        [Route("DeleteAllDMSStockItems")]
        [UserLevelAccess(new[] { UserRole.SysAdministrator, UserRole.Reconciler, UserRole.Resolver, UserRole.Approver })]
        public async Task<int> DeleteAllDMSStockItems(int stockcheckId)
        {
            return await stockItemService.DeleteAllDMSStockItems(stockcheckId, userId);
        }

        [HttpGet]
        [Route("DeleteAllAgencyStockItems")]
        [UserLevelAccess(new[] { UserRole.SysAdministrator, UserRole.Reconciler, UserRole.Resolver, UserRole.Approver })]
        public async Task<int> DeleteAllAgencyStockItems(int stockcheckId)
        {
            return await stockItemService.DeleteAllAgencyStockItems(stockcheckId, userId);
        }

        [HttpGet]
        [Route("DeleteStockItems")]
        [UserLevelAccess(new[] { UserRole.SysAdministrator, UserRole.Reconciler, UserRole.Resolver, UserRole.Approver })]
        public async Task<int> DeleteStockItems(int stockcheckId, int itemId)
        {
            return await stockItemService.DeleteStockItem(stockcheckId, userId, itemId);
        }

        [HttpPost]
        [Route("SaveMissingResolution")]
        [UserLevelAccess(new[] { UserRole.SysAdministrator, UserRole.Reconciler, UserRole.Resolver, UserRole.Approver })]
        public async Task SaveMissingResolution(Resolution missingResolutionVM)
        {
            await stockItemService.SaveMissingResolution(missingResolutionVM, userId);
        }

        [HttpPost]
        [Route("DeleteResolution")]
        [UserLevelAccess(new[] { UserRole.SysAdministrator, UserRole.Reconciler, UserRole.Resolver, UserRole.Approver })]
        public async Task DeleteResolution(ResolutionDeleteParams parms)
        {
            await stockItemService.DeleteMissingResolution(parms.ResolutionId, parms.StockCheckId, userId);
        }

        [HttpPost]
        [Route("AddStockItems")]
        [UserLevelAccess(new[] { UserRole.SysAdministrator, UserRole.Reconciler, UserRole.Resolver, UserRole.Approver })]
        public async Task<IActionResult> AddStockItem(BulkAddStockItemsParams parms)
        {
            try
            {
                await stockItemService.AddStockItems(parms.newStockItems, parms.stockCheckId, userId, parms.fileImportId);
                return Ok();
            }
            catch (Exception e)
            {
                return BadRequest(e.Message);
            }
        }

        [HttpGet]
        [Route("GetStockItemsForSite")]
        public async Task<IEnumerable<StockItem>> GetStockItemsForSite(int siteId)
        {
            return await stockItemService.GetStockItemsForSite(siteId);
        }
    }
}
