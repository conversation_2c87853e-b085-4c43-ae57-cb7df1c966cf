﻿using Dapper;
using StockPulse.WebApi.Dapper;
using StockPulse.WebApi.ViewModel;
using System.Threading.Tasks;

namespace StockPulse.WebApi.DataAccess
{
    public interface IFileImportDataAccess
    {
        Task<int> AddFileImport(FileImport fileImport, int userId);
    }

    public class FileImportDataAccess : IFileImportDataAccess
    {
        private readonly IDapper dapper;

        public FileImportDataAccess(IDapper dapper)
        {
            this.dapper = dapper;
        }

        public async Task<int> AddFileImport(FileImport fileImport, int userId)
        {
            var paramList = new DynamicParameters();
            paramList.Add("FileName", fileImport.FileName);
            paramList.Add("FileDate", fileImport.FileDate);
            paramList.Add("LoadDate", fileImport.LoadDate);
            paramList.Add("LoadedByUserId", userId);

            return await dapper.GetAsync<int>("dbo.ADD_FileImport", paramList, System.Data.CommandType.StoredProcedure);

        }
    }
}
