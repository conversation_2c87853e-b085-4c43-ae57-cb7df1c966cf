﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].GET_RecProcessMatchChoice
(
    @StockCheckId INT = NULL,
	@UserId INT = NULL
)
AS
BEGIN

SET NOCOUNT ON

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END

	
SELECT
gb.BoolValue
FROM StockChecks sc
INNER JOIN Sites si on si.Id = sc.SiteId
INNER JOIN Divisions div on div.id = si.DivisionId
LEFT JOIN GlobalParams gb on gb.DealerGroupId = div.DealerGroupId
WHERE sc.Id = @StockCheckId
AND gb.Name = 'recProcessMatchRequiresBothRegAndVin'

	

END
GO




