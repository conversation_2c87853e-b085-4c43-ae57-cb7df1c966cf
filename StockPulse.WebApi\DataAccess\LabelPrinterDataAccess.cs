﻿using Dapper;
using StockPulse.WebApi.Dapper;
using System.Threading.Tasks;

namespace StockPulse.WebApi.DataAccess
{
    public interface ILabelPrinterDataAccess
    {
        Task SavePrintLog(string vin, int userId);
    }

    public class LabelPrinterDataAccess : ILabelPrinterDataAccess
    {
        private readonly IDapper dapper;

        public LabelPrinterDataAccess(IDapper dapper)
        {
            this.dapper = dapper;
        }

        public async Task SavePrintLog(string vin, int userId)
        {
            var paramList = new DynamicParameters();
            paramList.Add("UserId", userId);
            paramList.Add("Vin", vin);
            await dapper.ExecuteAsync("dbo.CREATE_LabelPrintLog", paramList, System.Data.CommandType.StoredProcedure);

        }
    }
}
