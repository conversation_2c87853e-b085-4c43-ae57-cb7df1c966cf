﻿using Dapper;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using StockPulse.Model;
using StockPulse.WebApi.Dapper;
using Microsoft.EntityFrameworkCore.Metadata.Internal;

namespace StockPulse.WebApi.DataAccess
{
    public interface IMaintenanceTableDataAccess
    {
        Task<IEnumerable<MaintenanceTable>> GetTables();
        Task<string> getTableName(int tableId);
        Task<string> getData(string query);
        Task<bool> SaveData(string query);
        Task<bool> DeleteData(string query);
    }
    public class MaintenanceTableDataAccess : IMaintenanceTableDataAccess
    {
        private readonly IDapper dapper;

        public MaintenanceTableDataAccess(IDapper dapper)
        {
            this.dapper = dapper;
        }

        public async Task<string> getData(string query)
        {

            var tables = (from row in await dapper.QueryAsync(query, null, System.Data.CommandType.Text)
                          select (IDictionary<string, object>)row).AsList();
            var json = JsonConvert.SerializeObject(tables, Formatting.Indented);
            //System.Console.WriteLine(json);

            return json;
        }

        public async Task<string> getTableName(int tableId)
        {
            var query = $"SELECT Name FROM MaintenanceTables WHERE Id = {tableId}";
            var result = (await dapper.GetAllAsync<string>(query, null, System.Data.CommandType.Text)).FirstOrDefault();
            return result;
        }

        public async Task<IEnumerable<MaintenanceTable>> GetTables()
        {
            var query = $"SELECT * FROM MaintenanceTables WHERE IsEnabled = 1";
            var result = await dapper.GetAllAsync<MaintenanceTable>(query, null, System.Data.CommandType.Text);

            return result;
        }

        public async Task<bool> SaveData(string query)
        {
            var result = await dapper.QueryAsync(query, null, System.Data.CommandType.Text);

            return true;
        }

        public async Task<bool> DeleteData(string query)
        {
            var result = await dapper.QueryAsync(query, null, System.Data.CommandType.Text);

            return true;
        }
    }
}
