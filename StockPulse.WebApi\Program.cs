using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Hosting;
using StockPulse.Repository.Database;
using System;

namespace StockPulse.WebApi
{
    public class Program
    {
        public static void Main(string[] args)
        {
            CreateHostBuilder(args).Build().Run();
        }

        public static IHostBuilder CreateHostBuilder(string[] args) =>
            Host.CreateDefaultBuilder(args)
                .ConfigureWebHostDefaults(webBuilder =>
                {
                    StockpulseContext.envi = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");
                    webBuilder.UseStartup<Startup>();
                });
    }
}
