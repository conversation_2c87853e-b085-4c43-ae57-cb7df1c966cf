﻿using System;

namespace StockPulse.WebApi.ViewModel
{
    //load stock and rec items
    public class StockItemWithScan : StockItem
    {
        public string ScanDescription { get; set; }
        public string ScannedBy { get; set; }
        public DateTime ScanDateTime { get; set; }
        public string LocationDescription { get; set; }
        public int ScanId { get; set; }
        public decimal Longitude { get; set; }
        public decimal Latitude { get; set; }
        public decimal DistanceFromDealershipInMiles { get; set; }
        public decimal StockCheckLongitude { get; set; }
        public decimal StockCheckLatitude { get; set; }
        public string SiteName {  get; set; }
        public string RegEditStatus { get; set; }
        public string VinEditStatus { get; set; }


        public void PopulateDistance(decimal dealershipLongitude, decimal dealershipLatitude)
        {
            /// https://stackoverflow.com/questions/6366408/calculating-distance-between-two-latitude-and-longitude-geocoordinates
            var longDouble = Decimal.ToDouble(Longitude);
            var latDouble = Decimal.ToDouble(Latitude);

            var dealershipLongDouble = Decimal.ToDouble(dealershipLongitude);
            var dealershipLatDouble = Decimal.ToDouble(dealershipLatitude);


            var d1 = dealershipLatDouble * (Math.PI / 180.0);
            var num1 = dealershipLongDouble * (Math.PI / 180.0);
            var d2 = latDouble * (Math.PI / 180.0);
            var num2 = longDouble * (Math.PI / 180.0) - num1;
            var d3 = Math.Pow(Math.Sin((d2 - d1) / 2.0), 2.0) + Math.Cos(d1) * Math.Cos(d2) * Math.Pow(Math.Sin(num2 / 2.0), 2.0);

            DistanceFromDealershipInMiles = (decimal)( 6376500.0 * (2.0 * Math.Atan2(Math.Sqrt(d3), Math.Sqrt(1.0 - d3))) / 1609); /// /1609 converts metres to miles
        }
    }


}
