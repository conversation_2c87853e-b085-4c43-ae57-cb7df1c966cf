﻿
using System;

namespace StockPulse.Loader.Services
{


    public static class PulsesService
        {
            
            public static DateTimeWrapper GenericLoaderJob = new DateTimeWrapper() { Date = DateTime.UtcNow };

            //Stockpulse
            public static DateTimeWrapper STK_JardineStock = new DateTimeWrapper() { Date = DateTime.UtcNow };
            public static DateTimeWrapper STK_JardineWIP = new DateTimeWrapper() { Date = DateTime.UtcNow };
            public static DateTimeWrapper STK_JardineTB = new DateTimeWrapper() { Date = DateTime.UtcNow };

            public static DateTimeWrapper STK_MMGTB = new DateTimeWrapper() { Date = DateTime.UtcNow };
            public static DateTimeWrapper STK_MMGWIP = new DateTimeWrapper() { Date = DateTime.UtcNow };
            public static DateTimeWrapper STK_MMGStock = new DateTimeWrapper() { Date = DateTime.UtcNow };

            public static DateTimeWrapper STK_MMGStockOnLoan = new DateTimeWrapper() { Date = DateTime.UtcNow };
            public static DateTimeWrapper STK_MMGAtAuction = new DateTimeWrapper() { Date = DateTime.UtcNow };

            public static DateTimeWrapper STK_LithiaTB = new DateTimeWrapper() { Date = DateTime.UtcNow };
            public static DateTimeWrapper STK_LithiaWIP = new DateTimeWrapper() { Date = DateTime.UtcNow };
            public static DateTimeWrapper STK_LithiaStock = new DateTimeWrapper() { Date = DateTime.UtcNow };
            public static DateTimeWrapper STK_LithiaBookedAndPending = new DateTimeWrapper() { Date = DateTime.UtcNow };
    }



}
