<div class="d-flex align-items-center">
    <div *ngIf="stockTypeFiles" class="d-inline-block" ngbDropdown>
        <button class="btn btn-primary left" ngbDropdownToggle>
            <img [src]="logoService.provideExcelLogo()">
            <span  class="ms-2">Missing Unresolved</span>
        </button>
        <div class="report-dropdown-menu" ngbDropdownMenu>
            <button ngbDropdownItem (click)="downloadAllFiles(true)">
                <div class="button-inner">
                    <span class="icon-and-label">
                        <img [src]="logoService.provideExcelLogo()"> <span class="ms-2">All Stock Types</span>
                    </span>
                    <span class="ms-1">({{ constantsService.pluralise(missingUnresolvedLength, 'vehicle', 'vehicles') }})</span>
                </div>
            </button>
            <div class="dropdown-divider"></div>
            <button *ngFor="let item of stockTypeFiles" ngbDropdownItem (click)="downloadFile(item, true)">
                <div class="button-inner">
                    <span class="icon-and-label">
                        <img [src]="logoService.provideExcelLogo()"> <span class="ms-2">{{ item.label }}</span>
                    </span>
                    <span class="ms-1">({{ constantsService.pluralise(getUnresolvedItemsCount(item.stockItems), 'vehicle', 'vehicles') }})</span>
                </div>
            </button>
        </div>
    </div>
    <div *ngIf="locationFiles" class="d-inline-block" ngbDropdown>
        <button class="btn btn-primary right" ngbDropdownToggle>
            <img [src]="logoService.provideExcelLogo()">
            <span  class="ms-2">Unknown Unresolved</span>
        </button>
        <div class="report-dropdown-menu" ngbDropdownMenu>
            <button ngbDropdownItem (click)="downloadAllFiles(false)">
                <div class="button-inner">
                    <span class="icon-and-label">
                        <img [src]="logoService.provideExcelLogo()"> <span class="ms-2">All Locations</span>
                    </span>
                    <span class="ms-1">({{ constantsService.pluralise(unknownUnresolvedLength, 'vehicle', 'vehicles') }})</span>
                </div>
            </button>
            <div class="dropdown-divider"></div>
            <button *ngFor="let item of locationFiles" ngbDropdownItem (click)="downloadFile(item, false)">
                <div class="button-inner">
                    <span class="icon-and-label">
                        <img [src]="logoService.provideExcelLogo()"> <span class="ms-2">{{ item.label }}</span>
                    </span>
                    <span class="ms-1">({{ constantsService.pluralise(getUnresolvedItemsCount(item.scanItems), 'vehicle', 'vehicles') }})</span>
                </div>
            </button>
        </div>
    </div>
</div>