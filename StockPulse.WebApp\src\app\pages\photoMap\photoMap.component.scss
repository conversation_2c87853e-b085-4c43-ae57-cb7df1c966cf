.content-new {
    background: rgba(2500, 2500, 2500, 0.6);
}

.cph-card-body.showingImages {
    row-gap: 0.5em !important;
    column-gap: 0.5em !important;
}

.button-spacing {
    margin-right: 6px;
  }

.cph-card {
    margin-bottom: 1em;

    .cph-card-body {
        display: flex;
        row-gap: 0.2em;
        column-gap: 0.2em;
        flex-wrap: wrap;

        img {
            width: 70px;
            height: 70px;
        }

        .regAndVinThumbnail {
            width: calc(10% - 0.5em);
            padding: 10px;
            border: 1px solid #000000;
        }
    }
}

.cph-card:last-of-type {
    margin-bottom: 0;
}

.imageOrBlobHolder {

    cursor: pointer;

    .img {
        width: 70px;
        height: 70px;
        border-radius: 5px;
    }

    .blob {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        border: 1px solid var(--primaryLightest);
    }
}


// Blob colours
.imageOrBlobHolder.Duplicate .blob {
    background: grey;
}

.imageOrBlobHolder.MatchedToStockOrScan .blob {
    background: green;
}

.imageOrBlobHolder.MatchedToOtherSite .blob {
    background: lightGreen;
}

.imageOrBlobHolder.MatchedToReport .blob {
    background: yellow;
}

.imageOrBlobHolder.Resolved .blob {
    background: orange;
}

.imageOrBlobHolder.OutstandingIssue .blob {
    background: red;
}


// Colours below images

.imageOrBlobHolder.Duplicate.showImage {
    border-bottom: 7px solid grey;
}

.imageOrBlobHolder.MatchedToStockOrScan.showImage {
    border-bottom: 7px solid green;
}

.imageOrBlobHolder.MatchedToOtherSite.showImage {
    border-bottom: 7px solid lightGreen;
}

.imageOrBlobHolder.MatchedToReport.showImage {
    border-bottom: 7px solid yellow;
}

.imageOrBlobHolder.Resolved.showImage {
    border-bottom: 7px solid orange;
}

.imageOrBlobHolder.OutstandingIssue.showImage {
    border-bottom: 7px solid red;
}





#keyHolder {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    min-height: 10em;

    #key {
        position: fixed;
        display: flex;
        border: 1px solid var(--grey70);
        background-color: #FFFFFF;
        padding: 1em;
        border-radius: 6px;
        margin-bottom: 1em;
        width: 50%;

        .imageOrBlobHolder {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            width: 16.6666666667%;

            .label {
                text-align: center;
                height: 30px;
            }

            .blob {
                height: 30px;
            }
        }
    }

    @media (min-width: 1680px) {
        #key {
            width: 40%;
        }
    }
}


#key.showingPhotos {
    .blob {
        width: 70px !important;
        height: 7px !important;
        border: none !important;
        border-radius: 0px !important;
    }
}


#scanners-dropdown {
    min-width: 300px;

    button {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
}