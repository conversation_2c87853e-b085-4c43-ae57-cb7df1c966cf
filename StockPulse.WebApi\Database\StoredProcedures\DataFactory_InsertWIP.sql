﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].[DataFactory_InsertWIP]
AS
BEGIN


	--------------------------------------------------------------------------------------------------
	----Backup the data---
	DECLARE @UniqueID UNIQUEIDENTIFIER
	SET @UniqueID  = NEWID()

	DECLARE @BackupDate DateTime
	SET @BackupDate = GETUTCDATE()

	INSERT INTO [dbo].[stockpulse_wip_backup]
           ([UniqueId]
           ,[BackupDate]
           ,[Reg]
           ,[WIP Number]
           ,[Chassis]
           ,[Description]
           ,[Comment]
           ,[Reference]
           ,[Location Description]
           ,[Delete Flag]
           ,[Fingerprint]
           ,[Updated])
	SELECT 
           @UniqueId
           ,@BackupDate
           ,[Reg]
           ,[WIP Number]
           ,[Chassis]
           ,[Description]
           ,[Comment]
           ,[Reference]
           ,[Location Description]
           ,[Delete Flag]
           ,[Fingerprint]
           ,[Updated]
	FROM [dbo].stockpulse_wip

	--DELETE Older backups. Delete backups older than 15days.
	DELETE FROM [stockpulse_wip_backup] WHERE BackupDate < GETUTCDATE() - 15



UPDATE [dbo].stockpulse_wip
SET [Description] = REPLACE([Description], '  ', '')

UPDATE [dbo].stockpulse_wip
SET [Comment] = REPLACE([Comment], '  ', '')


INSERT INTO [dbo].[ReconcilingItems]
           ([ReconcilingItemTypeId]
           ,[Reg]
           ,[Vin]
           ,[Description]
           ,[Comment]
           ,[Reference]
           ,[SourceReportId]
           ,[StockCheckId])
     

SELECT 7, WIP.Reg, WIP.Chassis, WIP.Description, WIP.Comment, WIP.Reference, 1, ISNULL(SC.Id,SCForMap.Id)
FROM [dbo].stockpulse_wip AS WIP
LEFT JOIN [dbo].[Sites] AS S ON S.Description = WIP.[Location Description]
LEFT JOIN [dbo].[stockchecks] AS SC ON S.Id = SC.SiteId AND SC.IsActive = 1
LEFT JOIN [import].[SiteDescriptionDictionary] AS BN ON WIP.[Location Description] = BN.Description AND BN.IsPrimarySiteId = 1 AND BN.DealerGroupId = 1
LEFT JOIN [dbo].[Sites] AS SForMap ON SForMap.Id = BN.SiteId 
LEFT JOIN [dbo].[stockchecks] AS SCForMap ON SForMap.Id = SCForMap.SiteId AND SCForMap.IsActive = 1
WHERE ISNULL(S.Id,SForMap.Id) IS NOT NULL AND ISNULL(SC.Id,SCForMap.Id) IS NOT NULL

END


 