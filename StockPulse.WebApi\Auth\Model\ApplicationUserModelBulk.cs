﻿using System.ComponentModel.DataAnnotations;

namespace StockPulse.WebApi.Auth.Model
{
    public class ApplicationUserModelBulk
    {
        [Required]
        [EmailAddress]
        public string Email { get; set; }

        [Required]
        public string UserName { get; set; }

        [Required]
        public int LinkedPersonId { get; set; }

        //[Required]
        //[StringLength(100, ErrorMessage = "The {0} must be at least {2} characters long.", MinimumLength = 6)]
        [DataType(DataType.Password)]
        public string Password { get; set; }

        //[Required]
        //[DataType(DataType.Password)]
        //[Compare("Password", ErrorMessage = "The password and confirmation password do not match.")]
        public string ConfirmPassword { get; set; }

        public string Name { get; set; }
        public string Sites { get; set; }
        public int SiteId { get; set; }
        public string RoleName { get; set; }
        public int DealerGroup { get; set; }


    }
}
