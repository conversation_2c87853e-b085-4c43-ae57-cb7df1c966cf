import { Injectable } from "@angular/core";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { ScanRegDifference } from "src/app/model/ScanRegDifference";
import { ApiAccessService } from "src/app/services/apiAccess.service";
import { ConstantsService } from "src/app/services/constants.service";
import { ToastService } from "src/app/services/newToast.service";
import { SelectionsService } from "src/app/services/selections.service";

@Injectable({
  providedIn: 'root'
})


export class VehiclesWithWrongRegService {


  vehicles: ScanRegDifference[];
  mismatchOptions: string[];
  mismatchOption: string;
  scanRegDifferences: ScanRegDifference[];

  

  constructor (
    public constants: ConstantsService,
    public selections: SelectionsService,
    public modalService: NgbModal,
    public api: ApiAccessService,
    public toastService: ToastService
  ){
    
  }



  initVehiclesWithWrongReg(reset?: boolean) {
    
    
        this.vehicles = [],
        this.scanRegDifferences = [];
        this.mismatchOptions = ['Reg differences', 'VIN differences', 'All differences'];
        this.mismatchOption = 'Reg differences';
  }



}