﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using static StockPulse.WebApi.Attribute.UserLevelAccessAttribute;
using StockPulse.WebApi.Attribute;
using StockPulse.WebApi.Service;
using System.Threading.Tasks;
using StockPulse.Model;
using System.Collections.Generic;
using StockPulse.WebApi.ViewModel;
using System;

namespace StockPulse.WebApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    [UserLevelAccess(new[] { UserRole.SysAdministrator, UserRole.Reconciler, UserRole.Resolver, UserRole.Approver, UserRole.GeneralManager, UserRole.ReadOnly, UserRole.ScanAndView })]

    public class StatusChangeLogItemsController : ControllerBase, IAttributeValueProvider
    {
        private readonly IStatusChangeLogItemsService StatusChangeLogItemsService;
        private readonly string userRole;

        public UserRole attributeUserRole => GetUserRole();
        private UserRole GetUserRole()
        {
            return (UserRole)Enum.Parse(typeof(UserRole), userRole);
        }

        public StatusChangeLogItemsController(IStatusChangeLogItemsService StatusChangeLogItemsService, IUserService userService)
        {
            this.StatusChangeLogItemsService = StatusChangeLogItemsService;
            userRole = userService.GetUserRole();
        }

        [HttpGet]
        [Route("GetStatusChangeLogItems")]
        public async Task<IEnumerable<StatusChangeLogItemVM>> GetStatusChangeLogItems(int stockCheckId)
        {
            return await StatusChangeLogItemsService.GetStatusChangeLogItems(stockCheckId);
        }

        [HttpPost]
        [Route("AddStatusChangeLogItem")]
        public async Task AddStatusChangeLogItem(StatusChangeLogItemParams parms)
        {
            await StatusChangeLogItemsService.AddStatusChangeLogItem(parms);
        }
    }
}
