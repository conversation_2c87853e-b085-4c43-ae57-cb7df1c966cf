SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].GET_ScansWithLocation
(
    @StockCheckId INT = NULL,
    @UserId INT = NULL
)
AS
BEGIN

SET NOCOUNT ON;

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END

DECLARE @isRegional INT;
SET @isRegional = (SELECT IsRegional FROM StockChecks
                    WHERE StockChecks.Id = @StockCheckId)

DECLARE @isTotal INT;
SET @isTotal = (SELECT IsTotal FROM StockChecks
                    WHERE StockChecks.Id = @StockCheckId)

IF @isRegional = 0 AND @isTotal = 0
	
    BEGIN 

    WITH temporaryTable AS 
    (
    SELECT Scans.Id, 
    Scans.[Description] AS Description, 
    Locations.[Description] AS Location,
    DIS,
    CASE
        WHEN (Scans.StockItemId IS NOT NULL AND Scans.IsDuplicate = 0) THEN 'Scanned, InStock'
        WHEN (Scans.ReconcilingItemId IS NOT NULL AND Scans.IsDuplicate = 0) THEN 'Not instock, matched to report'
        WHEN (Scans.UnknownResolutionId IS NOT NULL AND Scans.IsDuplicate = 0) THEN 'Unknown vehicle, resolved'
        WHEN (Scans.UnknownResolutionId IS NULL AND Scans.ReconcilingItemId IS NULL AND Scans.StockItemId IS NULL AND Scans.IsDuplicate = 0) THEN 'Unknown vehicle'
    END AS Status
    FROM [dbo].[Scans]
    LEFT JOIN StockItems ON StockItems.Id = Scans.StockItemId
    INNER JOIN Locations ON Locations.Id = Scans.LocationId
    WHERE [Scans].[StockCheckId] = @StockCheckId
    )
    SELECT Id, Description, Location, Status, DIS FROM temporaryTable;

	END

IF @isRegional = 1
	
    BEGIN

    DECLARE @DivisionId INT;

    SET @DivisionId = (SELECT DivisionId FROM StockChecks
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id
                        WHERE StockChecks.Id = @StockCheckId AND StockChecks.IsRegional = 1)

    ;WITH temporaryTable AS 
    (
    SELECT Scans.Id, 
    Scans.[Description] AS Description, 
    Locations.[Description] AS Location,
    DIS,
    CASE
        WHEN (Scans.StockItemId IS NOT NULL AND Scans.IsDuplicate = 0) THEN 'Scanned, InStock'
        WHEN (Scans.ReconcilingItemId IS NOT NULL AND Scans.IsDuplicate = 0) THEN 'Not instock, matched to report'
        WHEN (Scans.UnknownResolutionId IS NOT NULL AND Scans.IsDuplicate = 0) THEN 'Unknown vehicle, resolved'
        WHEN (Scans.UnknownResolutionId IS NULL AND Scans.ReconcilingItemId IS NULL AND Scans.StockItemId IS NULL AND Scans.IsDuplicate = 0) THEN 'Unknown vehicle'
    END AS Status
    FROM [dbo].[Scans]
    LEFT JOIN StockItems ON StockItems.Id = Scans.StockItemId
    INNER JOIN Locations ON Locations.Id = Scans.LocationId
    INNER JOIN StockChecks ON StockChecks.Id=StockItems.StockCheckId
    INNER JOIN Sites ON Sites.Id=StockChecks.SiteId
    INNER JOIN Divisions ON Divisions.Id=Sites.DivisionId
    WHERE Divisions.Id = @DivisionId
    )
    SELECT Id, Description, Location, Status, DIS FROM temporaryTable;

	END
    
IF @isTotal = 1

    BEGIN

    DECLARE @DealerGroupId INT;
    SET @DealerGroupId = (SELECT DealerGroupId FROM StockChecks
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id
                        WHERE StockChecks.Id = @StockCheckId)

    ;WITH temporaryTable AS 
    (
    SELECT Scans.Id, 
    Scans.[Description] AS Description, 
    Locations.[Description] AS Location,
    DIS,
    CASE
        WHEN (Scans.StockItemId IS NOT NULL AND Scans.IsDuplicate = 0) THEN 'Scanned, InStock'
        WHEN (Scans.ReconcilingItemId IS NOT NULL AND Scans.IsDuplicate = 0) THEN 'Not instock, matched to report'
        WHEN (Scans.UnknownResolutionId IS NOT NULL AND Scans.IsDuplicate = 0) THEN 'Unknown vehicle, resolved'
        WHEN (Scans.UnknownResolutionId IS NULL AND Scans.ReconcilingItemId IS NULL AND Scans.StockItemId IS NULL AND Scans.IsDuplicate = 0) THEN 'Unknown vehicle'
    END AS Status
    FROM [dbo].[Scans]
    LEFT JOIN StockItems ON StockItems.Id = Scans.StockItemId
    INNER JOIN Locations ON Locations.Id = Scans.LocationId
    INNER JOIN StockChecks ON StockChecks.Id=StockItems.StockCheckId
    INNER JOIN Sites ON Sites.Id=StockChecks.SiteId
    INNER JOIN Divisions ON Divisions.Id=Sites.DivisionId
    INNER JOIN DealerGroup ON Divisions.DealerGroupId=DealerGroup.Id
    WHERE DealerGroup.Id = @DealerGroupId
    )
    SELECT Id, Description, Location, Status, DIS FROM temporaryTable;

    END

END

GO




--To use this run 
--exec [GET_ScansWithLocation] @StockCheckId = 99, @UserId = 99