﻿using StockPulse.Model;
using System.Collections.Generic;

namespace StockPulse.WebApi.ViewModel
{
    public class WhoAmIResult
    {
        public string Role { get; set; }
        public int UserId { get; set; }
        public string UsersName { get; set; }
        public string UserSiteIds { get; set; }
        public string UserHomeSiteName { get; set; }

        public IEnumerable<DealerGroupVM> DealerGroupsForSysAdmin { get; set; }

        public IEnumerable<ResolutionTypeVM> ResolutionTypes { get; set; }
        public IEnumerable<GlobalParam> GlobalParams { get; set; }

        public string UserUsername { get; set; }
        public IEnumerable<UserPreference> UserPreferences { get; set; }
    }
}
