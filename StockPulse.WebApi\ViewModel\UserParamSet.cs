﻿namespace StockPulse.WebApi.ViewModel
{
    public class UserParamSet
    {
        public string DealerGroupName { get; set; }
        public int DealerGroupId { get; set; }
        public int UserId { get; set; }
        public string Name { get; set; }
        public string AspNetUserId { get; set; }
        public string UserName { get; set; }
        public string Email { get; set; }
        public string SiteIds { get; set; }
        public string SiteNames { get; set; }
        public string SiteShortNames { get; set; }
        public string DefaultSiteName { get; set; }
        public string DivisionNames { get; set; }

        public string RoleName { get; set; }
        public bool CanSeeDebug { get; set; }
        public bool CanSubmit { get; set; }
        public bool CanApproveSubmissions { get; set; }
        public bool CanCreateVersions { get; set; }
    }
}

