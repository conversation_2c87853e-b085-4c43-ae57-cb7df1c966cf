/****** Object:  StoredProcedure [dbo].[GET_ReconcilingItemsWithType]    Script Date: 22/03/21 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].GET_ReconcilingItemsWithType
(
    @StockCheckId INT = NULL,
    @UserId INT = NULL
)
AS
BEGIN

SET NOCOUNT ON;

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END


	
SELECT 
recitems.Id, 
Reg, 
Vin, 
recitems.Description, 
Comment, 
rectypes.Id as ReconcilingItemTypeId, 
rectypes.Description as ReconcilingItemTypeDescription
FROM ReconcilingItems recitems
INNER JOIN ReconcilingItemTypes rectypes on recitems.ReconcilingItemTypeId = rectypes.Id
WHERE recitems.StockCheckId = @StockCheckId;



END

GO



--To use this run 
--exec [GET_ReconcilingItemsWithType] @StockCheckId = 1, @UserId = 104