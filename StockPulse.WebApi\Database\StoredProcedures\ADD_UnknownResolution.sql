﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].[ADD_UnknownResolution]
(
    @StockCheckId INT,
	@UserId INT,
	@ResolutionTypeId INT,
	@ResolutionDate datetime,
	@Notes nvarchar(250),
	@IsResolved bit
)
AS
BEGIN

SET NOCOUNT ON;

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END

INSERT INTO [dbo].[UnknownResolutions]
    ([ResolutionTypeId]
    ,[ResolutionDateTime]
    ,[Notes]
    ,[IsResolved]
    ,[UserId])
VALUES
    (@ResolutionTypeId
    ,@ResolutionDate
    ,@Notes
    ,@IsResolved
    ,@UserId)


DECLARE @Id INT
SET @Id = SCOPE_IDENTITY();

EXEC [dbo].[UPDATE_StockCheckLastUpdate] @StockCheckId, @UserId

SELECT @Id



END
	


GO

