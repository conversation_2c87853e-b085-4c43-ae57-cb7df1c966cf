﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace StockPulse.WebApi.Migrations
{
    /// <inheritdoc />
    public partial class fixissuewithfileimporttablev3 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "Id",
                schema: "import",
                table: "FileImports",
                type: "int",
                nullable: false,
                defaultValue: 0)
                .Annotation("SqlServer:Identity", "1, 1");

            migrationBuilder.AddPrimaryKey(
                name: "PK_FileImports",
                schema: "import",
                table: "FileImports",
                column: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropPrimaryKey(
                name: "PK_FileImports",
                schema: "import",
                table: "FileImports");

            migrationBuilder.DropColumn(
                name: "Id",
                schema: "import",
                table: "FileImports");
        }
    }
}
