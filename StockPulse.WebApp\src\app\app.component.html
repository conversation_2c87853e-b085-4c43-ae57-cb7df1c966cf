<script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyAEVEZthjZnRUnc0xvjOXgpguJRlADhqAg"></script>

<app-navbar *ngIf="selections.userId"></app-navbar>
<app-sidenav *ngIf="selections.userId"></app-sidenav>

<section id="appWrapper" class="amLoggedIn" 
  [style.background]="background()" [ngClass]="appWrapperClassProvider">
  <router-outlet></router-outlet>
</section>




<!-- Alert box -->
<div *ngIf="constants.alert.show" class="alert animated slideInRight" [ngClass]="constants.alert.type" role="alert">
  {{constants.alert.message}}
</div>

<!-- Edit modal for vehicle rec details -->
<vehicleModal #vehicleModal></vehicleModal>

<!-- Shared Modals -->
<ng-template #alertModal let-modal>
  <div class="modal-header">
    <h4 class="modal-title" id="modal-basic-title">{{constants.alertModal.title}}</h4>
    <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body alertModalBody lowHeight">
    <pre>{{constants.alertModal.message}}</pre>

  </div>
  <div class="modal-footer">
    <button type="button" [ngClass]="{'btn-danger': !constants.alertModal.showOkInSuccessColour, 'btn-success':constants.alertModal.showOkInSuccessColour}" class="btn " (click)="modal.close('Save click')">OK</button>
    <button type="button" class="btn btn-primary" (click)="modal.dismiss('Cancelled')">Cancel</button>
  </div>
</ng-template>

<!-- Ror modal -->
<addUserModal #addUserModal></addUserModal>
<!-- Confirm modal -->
<confirmModal #confirmModal></confirmModal>

<dealerGroupSelectionModal #dealerGroupSelectionModal></dealerGroupSelectionModal>

<!-- Spinner backdrop -->
<div *ngIf="constants.spinnerBackdrop" id="spinner-backdrop"></div>

<scanLocationsModal #scanLocationsModal></scanLocationsModal>