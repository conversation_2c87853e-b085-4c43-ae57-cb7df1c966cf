﻿using Microsoft.EntityFrameworkCore;
using StockPulse.Loader.Stockpulse.ViewModel;
using StockPulse.Model;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using StockPulse.Repository.Database;
using StockPulse.Loader.ViewModel;
using Quartz.Util;

namespace StockPulse.Loader.Services
{


   public class JardineWIPLoaderService : GenericLoaderJobServiceParams
   {
      private LogMessage logMessage;



      //constructor
      public JardineWIPLoaderService()
      {

      }

      public JobParams GetMatchingFilesAndImportParams(string incomingRoot)
      {
         string customer = "jardine";

         JobParams parms = new JobParams()
         {
            jobType = LoaderJob.JardineWIP,
            customerFolder = "jardine",
            filename = "*STK-Jardine-WIP.csv",
            importSPName = null,
            loadingTableName = "ReconcilingItems",
            jobName = "JardineWIPs",
            pulse = PulsesService.STK_JardineWIP,
            fileType = FileType.csv,
            dealerGroupId = 7,
            regexPattern = "(?<=^|\\|)(\\\"[^\\\"]*\\\"|[^|]*)",
            headerFailColumn = "StockCheckSite",
            //parsedItemType = typeof(JardineStock),
            headerDefinitions = BuildHeaderDictionary(),
            errorCount = 0,
            allMatchingFiles = Directory.GetFiles(incomingRoot.Replace("{customer}", customer), $"*STK-Jardine-WIP.csv"),
            reconcilingItemTypeIdsToInclude = "71",
            delimiter = '|'
         };

         return parms;
      }

      public DataTable InterpretFile(JobParams parms, RowsAndHeadersResult rowsAndHeaders, LogMessage logMessage)
      {
         List<Model.Input.ReconcilingItem> incomingWIP = new List<Model.Input.ReconcilingItem>();// ExtractIncoming(parms, ref parms.errorCount, logMessage, allRows, headerLookup);

         int incomingProcessCount = 0;
         incomingWIP = new List<Model.Input.ReconcilingItem>(10000);  //preset the list size (slightly quicker than growing it each time)

         Dictionary<string, int> missingSitesDictionary = new Dictionary<string, int>();
         string sitesUnique = "";

         using (var db = new StockpulseContext())
         {
            // Main sites
            List<Site> sites = db.Sites
                .AsNoTracking()
                .Where(s => s.Divisions.DealerGroupId == parms.dealerGroupId)
                .Include(s => s.Divisions)
                .ToList();

            // Description dictionary backup
            var siteDescriptionDictionary = db.SiteDescriptionDictionary.Where(x => x.DealerGroupId == parms.dealerGroupId).AsEnumerable().ToDictionary(x => x.Description.ToUpper(), x => x);

            HashSet<string> closedSites = new HashSet<string>
                {
                    "DAF BIRTLEY",
                    "DAF LEEDS",
                    "DAF STOCKTON",
                    "LAND ROVER AYLESBURY"
                };

            foreach (var rowCols in rowsAndHeaders.rowsAndCells)
            {
               incomingProcessCount++;

               try
               {
                  if (rowCols.Length != rowsAndHeaders.headerLookup.Count())
                  {
                     //something weird happened, not got enough rowCols for the number of headerCols, skip this record
                     logMessage.FailNotes = logMessage.FailNotes + $"{rowCols[rowsAndHeaders.headerLookup[parms.headerFailColumn]]}: Skipped rowCol as had {rowCols.Length} rowCols and needed {rowsAndHeaders.headerLookup.Count()}";
                     parms.errorCount++;
                     continue;
                  }

                  string siteName = rowCols[rowsAndHeaders.headerLookup["Site"]].ToUpper().Trim();
                  int siteId = 0;

                  // Closed site - don't load
                  bool isClosedSite = closedSites.Contains(siteName);

                  if (isClosedSite)
                  {
                     continue;
                  }

                  Site site = sites.Where(x => x.Description.ToUpper() == siteName).FirstOrDefault();

                  if (site == null)
                  {
                     // If no match, then try the dictionary
                     if (!siteDescriptionDictionary.TryGetValue(siteName.ToUpper(), out var siteDictionary) || !siteDictionary.IsPrimarySiteId)
                     {
                        // logMessage.FailNotes = logMessage.FailNotes + $" failed on adding item, Item: {incomingProcessCount} Unable to find site: {siteName} \n";
                        parms.errorCount++;

                        if (siteName == "" || siteName == null)
                        {
                           siteName = "[BLANK NAME]";
                        }
                        if (!missingSitesDictionary.ContainsKey(siteName))
                        {
                           missingSitesDictionary[siteName] = 1;
                        }
                        else
                        {
                           missingSitesDictionary[siteName] += 1;
                        }

                        continue;
                     }
                     else
                     {
                        siteId = siteDictionary.SiteId;
                     }
                  }
                  else
                  {
                     siteId = site.Id;
                  }

                  string reg = rowCols[rowsAndHeaders.headerLookup["RegNo"]];
                  string vin = rowCols[rowsAndHeaders.headerLookup["VIN"]];

                  if (string.IsNullOrEmpty(reg) && string.IsNullOrEmpty(vin))
                  {
                     continue;
                  }

                  Model.Input.ReconcilingItem jardineWIP = new Model.Input.ReconcilingItem()
                  {
                     SiteId = siteId,
                     ReconcilingItemTypeId = 71,
                     Reg = reg?.Replace(" ", "") ?? "",
                     Vin = vin,
                     Description = rowCols[rowsAndHeaders.headerLookup["Description"]],
                     Reference = rowCols[rowsAndHeaders.headerLookup["Reference"]],
                     DealerGroupId = parms.dealerGroupId,
                     FileImportId = parms.fileImportId
                  };

                  incomingWIP.Add(jardineWIP);
               }

               catch (Exception err)
               {
                  logMessage.FailNotes = logMessage.FailNotes + $" failed on adding item, Item: {incomingProcessCount} {err.ToString()}";
                  parms.errorCount++;
                  continue;
               }
            }
         }

         missingSitesDictionary = missingSitesDictionary
               .OrderBy(kvp => kvp.Key) // Sort by siteName
               .ToDictionary(kvp => kvp.Key, kvp => kvp.Value);

         foreach (var item in missingSitesDictionary)
         {
            logMessage.FailNotes += $"Unable to find site: {item.Key} ({item.Value}) <br>";
         }

         DataTable result = incomingWIP.ToDataTable();
         result.Columns.Remove("Sites");
         result.Columns.Remove("FileImport");
         result.Columns.Remove("DealerGroup");
         result.Columns.Remove("ReconcilingItemType");
         result.Columns.Remove("SourceReports");
         return result;
      }


      private Dictionary<string, string> BuildHeaderDictionary()
      {
         Dictionary<string, string> headerDefinitions = new Dictionary<string, string>()
                {
                        { "Site",  "SITE"},
                        { "RegNo", "REGNO" },
                        { "VIN", "VIN" },
                        { "Description", "DESCRIPTION" },
                        { "Arrival_Date", "ARRIVAL_DATE" },
                        { "Reference", "REFERENCE" },
                };

         return headerDefinitions;
      }








   }
}
