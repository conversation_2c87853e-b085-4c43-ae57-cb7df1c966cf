import { Component, OnInit } from '@angular/core';
import { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { forkJoin } from 'rxjs';
import { BaseURLVM } from 'src/app/model/BaseURLVM';
import { ApiAccessService } from 'src/app/services/apiAccess.service';
import { ConstantsService } from 'src/app/services/constants.service';
import { GetDataService } from 'src/app/services/getData.service';
import { IconService } from 'src/app/services/icon.service';
import { MultiDealerGroupService } from 'src/app/services/multiDealerGroup.service';
import { ToastService } from 'src/app/services/newToast.service';
import { SelectionsService } from 'src/app/services/selections.service';

@Component({
  selector: 'app-forgotpassword',
  templateUrl: './forgotpassword.component.html',
  styleUrls: ['./forgotpassword.component.scss', './../../../styles/accountPage.scss']
})
export class ForgotpasswordComponent implements OnInit {

  forgotPasswordFormGroup = new UntypedFormGroup({
    email: new UntypedFormControl('', Validators.required),
  });

  forgotSuccess: boolean;
  disableSubmit: boolean = true;
  showMobileView: boolean;

  constructor(
    private constants: ConstantsService,
    private apiAccess: ApiAccessService,
    private router: Router,
    public icon: IconService,
    public multiDealerGroupService: MultiDealerGroupService,
    public selections: SelectionsService,
    public toastService: ToastService,
  ) { }

  ngOnInit(): void {
    this.showMobileView = window.innerWidth < 500;
  }

  Submit(): void{
    this.disableSubmit = true;
    if (this.forgotPasswordFormGroup.valid) {
      let e = this.forgotPasswordFormGroup.get('email').value;
      const f = { email: e};

      let requests = [];
      requests = this.multiDealerGroupService.createRequests(e);

      forkJoin(requests).subscribe((data: any) => {
        //add more country reults here
        const baseURLUK: BaseURLVM = data[0];
        const baseURLUS: BaseURLVM = data[1];

        //add more country checks here 
        this.multiDealerGroupService.checkandSetBaseURL(baseURLUK, baseURLUS)
       

        if (this.constants.isMultiDealerGroupAccount){
          this.setCountryAccountAndForgotPassword(f);
        }
        else {
          this.continueWithForgotPassword(f);
        }

      })
      
    }
  }

  setCountryAccountAndForgotPassword(f: any){
      
    let mySubscription = this.selections.dealerGroupSelectionModalEmitter.subscribe(res =>{
        if(res){
          this.continueWithForgotPassword(f);
        }
        mySubscription.unsubscribe();
    })

    this.constants.dealerGroupSelectionModal.showModal();
   
    }

    continueWithForgotPassword(f: any){
      this.apiAccess.post('account','Forgotpassword',f).subscribe((res:any) => {
        if (res?.error) {
          this.toastService.errorToast(res.error)
        }
        else {
        this.forgotSuccess = true;
        }
      },
      err => {
        this.forgotSuccess = true;
      });
      
    }

  RedirectToLogin(){
    this.router.navigateByUrl('/login');
  }

  validateEmail(event: any) {
    const emailRegex: RegExp = /^['a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  
    if (emailRegex.test(event.target.value)) {
      this.disableSubmit = false;
    } else {
      this.disableSubmit = true;
    }
  }

}
