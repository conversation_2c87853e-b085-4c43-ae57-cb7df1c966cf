import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Status } from 'src/app/model/Status';
import { StatusUpdate } from 'src/app/model/StatusUpdate';
import { StockCheck } from 'src/app/model/StockCheck';
import { ApiAccessService } from 'src/app/services/apiAccess.service';
import { ConstantsService } from 'src/app/services/constants.service';
import { ToastService } from 'src/app/services/newToast.service';
import { SelectionsService } from 'src/app/services/selections.service';
import { ConfirmModalComponent } from '../confirmModal/confirmModal.component';

@Component({
  selector: 'statusAndBarChart',
  templateUrl: './statusAndBarChart.component.html',
  styleUrls: ['./statusAndBarChart.component.scss']
})
export class StatusAndBarChartComponent implements OnInit {
  @ViewChild('confirmModal', { static: true }) confirmModal: ConfirmModalComponent;

  @Input() showStatusPicker: boolean;
  statusUpdate: StatusUpdate;
  statusRequested: Status;
  chosenNewStatus: Status;
  hideSaveCancelButtons: boolean;

  constructor(
    public selectionsService: SelectionsService,
    public constantsService: ConstantsService,
    public modalService: NgbModal,
    public apiAccess: ApiAccessService,
    public toastService: ToastService
  ) {

  }

  ngOnInit(): void {
    this.statusUpdate = {
      statusId: this.selectionsService.stockCheck.statusId,
      stockCheckId: this.selectionsService.stockCheck.id,
      images:[]
    } as StatusUpdate

    if (this.constantsService.strictTBStockCheckStatus) {
      this.showStatusPicker = false;
    }
  }

  chooseNewStatus(status: Status) {

    this.statusRequested = status;

    // ask to confirm if about to put to complete
    if (this.statusUpdate.statusId < 4 && status.id >= 4) {
      this.hideSaveCancelButtons = true;
      //are setting to complete, which can't be undone, confirm ok?
      this.confirmModal.confirmModalHeader = "This action can only be undone by a reviewer, continue?"

      this.modalService.open(this.confirmModal, { size: 'sm', keyboard: true, ariaLabelledBy: 'modal-basic-title' }).result.then((result) => {
        //'ok'
        this.chosenNewStatus = status
        this.statusUpdate.statusId = status.id;
        this.statusRequested = null;
        this.saveNewStatus();
      }, (reason) => {
        //cancel, don't do anything
        this.statusRequested = null;

      }
      );
    } else {
      //not set to completed, just change it
      this.hideSaveCancelButtons = false;
      this.statusUpdate.statusId = status.id;
      this.chosenNewStatus = status

    }
  }


  saveNewStatus() {
    this.apiAccess.post('StockChecks', 'UpdateStatus', this.statusUpdate).subscribe(res => {
      if (this.chosenNewStatus) {
        this.selectionsService.stockCheck.status = this.chosenNewStatus.description;
        this.selectionsService.stockCheck.statusId = this.chosenNewStatus.id;
      }

      if (this.selectionsService.stockCheck.statusId > 3) {
        if (!this.selectionsService.stockCheck.approvedByAccountant) {
          this.selectionsService.stockCheck.approvedByAccountant = this.selectionsService.usersName;
          this.selectionsService.stockCheck.reconciliationCompletedDate = new Date();
        }
      } else {
        this.selectionsService.stockCheck.approvedByAccountant = null;
        this.selectionsService.stockCheck.reconciliationCompletedDate = null;
      }

      if (this.selectionsService.stockCheck.statusId > 4) {
        this.selectionsService.stockCheck.approvedBy = this.selectionsService.usersName;
        this.selectionsService.stockCheck.reconciliationApprovedDate = new Date();
      } else {
        this.selectionsService.stockCheck.approvedBy = null;
        this.selectionsService.stockCheck.reconciliationApprovedDate = null;
      }

      if (this.selectionsService.stockCheck.statusId < 5) {
        this.selectionsService.stockCheck.approvedBy = null;
      }

      if (this.selectionsService.stockCheck.statusId < 4) {
        this.selectionsService.stockCheck.approvedByAccountant = null;
      }



      this.toastService.successToast('Updated stock check status')
    }, e => {
      //failed to update
      this.toastService.errorToast('Failed to update status');
    })

  }

  cancelNewStatus() {
    let stockCheckStatus = this.constantsService.Statuses.find(x => x.description == this.selectionsService.stockCheck.status);
    this.chosenNewStatus = stockCheckStatus;
    this.statusUpdate.statusId = stockCheckStatus.id;
  }

  getBarClass() {
    if (this.selectionsService.stockCheck?.percentageComplete >= 1 || this.selectionsService.stockCheck?.percentageComplete === 0) { return 'good'; }
    if (this.selectionsService.stockCheck?.percentageComplete >= .5) { return 'ok'; }
    return 'bad';
  }

  getPercentageComplete() {
    const stockCheck: StockCheck = this.selectionsService.stockCheck;
    if (stockCheck.scans === 0 && stockCheck.stockItemsCount === 0 && stockCheck.statusId > 2) {
      return 1;
    }
    return Math.floor(stockCheck.percentageComplete * 100) / 100;
  }

  getBarWidth() {
    const stockCheck: StockCheck = this.selectionsService.stockCheck;
    if (stockCheck.scans === 0 && stockCheck.stockItemsCount === 0 && stockCheck.statusId > 2) {
      return 100;
    }
    if (stockCheck.percentageComplete * 100 < 1) { return 0; }
    else { return stockCheck.percentageComplete * 100; }
  }
}
