import { Injectable, EventEmitter } from '@angular/core';
import jsPDF from 'jspdf';
import { CphPipe } from 'src/app/cph.pipe';
import { SiteVMWithSelected } from 'src/app/model/SiteVMWithSelected';
import { StockItem } from 'src/app/model/StockItem';
import { ApiAccessService } from 'src/app/services/apiAccess.service';
import { ToastService } from 'src/app/services/newToast.service';
import { ConstantsService } from '../../services/constants.service';
import { semi } from '../labelPrinter/IBMPlexMono-SemiBold-normal.js';
import { bold } from '../labelPrinter/IBMPlexMono-Bold-normal.js';
import { GridApi } from 'ag-grid-community';

export interface Label {
  vin: string;
  stockNumber: string;
  description: string;
  colour: string;
}

@Injectable({
  providedIn: 'root'
})

export class LabelPrinterService {
  selectedSite: SiteVMWithSelected;
  sitesCopy: SiteVMWithSelected[];
  searchString: string = '';
  rowData: StockItem[];
  selectedRows: StockItem[];
  labelsFromModal: Label[];
  showIframe: boolean;
  gridApi: GridApi;
  copies: number = 1;
  rowDataUpdatedEmitter: EventEmitter<void> = new EventEmitter<void>();

  constructor(
    public constantsService: ConstantsService,
    public toastService: ToastService,
    public apiAccessService: ApiAccessService,
    public cph: CphPipe
  ) { }

  initialise() {
    if (this.constantsService.Sites) {
      this.sitesCopy = JSON.parse(JSON.stringify(this.constantsService.Sites));
    } else {
      this.apiAccessService.get('Sites', 'All').subscribe((sites: SiteVMWithSelected[]) => {
        this.constantsService.Sites = sites;
        this.sitesCopy = JSON.parse(JSON.stringify(this.constantsService.Sites));
      })
    }
    this.toastService.destroyToast();
  }

  loadStockForSite(site: SiteVMWithSelected) {
    this.selectedSite = site;

    this.apiAccessService.get('StockItems', `GetStockItemsForSite?siteId=${site.id}`).subscribe((res: any[]) => {
      this.rowData = res;
      
      if (this.gridApi) {
        this.gridApi.setRowData(res);
        this.gridApi.redrawRows();
      }
    });
  }

  generateLabels(fromTableRows?: boolean) {
    const pdf: jsPDF = new jsPDF('l', 'mm', [60, 18]);

    pdf.addFileToVFS('IBMPlexMono-SemiBold-normal', semi);
    pdf.addFont('IBMPlexMono-SemiBold-normal', 'IBMPlexMono-SemiBold', 'normal');

    pdf.addFileToVFS('IBMPlexMono-Bold-normal', bold);
    pdf.addFont('IBMPlexMono-Bold-normal', 'IBMPlexMono-Bold', 'normal');

    let labels: Label[];
    if (fromTableRows) {
      labels = this.formatLabelsFromTableRows();
    } else {
      labels = this.labelsFromModal;
    }

    if (this.copies > 1) {
      let labelsMultiplied: Label[] = [];

      labels.forEach((label) => {
        for (var i = 0; i < this.copies; i++) {
          labelsMultiplied.push(label);
        }
      });

      labels = labelsMultiplied;
    }

    labels.forEach((row, i) => {
      this.savePrintLog(row.vin);

      if (i > 0) pdf.addPage();

      // Top left
      pdf.setFont('IBMPlexMono-Bold');
      pdf.setFontSize(11);
      pdf.text(row.stockNumber, 1, 4);

      // Top right
      let stockpulseLogo: HTMLImageElement = new Image();
      stockpulseLogo.src = 'assets/imgs/stockpulse-logo-long-printer.png';
      pdf.addImage(stockpulseLogo, 'png', 34.7, 1, 24, 4, null, 'NONE');

      // Left
      let cphLogo: HTMLImageElement = new Image();
      cphLogo.src = 'assets/imgs/cphi-logo-printer.png';
      pdf.addImage(cphLogo, 'png', 1.35, 5.5, 11, 11, null, 'NONE');

      // Middle
      pdf.setFont('IBMPlexMono-SemiBold');
      pdf.setFontSize(8);
      let topTextHeight = pdf.getTextDimensions(row.stockNumber, { fontSize: 11 });
      pdf.text(row.description, 14.4, topTextHeight.h + 4.25);

      let middleTextHeight = pdf.getTextDimensions(row.description);
      pdf.text(row.colour, 14.4, topTextHeight.h + middleTextHeight.h + 4.25);

      pdf.setFontSize(12);

      if (row.vin.length > 8) {
        const vinStart: string = row.vin.substring(0, row.vin.length - 8);
        const vinEnd: string = row.vin.substring(row.vin.length - 8);

        let vinEndWidth = pdf.getTextDimensions(vinEnd);
        pdf.text(vinStart, 58.6 - vinEndWidth.w - 1, 16, { align: 'right' });

        pdf.setFont('IBMPlexMono-Bold');
        pdf.text(vinEnd, 58.6, 16, { align: 'right' });
      } else {
        pdf.setFont('IBMPlexMono-Bold');
        pdf.text(row.vin, 58.6, 16, { align: 'right' });
      }

      if (i === labels.length - 1) {
        pdf.setProperties({ title: 'Labels' });
        const dataUrl: string = pdf.output('dataurlstring');
        const iframe: HTMLIFrameElement = document.createElement('iframe');

        iframe.src = dataUrl;
        iframe.width = '100%';
        iframe.height = '100%';

        this.showIframe = true;
        setTimeout(() => {
          document.getElementById('iframeContainer').appendChild(iframe);
        }, 250)
      }
    })
  }

  private formatLabelsFromTableRows() {
    let labels: Label[] = [];
    this.selectedRows.forEach((row, i) => {
      labels.push({
        vin: row.vin.substring(0, 17),
        stockNumber: row.reference.substring(0, 13),
        description: row.description.substring(0, 26).toUpperCase(),
        colour: row.comment.substring(0, 26).toUpperCase()
      })
    })
    return labels;
  }

  savePrintLog(vin: string) {
    this.apiAccessService.post('LabelPrinter', 'SavePrintLog', { vin: vin }).subscribe((res: any) => { });
  }
}
