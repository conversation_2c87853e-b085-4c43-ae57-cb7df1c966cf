import { Injectable } from '@angular/core';
import { HttpRequest, HttpHandler, HttpEvent, HttpInterceptor } from '@angular/common/http';
import { BehaviorSubject, Observable, of, throwError } from 'rxjs';
import { catchError, filter, finalize, switchMap, take } from 'rxjs/operators';

import { GetDataService } from './services/getData.service';
import { ConstantsService } from './services/constants.service';
import { Router } from '@angular/router';
import { AuthenticationService } from './services/authentication.service';
import { ToastService } from './services/newToast.service';
import { SelectionsService } from './services/selections.service';
import { MsalService } from '@azure/msal-angular';
import version from 'package.json'

@Injectable()
export class ErrorInterceptor implements HttpInterceptor {


    isRefreshingToken: boolean;
    tokenSubject: BehaviorSubject<string> = new BehaviorSubject<string>(null);


    
    constructor(
        public data: GetDataService,
        public constants: ConstantsService,
        public selections: SelectionsService,
        public router: Router,
        private authService:AuthenticationService,
        public toastService: ToastService,
        private msalService: MsalService,
        ) { }

    intercept(request: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
        return next.handle(request).pipe(catchError(err => {
            let error = err;
            
            if (this.router.url.startsWith('/resetpassword')){
                console.log('resetpassword - do nothing');
                
            }
            
            else if(request.url.includes('account/login')){
                //this.toastService.errorToast('Login error');
            }
            
            else if (err.status === 401) {
                return this.handleUnauthorized(request, next);
            }
            
            else if (error.status === 500 && (request.url.includes('api/whoami'))) {
                //console.error('No StockPulse account for ' + this.msalService.instance.getActiveAccount().username)
                this.toastService.userNotFoundToast(`No StockPulse account for ${this.msalService.instance.getActiveAccount().username}`)
              }
            
              else if (err.status === 400 && err.error.startsWith("Please refresh your browser")  ){
                this.toastService.errorToast(err.error);
                return of(false);
             }
          

            else if (err.status != 401){
                error = err.error || err.message || err.value ||  err.statusText;
            }
            return throwError(error);
        }))
    }





    //https://stackoverflow.com/questions/********/trying-to-repeat-a-http-request-after-refresh-token-with-a-interceptor-in-angula/********
    //https://www.intertech.com/Blog/angular-4-tutorial-handling-refresh-token-with-new-httpinterceptor/
    
    handleUnauthorized(req: HttpRequest<any>, next: HttpHandler): Observable<any> {
        if (!this.isRefreshingToken) {
            this.isRefreshingToken = true;

            // Reset here so that the following requests wait until the token
            // comes back from the refreshToken call.
            this.tokenSubject.next(null);
            // get a new token via userService.refreshToken
            return this.data.getNewAccessToken()
                .pipe(switchMap((tokenReponse: any) => {
                    // did we get a new token retry previous request
                    if (tokenReponse && tokenReponse.accessToken) {
                        this.tokenSubject.next(tokenReponse.accessToken);
                        localStorage.setItem('accessToken', tokenReponse.accessToken);
                        localStorage.setItem('refreshToken', tokenReponse.refreshToken.token);
                        this.constants.accessToken = tokenReponse.accessToken;
                        this.constants.refreshToken = tokenReponse.refreshToken.token;
                        return next.handle(this.addToken(req, tokenReponse.accessToken));
                    }

                    // If we don't get a new token, we are in trouble so logout.
                    this.authService.logout();
                    return// throwError('');
                })
                    , catchError(error => {
                        // If there is an exception calling 'refreshToken', bad news so logout.
                        this.authService.logout();
                        return throwError('');
                    })
                    , finalize(() => {
                        setTimeout(()=>{
                            this.isRefreshingToken = false; //limited understanding of RxJs.  Seems that without this debounce am requesting new token for every failed request.  SHouldn't see failed token within x seconds of last fail
                        },25000)
                    })
                );
        } else {
            return this.tokenSubject
                .pipe(
                    filter(token => token != null)
                    , take(1)
                    , switchMap(token => {
                        return next.handle(this.addToken(req, token));
                    })
                );
        }
    }


    addToken(req: HttpRequest<any>, token: string): HttpRequest<any> {
        return req.clone({ setHeaders: {
             Authorization: 'Bearer ' + token,
             WebVersion: version.version
            } })
    }
}