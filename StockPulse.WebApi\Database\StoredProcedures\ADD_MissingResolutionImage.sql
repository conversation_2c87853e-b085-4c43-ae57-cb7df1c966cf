﻿
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].[ADD_MissingResolutionImage]
(
	@FileName nvarchar(255),
    @StockCheckId INT,
	@UserId INT,
	@MissingResolutionId INT
)
AS
BEGIN

SET NOCOUNT ON;

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END

INSERT INTO [dbo].[MissingResolutionImages]
    ([MissingResolutionId],[FileName])
VALUES
    (@MissingResolutionId, @FileName)

	DECLARE @Id INT
	SET @Id = SCOPE_IDENTITY();

	EXEC [dbo].[UPDATE_StockCheckLastUpdate] @StockCheckId, @UserId

	SELECT @Id


END
	


GO


