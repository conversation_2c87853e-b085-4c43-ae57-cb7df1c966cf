﻿  
CREATE OR ALTER PROCEDURE [dbo].[UPDATE_ImportMask]  
(
@UserId int,  
@Id int,
@Name varchar(30) = NULL ,  
 
 @TopRowsToSkip int = NULL ,  
 @ColumnValueEqualsesJSON varchar(500) = NULL ,  
 @ColumnValueDifferentFromsJSON varchar(500) = NULL ,  
 @ColumnValueNotNullsJSON varchar(500) = NULL ,  
 @ColumnsWeWantJSON varchar(500) = NULL,
 @IsStandard bit = 0,
 @IsMultiSite bit = 0,
 @IgnoreZeroValues bit = 0   
)  
AS  
BEGIN  
  
SET NOCOUNT ON  

UPDATE ImportMasks
SET 
[Name] = @Name,
TopRowsToSkip = @TopRowsToSkip,
ColumnValueEqualsesJSON  = @ColumnValueEqualsesJSON,
ColumnValueDifferentFromsJSON = @ColumnValueDifferentFromsJSON,
ColumnValueNotNullsJSON = @ColumnValueNotNullsJSON,
ColumnsWeWantJSON = @ColumnsWeWantJSON,
UserId = @UserId,
IsStandard = @IsStandard,
IsMultiSite = @IsMultiSite,
IgnoreZeroValues = @IgnoreZeroValues

WHERE Id = @Id
  

END  

GO
  