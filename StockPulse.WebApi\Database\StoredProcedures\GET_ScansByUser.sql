﻿/****** Object:  StoredProcedure [dbo].[GET_ScansByUser]    Script Date: 10/3/21 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO



CREATE OR ALTER PROCEDURE [dbo].GET_ScansByUser
(
    @StockCheckId INT = NULL,
    @UserId INT = NULL	
)
AS
BEGIN

SET NOCOUNT ON

--Early return if no access
IF dbo.[AuthenticateUser](@UserId, @StockCheckId)   = 0 
BEGIN 
	RETURN SELECT 0
END


--Still here so continue  
	
SELECT [Id]
      ,[UserId]
      ,[StockCheckId]
      ,[LastEditedById]
      ,[LocationId]
      ,[LastEditedDateTime]
      ,[RegConfidence]
      ,[VinConfidence]
      --,[IsEdited]
      ,[Longitude]
      ,[Latitude]
      ,[ScanDateTime]
      ,[Comment]
      ,[Reg]
      ,[Vin]
      ,[Description]
      ,[CoordinatesJSON]
      ,[HasVinImage]
  FROM [dbo].[Scans] 
  WHERE StockCheckId = @StockCheckId
  AND UserId = @UserId

	
	

END
GO




--To use this run 
--exec [GET_Scans] @StockCheckId = 1