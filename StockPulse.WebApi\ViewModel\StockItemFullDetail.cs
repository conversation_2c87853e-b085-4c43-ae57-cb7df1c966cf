﻿using System;

namespace StockPulse.WebApi.ViewModel
{
    public class StockItemFullDetail : StockItem
    {
        //scan stuff
        public int? ScanId { get; set; }
        public string LocationDescription { get; set; }
        public string ScannerName { get; set; }
        public decimal? RegConfidence { get; set; }
        public decimal? VinConfidence { get; set; }
        public decimal? Longitude { get; set; }
        public decimal? Latitude { get; set; }
        public DateTime ScanDateTime { get; set; }
        public string ScanComment { get; set; }
        public string ScanReg { get; set; }
        public string ScanVin { get; set; }
        public string ScanDescription { get; set; }
        public bool? HasVinImage { get; set; }
        public ReconciliationState? ScanState { get; set; }
        public decimal? DistanceFromDealershipInMiles { get; set; }
        public string ScanSiteName { get; set; }


        //Reconciling item things
        public int ReconcilingItemId { get; set; }
        public int ReconcilingItemTypeId { get; set; }
        public string ReconcilingItemTypeDescription { get; set; }
        public string ReconcilingItemReg { get; set; }
        public string ReconcilingItemVin { get; set; }
        public string ReconcilingItemDesc { get; set; }
        public string ReconcilingItemComment { get; set; }
        public string ReconcilingItemRef { get; set; }



        //Resolution things
        public int ResolutionId { get; set; }
        public int ResolutionTypeId { get; set; }
        public string ResolutionTypeDescription { get; set; }
        public string ResolutionTypeBackup { get; set; }
        public bool IsResolved { get; set; }
        public string ResolvedBy { get; set; }
        public DateTime? ResolutionDate { get; set; }
        public string ResolutionNotes { get; set; }
        public string ResolutionImageIds { get; set; }



        //Matching Site name stuff
        public string OtherSiteName { get; set; }

        //Extra props if it is a duplicate
        public int OriginalId { get; set; }
        public string OriginalStockType { get; set; }
        public string OriginalComment { get; set; }
        public string OriginalReference { get; set; }

        public bool IsRegEditedOnDevice { get; set; }
	    public bool IsRegEditedOnWeb {  get; set; }
	    public string InterpretedReg { get; set; }
	    public bool IsVinEditedOnDevice { get; set; }
	    public bool IsVinEditedOnWeb { get; set; }
	    public string InterpretedVin {  get; set; }
        public int StockCheckId { get; set; }
    }
}
