﻿  
CREATE OR ALTER PROCEDURE [dbo].[GET_Scans]  
(  
    @StockCheckId INT = NULL,  
    @UserId INT = NULL  
)  
AS  
BEGIN  
   
SET NOCOUNT ON;    
  
DECLARE @isRegional INT;    
DECLARE @isTotal INT;    
DECLARE @StockCheckDate DateTime;  
DECLARE @DivisionId INT = NULL;  
DECLARE @DealerGroupId INT = NULL;  
DECLARE @SCId INT = NULL;  
  


--Early return if no access
IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0 
BEGIN 
	RETURN
END

  
SELECT   
@isRegional = IsRegional,   
@isTotal = IsTotal,   
@StockCheckDate = Date   
FROM StockChecks    
WHERE StockChecks.Id = @StockCheckId  
    


--Still here so continue  
IF @isRegional = 0 AND @isTotal = 0    
    
    BEGIN    
  
 SET @SCId = @StockCheckId;  
    
  
    END    
    
ELSE IF @isRegional = 1 AND @isTotal = 0    
    
    BEGIN    
    
    
    SET @DivisionId = (SELECT DivisionId FROM StockChecks    
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId    
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id    
                        WHERE StockChecks.Id = @StockCheckId AND StockChecks.IsRegional = 1)    
    
    END    
    
ELSE IF @isRegional = 0 AND @isTotal = 1    
    
    BEGIN    
    
        
    SET @DealerGroupId = (SELECT DealerGroupId FROM StockChecks    
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId    
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id    
                        WHERE StockChecks.Id = @StockCheckId)    
    
    
    END   
  
SELECT Scans.Id  
,Locations.[Description] AS locationDescription  
,Users.Name AS scannerName  
,Scans.[UserId]  
,[StockCheckId]  
,[LastEditedById]  
,[LocationId]  
,[UnknownResolutionId]  
,[StockItemId]  
,[ReconcilingItemId]  
,[LastEditedDateTime]  
,[RegConfidence]  
,[VinConfidence]  
--,[IsEdited]  
,Scans.[Longitude]  
,Scans.[Latitude]  
,[ScanDateTime]  
,[Comment]  
,[Reg]  
,[Vin]  
,Scans.[Description]  
,[CoordinatesJSON]  
,[HasVinImage]  
,[IsDuplicate]  
,Sites.[Description] AS SiteName  
FROM [dbo].[Scans]   
INNER JOIN Users ON Users.Id=Scans.UserId  
INNER JOIN Locations ON Locations.Id=Scans.LocationId  
INNER JOIN StockChecks AS SC ON SC.Id=Scans.StockCheckId  
INNER JOIN Sites ON Sites.Id=SC.SiteId  
INNER JOIN Divisions AS D ON D.Id=Sites.DivisionId  
INNER JOIN DealerGroup AS DG ON D.DealerGroupId=DG.Id  
WHERE   
--StockCheckId = @StockCheckId  
SC.Id = ISNULL(@SCId, SC.Id)  
AND SC.Date = @StockCheckDate  
AND D.Id = ISNULL(@DivisionId, D.Id)  
AND DG.Id = ISNULL(@DealerGroupId, DG.Id)  



END  
  
  
  
  
  