import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import {AuthGuard} from './auth.guard'
import { ForgotpasswordComponent } from './pages/forgotpassword/forgotpassword.component';
import { LoginComponent } from './pages/login/login.component';
import { ResetPasswordComponent } from './pages/resetPassword/resetPassword.component';
import { GlobalSearchComponent } from './pages/globalSearch/globalSearch.component';
import { HomeComponent } from './pages/home/<USER>';
import { StockChecksComponent } from './pages/stockChecks/stockChecks.component';
import { ReconcileComponent } from './pages/reconcile/reconcile.component';
import { ReconcilingItemsComponent } from './pages/loadItems/loadItems.component';
import { RepeatOffendersComponent } from './pages/repeatOffenders/repeatOffenders.component';
import { SignoffComponent } from './pages/signoff/signoff.component';
import { UserSetupComponent } from './pages/userSetup/userSetup.component';
import { VehiclesWithWrongRegComponent } from './pages/vehiclesWithWrongReg/vehiclesWithWrongReg.component';
import { PhotoMapComponent } from './pages/photoMap/photoMap.component';
import { LabelPrinterComponent } from './pages/labelPrinter/labelPrinter.component';
import { TableMaintenanceComponent } from './components/tableMaintenance/tableMaintenance.component';

const routes: Routes = [
  { path: '', component: HomeComponent, canActivate:[AuthGuard] },
  { path: 'home', component: HomeComponent,  },
  { path: 'stockChecks', component: StockChecksComponent, canActivate:[AuthGuard] },
  { path: 'signoff', component: SignoffComponent, canActivate:[AuthGuard], },
  { path: 'loadItems', component: ReconcilingItemsComponent, canActivate:[AuthGuard], },
  { path: 'reconcile', component: ReconcileComponent, canActivate:[AuthGuard], },
  { path: 'repeatOffenders', component: RepeatOffendersComponent, canActivate:[AuthGuard], },
  { path: 'vehicleSearch', component: GlobalSearchComponent, canActivate:[AuthGuard], },
  { path: 'vehiclesWithWrongReg', component: VehiclesWithWrongRegComponent, canActivate:[AuthGuard], },
  { path: 'userMaintenance', component: UserSetupComponent, canActivate:[AuthGuard], },
  { path: 'login' , component: LoginComponent },
  { path: 'forgotpassword' , component: ForgotpasswordComponent },
  { path: 'resetpassword' , component: ResetPasswordComponent },
  { path: 'photoMap', component: PhotoMapComponent, canActivate:[AuthGuard] },  
  { path: 'labelPrinter', component: LabelPrinterComponent, canActivate:[AuthGuard] },  

  { path: 'auth', component: HomeComponent, canActivate:[AuthGuard] },
  { path: 'tableMaintenance', component: TableMaintenanceComponent, canActivate:[AuthGuard] },
  //catch all
  {path:'**',redirectTo:''},
 

];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})
export class AppRoutingModule { 





}
