import { Component, OnInit,  Input, HostListener } from "@angular/core";
import { SelectionsService } from '../../services/selections.service';
import { CphPipe } from '../../cph.pipe';
import { RegPlateComponent } from '../../_cellRenderers/regPlate.component';
import { ChassisComponent } from '../../_cellRenderers/chassis.component';
import { CustomHeaderComponent } from '../../_cellRenderers/customHeader.component';
import { Router } from '@angular/router';
import { ScanNotesComponent } from "src/app/_cellRenderers/scanNotes.component";
import { RepeatMissingOffender } from "src/app/model/RepeatMissingOffender";
import { StockItem } from "src/app/model/StockItem";
import { RepeatOffendersService } from "./repeatOffenders.service";
import { GridOptions } from "ag-grid-community";
import { StockItemWithResolution } from "src/app/model/StockItemWithResolution";
import { ExcelExportService } from "src/app/services/excelExport";
import { LogoService } from "src/app/services/logo.service";
import { ToastService } from "src/app/services/newToast.service";
import { SheetToExtractOld } from "src/app/model/SheetToExtractOld";
import * as excelJS from 'exceljs';
import * as fs from 'file-saver';


@Component({
  selector: 'repeatMissingsTable',
  template:    `
    <div id="gridHolder">
    <button class="btn floatTopRightOfGrid" (click)="generateExcelSheet()">
      <img style="width:2em;" [src]="logo.provideExcelLogo()">
    </button>

    <ag-grid-angular 
    [frameworkComponents]="frameworkComponents"
     id="importTable" 
     class="ag-theme-balham" 
     [gridOptions]="mainTableGridOptions" 
     [rowData]="rowData"
    (gridReady)="onGridReady($event)">

    </ag-grid-angular> 
   
    </div>


    `
  ,
  styles: [
    `
    #gridHolder {
      position: relative;
      overflow-y: auto;
      overflow-x: hidden;
      width: 100%;
    }

    #counter{position:absolute;top:-2em;height:2em;right:0em;    background: var(--secondaryLighter);      padding: 0.2em 1em;      border-radius: 0.3em 0.3em 0 0;}
    h4{font-weight:700;}
    #importTable{height: 100%}
    .numberChip{position:fixed;top:60px;right:20px;}
  `
  ]
})


export class RepeatMissingsTableComponent implements OnInit {
  @Input() rowData: StockItemWithResolution[];

  @HostListener('window:resize', [])
  private onresize (event) {
    if (this.gridApi) this.gridApi.sizeColumnsToFit();
  }

  mainTableGridOptions: GridOptions;
  frameworkComponents: { agColumnHeader: any; };
  public gridApi; 
  

  constructor(
   public selections: SelectionsService,
   public cphPipe: CphPipe,
   public router: Router,
   private service: RepeatOffendersService,
   public logo: LogoService,
   public toastService: ToastService,
   private excelExportService: ExcelExportService,
  ) {

   

  }


  ngOnInit(): void {
    this.initParams()
    
  }
  

  initParams() {
    this.frameworkComponents = { agColumnHeader: CustomHeaderComponent };
    this.mainTableGridOptions = {
      domLayout: 'autoHeight',
      columnTypes: {
        "numberColumn":{ cellRenderer: (params) => { return this.cphPipe.transform(params.value, 'number', 0) } },
        "labelColumn": { cellClass: 'agAlignCentre', filter: 'agTextColumnFilter' },
        "date": { cellClass: 'agAlignCentre', cellRenderer:(params)=>this.cphPipe.transform(params.value,'date',0), filter: 'agTextColumnFilter' }
      },
      onGridReady:(params)=>this.onGridReady(params),
      onRowDoubleClicked:(params)=>this.rowClicked(params),

    
      columnDefs : [
        { headerName: 'Stock Check Date', field: 'stockCheckDate', type: 'date', sort: 'desc' },
        { headerName: "Reg", field: "reg", width: 100, cellRendererFramework: RegPlateComponent, autoHeight: false, type: 'labelColumn' },
        { headerName: "VIN", field: "vin", width: 100, cellRendererFramework: ChassisComponent, type: 'labelColumn' },
        { headerName: "Description", cellClass: 'description', field: "description", width: 250, type: "labelColumn" },
        { headerName: "Branch", sortable: true, field: "branch", width: 70, type: 'labelColumn' },
        { headerName: "Stock Type", sortable: true, field: "stockType", width: 100, type: 'labelColumn' },
        { headerName: "Notes", sortable: true, field: "comment", width: 200, type: 'labelColumn' },
        { headerName: "Resolution", sortable: true, field: "resolutionTypeDescription", cellRenderer:(params)=>params.value ? `<div class="cellContentBox">${params.value}</div>` : '', width: 200, type: 'labelColumn' },
        { headerName: "Resolution Detail", sortable: true, field: "resolutionNotes", cellRendererFramework: ScanNotesComponent, width: 625, type: 'labelColumn' },
        // { headerName: "Missing Vehicle Resolutions", autoHeight:true, sortable: true, cellRendererFramework: CarResolutionsListComponent, colId:'missing', width: 400, type: 'labelColumn' },
      ]
    }
  }
  
  onGridReady(params) {
    this.gridApi = params.api;
    this.gridApi.sizeColumnsToFit();    
  }

  
  rowClicked(row: {data: RepeatMissingOffender}){
    this.service.globalSearchService.reg = row.data.reg;
    this.service.globalSearchService.chassis = row.data.vin;
    this.service.globalSearchService.requireAndMatch = false;
    //this.service.globalSearchService.stageValue = 'missing';
    //this.service.globalSearchService.data = v.lastFourMissingInstances;
  
     this.router.navigateByUrl('/vehicleSearch');
  }

  generateExcelSheet() {
    this.toastService.loadingToast('Generating Excel file...');

    let rowsToInclude: any[] = [];

    this.gridApi.forEachNodeAfterFilter(n => {
      if (!!n.data) {
        let stockCheckDate  = new Date(n.data.stockCheckDate).getDate() + ' ' + new Date(n.data.stockCheckDate).toLocaleString('en-gb', { month: 'short' }) + ' ' + new Date(n.data.stockCheckDate).getFullYear();

        rowsToInclude.push({
          StockCheckDate: stockCheckDate,
          Reg: n.data.reg,
          VIN: n.data.vin,
          Description: n.data.description,
          Branch: n.data.branch,
          StockType: n.data.stockType,
          Comment: n.data.comment,
          Resolution: n.data.resolutionTypeDescription,
          Notes: n.data.resolutionNotes,
        });
      }
    })

    let sheet = {
      tableData: rowsToInclude,
      tableName: `Repeat Offenders - ${rowsToInclude.length}`,
      columnWidths: rowsToInclude.length > 0 ? this.excelExportService.workoutColWidths(rowsToInclude) : []
    }

    this.downloadExcelFile(sheet);
    
  }

  
  downloadExcelFile(sheet: SheetToExtractOld) {
    let workbook = new excelJS.Workbook();

    try {
      //define worksheet
      let worksheet = workbook.addWorksheet(sheet.tableName)

      //generic stuff for worksheet
      worksheet.views = [
        { state: 'frozen', xSplit: 1, ySplit: 5, zoomScale: 85 }
      ];

      //columns things
      let columns = []

      sheet.columnWidths.forEach(w => {
        columns.push({ width: w })
      })

      worksheet.columns = columns;


      //rows
      let titleRow = worksheet.addRow([sheet.tableName])//title
      titleRow.font = { name: 'Calibri', family: 4, size: 16, bold: true, color: 'white', fill: { bgColor: { rgb: '220,230,241' } } };

      let subtitle = 'Extracted ' + this.cphPipe.transform(new Date(), 'time', 0) + ' ' + this.cphPipe.transform(new Date(), 'date', 0) + ' ' + ' by ' + this.selections.usersName;
      let subTitleRow = worksheet.addRow([subtitle])//title
      subTitleRow.font = { name: 'Calibri', family: 4, size: 12, bold: false, italic: true, color: 'white', fill: { bgColor: { rgb: '220,230,241' } } };

      worksheet.addRow([]);
      worksheet.addRow([]);

      worksheet.getRow(1).height = 28;

      //the table headerRow 
      let columnHeadersRaw: string[] = Object.keys(sheet.tableData[0]);
      let columnHeadersFormatted: string[] = [];

      columnHeadersRaw.forEach(element => {
        let temp = element.replace('Vehicles', '').match(/[A-Z]+(?![a-z])|[A-Z]?[a-z]+|\d+/g).join(' ').trim();
        columnHeadersFormatted.push(temp);
      });

      let columnHeadersFinal: string[] = [];

      columnHeadersFormatted.forEach(element => {
         let temp = element.trim();
         columnHeadersFinal.push(temp);
       });

      worksheet.addRow(columnHeadersFinal);
      let colCount = columnHeadersFinal.length;

      //loop through each column in active range and colour cells
      for (let i = 0; i < colCount; i++) {
        let colLetter = String.fromCharCode(65 + i)
        worksheet.getCell(colLetter + '4').font = { name: 'Calibri', family: 4, size: 11, color: { argb: 'FFFFFFFF' } }
        worksheet.getCell(colLetter + '4').fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF252424' } }
        worksheet.getCell(colLetter + '5').font = { name: 'Calibri', family: 4, size: 11, color: { argb: 'FFFFFFFF' } }
        worksheet.getCell(colLetter + '5').fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF252424' } }
      }

      //the data rows
      sheet.tableData.forEach(x => {
        let values = Object.values(x);
        const row = worksheet.addRow(values)

      })

      //loop through the first cell of each row and colour
      let rowCount = worksheet.rowCount + 1;


      // This goes down rows
      for (let i = 6; i < rowCount; i++) {

        // If 0, show dash
        for (let j = 6; j < 13; j++) {
          let colLetter = String.fromCharCode(65 + j);
          worksheet.getCell(colLetter + i.toString()).numFmt = '#,##0;-#,##0;-';
        }

      }

      
    }
    catch (e) {
      //carry on
    }

    let workbookName = 'StockPulse Extract ' + new Date().getDate() + new Date().toLocaleString('en-gb', { month: 'short' }) + new Date().getFullYear();

    workbook.xlsx.writeBuffer().then((data) => {
      let blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      fs.saveAs(blob, workbookName + '.xlsx');
    });

    this.toastService.destroyToast();
  }


   
 

}


