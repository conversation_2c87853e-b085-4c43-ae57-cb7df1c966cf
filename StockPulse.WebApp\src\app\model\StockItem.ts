// -------------------------------------------------------------------------
//  StockItems
// -------------------------------------------------------------------------

import { ReconciliationState } from "./ReconciliationState";

export interface StockItem {

  reg: string;
  stockItemId: number | null;
  vin: string;
  description: string;
  dis: number;
  groupDIS: number;
  branch: string;
  stockType: string;
  comment: string;
  reference: string;
  stockValue: number;
  site: string;
  stockCheckId: number;
  state: ReconciliationState;
  isAgencyStock: boolean; 
  hasBeenScanned?: number;
  scanId?: number;
  regImageLargeUrl?: string;
  regImageThumbnailUrl?: string;
  flooring: number;
  fileName?: string;
  fileDate?: Date;
  loadDate?: Date;
  userName?: string;
}


