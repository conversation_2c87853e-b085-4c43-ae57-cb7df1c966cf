# To prune stale remote branches: git remote prune origin
# PowerShell script to delete all branches merged into master/main

# Set the default branch name (e.g., master or main)
$DefaultBranch = "master"

# Ensure the user is on the default branch
$CurrentBranch = git branch --show-current
if ($CurrentBranch -ne $DefaultBranch) {
    Write-Host "Error: You are not on the $DefaultBranch branch. Please switch to $DefaultBranch and try again." -ForegroundColor Red
    exit 1
}


# Step 1: Delete local branches merged into the default branch
Write-Host "Deleting local branches merged into $DefaultBranch..."
git branch --merged $DefaultBranch | ForEach-Object {
    $BranchName = $_.Trim()  # Trim whitespace from the branch name
    if ($BranchName -ne $DefaultBranch) {
        git branch -d $BranchName
    }
}

# Step 2: Delete remote branches merged into the default branch
Write-Host "Deleting remote branches merged into $DefaultBranch..."
git branch -r --merged $DefaultBranch | ForEach-Object {
    $BranchName = $_.Trim() -replace "origin/", ""  # Trim whitespace and remove 'origin/' prefix
    if ($BranchName -ne $DefaultBranch) {
        git push origin --delete $BranchName
    }
}

Write-Host "Done. All merged branches have been deleted locally and remotely." -ForegroundColor Green
