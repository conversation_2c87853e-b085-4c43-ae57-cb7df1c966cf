import { EventEmitter, Injectable } from '@angular/core';
import { SiteNameLookup } from 'src/app/model/SiteNameLookup';
import { Changes } from "src/app/model/UpdateSiteNameLookupsParams";

@Injectable({
  providedIn: 'root'
})
export class SiteNameLookupsModalService {
  editing: boolean = false;
  siteNameLookups: SiteNameLookup[];
  siteNameLookupsCopy: SiteNameLookup[];
  changes: Changes[] = [];
  newSiteNameLookupEmitter: EventEmitter<SiteNameLookup> = new EventEmitter<SiteNameLookup>();

  constructor() {}
}
