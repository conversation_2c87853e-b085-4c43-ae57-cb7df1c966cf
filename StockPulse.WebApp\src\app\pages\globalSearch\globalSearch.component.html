<nav class="page-specific-navbar">
  <div class="page-title">
    
  </div>

  <div id="chooseVehicle">

    <!-- Reg choice -->
    <input *ngIf="showReg()" class="regPlate inNav" placeholder="Reg" autofocus [ngModel]="service.reg"
      (ngModelChange)="service.reg = $event.toUpperCase()" (keyup.enter)="chooseRegAndChassis()" />

    <!-- Chassis choice -->
    <input class="chassis inNav" placeholder="Chassis" [ngModel]="service.chassis"
      (ngModelChange)="service.chassis = $event.toUpperCase()" (keyup.enter)="chooseRegAndChassis()" />

  </div>

  <!-- Require both choice -->

  <sliderSwitch *ngIf="showReg()" [defaultValue]="service.requireAndMatch" [text]="'Require match on both'"
    (toggle)="toggleRequireAndMatch()">
  </sliderSwitch>

  <button id="searchButton" class="btn btn-success" (click)="chooseRegAndChassis()">Search</button>
</nav>

<div class="content-new">

  <instructionRow
    [message]="'Enter a full Reg without spaces or full 8 character VIN in the boxes above to search through all stockchecks at all sites for a vehicle.  Searching using just a Reg or just a VIN may provide more matches than searching on both at once.'">
  </instructionRow>

  <div class="stockCheckHolder">
    <div *ngFor="let resultItem of service.resultItems" class="cph-card vehicleCard">

      <div (dblclick)="loadItem(resultItem)" class="cph-card globalSearchCard vehicle-modal-card">
        <div class="cph-card-header">
          <span class="cph-card-header-title">{{resultItem.stockCheckSiteName}}
            {{resultItem.stockCheckDate|cph:'date':0}}</span>
        </div>

        <div class="cph-card-body">

          <vehicleModalBodyNew [showCompact]="true" [item]="resultItem.item"></vehicleModalBodyNew>

        </div>
      </div>





    </div>
  </div>
</div>