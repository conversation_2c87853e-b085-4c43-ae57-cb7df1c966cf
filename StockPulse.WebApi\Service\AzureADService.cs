﻿//using Azure.Identity;
//using System.Threading.Tasks;
//using Microsoft.Graph;
//using Microsoft.Graph.Models;

//namespace StockPulse.WebApi.Service
//{
//    public interface IAzureADService
//    {
//        Task<string> CreateUser(string name, string email);
//        Task DeleteUser(string emailAddress);
//    }
//    public class AzureADService: IAzureADService
//    {
//        public async Task DeleteUser(string emailAddress)
//        {
//            GraphServiceClient graphClientUserRead = GetGraphClient();

//            var userToDelete = await graphClientUserRead.Users.
//            .Request()
//            .Filter($"mail eq '{emailAddress}'")
//            .GetAsync();

//            var userId = userToDelete.CurrentPage[0].Id;

//            await graphClientUserRead.Users[$"{userId}"]
//                .Request()
//                .DeleteAsync();
//        }

//        public async Task<string> CreateUser(string name, string email)
//        {
//            string userPrincipalName = $"{email.Replace('+', '_').Replace('@','_')}#EXT#@stockpulsesso.onmicrosoft.com";
//            User newUser = new User
//            {
//                //Id = user.Id,
//                //BusinessPhones = someUser.BusinessPhones,
//                DisplayName = name,
//                GivenName = name,
//                //JobTitle = someUser.JobTitle,
//                Mail = email,
//                //MobilePhone = someUser.MobilePhone,
//                //OfficeLocation = someUser.OfficeLocation,
//                //PreferredLanguage = someUser.PreferredLanguage,
//                //Surname = someUser.Surname,
//                UserPrincipalName = userPrincipalName
//            };
//            var invitation = new Invitation
//            {
//                InvitedUser = newUser,  
//                InvitedUserEmailAddress = email,
//                InviteRedirectUrl = "https://stockpulse.cphi.co.uk/",
//                //SendInvitationMessage = true,
//                InvitedUserDisplayName= name,
//            };


//            GraphServiceClient graphClient = GetGraphClient();

//            var invitationResult = await graphClient.Invitations
//                .Request()
//                .AddAsync(invitation);

//            var invitationURL = invitationResult.InviteRedeemUrl;

//            return invitationURL;
//        }

//        private GraphServiceClient GetGraphClient()
//        {
//            /* 
//            var defaultAzureCredentialOptions = new DefaultAzureCredentialOptions()
//            {
//                ExcludeAzureCliCredential = true,
//                ExcludeAzurePowerShellCredential = true,
//                ExcludeEnvironmentCredential = true,
//                ExcludeInteractiveBrowserCredential = true,
//                ExcludeManagedIdentityCredential = true,
//                ExcludeSharedTokenCacheCredential = true,
//                ExcludeVisualStudioCodeCredential = true,
//                ExcludeVisualStudioCredential = true,
//                TenantId = "7c646534-1708-41c7-a4b2-a981495b4ebe"
//            };

//            defaultAzureCredentialOptions.ExcludeAzurePowerShellCredential = false;

//            var xx = new DefaultAzureCredential(defaultAzureCredentialOptions);

//            return new GraphServiceClient(xx);
//            */
//            return new GraphServiceClient(new DefaultAzureCredential(new DefaultAzureCredentialOptions() { TenantId = "7c646534-1708-41c7-a4b2-a981495b4ebe" }));

//        }


//    }
//}
