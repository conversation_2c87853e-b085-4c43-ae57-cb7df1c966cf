﻿--1. Find the dependent SPs for this type via SSMS Object Explorer (right click -> View dependencies)
--2. Drop the dependent SPs
--3. Drop the type
--4. Recreate the type
--5. Recreate the dependent SPs


--Deleting Dependent SPs
DROP PROC dbo.[BULKADD_StockItems]
GO

--Deleting Type
DROP TYPE [dbo].[StockItemType]
GO


--Creating Type
CREATE TYPE [dbo].[StockItemType] AS TABLE(
	[ScanId] [int] NULL,
	[ReconcilingItemId] [int] NULL,
	[MissingResolutionId] [int] NULL,
	[StockCheckId] [int] NULL,
	[SourceReportId] [int] NULL,
	[Reg] [nvarchar](50) NULL,
	[Vin] [nvarchar](50) NULL,
	[Description] [nvarchar](250) NULL,
	[DIS] [int] NULL,
	[GroupDIS] [int] NULL,
	[Branch] [nvarchar](50) NULL,
	[Comment] [nvarchar](500) NULL,
	[StockType] [nvarchar](50) NULL,
	[Reference] [nvarchar](50) NULL,
	[StockValue] [decimal](18, 3) NULL,
	[Flooring] [decimal](18, 3) NULL,
	[IsAgencyStock] [bit],
	[FileImportId] [int]

)
GO

--Create the dependent SPs 
--(best would be to run all_sps.sql)


