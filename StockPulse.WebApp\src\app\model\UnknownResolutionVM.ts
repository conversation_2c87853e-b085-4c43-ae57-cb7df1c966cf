import { ImageToUpdate } from "./ImageToUpdate";


export interface UnknownResolutionVM {
  id: number;
  stockCheckId: number;
  scanId: number;
  OriginalItemId: number;

  resolutionTypeId: number | null;
  resolutionTypeDescription: string;


  resolutionDateTime: Date | string;
  //userId: number;
  usersName: string;

  notes: string;

  isResolved: boolean;
  images: Array<ImageToUpdate>;

  requiredBackup?: string;
}
