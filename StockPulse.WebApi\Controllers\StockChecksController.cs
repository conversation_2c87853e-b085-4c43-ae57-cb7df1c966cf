﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using StockPulse.Model;
using StockPulse.WebApi.ViewModel;
using StockPulse.WebApi.Service;
using System.Collections.Generic;
using System.Threading.Tasks;
using StockPulse.WebApi.Attribute;
using System;
using static StockPulse.WebApi.Attribute.UserLevelAccessAttribute;

namespace StockPulse.WebApi.Controllers
{
   [Route("api/[controller]")]
   [ApiController]
   [Authorize]
   [UserLevelAccess(new[] { UserRole.SysAdministrator, UserRole.Reconciler, UserRole.Resolver, UserRole.Scanner, UserRole.Approver, UserRole.GeneralManager, UserRole.ReadOnly, UserRole.ScanAndPrint, UserRole.ScanAndView })]
   public class StockChecksController : ControllerBase, IAttributeValueProvider
   {
      private readonly int userId;
      private readonly IStockCheckService stockCheckService;
      private readonly IStockCheckReconciliationService stockCheckReconciliationService;
      private readonly ISiteService siteService;
      private readonly IUserService userService;
      private readonly string userRole;

      public UserRole attributeUserRole => GetUserRole();
      private UserRole GetUserRole()
      {
         return (UserRole)Enum.Parse(typeof(UserRole), userRole);
      }


      //constructor
      public StockChecksController(IStockCheckService stockCheckService, ISiteService siteService, IUserService userService, IStockCheckReconciliationService stockCheckReconciliationService)
      {
         this.stockCheckService = stockCheckService;
         this.siteService = siteService;
         this.userService = userService;

         this.userId = userService.GetUserId();
         this.userRole = userService.GetUserRole();
         this.stockCheckReconciliationService = stockCheckReconciliationService;
      }



      [HttpGet]
      [UserLevelAccess(new[] { UserRole.SysAdministrator, UserRole.Reconciler, UserRole.Resolver, UserRole.Approver, UserRole.Scanner, UserRole.GeneralManager, UserRole.ReadOnly, UserRole.ScanAndPrint, UserRole.ScanAndView })]
      [Route("GetStockChecks")]
      public async Task<IEnumerable<StockCheckVM>> GetStockChecks(bool? isActive, DateTime? fromDate, DateTime? toDate, int? stockCheckId)
      {
         bool isActiveToUse = isActive == null ? true : (bool)isActive;
         return await stockCheckService.GetStockChecks(userId, isActiveToUse, fromDate, toDate, stockCheckId);
      }

      [HttpGet]
      [UserLevelAccess(new[] { UserRole.SysAdministrator, UserRole.Reconciler, UserRole.Resolver, UserRole.Approver, UserRole.GeneralManager, UserRole.ReadOnly, UserRole.ScanAndView })]
      [Route("GetStockChecksForSite")]
      public async Task<IEnumerable<StockCheckVM>> GetStockChecksForSite(int stockCheckId)
      {
         return await stockCheckService.GetStockChecksForSite(stockCheckId, userId);
      }


      //[HttpGet]
      //// Gets Sign-Off 
      //// Redundant now really
      //public async Task<IEnumerable<string>> GetStockCheckSignOffImage(int stockcheckId)
      //{
      //    return await stockCheckService.GetStockCheckSignOffImage(stockcheckId);
      //}


      [HttpPost]
      [UserLevelAccess(new[] { UserRole.SysAdministrator, UserRole.Reconciler, UserRole.Resolver, UserRole.Approver, UserRole.GeneralManager, UserRole.ReadOnly, UserRole.ScanAndView })]
      [Route("GetWaterfallBarDetail")]
      public async Task<IEnumerable<WaterfallBarDetailItem>> GetWaterfallBarDetail(WaterfallBarDetailParams parms)
      {
         return await stockCheckService.GetWaterfallBarDetail(parms, userId);
      }


      [HttpGet]
      [UserLevelAccess(new[] { UserRole.SysAdministrator, UserRole.Reconciler, UserRole.Resolver, UserRole.Approver, UserRole.GeneralManager, UserRole.ReadOnly, UserRole.ScanAndView })]
      [Route("GetAllWaterfallBarDetail")]
      public async Task<IEnumerable<WaterfallBarSet>> GetAllWaterfallBarDetail(int stockCheckId)
      {
         return await stockCheckService.GetAllWaterfallBarDetail(stockCheckId, userId);
      }

      [HttpPost]
      [UserLevelAccess(new[] { UserRole.SysAdministrator, UserRole.Reconciler, UserRole.Resolver, UserRole.Approver })]
      [Route("ImportLatestData")]
      public async Task<IActionResult> ImportLatestData(ImportLatestDataParams parms)
      {
         try
         {
            int dealerGroupId = await userService.GetDealerGroupIdDirectFromDb(userId);
            await stockCheckService.ImportLatestData(parms, userId, dealerGroupId);
            return Ok();
         }
         catch (Exception ex)
         {
            return UnprocessableEntity(ex.Message);
         }
      }

      [HttpGet]
      [UserLevelAccess(new[] { UserRole.SysAdministrator, UserRole.Reconciler, UserRole.Resolver, UserRole.Approver, UserRole.GeneralManager, UserRole.ReadOnly, UserRole.ScanAndView })]
      [Route("ReconciliationBuckets")]
      public async Task<IEnumerable<ReconciliationBucket>> GetReconciliationBuckets(int stockcheckId)
      {
         return await stockCheckService.GetReconciliationBuckets(stockcheckId, userId);
      }

      [HttpGet]
      [UserLevelAccess(new[] { UserRole.SysAdministrator, UserRole.Reconciler, UserRole.Resolver, UserRole.Approver, UserRole.GeneralManager, UserRole.ReadOnly, UserRole.ScanAndView })]
      [Route("GetLatestDataRecievedDate")]
      public async Task<DateTime> GetLatestDataRecievedDate()
      {
         int dealerGroupId = await userService.GetDealerGroupIdDirectFromDb(userId);
         return await stockCheckService.GetLatestDataRecievedDate(dealerGroupId);
      }


      [HttpGet]
      [UserLevelAccess(new[] { UserRole.SysAdministrator, UserRole.Reconciler, UserRole.Resolver, UserRole.Approver, UserRole.GeneralManager, UserRole.ReadOnly, UserRole.ScanAndView })]
      [Route("ResolutionBuckets")]
      public async Task<IEnumerable<ResolutionBucket>> GetResolutionBuckets(int stockcheckId)
      {
         return await stockCheckService.GetResolutionBuckets(stockcheckId, userId);
      }

      [HttpGet]
      [UserLevelAccess(new[] { UserRole.SysAdministrator, UserRole.Reconciler, UserRole.Resolver, UserRole.Approver, UserRole.GeneralManager, UserRole.ReadOnly, UserRole.ScanAndView })]
      [Route("StockCheckLocations")]
      public async Task<IEnumerable<Location>> GetStockCheckLocations(int stockCheckId)
      {
         return await siteService.GetLocationsForStockCheck(stockCheckId, userId);
      }

      [HttpGet]
      [Route("GetStockCheckMobileApp")]
      public async Task<StockCheckMobileApp> GetStockCheckMobileApp(int stockCheckId)
      {
         return await stockCheckService.GetStockCheckMobileApp(stockCheckId, userId);
      }


      [HttpPost]
      [UserLevelAccess(new[] { UserRole.SysAdministrator, UserRole.Reconciler, UserRole.Resolver, UserRole.Approver })]
      [Route("Create")]
      public async Task CreateStockCheck(CreateStockCheckParams parms)
      {
         await stockCheckService.CreateStockCheck(parms, userId);
      }

      [HttpPost]
      [UserLevelAccess(new[] { UserRole.SysAdministrator })]
      [Route("CreateStockChecksForAllSites")]
      public async Task CreateStockChecksForAllSites(CreateAllStockCheckParams parms)
      {
         int dealerGroupId = await userService.GetDealerGroupIdDirectFromDb(userId);
         await stockCheckService.CreateStockChecksForAllSites(parms.Date, userId, dealerGroupId);
      }


      [HttpPost]
      [UserLevelAccess(new[] { UserRole.SysAdministrator, UserRole.Reconciler, UserRole.Resolver, UserRole.Approver, UserRole.GeneralManager })]
      [Route("UpdateStatus")]
      public async Task UpdateStatus(StatusUpdate updateStatus)
      {
         await stockCheckService.UpdateStatus(updateStatus, userId);
      }

      [HttpPost]
      [UserLevelAccess(new[] { UserRole.SysAdministrator, UserRole.Reconciler, UserRole.Resolver, UserRole.Approver })]
      [Route("AddSignoffPicture")]
      public async Task<IActionResult> AddSignoffPicture(SignoffPictureSubmission pictureDetails)
      {
         var result = await stockCheckService.AddSignoffPicture(pictureDetails, userId);

         if (result)
         {
            return Ok();
         }
         else
         {
            return BadRequest("Failed to save");
         }
      }


      [HttpGet]
      [UserLevelAccess(new[] { UserRole.SysAdministrator, UserRole.Reconciler, UserRole.Resolver, UserRole.Approver, UserRole.GeneralManager, UserRole.ReadOnly, UserRole.ScanAndView })]
      [Route("ReconcileStockCheck")]
      public async Task ReconcileStockCheck(int stockcheckId)
      {
         await stockCheckReconciliationService.ReconcileStockCheck(stockcheckId, userId);
      }


      [HttpPost]
      [UserLevelAccess(new[] { UserRole.SysAdministrator, UserRole.Reconciler, UserRole.Resolver, UserRole.Approver, UserRole.GeneralManager, UserRole.ReadOnly, UserRole.ScanAndView })]
      [Route("GetPhotoMapItemGroups")]
      public async Task<IEnumerable<PhotoMapItemGroup>> GetPhotoMapItemGroups(GetPhotoMapItemGroupsParms parms)
      {
         return await stockCheckService.GetPhotoMapItemGroups(parms.StockCheckId, parms.ReportType, userId);
      }


      [HttpPost]
      [UserLevelAccess(new[] { UserRole.SysAdministrator, UserRole.Reconciler, UserRole.Resolver, UserRole.Approver })]
      [Route("Archive")]
      public async Task Archive(List<int> stockCheckIds)
      {
         await stockCheckService.Archive(stockCheckIds, userId);
      }

      [HttpPost]
      [UserLevelAccess(new[] { UserRole.SysAdministrator, UserRole.Reconciler, UserRole.Resolver, UserRole.Approver })]
      [Route("UnArchive")]
      public async Task UnArchive(List<int> stockCheckIds)
      {
         await stockCheckService.UnArchive(stockCheckIds, userId);
      }

      [HttpPost]
      [UserLevelAccess(new[] { UserRole.SysAdministrator, UserRole.Reconciler, UserRole.Resolver, UserRole.Approver })]
      [Route("Delete")]
      public async Task<IActionResult> Delete(List<int> stockCheckIds)
      {
         string result = await stockCheckService.Delete(stockCheckIds, userId);

         if (result == "Error")
         {
            return BadRequest();
         }
         else
         {
            return Ok();
         }
      }

      [HttpPost]
      [UserLevelAccess(new[] { UserRole.SysAdministrator, UserRole.Reconciler, UserRole.Resolver, UserRole.Approver })]
      [Route("CheckIfAlreadyExists")]
      public async Task<IEnumerable<int>> CheckIfAlreadyExists(StockCheckExistsParams parms)
      {
         return await stockCheckService.CheckIfAlreadyExists(parms.StockCheckDate, parms.SiteIds);
      }

      [HttpGet]
      [Route("GetStockCheckIdsForUserDealerGroup")]
      public async Task<IEnumerable<int>> GetStockCheckIdsForUserDealerGroup()
      {
         return await stockCheckService.GetStockCheckIdsForUserDealerGroup(userId);
      }
   }
}
