﻿using Microsoft.AspNetCore.Components.Forms;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using StockPulse.Model;
using System.Linq;

namespace StockPulse.Repository.Database
{
    public class StockpulseContext : DbContext
    {
        private readonly bool _useAlternativeConnection;

        public StockpulseContext(bool useAlternativeConnection = false)
        {
            _useAlternativeConnection = useAlternativeConnection;
        }

        public StockpulseContext(DbContextOptions<StockpulseContext> options)
           : base(options)
        {

        }

        public static IConfiguration Configuration { get; set; }

        public DbSet<GlobalParam> GlobalParams { get; set; }
        public DbSet<Site> Sites { get; set; }
        public DbSet<User> Users { get; set; }
        public DbSet<Division> Divisions { get; set; }
        public DbSet<UserSite> UserSites { get; set; }

        // No foreign keys
        public DbSet<Status> Statuses { get; set; }
        public DbSet<Location> Locations { get; set; }
        public DbSet<ReconcilingItemType> ReconcilingItemTypes { get; set; }
        public DbSet<ResolutionType> ResolutionTypes { get; set; }
        public DbSet<RegPicture> RegPictures { get; set; }
        public DbSet<RegScanStat> RegScanStats { get; set; }


        // x1 foreign keys
        public DbSet<Model.ReconcilingItem> ReconcilingItems { get; set; }

        public DbSet<SourceReport> SourceReports { get; set; }

        public DbSet<ImportMask> ImportMasks { get; set; }

        public DbSet<StockCheck> StockChecks { get; set; }

        public DbSet<Scan> Scans { get; set; }

        public DbSet<UnknownResolution> UnknownResolutions { get; set; }

        public DbSet<MissingResolution> MissingResolutions { get; set; }

        public DbSet<MissingResolutionImage> MissingResolutionImages { get; set; }

        public DbSet<UnknownResolutionImage> UnknownResolutionImages { get; set; }

        public DbSet<Model.FinancialLine> FinancialLines { get; set; }

        public DbSet<Model.StockItem> StockItems { get; set; }

        public DbSet<SiteLocation> SiteLocations { get; set; }
        public DbSet<ErrorReport> ErrorReports { get; set; }
        public DbSet<LogMessage> LogMessages { get; set; }
        public DbSet<ReconcilingItemBackup> ReconcilingItemBackups { get; set; }

        // New Import Approach
        public DbSet<Model.Import.FileImport> FileImports { get; set; }


        // Import Schema
        
        public DbSet<Model.Import.SiteDescriptionDictionary> SiteDescriptionDictionary { get; set; }


        public DbSet<Model.Input.FinancialLine> InputFinancialLines { get; set; }
        public DbSet<Model.Input.ReconcilingItem> InputReconcilingItems { get; set; }
        public DbSet<Model.Input.StockItem> InputStockItems { get; set; }

        public DbSet<PrintLog> PrintLogs { get; set; }

        public DbSet<StatusChangeLogItem> StatusChangeLogItems { get; set; }
        public DbSet<MaintenanceTable> MaintenanceTables { get; set; }

        public DbSet<UserPreference> UserPreferences { get; set; }

        public static string envi;

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.HasDefaultSchema("dbo");

            var stringProps = from e in modelBuilder.Model.GetEntityTypes()
                              from p in e.GetProperties()
                              where p.PropertyInfo.PropertyType == typeof(string)
                              select p;


            foreach (var p in stringProps)
            {
                // Widen certain fields
                if (p.Name == "Comment" || p.Name.Contains("JSON") || p.Name.Contains("Json"))
                {
                    p.SetMaxLength(500);
                }
                else
                {
                    if (p.Name == "Description" || p.Name == "Notes" || p.Name == "AccountDescription" || p.Name == "FileName")
                    {
                        p.SetMaxLength(250);
                    }
                    else if(p.Name == "FailNotes")
                    {
                        p.SetMaxLength(99999);
                    }
                    else
                    {
                        p.SetMaxLength(50);
                    }
                }

            }

            modelBuilder.Entity<ResolutionType>().Property(x => x.BackupRequired).HasMaxLength(5000); //sets its to max.
            modelBuilder.Entity<RegPicture>().Property(x => x.Result).HasMaxLength(2000);
            modelBuilder.Entity<MissingResolution>().Property(x => x.StockcheckIdAndReference).HasMaxLength(250);

            // modelBuilder.Entity<Model.Input.StockItem>().HasIndex(p => new { p.Reg, p.Vin, p.SiteId }).IsUnique();

            //set decimals to 12,3 precision
            foreach (var property in modelBuilder.Model.GetEntityTypes()
                .SelectMany(t => t.GetProperties())
                .Where(p => p.ClrType == typeof(decimal) || p.ClrType == typeof(decimal?)))
            {
                // EF Core 3
                property.SetColumnType("decimal(15, 3)");

            }

            modelBuilder.Entity<Site>().Property(n => n.Longitude).HasPrecision(9, 4);
            modelBuilder.Entity<Site>().Property(n => n.Latitude).HasPrecision(9, 4);

            modelBuilder.Entity<Scan>().Property(n => n.Longitude).HasPrecision(9, 4);
            modelBuilder.Entity<Scan>().Property(n => n.Latitude).HasPrecision(9, 4);
            modelBuilder.Entity<Scan>().HasIndex(s => new { s.UserId, s.ScanDateTime, s.Reg, s.Vin }).IsUnique();

            modelBuilder.Entity<Model.Import.FileImport>().ToTable("FileImports", "import");
            modelBuilder.Entity<Model.Import.SiteDescriptionDictionary>().ToTable("SiteDescriptionDictionary", "import");

            modelBuilder.Entity<Model.Input.FinancialLine>().ToTable("FinancialLines", "input").HasNoKey();
            modelBuilder.Entity<Model.Input.ReconcilingItem>().ToTable("ReconcilingItems", "input").HasNoKey();
            modelBuilder.Entity<Model.Input.StockItem>().ToTable("StockItems", "input").HasNoKey();
            modelBuilder.Entity<MaintenanceTable>().Property(x => x.Name).HasMaxLength(500);

            modelBuilder.Entity<ImportMask>().Property(x => x.Name).HasMaxLength(200);
        }

        protected override void OnConfiguring(DbContextOptionsBuilder options)
        {

            if (!options.IsConfigured)
            {
                if (envi == "loaderProject")
                {
                    Configuration = new ConfigurationBuilder()
                    .AddJsonFile("appsettings.json")
                    .Build();

                    bool isUSConnection = _useAlternativeConnection;
                    string connString = isUSConnection ? Configuration.GetSection("ConnectionStrings")["USConnection"] : Configuration.GetSection("ConnectionStrings")["DefaultConnection"];
                    options.UseSqlServer(connString, sqlServerOptions => sqlServerOptions.CommandTimeout(300));
                }
                else
                {
                    Configuration = new ConfigurationBuilder()
                    .AddJsonFile("appsettings.json")
                    // .AddJsonFile($"appsettings.{envi}.json") // Comment this out to do a DB migration and add valid appsettings.json
                    .Build();

                    string connString = Configuration.GetSection("ConnectionStrings")["DefaultConnection"];
                    options.UseSqlServer(connString, sqlServerOptions => sqlServerOptions.CommandTimeout(300));
                }


            }
            base.OnConfiguring(options);
        }
    }
}
