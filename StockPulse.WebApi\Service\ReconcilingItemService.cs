﻿using Microsoft.ApplicationInsights.Extensibility;
using Microsoft.ApplicationInsights;
using Microsoft.CodeAnalysis;
using StockPulse.WebApi.DataAccess;
using StockPulse.Model;
using StockPulse.WebApi.ViewModel;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using System.Data.SqlClient;

namespace StockPulse.WebApi.Service
{
    public interface IReconcilingItemService
    {
        Task<IEnumerable<ReconcilingItem>> GetReconcilingItems(int stockcheckId, int userId);
        Task DeleteAllItems(int stockcheckId, int userId, int reconcilingItemTypeId);
        Task DeleteRecItem(int stockcheckId, int userId, int itemId);
        //Task<ReconcilingItem> GetReconcilingItem(int stockcheckId, int userId);
        Task<IEnumerable<ReconcilingItemTypeStat>> GetReconcilingItemTypeStats(int stockcheckId, int userId);
        Task<IEnumerable<ReconcilingItemWithType>> GetReconcilingItemsWithType(int stockcheckId, int userId);
        Task<IEnumerable<ReconcilingItemVM>> GetReconcilingItemArray(int stockcheckId, int userId, int reconcilingItemTypeId);
        Task SaveNewReconcilingItems(List<ReconcilingItemSave> items, int userId);
        Task SaveBackup(SaveBackupForReconcilingItemsParams parms, int userId);
        Task<IEnumerable<ImageToUpdate>> GetBackups(int stockCheckId, int reconcilingItemTypeId, int userId);
    }

    public class ReconcilingItemService : IReconcilingItemService
    {
        //properties of the service
        private readonly IReconcilingItemDataAccess reconcilingItemDataAccess;
        private readonly IImageService imageService;
        private readonly IConfiguration _config;
        private string Connectionstring = "DefaultConnection";

        //constructor
        public ReconcilingItemService(
            IReconcilingItemDataAccess reconcilingItemDataAccess,
            IImageService imageService,
            IConfiguration config)
        {
            this.reconcilingItemDataAccess = reconcilingItemDataAccess;
            this.imageService = imageService;
            this._config = config;
        }


        //methods of the service
        public async Task<IEnumerable<ReconcilingItem>> GetReconcilingItems(int stockcheckId, int userId)
        {
            return await reconcilingItemDataAccess.GetReconcilingItems(stockcheckId, userId);
        }

        public async Task<IEnumerable<ReconcilingItemTypeStat>> GetReconcilingItemTypeStats(int stockcheckId, int userId)
        {
            return await reconcilingItemDataAccess.GetReconcilingItemTypeStats(stockcheckId,  userId);
        }

        //public async Task<ReconcilingItem> GetReconcilingItem(int stockcheckId, int userId)
        //{
        //    return await reconcilingItemDataAccess.GetReconcilingItem(stockcheckId, userId);
        //}

        public async Task<IEnumerable<ReconcilingItemWithType>> GetReconcilingItemsWithType(int stockcheckId, int userId)
        {
            return await reconcilingItemDataAccess.GetReconcilingItemsWithType(stockcheckId, userId);
        }

        public async Task<IEnumerable<ReconcilingItemVM>> GetReconcilingItemArray(int stockcheckId, int userId, int reconcilingItemTypeId)
        {
            return await reconcilingItemDataAccess.GetReconcilingItemArray(stockcheckId, userId, reconcilingItemTypeId);
        }

        public async Task DeleteAllItems(int stockcheckId, int userId, int reconcilingItemType)
        {
            await reconcilingItemDataAccess.DeleteAllItems(stockcheckId, userId, reconcilingItemType);
        }

        public async Task DeleteRecItem(int stockcheckId, int userId, int itemId)
        {
            await reconcilingItemDataAccess.DeleteRecItem(stockcheckId, userId, itemId);
        }

        public async Task SaveNewReconcilingItems(List<ReconcilingItemSave> items, int userId)
        {
            await reconcilingItemDataAccess.SaveNewReconcilingItems(items, userId);
        }

        public async Task SaveBackup(SaveBackupForReconcilingItemsParams parms, int userId)
        {
            using IDbConnection db = new SqlConnection(_config.GetConnectionString(Connectionstring));
            try
            {
                if (db.State == ConnectionState.Closed)
                {
                    db.Open();
                }

                using var tran = db.BeginTransaction();
                try
                {
                    foreach (var file in parms.Files)
                    {
                        if (file.Status.ToUpper() == "ADD")
                        {
                            string cleanFileName = imageService.RemoveUnwantedCharsFromFileName(file.FileName);
                            int id = await reconcilingItemDataAccess.SaveBackup(cleanFileName, parms.StockCheckId, parms.ReconcilingItemTypeId, userId, db, tran);

                            using (Stream stream = imageService.convertBase64ToStream(file.FileBase64))
                            {
                                string contentType = imageService.GetContentType(file.FileBase64);
                                var result = await imageService.UploadReconcilingItemBackupFile(stream, id, cleanFileName, contentType);
                                if (result.Equals(false))
                                {
                                    string errMsg = $"Upload Failed for StockcheckId: {parms.StockCheckId}| ReconcilingItemTypeId: {parms.ReconcilingItemTypeId}| Id: {id}";
                                    throw new Exception(errMsg);
                                }
                            }

                        }
                        else if (file.Status.ToUpper() == "DELETE")
                        {
                            await reconcilingItemDataAccess.DeleteBackup(parms.StockCheckId, parms.ReconcilingItemTypeId, file.Id.Value, userId, db, tran);

                            var result = await imageService.DeleteReconcilingItemBackupFile(file.Id.Value);
                            if (result.Equals(false))
                            {
                                string errMsg = $"Delete Failed for StockcheckId: {parms.StockCheckId}| MissingResolutionId: {parms.ReconcilingItemTypeId}| ImageId: {file.Id.Value}";
                                throw new Exception(errMsg);
                            }

                        }
                    }

                    tran.Commit();
                }
                catch (Exception ex)
                {
                    tran.Rollback();
                    throw new Exception(ex.Message, ex);
                }
            }
            catch (Exception ex)
            {
                var config = TelemetryConfiguration.CreateDefault();
                var client = new TelemetryClient(config);
                client.TrackException(ex);
                throw new Exception(ex.Message, ex);
            }
            finally
            {
                if (db.State == ConnectionState.Open)
                    db.Close();
            }
        }

        public async Task<IEnumerable<ImageToUpdate>> GetBackups(int stockCheckId, int reconcilingItemTypeId, int userId)
        {
            var existingBackups = await reconcilingItemDataAccess.GetBackups(stockCheckId, reconcilingItemTypeId, userId);
            return BuildOutImageURLs(existingBackups);
        }

        private IEnumerable<ImageToUpdate> BuildOutImageURLs(IEnumerable<ReconcilingItemBackup> existingBackups)
        {
            var files = new List<ImageToUpdate>();
         
            if (existingBackups != null)
            {
                foreach (var file in existingBackups)
                {
                    files.Add(new ImageToUpdate() { Id = file.Id, Status = "BLOB", FileBase64 = "", Url = imageService.GetReconcilingItemBackupFileURL(file.Id), FileName = file.FileName });
                }
            }

            return files;
        }
    }
}
