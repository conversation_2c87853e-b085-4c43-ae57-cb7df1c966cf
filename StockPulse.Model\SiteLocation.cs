﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace StockPulse.Model
{
    public class SiteLocation
    {
        // Primary key is a FK combo
        [Key]
        public int Id { get; set; }

        public int SiteId { get; set; }
        [ForeignKey("SiteId"), Column(Order = 0)]
        public virtual Site Sites { get; set; }

        public int LocationId { get; set; }
        [ForeignKey("LocationId"), Column(Order = 1)]
        public virtual Location Locations { get; set; }
    }
}