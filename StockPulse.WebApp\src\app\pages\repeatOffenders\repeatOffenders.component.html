<nav class="page-specific-navbar">
  <singleSitePickerWithSearch></singleSitePickerWithSearch>

  <div class="buttonGroup">

    <!-- The various stages -->
    <button class="btn tableType btn-primary" [disabled]="disableStockChecksButton()"
      (click)="selectStage(stages.chooseStockChecks)">
      Choose Stock Checks
    </button>

    <button class="btn tableType btn-primary" [ngClass]="{active:service.stage === stages.unknown}"
      [disabled]="disableStockChecksButton()" (click)="selectStage(stages.unknown)">
      {{repeatUnknownsLabel()}}
    </button>

    <button class="btn tableType btn-primary" [ngClass]="{active:service.stage === stages.missing}"
      [disabled]="disableStockChecksButton()" (click)="selectStage(stages.missing)">
      {{repeatMissingsLabel()}}
    </button>



    <!-- The go button -->
    <button [disabled]="!showCalculateButton()"  id="goButton" class="btn btn-success ms-3" (click)="calculateRepeatIssues()"
      [ngbPopover]="service.constants.smallScreenSize ? 'Calculate repeat missing and unknown' : null" triggers="mouseover:mouseleave">
      {{ service.constants.smallScreenSize ? 'Calculate' : 'Calculate repeat missing and unknown' }}
    </button>
  </div>

  <statusAndBarChart></statusAndBarChart>
</nav>


<div class="content-new">

  <!-- Instruction row -->
  <instructionRow [message]="instructionRowMessage()"></instructionRow>

  <!-- Choose stockchecks -->
  <div id="chooseStockChecks" *ngIf="service.stage === stages.chooseStockChecks">


    <div class="tableHolder">
      <table id="repeatOffendersTable" class="cph fullWidth">
        <thead>

          <tr>
            <th>Site</th>
            <th>Date</th>
            <th>Last Updated</th>
            <th>Last Updated By</th>
            <th>Status</th>
            <th>DMS <br>Stock</th>
            <th>Scans</th>
            <th>Scanned <br> And In Stock</th>
            <th class="spacer"></th>
            <th>Unknown <br> Vehicles</th>
            <th>Missing <br> Vehicles</th>
            <th class="spacer"></th>
            <th>Include?</th>
          </tr>
        </thead>
        <tbody>

          <ng-container *ngFor="let stockCheck of service.stockChecks">

            <tr  [ngClass]="{'highlighted':stockCheck.isChosen}">
              <td>{{stockCheck.site}}</td>
              <td>{{stockCheck.date|cph:'dateMed':0}}</td>
              <td>{{stockCheck.lastUpdated|cph:'dateMed':0}}</td>
              <td>{{stockCheck.person}}</td>
              <td>{{stockCheck.status}}</td>
              <td>{{stockCheck.stockItemsCount}}</td>
              <td>{{stockCheck.scans}}</td>
              <td>{{stockCheck.scannedInStock|cph:'number':0}}</td>
              <td class="spacer"></td>
              <td>{{stockCheck.unknowns|cph:'number':0}}</td>
              <td>{{stockCheck.missings|cph:'number':0}}</td>
              <td class="spacer"></td>
              <td>
                <button class="custom-checkbox" [ngClass]="{ 'checked': stockCheck.isChosen }"
                  (click)="toggleStockCheck(stockCheck)">
                  <fa-icon *ngIf="stockCheck.isChosen" [icon]="icon.faCheck"></fa-icon>
                </button>
              </td>


            </tr>

          </ng-container>
        </tbody>
      </table>
    </div>
  </div>



  <!-- Unknowns -->
  <div *ngIf="service.stage && service.stage === stages.unknown" id="unknown">
    <repeatUnknownsTable [rowData]="service.unknownVehicles"></repeatUnknownsTable>
  </div>




  <!-- Missing  -->
  <div id="missing" *ngIf="service.stage === stages.missing">
    <repeatMissingsTable [rowData]="service.missingVehicles"></repeatMissingsTable>
  </div>


</div>