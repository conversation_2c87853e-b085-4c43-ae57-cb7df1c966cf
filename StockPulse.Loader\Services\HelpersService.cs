﻿
using System;



using log4net;
using System.IO;
using System.Linq;
using System.Collections.Generic;
using Dapper;
using System.Data;
using System.ComponentModel;
using System.Threading.Tasks;
using System.Data.SqlClient;
using Microsoft.VisualBasic.FileIO;
using OfficeOpenXml;

namespace StockPulse.Loader.Services
{


    public static class HelpersService
    {
        private static readonly IDapper dapper = new Dapperr();

        public static DataTable ToDataTable<T>(this IList<T> data)
        {
            PropertyDescriptorCollection properties =
                TypeDescriptor.GetProperties(typeof(T));
            DataTable table = new DataTable();
            foreach (PropertyDescriptor prop in properties)
                table.Columns.Add(prop.Name, Nullable.GetUnderlyingType(prop.PropertyType) ?? prop.PropertyType);
            foreach (T item in data)
            {
                DataRow row = table.NewRow();
                foreach (PropertyDescriptor prop in properties)
                    row[prop.Name] = prop.GetValue(item) ?? DBNull.Value;
                table.Rows.Add(row);
            }
            return table;
        }


        public static DateTime FirstDateInWeek(this DateTime dt, DayOfWeek weekStartDay)
        {
            while (dt.DayOfWeek != weekStartDay)
                dt = dt.AddDays(-1);
            return dt;
        }

        public static int CountTrue(params bool[] args)
        {
            return args.Count(t => t);
        }


        public static string LimitTo(string SourceString, int CharCount)
        {
            var length = SourceString.Length;
            return SourceString.Substring(0, Math.Min(CharCount, length));
        }

        public static void RemoveQuotesAndTrim(string[] stringArray)
        {
            for (int i = 0; i < stringArray.Length; i++)
            {
                if (stringArray[i].Substring(0, 1) == ("\""))
                {
                    stringArray[i] = stringArray[i].Trim().Replace("\"", ""); //get rid of double quotes around text fields, and Trim surrounding white space
                }
            }
        }

        public static void NoFileFoundMessage(ILog Logger, DateTime LastLoggedNoFiles, string filePattern)
        {
            TimeSpan age = DateTime.UtcNow - LastLoggedNoFiles;

            if (age.Minutes > 120)
            {
                LastLoggedNoFiles = DateTime.UtcNow;
                Logger.Info($@"[{DateTime.UtcNow}] | No files found matching pattern {filePattern}");
            }
        }

        public static void NewFileFoundMessage(ILog Logger, string fileToProcess)
        {
            Logger.Info($@"[{DateTime.UtcNow}] New file found at {ConfigService.incomingRoot}.  Starting {fileToProcess}");
        }

        public static void MoveFileToProcessed(string inboundFilePath, string fileExt)
        {
            File.Move(inboundFilePath, inboundFilePath.Replace(@"\inbound", @"\processed").Replace("p" + fileExt, $"processed{DateTime.UtcNow.ToString("yyyyMMdd_HHmmss")}" + fileExt));
        }

        public static int GetIntFromString(string inputString)
        {
            if (inputString.Length > 0 && IsStringNumeric(inputString))
            {
                return Int32.Parse(inputString);
            }

            return 0;
        }

        private static bool IsStringNumeric(string str) { return str.All(char.IsDigit); }


        public static decimal DecimalParseAndValidate(string textIn)
        {
            decimal result = decimal.Parse(textIn);

            if (result < -999999999.999M)
            {
                throw new Exception($"Given value of {textIn} is too small");
            }
            if (result > 999999999.999M)
            {
                throw new Exception($"Given value of {textIn} is too big");
            }

            return result;
        }

        public static async Task ClearInputTBs(int dealerGroupId, bool isUS)
        {
            await dapper.ExecuteWithConnectionAsync($"DELETE FROM [input].[FinancialLines] WHERE DealerGroupId = {dealerGroupId}", null, System.Data.CommandType.Text, isUS);
        }

        public static async Task ClearInputStocks(int dealerGroupId, string stockTypes, bool isUS)
        {
            if (stockTypes == null)
            {
                await dapper.ExecuteWithConnectionAsync($"DELETE FROM [input].[StockItems] WHERE DealerGroupId = {dealerGroupId}", null, System.Data.CommandType.Text, isUS);
            }
            else
            {
                string sqlArray = ConvertStringToSQL(stockTypes);
                await dapper.ExecuteWithConnectionAsync($"DELETE FROM [input].[StockItems] WHERE DealerGroupId = {dealerGroupId} AND StockType IN ({sqlArray})", null, System.Data.CommandType.Text, isUS);

            }
        }

        public static string ConvertStringToSQL(string input)
        {
            // Split the input string by commas
            string[] items = input.Split(',');

            // Wrap each item with single quotes and join them back with commas
            string result = String.Join(",", Array.ConvertAll(items, item => $"'{item}'"));

            return result;
        }

        public static async Task ClearInputReconcilingItems(int dealerGroupId, string recItemIdsToInclude, string recItemIdsToExclude, bool isUS)
        {
            // Start with a base query
            var query = $"DELETE FROM [input].[ReconcilingItems] WHERE DealerGroupId = {dealerGroupId}";

            // Add conditions based on the presence of include and exclude IDs
            if (!string.IsNullOrEmpty(recItemIdsToInclude) && !string.IsNullOrEmpty(recItemIdsToExclude))
            {
                query += $" AND ReconcilingItemTypeId IN ({recItemIdsToInclude}) AND ReconcilingItemTypeId NOT IN ({recItemIdsToExclude})";
            }
            else if (!string.IsNullOrEmpty(recItemIdsToInclude))
            {
                query += $" AND ReconcilingItemTypeId IN ({recItemIdsToInclude})";
            }
            else if (!string.IsNullOrEmpty(recItemIdsToExclude))
            {
                query += $" AND ReconcilingItemTypeId NOT IN ({recItemIdsToExclude})";
            }

            await dapper.ExecuteWithConnectionAsync(query, null, System.Data.CommandType.Text, isUS);
        }


        public static async Task TruncateTable(string schema, string table, bool isUS)
        {
            await dapper.ExecuteWithConnectionAsync($"TRUNCATE TABLE [{schema}].[{table}]", null, System.Data.CommandType.Text, isUS);
        }

        public static async Task BulkCopyTable(string schema, string table, DataTable dataTable, bool isUS)
        {
            string connectionString = isUS ? ConfigService.connectionNameUS : ConfigService.connectionNameUK;

            using (SqlBulkCopy sqlBulkCopy = new SqlBulkCopy(connectionString))
            {
                foreach (DataColumn col in dataTable.Columns)
                {
                    sqlBulkCopy.ColumnMappings.Add(col.ColumnName, col.ColumnName);
                }
                sqlBulkCopy.DestinationTableName = $"[{schema}].[{table}]";
                sqlBulkCopy.BulkCopyTimeout = 1000;
                await sqlBulkCopy.WriteToServerAsync(dataTable);
            }
        }

        public static async Task MergeTables(string mergeSP)
        {
            await ExecuteSPAsync(mergeSP, null);
        }

        public static async Task ExecuteSPAsync(string sp, DynamicParameters dynamicParameters)
        {
            await dapper.ExecuteAsync($"{sp}", dynamicParameters, System.Data.CommandType.StoredProcedure);
        }

        public static decimal Divide(int numerator, int denominator)
        {
            return denominator != 0 ? Math.Round((decimal)((double)numerator / (double)denominator), 3, MidpointRounding.AwayFromZero) : 0;
        }
        public static decimal DivideByDecimal(int numerator, decimal denominator)
        {
            return denominator != 0 ? Math.Round((decimal)((double)numerator / (double)denominator), 3, MidpointRounding.AwayFromZero) : 0;
        }

        public static decimal DivideByDecimal(decimal numerator, decimal denominator)
        {
            return denominator != 0 ? Math.Round((decimal)((double)numerator / (double)denominator), 3, MidpointRounding.AwayFromZero) : 0;
        }

        public static async Task ExecuteSPWithConnectionAsync(string sp, string clientSpecificConnectionString, bool isUS)
        {
            await dapper.ExecuteWithConnectionAsync(sp, null, CommandType.StoredProcedure, isUS);
        }

        
        }
    }
