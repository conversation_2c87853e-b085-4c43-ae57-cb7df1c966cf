// import { Injectable } from '@angular/core';
// import { HttpRequest, HttpHandler, HttpEvent, HttpInterceptor } from '@angular/common/http';
// import { BehaviorSubject, Observable, throwError } from 'rxjs';
// import { catchError, filter, finalize, switchMap, take } from 'rxjs/operators';
// import { ConstantsService } from './services/constants.service';
// import { Router } from '@angular/router';
// import { NewToastService } from './services/newToast.service';

// @Injectable()
// export class ImportInterceptor implements HttpInterceptor {


//     constructor(
//         public constants: ConstantsService,
//         public router: Router,
//         public toastService: NewToastService
//         ) { }

//     intercept(request: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {

//         return next.handle(request).pipe(catchError(err => {
            
//             let error = err;

//             // Check url is for import
//             if(request.url.includes('ReconcilingItems/ReconcilingItems')){

//                 if (err.status != 401){
//                     let errorMessage = err.error.replace("System.Exception:", "").split('\n')[0];
//                     this.toastService.errorToast(errorMessage);
//                     error = err.error.value || err.error.message || err.statusText;
//                 }
//             }

//             return throwError(error);

//         }))
//     }

// }