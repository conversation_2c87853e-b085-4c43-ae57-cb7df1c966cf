﻿using System;

namespace StockPulse.WebApi.ViewModel
{




    //load stock and rec items
    public class StockItem
    {
        public StockItem() { }  
        public StockItem(StockItemFullDetail stockItem)
        {
            StockItemId = stockItem.StockItemId;
            Reg = stockItem.Reg;
            Vin = stockItem.Vin;
            Description = stockItem.Description;
            DIS = stockItem.DIS;
            GroupDIS = stockItem.GroupDIS;
            Branch = stockItem.Branch;
            StockType = stockItem.StockType;
            Comment = stockItem.Comment;
            Reference = stockItem.Reference;
            StockValue = stockItem.StockValue;
            Flooring = stockItem.Flooring;
            Site = stockItem.SiteName;
            State = stockItem.State;
            IsAgencyStock = stockItem.IsAgencyStock;
        }

        public StockItem(ScanFullDetail scan)
        {
            //we are being given a scanFull with all the stockItem fields within it
            StockItemId = scan.StockItemId;
            Reg = scan.Reg;
            Vin = scan.Vin; 
            Description = scan.Description;
            DIS = (int)(scan.DIS !=null ? scan.DIS : 0);
            GroupDIS = (int)(scan.GroupDIS != null ? scan.GroupDIS : 0);
            Branch = scan.Branch;
            StockType = scan.StockType;
            Comment = scan.Comment;
            Reference = scan.Reference;
            StockValue = (decimal)(scan.StockValue != null ? scan.StockValue : 0);
            StockValue = (decimal)(scan.Flooring != null ? scan.Flooring : 0);
            Site = scan.OtherSiteName;
            State = (ReconciliationState)scan.State;
        }


        public int? StockItemId { get; set; }
        public string Reg { get; set; }
        public string Vin { get; set; }
        public string Description { get; set; }
        public int DIS { get; set; }
        public int GroupDIS { get; set; }
        public string Branch { get; set; }
        public string StockType { get; set; }
        public string Comment { get; set; }
        public string Reference { get; set; }
        public decimal StockValue { get; set; }
        public decimal Flooring { get; set; }
        public string Site { get; set; }

        public ReconciliationState State { get; set; }
        //public int StockCheckId { get; set; }

        public bool IsAgencyStock { get; set; }
        public string SiteName { get; set; }
        public string? FileName { get; set; }
        public DateTime? FileDate {  get; set; }
        public DateTime? LoadDate { get; set; }
        public string? UserName { get; set; }
    }
}
