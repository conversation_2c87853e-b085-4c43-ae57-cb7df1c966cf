import { ReconciliationBucket } from "./ReconciliationBucket";


export interface BarNew extends ReconciliationBucket {
  // description: string;
  // isFullHeight: boolean;
  // isProblem: boolean;
  // isScan: boolean;
  // isStock: boolean;
  // order: number;
  // reconciliationTypeId: number;
  // vehicleCount: number;
  // reconciliationState:ReconciliationState
  
  
  //client side props

  vehicleCountAdjusted:number;
  isGood: boolean;
  label: number;
  popoverLabel: string;
  height: number;
  bottomEdge: number;
  //heightAction?: number;
  heightUltimate: number;
  bottomEdgeUltimate: number;
}

