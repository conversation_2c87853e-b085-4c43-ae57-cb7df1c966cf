import { Component } from "@angular/core";
import { ICellRendererAngularComp } from "ag-grid-angular";
import { ApiAccessService } from "../../services/apiAccess.service";
import { ConstantsService } from '../../services/constants.service';
import { IconService } from '../../services/icon.service';
import { ToastService } from "../../services/newToast.service";
import { SelectionsService } from '../../services/selections.service';

@Component({
    selector: 'deleteSiteNameLookup-cell',
    template: `
        <button *ngIf="params.data" class="btn btn-danger" (click)="maybeDelete()">
            <fa-icon [icon]="icon.faTrash">Delete</fa-icon>
        </button>
    `
    ,
    styles: [`
        button {
            height: 1.6em;
            margin: 0px;
            margin-top: -5px;
            padding: 0px 10px;
        }

        .btn {
            opacity: 0.2;
        }

        .btn:hover {
            opacity: 1;
        }
    `]
})
export class DeleteSiteNameLookupComponent implements ICellRendererAngularComp {
    params: any;

    constructor(
        public selections: SelectionsService,
        public icon: IconService,
        public constants: ConstantsService,
        public apiAccess: ApiAccessService,
        public toastService: ToastService
    ) { }


    agInit(params: any): void {
        this.params = params
    }

    refresh(): boolean {
        return false;
    }

    maybeDelete() {
        if (this.params.data.id === 0) {
            this.params.api.applyTransaction({ remove: [this.params.data] });
            this.params.api.refreshClientSideRowModel('aggregate');
            this.params.context.componentParent.service.changes = this.params.context.componentParent.service.changes.filter(x => x.newValue !== this.params.data.inputName);
        } else {
            this.constants.confirmModal.confirmModalHeader = 'Really delete this mapping?';
            this.constants.confirmModal.isDestructive = true;
            let mySubscription = this.selections.confirmModalEmitter.subscribe(res => {
                if (res) { this.delete(); }
                mySubscription.unsubscribe();
            })

            this.constants.confirmModal.showModal();
        }
    }

    delete() {
        this.apiAccess.deleteUser('Sites', 'DeleteSiteNameLookup', this.params.data.id).subscribe(res => {
            this.toastService.successToast('Deleted mapping');
            this.params.api.applyTransaction({ remove: [this.params.data] });
            this.params.context.componentParent.service.siteNameLookups = this.params.context.componentParent.service.siteNameLookups.filter(x => x.id !== this.params.data.id);
            this.params.context.componentParent.service.siteNameLookupsCopy = this.params.context.componentParent.service.siteNameLookupsCopy.filter(x => x.id !== this.params.data.id);
            this.params.api.refreshClientSideRowModel('aggregate');
        }, e => {
            this.toastService.errorToast('Failed to delete mapping');
        })
    }
}
