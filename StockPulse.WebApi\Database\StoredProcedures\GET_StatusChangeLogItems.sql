﻿

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].GET_StatusChangeLogItems
(
	@StockCheckId int = NULL
)
AS
BEGIN

SET NOCOUNT ON

SELECT
scl.Stock<PERSON>heckId,
scl.UserId,
scl.StatusId,
u.Name AS "UserName",
s.Description AS "Status",
scl.Date
FROM StatusChangeLogItems scl
INNER JOIN Users u ON u.Id = scl.UserId
INNER JOIN Statuses s ON s.Id = scl.StatusId
WHERE scl.StockCheckId = @StockCheckId

END

GO
