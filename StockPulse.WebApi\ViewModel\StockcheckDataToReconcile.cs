﻿using System.Collections.Generic;

namespace StockPulse.WebApi.ViewModel
{
    //public class AddMissingResolution
    //{
    //    public int Id { get; set; }

    //    public int? ResolutionTypeId { get; set; }
    //    public string ResolutionTypeDescription { get; set; }

    //    public DateTime? ResolutionDate { get; set; }

    //    public int UserId { get; set; }
    //    public string UsersName { get; set; }

    //    public string Notes { get; set; }

    //    public bool IsResolved { get; set; }
    //}


    public class StockcheckDataToReconcile
    {

        public StockcheckDataToReconcile()
        {
            OurScans = new List<MatchItem>();
            OurStockItems = new List<MatchItem>();
            OurReconcilingItemsForMissing = new List<MatchItem>();
            OurReconcilingItemsForUnknown = new List<MatchItem>();
            OtherSitesUnMatchedScans = new List<MatchItem>();
            OtherSiteUnmatchedStockItems = new List<MatchItem>();
        }


        public IEnumerable<MatchItem> OurScans { get; set; }
        public IEnumerable<MatchItem> OurStockItems { get; set; }
        public IEnumerable<MatchItem> OurReconcilingItemsForMissing { get; set; }
        public IEnumerable<MatchItem> OurReconcilingItemsForUnknown { get; set; }
        public IEnumerable<MatchItem> OtherSitesUnMatchedScans { get; set; }
        public IEnumerable<MatchItem> OtherSiteUnmatchedStockItems { get; set; }
        public bool RecProcessMatchRequiresBothRegAndVin { get; set; }
    }
}


