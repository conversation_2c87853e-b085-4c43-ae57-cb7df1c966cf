{"name": "stockpuls<PERSON><PERSON>", "version": "3.4.4", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e", "dev": "ng build --configuration=dev", "prod": "ng build --configuration=production && npx brotli-cli compress -q 11 ./dist/**/* --glob && del /s /q dist\\config\\*.br 2> nul"}, "private": true, "dependencies": {"@angular/animations": "^15.1.5", "@angular/cdk": "^15.1.5", "@angular/common": "^15.1.5", "@angular/compiler": "^15.1.5", "@angular/core": "^15.1.5", "@angular/forms": "^15.1.5", "@angular/google-maps": "^15.1.5", "@angular/platform-browser": "^15.1.5", "@angular/platform-browser-dynamic": "^15.1.5", "@angular/platform-server": "^15.1.5", "@angular/router": "^15.1.5", "@azure/msal-angular": "^2.5.3", "@azure/msal-browser": "^2.33.0", "@fortawesome/angular-fontawesome": "0.12.1", "@fortawesome/fontawesome-svg-core": "6.3.0", "@fortawesome/free-regular-svg-icons": "6.3.0", "@fortawesome/free-solid-svg-icons": "6.3.0", "@fortawesome/pro-light-svg-icons": "6.3.0", "@fortawesome/pro-regular-svg-icons": "6.3.0", "@fortawesome/pro-solid-svg-icons": "6.3.0", "@microsoft/applicationinsights-web": "^2.8.6", "@ng-bootstrap/ng-bootstrap": "14.0.1", "@ngneat/hot-toast": "^5.0.1", "@ngneat/overview": "^4.1.0", "@popperjs/core": "^2.11.6", "add": "^2.0.6", "ag-grid-angular": "29.1.0", "ag-grid-community": "29.1.0", "animate.css": "4.1.1", "bootstrap": "5.2.3", "brotli": "^1.3.3", "canvas-confetti": "^1.6.0", "core-js": "^3.1.3", "dexie": "3.2.3", "eslint": "^8.13.0", "exceljs": "4.3.0", "file-saver": "^2.0.5", "jspdf": "^2.5.1", "rxjs": "7.8.0", "sass": "^1.58.3", "tslib": "^2.0.0", "xlsx": "0.18.5", "yarn": "^1.15.2", "zone.js": "0.12.0"}, "devDependencies": {"@angular-devkit/build-angular": "^15.1.6", "@angular/cli": "^15.1.6", "@angular/compiler-cli": "^15.1.5", "@angular/language-service": "^15.1.5", "@angular/localize": "^15.1.5", "@fortawesome/fontawesome-pro": "6.3.0", "@types/googlemaps": "^3.39.13", "@types/jasmine": "4.3.1", "@types/jasminewd2": "~2.0.3", "@types/node": "18.14.0", "codelyzer": "^6.0.0", "jasmine-core": "4.5.0", "jasmine-spec-reporter": "7.0.0", "karma": "~6.4.1", "karma-chrome-launcher": "~3.1.0", "karma-cli": "^2.0.0", "karma-coverage-istanbul-reporter": "~3.0.2", "karma-jasmine": "5.1.0", "karma-jasmine-html-reporter": "2.0.0", "protractor": "~7.0.0", "rxjs-tslint": "^0.1.7", "ts-node": "10.9.1", "typescript": "4.9.5", "webpack": "5.75.0", "webpack-bundle-analyzer": "4.8.0"}}