﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace StockPulse.Repository.Migrations
{
    /// <inheritdoc />
    public partial class IgnoreZeroValuestoImportMask : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "IgnoreZeroValues",
                schema: "dbo",
                table: "ImportMasks",
                type: "bit",
                nullable: false,
                defaultValue: false);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IgnoreZeroValues",
                schema: "dbo",
                table: "ImportMasks");
        }
    }
}
