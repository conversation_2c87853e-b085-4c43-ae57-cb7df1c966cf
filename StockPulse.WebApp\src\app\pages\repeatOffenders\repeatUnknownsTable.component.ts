import { Component, OnInit,  Input, HostListener,} from "@angular/core";
import { SelectionsService } from '../../services/selections.service';
import { CphPipe } from '../../cph.pipe';
import { RegPlateComponent } from '../../_cellRenderers/regPlate.component';
import { ChassisComponent } from '../../_cellRenderers/chassis.component';
import { ScanNotesComponent } from '../../_cellRenderers/scanNotes.component';
import { CustomHeaderComponent } from '../../_cellRenderers/customHeader.component';
import { Router } from '@angular/router';
import { RepeatUnknownOffender } from "src/app/model/RepeatUnknownOffender";
import { RepeatOffendersService } from "./repeatOffenders.service";
import { ColDef, GridOptions } from "ag-grid-community";
import { ImageComponent } from "src/app/_cellRenderers/image.component";
import { ScanWithResolution } from "src/app/model/ScanWithResolution";
import { LogoService } from "src/app/services/logo.service";
import { ToastService } from "src/app/services/newToast.service";
import { ExcelExportService } from "src/app/services/excelExport";
import { SheetToExtractOld } from "src/app/model/SheetToExtractOld";
import * as excelJS from 'exceljs';
import * as fs from 'file-saver';


@Component({
  selector: 'repeatUnknownsTable',
  template:    `
  <div id="gridHolder">
    <button class="btn floatTopRightOfGrid" (click)="generateExcelSheet()">
      <img style="width:2em;" [src]="logo.provideExcelLogo()">
    </button>

    <ag-grid-angular 
     id="importTable" 
     class="ag-theme-balham" 
     [gridOptions]="mainTableGridOptions" 
    >

    </ag-grid-angular>
    
  </div>



    `
  ,
  styles: [
    `

#gridHolder {
  position: relative;
  overflow-y: auto;
  overflow-x: hidden;
  width: 100%;
}

    #counter{position:absolute;top:-2em;height:2em;right:0em;    background: var(--secondaryLighter);      padding: 0.2em 1em;      border-radius: 0.3em 0.3em 0 0;}
    h4{font-weight:700;}
    #importTable{height: 100%}
    .numberChip{position:fixed;top:60px;right:20px;}
    .cellContentBox{    white-space: normal;            line-height: 1.5em;            }
  `
  ]
})


export class RepeatUnknownsTableComponent implements OnInit {
  @Input() rowData: ScanWithResolution[];

  @HostListener('window:resize', [])
  private onresize (event) {
    if (this.gridApi) this.gridApi.sizeColumnsToFit();
  }

  mainTableGridOptions: GridOptions;
  public gridApi;  

  constructor(
    public logo: LogoService,
    public toastService: ToastService,
    private excelExportService: ExcelExportService,
    public cphPipe: CphPipe,
    public selections: SelectionsService,
   private service: RepeatOffendersService,
  ) { }

  ngOnInit(): void {
    this.initParams()
    
  }

  rowClicked(row: {data: RepeatUnknownOffender}){
    //let repeatUnknown = row.data;
    this.service.globalSearchService.reg = row.data.scanReg;
    this.service.globalSearchService.chassis = row.data.scanVin;
    this.service.globalSearchService.requireAndMatch = false;
    //this.service.globalSearchService.stageValue = 'unknown'

     this.service.router.navigateByUrl('/vehicleSearch');
  }

  initParams() {
    //console.log(this.rowData)

   this.mainTableGridOptions = {
    domLayout: 'autoHeight',
     rowData: this.rowData,
    frameworkComponents:  { agColumnHeader: CustomHeaderComponent },
    columnTypes: {
       "numberColumn":{ cellRenderer: (params) => { return this.service.cphPipe.transform(params.value, 'number', 0) } },
       "labelColumn": { cellClass: 'agAlignCentre', filter: 'agTextColumnFilter' },
       "date": { cellClass: 'agAlignCentre', cellRenderer:(params)=>this.service.cphPipe.transform(params.value,'date',0), filter: 'agTextColumnFilter' }
     },
     onGridReady:(params)=>this.onGridReady(params),
     onRowDoubleClicked:(params)=>this.rowClicked(params),
     columnDefs : [
       //{ headerName: 'Stock Check Date', field: 'stockCheckDate', type: 'date', sort: 'desc', width: 100 },
       //{ headerName: "", field: "regImageThumbnailUrl", width: 100,  autoHeight: true, filter: null, type: 'labelColumn' },
       { headerName: "Photo",  width: 50, cellRendererFramework: ImageComponent, autoHeight: true, filter: null, type: 'labelColumn', cellClass: 'agAlignCentre' },
       { headerName: "Scan Id", field: "scanId", width: 50, autoHeight: false, type: 'labelColumn' },
       { headerName: "Reg", field: "scanReg", width: 100, cellRendererFramework: RegPlateComponent, autoHeight: false, type: 'labelColumn' },
       { headerName: "VIN", field: "scanVin", width: 100, cellRendererFramework: ChassisComponent, type: 'labelColumn' },
       { headerName: "Scan Description", cellClass: 'description', sortable: true, field: "scanDescription", width: 300, type: "labelColumn" },
       { headerName: "Scanned By", sortable: true, field: "scannerName", width: 150, type: "labelColumn" },
       { headerName: "Location", sortable: true, field: "locationDescription", width: 100, type: "labelColumn" },
       { headerName: "Resolution", sortable: true, field: "resolutionTypeDescription", cellRenderer:(params)=>params.value ? `<div class="cellContentBox">${params.value}</div>` : '', width: 150, type: 'labelColumn' },
       { headerName: "Resolution Detail", sortable: true, field: "resolutionNotes", cellRendererFramework: ScanNotesComponent, width: 700, type: 'labelColumn' },
      //  { headerName: "Unknown Vehicle Resolutions", autoHeight:true, sortable: true, cellRendererFramework: CarResolutionsListComponent, colId:'unknown', width: 400, type: 'labelColumn' },
     ]
   }



  }
  
  onGridReady(params) {
    this.gridApi = params.api;
    this.gridApi.sizeColumnsToFit();
    
  }

  generateExcelSheet() {
    this.toastService.loadingToast('Generating Excel file...');

    let rowsToInclude: any[] = [];

    this.gridApi.forEachNodeAfterFilter(n => {
      if (!!n.data) {
        rowsToInclude.push({
          scanId: n.data.scanId,
          scanReg: n.data.scanReg,
          scanVin: n.data.scanVin,
          scanDescription: n.data.scanDescription,
          scannerName: n.data.scannerName,
          locationDescription: n.data.locationDescription,
          resolutionTypeDescription: n.data.resolutionTypeDescription,
          resolutionNotes: n.data.resolutionNotes,
        });
      }
    })


    let sheet = {
      tableData: rowsToInclude,
      tableName: `Repeat Offenders - ${rowsToInclude.length}`,
      columnWidths: rowsToInclude.length > 0?  this.excelExportService.workoutColWidths(rowsToInclude): []
    }

    this.downloadExcelFile(sheet);
    
  }


  downloadExcelFile(sheet: SheetToExtractOld) {
    console.log('sheet',sheet);
    let workbook = new excelJS.Workbook();

    try {
      //define worksheet
      let worksheet = workbook.addWorksheet(sheet.tableName)

      //generic stuff for worksheet
      worksheet.views = [
        { state: 'frozen', xSplit: 1, ySplit: 5, zoomScale: 85 }
      ];

      //columns things
      let columns = []

      sheet.columnWidths.forEach(w => {
        columns.push({ width: w })
      })

      worksheet.columns = columns;


      //rows
      let titleRow = worksheet.addRow([sheet.tableName])//title
      titleRow.font = { name: 'Calibri', family: 4, size: 16, bold: true, color: 'white', fill: { bgColor: { rgb: '220,230,241' } } };

      let subtitle = 'Extracted ' + this.cphPipe.transform(new Date(), 'time', 0) + ' ' + this.cphPipe.transform(new Date(), 'date', 0) + ' ' + ' by ' + this.selections.usersName;
      let subTitleRow = worksheet.addRow([subtitle])//title
      subTitleRow.font = { name: 'Calibri', family: 4, size: 12, bold: false, italic: true, color: 'white', fill: { bgColor: { rgb: '220,230,241' } } };

      worksheet.addRow([]);
      worksheet.addRow([]);

      worksheet.getRow(1).height = 28;

      //the table headerRow 
      let columnHeadersRaw: string[] = Object.keys(sheet.tableData[0]);
      let columnHeadersFormatted: string[] = [];

      columnHeadersRaw.forEach(element => {
        let temp = element.replace('Vehicles', '').match(/[A-Z]+(?![a-z])|[A-Z]?[a-z]+|\d+/g).join(' ').trim();
        columnHeadersFormatted.push(temp);
      });

      let columnHeadersFinal: string[] = [];

      columnHeadersFormatted.forEach(element => {
         let temp = element
         .replace('scan Id', 'Scan Id')
         .replace('scan Reg', 'Reg')
         .replace('scan Vin', 'VIN')
         .replace('scan Description', 'Scan Description')
         .replace('scanner Name', 'Scanned By')
         .replace('location Description', 'Location')
         .replace('resolution Type Description', 'Resolution')
         .replace('resolution Notes', 'Resolution Detail')
         .trim();
         columnHeadersFinal.push(temp);
       });

      worksheet.addRow(columnHeadersFinal);
      let colCount = columnHeadersFinal.length;

      //loop through each column in active range and colour cells
      for (let i = 0; i < colCount; i++) {
        let colLetter = String.fromCharCode(65 + i)
        worksheet.getCell(colLetter + '4').font = { name: 'Calibri', family: 4, size: 11, color: { argb: 'FFFFFFFF' } }
        worksheet.getCell(colLetter + '4').fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF252424' } }
        worksheet.getCell(colLetter + '5').font = { name: 'Calibri', family: 4, size: 11, color: { argb: 'FFFFFFFF' } }
        worksheet.getCell(colLetter + '5').fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF252424' } }
      }

      //the data rows
      sheet.tableData.forEach(x => {
        let values = Object.values(x);
        const row = worksheet.addRow(values)

      })

      //loop through the first cell of each row and colour
      let rowCount = worksheet.rowCount + 1;


      // This goes down rows
      for (let i = 6; i < rowCount; i++) {

        // If 0, show dash
        for (let j = 6; j < 13; j++) {
          let colLetter = String.fromCharCode(65 + j);
          worksheet.getCell(colLetter + i.toString()).numFmt = '#,##0;-#,##0;-';
        }
      }

      
    }
    catch (e) {
      //carry on
    }

    let workbookName = 'StockPulse Extract ' + new Date().getDate() + new Date().toLocaleString('en-gb', { month: 'short' }) + new Date().getFullYear();

    workbook.xlsx.writeBuffer().then((data) => {
      let blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      fs.saveAs(blob, workbookName + '.xlsx');
    });

    this.toastService.destroyToast();
  }




  
 

}


