import { EventEmitter, Injectable } from "@angular/core";
import { Router } from "@angular/router";
import { ColumnState } from "ag-grid-community";
import { BehaviorSubject, Observable } from "rxjs";
import { CphPipe } from "src/app/cph.pipe";
import { StockCheck } from "src/app/model/StockCheck";
import { ApiAccessService } from "src/app/services/apiAccess.service";
import { ConstantsService } from "src/app/services/constants.service";
import { ToastService } from "src/app/services/newToast.service";
import { SelectionsService } from "src/app/services/selections.service";
import { RepeatOffendersService } from "../repeatOffenders/repeatOffenders.service";

@Injectable({
  providedIn: 'root'
})

export class StockChecksService {

  stockCheckVMs: StockCheck[] = [];
  showActive: boolean = true;
  importInProgress: boolean = false;
  
  refreshGridEmitter: EventEmitter<StockCheck> = new EventEmitter<StockCheck>();
  fromDate: string;
  toDate: string;
  // currencySymbol: string;
  latestDataRecievedDate: string;

  importInProgressSubject = new BehaviorSubject<boolean>(false);

  // Observable to be used in components
  importInProgressObservable = this.importInProgressSubject.asObservable();
  columnState: ColumnState[];
  filterState;

  constructor(
    public apiAccess: ApiAccessService,
    public selections: SelectionsService,
    public cphPipe: CphPipe,
    public constants: ConstantsService,
    public toastService: ToastService,
    //private router: Router,
    public repeatOffendersService: RepeatOffendersService,
  ) { }

  getAndSetStockChecks(showActive: boolean, supressLoadedToast:boolean, fromDate?: string, toDate?: string):Observable<void> {

    const myObserverable:Observable<void> = new Observable(observer=>{
      
      let toastRef;
      if(!supressLoadedToast) {
        toastRef = this.toastService.loadingToast();
      }
  
      let endpoint: string = `getStockChecks?isActive=${showActive}`;

      if (!showActive) {endpoint += `&fromDate=${fromDate}&toDate=${toDate}`}

      this.apiAccess.get('stockchecks', endpoint).subscribe((data:StockCheck[]) => {
        
        //----------------
        //success
        //----------------
        //this.stockCheckVMs = data;
        data.map(x => {
          x.date = new Date(x.date);
          x.lastUpdated = new Date(x.lastUpdated);
        });
        let stockChecksSite: StockCheck[] = data.filter((s) => s.isRegional === false && s.isTotal === false);
        let stockChecksRegion: StockCheck[] = data.filter(s => s.isRegional === true && s.isTotal !== true);
        let stockChecksTotal: StockCheck[] = data.filter(s => s.isTotal === true);
        
        let stockChecksSiteAndRegion = stockChecksSite.concat(stockChecksRegion);
        this.stockCheckVMs = stockChecksSiteAndRegion.concat(stockChecksTotal);

        this.generateRegionalAndTotalStats();
        if(!supressLoadedToast) this.toastService.successToast('Stock checks loaded');
        //this.toastService.destroyToast();
        if(toastRef){this.toastService.closeToast(toastRef)}
        
        observer.next();
        
        
      },e=>{
        //----------------
        // error
        //----------------
        
        if(!supressLoadedToast) this.toastService.errorToast('Failed to load stock checks');
        
      },()=>{
        //----------------
        // finally
        //----------------
        this.refreshGridEmitter.emit();
        this.constants.refreshPage.emit(false);
        observer.complete();
        this.importInProgress = false;
        this.importInProgressSubject.next(false); 

      });
    })

    return myObserverable;

  }

  getLatestDataRecievedDate() {
    this.apiAccess.get('stockchecks', 'GetLatestDataRecievedDate').subscribe((res: any) => {
      // console.log('getLatestDataRecievedDate', res);
      this.latestDataRecievedDate = this.cphPipe.transform(res, 'dateTime', 0);
    })
  }

  loadStockCheck(stockCheckVM: StockCheck, suppressLoadedToast: boolean, reloadView?: boolean) {

    const toastRef = this.toastService.loadingToast('Loading stock check...');

    // this.selections.stockCheck = stockCheckVM;
    this.repeatOffendersService.reset();
    //this.selections.newStockCheckSelectedEmitter.emit();
    const dateUTC: string = new Date(stockCheckVM.date).toISOString().split('T')[0]
    this.selections.stockCheckLongName = `${stockCheckVM.site} - ${this.cphPipe.transform(dateUTC, 'date', 0)}`
    
    this.apiAccess.get('stockchecks', 'ReconcileStockCheck', [{ key: 'stockCheckId', value: stockCheckVM.id }]).subscribe((res: any) => {
      
      this.apiAccess.get('stockchecks', 'stockchecklocations', [{ key: 'stockCheckId', value: stockCheckVM.id }]).subscribe((res: any) => {
        this.constants.Locations = res.sort((a, b) => a.description.localeCompare(b.description));

        this.refreshStockCheck(stockCheckVM.id).subscribe(res => {
          this.toastService.closeToast(toastRef);
          if (!suppressLoadedToast){ this.toastService.successToast('Stock check loaded');}

          if (reloadView) {
            // this.toastService.loadingToast('Updating...');
            this.constants.refreshPage.emit(true);
          }
        });
  
        //this.router.navigateByUrl('/reconcile');
      })
  
      // if (this.selections.stockCheck.hasSignoffImage) {
      //   this.selections.stockCheck.signoffImageURL = this.constants.buildSignOffImageURL(this.selections.stockCheck.id);
      // }
    })
  }




  archiveStockChecks(stockcheckIds: number[], supressLoadedToast:boolean):Observable<void> {

    const myObserverable:Observable<void> = new Observable(observer=>{
      
      let toastRef;
      if(!supressLoadedToast) {
        toastRef = this.toastService.loadingToast('Archiving stock check...');
      }
  
      this.apiAccess.post('stockchecks', 'Archive', stockcheckIds).subscribe((data:StockCheck[]) => {
        
        //----------------
        //success
        //----------------
        //this.stockCheckVMs = data;
        
        if(!supressLoadedToast) this.toastService.successToast('Stock checks archived');
        //this.toastService.destroyToast();
        if(toastRef){this.toastService.closeToast(toastRef)}
        
        observer.next();
        
        
      },e=>{
        //----------------
        // error
        //----------------
        
        if(!supressLoadedToast) this.toastService.errorToast('Failed to archive stock checks');
        if(toastRef){this.toastService.closeToast(toastRef)}
      },()=>{
        //----------------
        // finally
        //----------------
        this.constants.refreshPage.emit(true);
        observer.complete();

      });
    })

    return myObserverable;

  }


  unArchiveStockChecks(stockcheckIds: number[], supressLoadedToast:boolean):Observable<void> {

    const myObserverable:Observable<void> = new Observable(observer=>{
      
      let toastRef;
      if(!supressLoadedToast) {
        toastRef = this.toastService.loadingToast('Changing stock check status...');
      }
  
      this.apiAccess.post('stockchecks', 'UnArchive', stockcheckIds).subscribe((data:StockCheck[]) => {
        
        //----------------
        //success
        //----------------
        //this.stockCheckVMs = data;
        
        if(!supressLoadedToast) this.toastService.successToast('Stock checks set to current');
        //this.toastService.destroyToast();
        if(toastRef){this.toastService.closeToast(toastRef)}
        
        observer.next();
        
        
      },e=>{
        //----------------
        // error
        //----------------
        
        if(!supressLoadedToast) this.toastService.errorToast('Failed to set stock checks to current');
        if(toastRef){this.toastService.closeToast(toastRef)}
      },()=>{
        //----------------
        // finally
        //----------------
        this.constants.refreshPage.emit(true);
        observer.complete();

      });
    })

    return myObserverable;

  }

  
  deleteStockChecks(stockcheckIds: number[], supressLoadedToast:boolean):Observable<void> {

    const myObserverable:Observable<void> = new Observable(observer=>{
      
      let toastRef;
      if(!supressLoadedToast) {
        if (stockcheckIds.length > 1){
          toastRef = this.toastService.loadingToast('Deleting stock checks...');
        } else{
          toastRef = this.toastService.loadingToast('Deleting stock check...');
        }
      }
  
      this.apiAccess.post('stockchecks', 'Delete', stockcheckIds).subscribe((data:StockCheck[]) => {
        
        //----------------
        //success
        //----------------
        //this.stockCheckVMs = data;
        if (stockcheckIds.length > 1){
          if(!supressLoadedToast) this.toastService.successToast('Stock checks deleted');
        }
        else{
          if(!supressLoadedToast) this.toastService.successToast('Stock check deleted');
        }
        //this.toastService.destroyToast();
        if(toastRef){this.toastService.closeToast(toastRef)}
        
        observer.next();
        
        
      },e=>{
        //----------------
        // error
        //----------------
        
        if(!supressLoadedToast) this.toastService.errorToast('Stockcheck could not be deleted as it is use');
        if(toastRef){this.toastService.closeToast(toastRef)}
      },()=>{
        //----------------
        // finally
        //----------------
        this.constants.refreshPage.emit(true);
        observer.complete();

      });
    })

    return myObserverable;

  }

  refreshStockCheck(stockCheckId: number): Observable<void> {
    const myObserverable: Observable<void> = new Observable(observer => {
      let endpoint: string = `getStockChecks?isActive=${this.showActive}`;

      if (!this.showActive) { endpoint += `&fromDate=${this.fromDate}&toDate=${this.toDate}`; }

      endpoint += `&stockCheckId=${stockCheckId}`;

      this.apiAccess.get('stockchecks', endpoint).subscribe((data: StockCheck[]) => {
        let toReplace = this.stockCheckVMs.find(x => x.id === data[0].id);
        this.selections.stockCheck = data[0];
        if (this.selections.stockCheck.hasSignoffImage) {
          this.selections.stockCheck.signoffImageURL = this.constants.buildSignOffImageURL(this.selections.stockCheck.id);
        }
        Object.assign(toReplace, data[0]);
        this.refreshGridEmitter.emit();
        observer.next();
      }, e => { }, () => {
        observer.complete();
      });
    })

    return myObserverable;
  }

  generateRegionalAndTotalStats() {
    let totalStockChecksByRegion: { date: Date, region: string, total: number }[] = [];
    let totalApprovedStockChecksForRegion: { date: Date, region: string, total: number }[] = [];
    let totalCompleteStockChecksForRegion: { date: Date, region: string, total: number }[] = [];

    this.stockCheckVMs.forEach(sc => {
      if (sc.isTotal || sc.isRegional) return;
      if (sc.status === 'Reconciliation Approved') {
        if (totalApprovedStockChecksForRegion.find(scr => scr.region === sc.siteRegion && sc.date.toDateString() === scr.date.toDateString())) {
          totalApprovedStockChecksForRegion.find(scr => scr.region === sc.siteRegion && sc.date.toDateString() === scr.date.toDateString()).total += 1;
        } else {
          totalApprovedStockChecksForRegion.push({
            date: sc.date,
            region: sc.siteRegion,
            total: 1
          })
        }
      }

      if (sc.percentageComplete >= 1) {
        if (totalCompleteStockChecksForRegion.find(scr => scr.region === sc.siteRegion && sc.date.toDateString() === scr.date.toDateString())) {
          totalCompleteStockChecksForRegion.find(scr => scr.region === sc.siteRegion && sc.date.toDateString() === scr.date.toDateString()).total += 1;
        } else {
          totalCompleteStockChecksForRegion.push({
            date: sc.date,
            region: sc.siteRegion,
            total: 1
          })
        }
      }

      if (totalStockChecksByRegion.find(scr => scr.region === sc.siteRegion && sc.date.toDateString() === scr.date.toDateString())) {
        totalStockChecksByRegion.find(scr => scr.region === sc.siteRegion && sc.date.toDateString() === scr.date.toDateString()).total += 1;
      } else {
        totalStockChecksByRegion.push({
          date: sc.date,
          region: sc.siteRegion,
          total: 1
        })
      }
    })

    this.stockCheckVMs.forEach(sc => {
      const totalStockChecks: number = totalStockChecksByRegion.filter(x => x.date.toDateString() === sc.date.toDateString()).map(x => x.total).reduce((a, b) => a + b, 0);
      const totalCompleteStockChecks: number = totalCompleteStockChecksForRegion.filter(x => x.date.toDateString() === sc.date.toDateString()).map(x => x.total).reduce((a, b) => a + b, 0);
      const totalApprovedStockChecks: number = totalApprovedStockChecksForRegion.filter(x => x.date.toDateString() === sc.date.toDateString()).map(x => x.total).reduce((a, b) => a + b, 0);

      if (sc.isTotal) {
        sc.status = `${totalApprovedStockChecks}/${totalStockChecks} Approved`;
        sc.percentageCompleteExtra = `${totalCompleteStockChecks}/${totalStockChecks} Complete`;
      }

      if (sc.isRegional) {
        const totalStockChecksForRegion: number = totalStockChecksByRegion.find(x => x.region === sc.siteRegion && x.date.toDateString() === sc.date.toDateString())?.total;
        if (totalApprovedStockChecksForRegion.find(x => x.region === sc.siteRegion)) {
          sc.status = `${totalApprovedStockChecksForRegion.find(x => x.region === sc.siteRegion).total}/${totalStockChecksForRegion} Approved`;
        } else {
          sc.status = `0/${totalStockChecksForRegion} Approved`;
        }

        if (totalCompleteStockChecksForRegion.find(x => x.region === sc.siteRegion)) {
          sc.percentageCompleteExtra = `${totalCompleteStockChecksForRegion.find(x => x.region === sc.siteRegion && x.date.toDateString() === sc.date.toDateString())?.total}/${totalStockChecksForRegion} Complete`;
        } else {
          sc.percentageCompleteExtra = `0/${totalStockChecksForRegion} Complete`;
        }
      }
    })
  }
}