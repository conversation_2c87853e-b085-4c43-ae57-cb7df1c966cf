﻿using System;

namespace StockPulse.WebApi.ViewModel
{
    public class ScanThatIsADuplicate : Scan
    {
        
        public int OriginalScanId { get; set; } //the matching scan which this is a  duplicate of.   Should only ever be 1 for the same stockcheck, based on matching on either reg or vin
        public string OriginalLocationDescription { get; set; }
        public string OriginalScannedBy { get; set; }
        public DateTime OriginalScannedDate { get; set; }

    }
}
