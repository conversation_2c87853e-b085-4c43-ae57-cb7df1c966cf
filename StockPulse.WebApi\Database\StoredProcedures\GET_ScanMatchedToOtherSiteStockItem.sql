  
CREATE OR ALTER PROCEDURE [dbo].[GET_ScanMatchedToOtherSiteStockItem]  
(  
    @StockCheckId INT = NULL,  
    @UserId INT = NULL  
)  
AS  
BEGIN  
  
SET NOCOUNT ON;  
  
  
DECLARE @accessVar INT;    
DECLARE @isRegional INT;    
DECLARE @isTotal INT;    
DECLARE @StockCheckDate DateTime;  
DECLARE @DivisionId INT = NULL;  
DECLARE @DealerGroupId INT = NULL;  
DECLARE @SCId INT = NULL;  
  
  
IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END


  
SELECT   
@isRegional = IsRegional,   
@isTotal = IsTotal,   
@StockCheckDate = Date   
FROM StockChecks    
WHERE StockChecks.Id = @StockCheckId  
    

    
IF @isRegional = 0 AND @isTotal = 0    
    
    BEGIN    
  
 SET @SCId = @StockCheckId;  
    
  
    END    
    
ELSE IF @isRegional = 1 AND @isTotal = 0    
    
    BEGIN    
    
    
    SET @DivisionId = (SELECT DivisionId FROM StockChecks    
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId    
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id    
                        WHERE StockChecks.Id = @StockCheckId AND StockChecks.IsRegional = 1)    
    
    END    
    
ELSE IF @isRegional = 0 AND @isTotal = 1    
    
    BEGIN    
    
        
    SET @DealerGroupId = (SELECT DealerGroupId FROM StockChecks    
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId    
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id    
                        WHERE StockChecks.Id = @StockCheckId)    
    
    
    END   
  
  
  
        SELECT Scans.Id as ScanId 
            ,Locations.[Description] AS locationDescription  
            ,Users.Name AS scannerName  
            ,Scans.[UserId]  
            ,scans.[StockCheckId] AS StockcheckId  
            ,[LastEditedById]  
            ,[LocationId]  
            ,scans.[UnknownResolutionId]  
            ,[StockItemId]  
            ,scans.[ReconcilingItemId] AS ReconcilingItemId  
            ,[LastEditedDateTime]  
            ,[RegConfidence]  
            ,[VinConfidence]
            --,[IsEdited]  
            ,Scans.[Longitude]  
            ,Scans.[Latitude]  
            ,[ScanDateTime]  
            ,scans.[Comment]  as ScanComment 
            ,scans.[Reg]  as ScanReg
            ,scans.[Vin]  as ScanVin
            ,Scans.[Description]  as ScanDescription
            ,[CoordinatesJSON]  
            ,[HasVinImage]  
            ,scans.[IsDuplicate]  
   ,SI.[Description] AS otherSiteDescription  
   ,SI.[Reference] AS matchingStockItemReference  
   ,SI.[Comment] AS matchingStockItemComment  
   ,SI.[StockType] AS matchingStockItemType  
   ,SI.[Description] AS matchingStockItemDescription
   
   ,stockCheckSite.[Description] AS MatchingSiteDescription  
   ,stockCheckSite.[Id] AS MatchingSiteId  
            ,Sites.Description AS SiteName  
            ,Sites.Longitude as StockCheckLongitude
            ,Sites.Latitude as StockCheckLatitude,

            CASE
                WHEN scans.IsRegEditedOnWeb = 1 THEN 'Web app'
                WHEN scans.IsRegEditedOnDevice = 1 THEN 'Mobile app'
                ELSE NULL
            END AS RegEditStatus,
            CASE
                WHEN scans.IsVinEditedOnWeb = 1 THEN 'Web app'
                WHEN scans.IsVinEditedOnDevice = 1 THEN 'Mobile app'
                ELSE NULL
            END AS VinEditStatus
        FROM [dbo].[Scans]  scans 
        INNER JOIN Users ON Users.Id=Scans.UserId  
        INNER JOIN Locations ON Locations.Id=Scans.LocationId  
        INNER JOIN StockItems AS SI ON SI.Id = Scans.StockItemId  
        INNER JOIN StockChecks AS SC ON SC.Id=Scans.StockCheckId  
        INNER JOIN Sites ON Sites.Id=SC.SiteId  
        INNER JOIN StockChecks as stockItemStockCheck on stockItemStockCheck.Id = si.StockCheckId
        INNER JOIN Sites stockCheckSite on stockCheckSite.Id = stockItemStockCheck.SiteId
  INNER JOIN Divisions AS D ON D.Id=Sites.DivisionId  
  INNER JOIN DealerGroup AS DG ON D.DealerGroupId=DG.Id  
        WHERE   
  --Scans.StockCheckId = @StockCheckId  
        SI.StockCheckId <> Scans.StockCheckId  
  AND SC.Id = ISNULL(@SCId, SC.Id)  
 AND SC.Date = @StockCheckDate  
 AND D.Id = ISNULL(@DivisionId, D.Id)  
 AND DG.Id = ISNULL(@DealerGroupId, DG.Id)  
    

  
END  
  