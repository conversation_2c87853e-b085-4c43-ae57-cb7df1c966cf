﻿

/****** Object:  StoredProcedure [dbo].[CREATE_UserSites]    Script Date: 30/04/2021 19:07:42 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

  
  
CREATE OR ALTER PROCEDURE [dbo].[CREATE_UserSites]  
(  
    @UserId int,  
    @NewUserId int,  
 @SiteId int,  
 @SiteString varchar(max)  
)  
AS  
BEGIN  
  
  
SET NOCOUNT ON  
  
DECLARE @UserDealerGroupId INT;
DECLARE @NewUserDealerGroupId INT;
SELECT @UserDealerGroupId = DealerGroupId FROM Users WHERE Id = @UserId
SELECT @NewUserDealerGroupId = DealerGroupId FROM Users WHERE Id = @NewUserId
IF (@NewUserDealerGroupId != @UserDealerGroupId)
BEGIN 
    RETURN
END
  

DELETE US  
FROM UserSites AS US   
WHERE US.UserId = @NewUserId   
AND Us.SiteId NOT IN (SELECT * FROM STRING_SPLIT(@SiteString, ','))  
  
  
INSERT INTO UserSites (UserId,SiteId,IsDefault)  

SELECT @NewUserId As UserId, NewSites.value As SiteId, 0 As IsDefault
FROM STRING_SPLIT(@SiteString, ',') AS NewSites 
WHERE NewSites.value NOT IN (SELECT SiteId FROM UserSites WHERE UserId = @NewUserId )




UPDATE UserSites  
SET IsDefault = 0  
WHERE UserId = @NewUserId 
  
UPDATE UserSites  
SET IsDefault = 1  
WHERE UserId = @NewUserId AND SiteId = @SiteId  
  
  
  
  
   
END  
  
GO


