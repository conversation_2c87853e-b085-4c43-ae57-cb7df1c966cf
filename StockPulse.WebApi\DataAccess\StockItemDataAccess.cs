﻿using Dapper;
using StockPulse.WebApi.Dapper;
using StockPulse.Model.Import;
using StockPulse.WebApi.Service;
using StockPulse.WebApi.ViewModel;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;

namespace StockPulse.WebApi.DataAccess
{
    public interface IStockItemDataAccess
    {

        Task<IEnumerable<ViewModel.StockItem>> GetStockItems(int stockcheckId, int userId);
        Task<IEnumerable<StockItemWithLocation>> GetStocksWithLocation(int stockcheckId, int userId);
        Task<IEnumerable<StockItemWithStockType>> GetStocksWithStockType(int stockcheckId, int userId);
        //Task<IEnumerable<StockItemWithResolution>> GetStockWithResolution(int stockcheckId, int userId);

        Task<IEnumerable<StockLoadReportSummary>> GetStockReport(int stockcheckId, int userId);
        Task<IEnumerable<ResolutionBucket>> GetResolutionBucket(int stockcheckId, int userId);
        Task<IEnumerable<StockItemMatchedToRecItem>> GetStockItemMatchedToRecItem(int stockcheckId, int userId);
        Task<IEnumerable<StockItemWithScan>> GetStockItemsWithScan(int stockcheckId, int userId);
        Task<IEnumerable<StockItemMatchedToRecItem>> GetMissingMatched(int stockcheckId, int userId);
        Task<IEnumerable<StockItemMatchedToRecItem>> GetStockVehicles(int stockcheckId, int userId);
        
        Task<IEnumerable<StockItemWithResolution>> GetStockItemsWithResolution(int stockcheckId, int userId);
        Task<IEnumerable<StockItemMatchedToOtherSiteScan>> GetStockItemMatchedToOtherSiteScan(int stockcheckId, int userId);
        Task<int> DeleteAllDMSStockItems(int stockcheckId, int userId);
        Task<StockItemFullDetail> GetStockItemFullDetailAcrossSites(int stockItemId, int userId);
        Task<IEnumerable<int>> GetStockItemIdLinkedToScan(int scanId);
        Task<int> GetTotalItems(int stockItemId, int userId);
        Task<int> GetAgencyStock(int stockItemId, int userId);
        Task<int> AddMissingResolutionAsync(Resolution missingResolutionVM, int userId);
        Task UpdateStockItemWithMissingResolutions(int stockCheckId, int userId, int stockItemId, int missingResolutionId, IDbConnection db, IDbTransaction tran);
        Task UpdateMissingResolution(Resolution missingResolutionVM, int userId, IDbConnection db, IDbTransaction tran);
        Task<int> AddMissingResolutionImage(string fileName, int missingResolutionId, int stockCheckId, int userId, IDbConnection db, IDbTransaction tran);
        Task DeleteMissingResolutionImage(int missingResolutionId, int imageId, int stockCheckId, int userId, IDbConnection db, IDbTransaction tran);
        //Task AddStockItem(ViewModel.StockItem newStockItem, int stockCheckId, int userId);
        Task AddStockItems(List<ViewModel.StockItem> newStockItems, int stockCheckId, int userId, int fileImportId);
        Task<StockConsignmentVM> GetStockConsignment(int stockcheckId, int userId);
        Task<SearchResult> GetGlobalSearchResults(int userId, string reg, string vin, bool requireAndMatch);
        Task<IEnumerable<MatchItem>> GetMatchItems(int stockCheckId, int userId);
        Task<IEnumerable<MatchItem>> GetMatchItemsOtherSites(int stockCheckId, int userId);
        Task DeleteMissingResolution(int resolutionId, int stockCheckId, int userId);
        Task<IEnumerable<StockItemThatIsADuplicate>> GetStockItemsThatAreDuplicates(int stockcheckId, int userId);
        Task<IEnumerable<int>> GetMissingResolutionImageIds(int resolutionId);
        Task<int> DeleteStockItem(int stockcheckId, int userId, int itemId);
        Task<int> GetTotalStockItemsCount(List<int> stockcheckIds, int userId);
        Task<int> DeleteAllAgencyStockItems(int stockcheckId, int userId);
        Task<IEnumerable<StockItem>> GetStockItemsForSite(int siteId);
    }

    public class StockItemDataAccess : IStockItemDataAccess
    {
        private readonly IDapper dapper;
        private readonly ISiteService siteService;
        private readonly IStockCheckDataAccess stockCheckDataAccess;

        public StockItemDataAccess(IDapper dapper, IStockCheckDataAccess stockCheckDataAccess, ISiteService siteService)
        {
            this.dapper = dapper;
            this.stockCheckDataAccess = stockCheckDataAccess;
            this.siteService = siteService;
        }

        public async Task<IEnumerable<ViewModel.StockItem>> GetStockItems(int stockcheckId, int userId)
        {
            var paramList = new DynamicParameters();

            paramList.Add("StockCheckId", stockcheckId);
            paramList.Add("UserId", userId);

            return await dapper.GetAllAsync<ViewModel.StockItem>("dbo.GET_StockItems", paramList, System.Data.CommandType.StoredProcedure);
        }
        public async Task<IEnumerable<ViewModel.StockItemThatIsADuplicate>> GetStockItemsThatAreDuplicates(int stockcheckId, int userId)
        {
            var paramList = new DynamicParameters();

            paramList.Add("StockCheckId", stockcheckId);
            paramList.Add("UserId", userId);

            return await dapper.GetAllAsync<ViewModel.StockItemThatIsADuplicate>("dbo.GET_StockItemsThatAreDuplicated", paramList, System.Data.CommandType.StoredProcedure);
        }




        public async Task<IEnumerable<StockItemWithLocation>> GetStocksWithLocation(int stockcheckId, int userId)
        {
            var paramList = new DynamicParameters();

            paramList.Add("StockCheckId", stockcheckId);
            paramList.Add("UserId", userId);

            return await dapper.GetAllAsync<StockItemWithLocation>("dbo.GET_StocksWithLocation", paramList, System.Data.CommandType.StoredProcedure);
        }

        public async Task<IEnumerable<StockItemWithStockType>> GetStocksWithStockType(int stockcheckId, int userId)
        {
            var paramList = new DynamicParameters();

            paramList.Add("StockCheckId", stockcheckId);
            paramList.Add("UserId", userId);

            return await dapper.GetAllAsync<StockItemWithStockType>("dbo.GET_StocksWithStockType", paramList, System.Data.CommandType.StoredProcedure);
        }

        //public async Task<IEnumerable<StockItemWithResolution>> GetStockWithResolution(int stockcheckId, int userId)
        //{
        //    var paramList = new DynamicParameters();

        //    paramList.Add("StockCheckId", stockcheckId);
        //    paramList.Add("UserId", userId);

        //    return await dapper.GetAllAsync<StockItemWithResolution>("dbo.GET_StockWithResolution", paramList, System.Data.CommandType.StoredProcedure);
        //}

        public async Task<IEnumerable<StockItemWithResolution>> GetStockItemsWithResolution(int stockcheckId, int userId)
        {
            var paramList = new DynamicParameters();
            paramList.Add("StockCheckId", stockcheckId);
            paramList.Add("UserId", userId);

            return await dapper.GetAllAsync<StockItemWithResolution>("dbo.GET_StockItemsWithResolution", paramList, System.Data.CommandType.StoredProcedure);
        }

        public async Task<IEnumerable<StockLoadReportSummary>> GetStockReport(int stockcheckId, int userId)
        {
            var paramList = new DynamicParameters();
            paramList.Add("StockCheckId", stockcheckId);
            paramList.Add("UserId", userId);

            return await dapper.GetAllAsync<StockLoadReportSummary>("dbo.GET_StockReport", paramList, System.Data.CommandType.StoredProcedure);
        }

       

        public async Task<IEnumerable<ResolutionBucket>> GetResolutionBucket(int stockcheckId, int userId)
        {
            var paramList = new DynamicParameters();
            paramList.Add("StockCheckId", stockcheckId);
            paramList.Add("UserId", userId);

            return await dapper.GetAllAsync<ResolutionBucket>("dbo.GET_ResolutionBucket", paramList, System.Data.CommandType.StoredProcedure);
        }

        public async Task<IEnumerable<StockItemMatchedToRecItem>> GetStockItemMatchedToRecItem(int stockcheckId, int userId)
        {
            var paramList = new DynamicParameters();
            paramList.Add("StockCheckId", stockcheckId);
            paramList.Add("UserId", userId);

            return await dapper.GetAllAsync<StockItemMatchedToRecItem>("dbo.GET_StockItemMatchedToRecItem", paramList, System.Data.CommandType.StoredProcedure);
        }

        public async Task<IEnumerable<StockItemWithScan>> GetStockItemsWithScan(int stockcheckId, int userId)
        {
            var paramList = new DynamicParameters();
            paramList.Add("StockCheckId", stockcheckId);
            paramList.Add("UserId", userId);

            return await dapper.GetAllAsync<StockItemWithScan>("dbo.GET_StockItemsWithScan", paramList, System.Data.CommandType.StoredProcedure);
        }

        
        

        public async Task<StockItemFullDetail> GetStockItemFullDetailAcrossSites(int stockItemId, int userId)
        {
            var paramList = new DynamicParameters();
            paramList.Add("StockItemId", stockItemId);
            paramList.Add("UserId", userId);
            paramList.Add("SkipAuthCheck", 1);

            return await dapper.GetAsync<StockItemFullDetail>("dbo.GET_StockItemFullDetail", paramList, System.Data.CommandType.StoredProcedure);
        }


        public async Task<int> GetTotalItems(int stockCheckId, int userId)
        {
            var paramList = new DynamicParameters();
            
            paramList.Add("StockCheckId", stockCheckId);
            paramList.Add("UserId", userId);

            return await dapper.GetAsync<int>("dbo.GET_TotalItems", paramList, System.Data.CommandType.StoredProcedure);
        }

        public async Task<int> GetAgencyStock(int stockCheckId, int userId)
        {
            var paramList = new DynamicParameters();
            paramList.Add("StockCheckId", stockCheckId);
            paramList.Add("UserId", userId);

            return await dapper.GetAsync<int>("dbo.GET_AgencyStock", paramList, System.Data.CommandType.StoredProcedure);
        }


        public async Task<IEnumerable<StockItemMatchedToRecItem>> GetMissingMatched(int stockcheckId, int userId)
        {
            var paramList = new DynamicParameters();
            paramList.Add("StockCheckId", stockcheckId);
            paramList.Add("UserId", userId);

            return await dapper.GetAllAsync<StockItemMatchedToRecItem>("dbo.GET_MissingMatched", paramList, System.Data.CommandType.StoredProcedure);
        }

        public async Task<IEnumerable<StockItemMatchedToRecItem>> GetStockVehicles(int stockcheckId, int userId)
        {
            var paramList = new DynamicParameters();
            paramList.Add("StockCheckId", stockcheckId);
            paramList.Add("UserId", userId);

            return await dapper.GetAllAsync<StockItemMatchedToRecItem>("dbo.GET_StockVehicles", paramList, System.Data.CommandType.StoredProcedure);
        }

        //public async Task<IEnumerable<RepeatMissing>> GetRepeatMissing(int stockcheckId)
        //{
        //    var paramList = new DynamicParameters();
        //    paramList.Add("StockCheckId", stockcheckId);

        //    return await dapper.GetAllAsync<RepeatMissing>("dbo.GET_RepeatMissing", paramList, System.Data.CommandType.StoredProcedure);
        //}

        public async Task<IEnumerable<int>> GetStockItemIdLinkedToScan(int scanId)
        {
            var paramList = new DynamicParameters();

            paramList.Add("ScanId", scanId);
            return await dapper.GetAllAsync<int>("dbo.GET_StockItemIdLinkedToScan", paramList, System.Data.CommandType.StoredProcedure);
        }

        public async Task<IEnumerable<StockItemMatchedToOtherSiteScan>> GetStockItemMatchedToOtherSiteScan(int stockcheckId, int userId)
        {
            var paramList = new DynamicParameters();
            paramList.Add("StockCheckId", stockcheckId);
            paramList.Add("UserId", userId);

            return await dapper.GetAllAsync<StockItemMatchedToOtherSiteScan>("dbo.GET_StockItemMatchedToOtherSiteScan", paramList, System.Data.CommandType.StoredProcedure);
        }

        public async Task<int> DeleteAllDMSStockItems(int stockcheckId, int userId)
        {
            var paramList = new DynamicParameters();
            paramList.Add("StockCheckId", stockcheckId);
            paramList.Add("UserId", userId);

            return await dapper.ExecuteAsync("dbo.DELETE_AllDMSStockItems", paramList, System.Data.CommandType.StoredProcedure);
        }

        public async Task<int> DeleteAllAgencyStockItems(int stockcheckId, int userId)
        {
            var paramList = new DynamicParameters();
            paramList.Add("StockCheckId", stockcheckId);
            paramList.Add("UserId", userId);

            return await dapper.ExecuteAsync("dbo.DELETE_AllAgencyStockItems", paramList, System.Data.CommandType.StoredProcedure);
        }

        public async Task<int> AddMissingResolutionAsync(Resolution missingResolutionVM, int userId)
        {
            var paramList = new DynamicParameters();
            paramList.Add("StockCheckId", missingResolutionVM.StockCheckId);
            paramList.Add("UserId", userId);
            paramList.Add("ResolutionTypeId", missingResolutionVM.ResolutionTypeId);
            paramList.Add("ResolutionDate", DateTime.UtcNow);
            paramList.Add("Notes", missingResolutionVM.Notes);
            paramList.Add("IsResolved", missingResolutionVM.IsResolved);
            paramList.Add("StockItemId", missingResolutionVM.OriginalItemId);

            return await dapper.InsertAsync<int>("dbo.ADD_MissingResolution", paramList, CommandType.StoredProcedure);
        }

        public async Task UpdateStockItemWithMissingResolutions(int stockCheckId, int userId, int stockItemId, int missingResolutionId, IDbConnection db, IDbTransaction tran)
        {
            var paramList = new DynamicParameters();
            paramList.Add("StockCheckId", stockCheckId);
            paramList.Add("UserId", userId);
            paramList.Add("MissingResolutionId", missingResolutionId);
            paramList.Add("StockItemId", stockItemId);

            await dapper.InsertAsync<int>("dbo.UPDATE_StockWithMissingResolutions", paramList, tran, db, CommandType.StoredProcedure);
        }

        public async Task UpdateMissingResolution(Resolution missingResolutionVM, int userId, IDbConnection db, IDbTransaction tran)
        {
            var paramList = new DynamicParameters();
            paramList.Add("StockCheckId", missingResolutionVM.StockCheckId);
            paramList.Add("UserId", userId);
            paramList.Add("MissingResolutionId", missingResolutionVM.ResolutionId);
            paramList.Add("ResolutionTypeId", missingResolutionVM.ResolutionTypeId);
            paramList.Add("ResolutionDate", DateTime.UtcNow);
            paramList.Add("Notes", missingResolutionVM.Notes);
            paramList.Add("IsResolved", missingResolutionVM.IsResolved);

            await dapper.InsertAsync<int>("dbo.UPDATE_MissingResolution", paramList, tran, db, CommandType.StoredProcedure);
        }

        public async Task<int> AddMissingResolutionImage(string fileName, int missingResolutionId, int stockCheckId, int userId, IDbConnection db, IDbTransaction tran)
        {
            var paramList = new DynamicParameters();
            paramList.Add("FileName", fileName);
            paramList.Add("StockCheckId", stockCheckId);
            paramList.Add("UserId", userId);
            paramList.Add("MissingResolutionId", missingResolutionId);

            return await dapper.InsertAsync<int>("dbo.ADD_MissingResolutionImage", paramList, tran, db, CommandType.StoredProcedure);
        }

        public async Task DeleteMissingResolutionImage(int missingResolutionId, int imageId, int stockCheckId, int userId, IDbConnection db, IDbTransaction tran)
        {
            var paramList = new DynamicParameters();
            paramList.Add("StockCheckId", stockCheckId);
            paramList.Add("UserId", userId);
            paramList.Add("MissingResolutionId", missingResolutionId);
            paramList.Add("ImageId", imageId);

            await dapper.InsertAsync<int>("dbo.DELETE_MissingResolutionImage", paramList, tran, db, CommandType.StoredProcedure);
        }

        public async Task DeleteMissingResolution(int resolutionId, int stockCheckId, int userId)
        {
            var paramList = new DynamicParameters();
            paramList.Add("StockCheckId", stockCheckId);
            paramList.Add("UserId", userId);
            paramList.Add("MissingResolutionId", resolutionId);

            await dapper.ExecuteAsync("dbo.DELETE_MissingResolution", paramList, CommandType.StoredProcedure);
        }

        public async Task<IEnumerable<int>> GetMissingResolutionImageIds(int resolutionId)
        {
            var paramList = new DynamicParameters();
            paramList.Add("ResolutionId", resolutionId);
            return await dapper.GetAllAsync<int>("dbo.GET_MissingResolutionImageIds", paramList, System.Data.CommandType.StoredProcedure);


        }

        //public async Task AddStockItem(ViewModel.StockItem newStockItem, int stockCheckId, int userId)
        //{
        //    var paramList = new DynamicParameters();

        //    paramList.Add("Reg", newStockItem.Reg);
        //    paramList.Add("Vin", newStockItem.Vin);
        //    paramList.Add("Description", newStockItem.Description);
        //    paramList.Add("Dis", newStockItem.DIS);
        //    paramList.Add("GroupDIS", newStockItem.GroupDIS);
        //    paramList.Add("Branch", newStockItem.Branch);
        //    paramList.Add("StockType", newStockItem.StockType);
        //    paramList.Add("Comment", newStockItem.Comment);
        //    paramList.Add("Reference", newStockItem.Reference);
        //    paramList.Add("StockValue", newStockItem.StockValue);
        //    paramList.Add("StockCheckId", stockCheckId);
        //    paramList.Add("UserId", userId);

        //    await dapper.ExecuteAsync("dbo.INSERT_StockItem", paramList, CommandType.StoredProcedure);
        //}

        public async Task AddStockItems(List<ViewModel.StockItem> newStockItems, int stockCheckId, int userId, int fileImportId)
        {

                var paramList = new DynamicParameters(0);                
                paramList.Add("StockCheckId", stockCheckId);
                paramList.Add("UserId", userId);

                DataTable dt = new DataTable();

                dt.Columns.Add("ScanId");
                dt.Columns.Add("ReconcilingItemId");
                dt.Columns.Add("MissingResolutionId");
                dt.Columns.Add("StockCheckId");
                dt.Columns.Add("SourceReportId");

                dt.Columns.Add("Reg");
                dt.Columns.Add("Vin");
                dt.Columns.Add("Description");
                dt.Columns.Add("DIS");
                dt.Columns.Add("GroupDIS");
                dt.Columns.Add("Branch");
                dt.Columns.Add("Comment");
                dt.Columns.Add("StockType");
                dt.Columns.Add("Reference");
                dt.Columns.Add("StockValue");
                dt.Columns.Add("Flooring");

                dt.Columns.Add("IsAgencyStock");
                dt.Columns.Add("FileImportId");

                List<string> unknownSiteNames = new List<string>();
                List<string> sitesWithoutActiveStockChecks = new List<string>();

            try
            {
                bool isMultiSite = newStockItems.Any(x => x.Site != null && x.Site != string.Empty);

                // Items need to be assigned to the stockcheck for their respective sites
                if (isMultiSite)
                {
                    // Get a list of site names that user has access to & a list of all sites for dealergroup
                    IEnumerable<ViewModel.SiteVM> sitesForUser = await siteService.GetSites(userId);
                    IEnumerable<ViewModel.SiteVM> sitesForDealerGroup = await siteService.GetAllSites(userId);
                    IEnumerable<SiteDescriptionDictionary> sitesForDealerGroupFromDictionary = await siteService.GetAllSitesFromDictionary(userId);

                    List<string> siteNamesForUser = sitesForUser.Select(x => x.Description.ToUpper()).ToList();
                    List<string> siteNamesForDealerGroup = sitesForDealerGroup.Select(x => x.Description.ToUpper()).ToList();

                    IEnumerable<StockCheckVM> stockchecks = await stockCheckDataAccess.GetStockChecksOverview(userId, true);

                    foreach (var item in newStockItems)
                    {

                        int stockCheckIdForSite;
                        string uniqueVehicleIdentifier = item.Reg != "" ? $"reg {item.Reg}" : $"VIN {item.Vin}";

                        if (item.Site != null) { item.Site = item.Site.TrimEnd(' ', '\r'); };

                        if (item.Site == null)
                        {
                            throw new Exception($"Item with {uniqueVehicleIdentifier} does not contain a site name.");
                        }
                        else if (!siteNamesForDealerGroup.Contains(item.Site.ToUpper()) && !sitesForDealerGroupFromDictionary.Select(x => x.Description).Contains(item.Site.ToUpper()))
                        {
                            stockCheckIdForSite = 0;
                            unknownSiteNames.Add(item.Site);
                        }
                        else if (!siteNamesForUser.Contains(item.Site.ToUpper()) && (siteNamesForDealerGroup.Contains(item.Site.ToUpper()) && sitesForDealerGroupFromDictionary.Select(x => x.Description).Contains(item.Site.ToUpper())))
                        {
                            throw new Exception($"Could not upload the report as you do not have access to the site {item.Site}.");
                        }
                        else
                        {
                            int siteIdToUse;

                            var siteToUse = sitesForDealerGroup.Where(x => x.IsActive == true).FirstOrDefault(x => x.Description.ToUpper() == item.Site.ToUpper());
                            if (siteToUse != null)
                            {
                                siteIdToUse = siteToUse.Id;
                            }
                            else
                            {
                                siteIdToUse = sitesForDealerGroupFromDictionary.FirstOrDefault(x => x.Description == item.Site.ToUpper()).SiteId;
                            }

                            var stockCheckForSite = stockchecks.Where(x => x.SiteId == siteIdToUse).FirstOrDefault();

                            if (stockCheckForSite == null)
                            {
                                // Instead of throwing exception immediately, add to list of sites without stock checks
                                sitesWithoutActiveStockChecks.Add(item.Site);
                                // Use 0 as a placeholder, we'll check this list before executing the stored procedure
                                stockCheckIdForSite = 0;
                            }
                            else
                            {
                                stockCheckIdForSite = stockCheckForSite.Id;
                            }
                        }

                        dt.Rows.Add(
                            null,
                            null,
                            null,
                            stockCheckIdForSite,
                            1,
                            item.Reg,
                            item.Vin,
                            item.Description,
                            item.DIS,
                            item.GroupDIS,
                            item.Branch,
                            item.Comment,
                            item.StockType,
                            item.Reference,
                            item.StockValue,
                            item.Flooring,
                            item.IsAgencyStock,
                            fileImportId
                            );
                    }
                }
                else
                {
                    foreach (var item in newStockItems)
                    {

                        dt.Rows.Add(
                            null,
                            null,
                            null,
                            stockCheckId,
                            1,
                            item.Reg,
                            item.Vin,
                            item.Description,
                            item.DIS,
                            item.GroupDIS,
                            item.Branch,
                            item.Comment,
                            item.StockType,
                            item.Reference,
                            item.StockValue,
                            item.Flooring,
                            item.IsAgencyStock,
                            fileImportId
                            );
                    }
                }

            }
            catch (Exception e)
            {
                throw new Exception(e.Message);
            }

            if (unknownSiteNames.Count() > 0)
            {
                throw new Exception($"Could not find the following site names: '{string.Join(", ", unknownSiteNames.Distinct())}'. Please double check that the spelling matches a valid site in StockPulse or a mapping has been created.");
            }

            if (sitesWithoutActiveStockChecks.Count > 0)
            {
                throw new Exception($"No active stock check for the following sites: {string.Join(", ", sitesWithoutActiveStockChecks.Distinct())}.");
            }

            paramList.Add("StockItems", dt, DbType.Object);

            await dapper.InsertAsync<int>("dbo.BULKADD_StockItems", paramList, System.Data.CommandType.StoredProcedure);
            
        }

        public async Task<StockConsignmentVM> GetStockConsignment(int stockcheckId, int userId)
        {
            var paramList = new DynamicParameters();
            paramList.Add("StockCheckId", stockcheckId);
            paramList.Add("UserId", userId);

            return await dapper.GetAsync<StockConsignmentVM>("dbo.GET_StockConsignment", paramList, System.Data.CommandType.StoredProcedure);

        }

        public async Task<SearchResult> GetGlobalSearchResults(int userId, string reg, string vin, bool requireAndMatch)
        {
            var paramList = new DynamicParameters();
            paramList.Add("UserId", userId);
            paramList.Add("Reg", reg);
            paramList.Add("Vin", vin);
            paramList.Add("RequireAndMatch", requireAndMatch);

            return await dapper.GetMultipleAsync("dbo.GET_GlobalSearchResults", paramList, System.Data.CommandType.StoredProcedure);

        }


        public async Task<IEnumerable<MatchItem>> GetMatchItems(int stockCheckId, int userId)
        {
            return await dapper.GetAllAsync<MatchItem>("dbo.GET_StockItemMatchItems", new DynamicParameters(new { stockCheckId, userId }), CommandType.StoredProcedure);
        }
        public async Task<IEnumerable<MatchItem>> GetMatchItemsOtherSites(int stockCheckId, int userId)
        {
            return await dapper.GetAllAsync<MatchItem>("dbo.GET_StockItemMatchItemsOtherSites", new DynamicParameters(new { stockCheckId, userId }), CommandType.StoredProcedure);
        }

        public async Task<int> DeleteStockItem(int stockcheckId, int userId, int itemId)
        {
            var paramList = new DynamicParameters();

            paramList.Add("StockCheckId", stockcheckId);
            paramList.Add("UserId", userId);
            paramList.Add("ItemId", itemId);

            return await dapper.ExecuteAsync("dbo.DELETE_StockItem", paramList, System.Data.CommandType.StoredProcedure);
        }

        public async Task<int> GetTotalStockItemsCount(List<int> stockCheckIds, int userId)
        {
            var paramList = new DynamicParameters();
            paramList.Add("StockCheckIds", string.Join(',', stockCheckIds));
            paramList.Add("UserId", userId);
            return await dapper.GetAsync<int>("dbo.Get_TotalStockItemsCount", paramList, CommandType.StoredProcedure);
        }

        public async Task<IEnumerable<StockItem>> GetStockItemsForSite(int siteId)
        {
            var paramList = new DynamicParameters();

            paramList.Add("SiteId", siteId);

            return await dapper.GetAllAsync<StockItem>("dbo.GET_StockItemsForSite", paramList, CommandType.StoredProcedure);
        }

    }
}
