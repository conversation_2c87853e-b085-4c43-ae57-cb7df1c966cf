{

  "ConnectionStrings": {
    "DefaultConnection": "Server=tcp:cphius.database.windows.net,1433; Initial Catalog=stockpulseDev; Persist Security Info=False; User ID=DBMigrationUser; Password=****************; MultipleActiveResultSets=False; Encrypt=True; TrustServerCertificate=False; Connection Timeout=30;"
  },

  //FOR MIGTATIONS USE: DBMigrationUser - password in dashlane


  "BlobStorage": {
    "StorageAccount": "cphius",
    "StorageAccountKey": "****************************************************************************************",

    //Dev -- the readonly key is incorrect
    "FilePath": "https://cphius.blob.core.windows.net/stockpulseimages-dev/",
    "ReadOnlyKey": "sp=r&st=2023-11-03T15:25:06Z&se=2053-11-29T23:25:06Z&sv=2022-11-02&sr=c&sig=9QlFXlYY3mjY4QWycbRGtuZuPJxPVKCPvwLEOqe2HL4%3D"
  },


  "WebApp": {
    "URL": "https://stockpulsedev.cphi.co.uk/",
    "Env": "DEV", // LOCAL, DEV, TEST, PROD
    "Country": "US" //UK, US
  }
}
