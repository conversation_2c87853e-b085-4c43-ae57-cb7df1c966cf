﻿  
  
CREATE OR ALTER PROCEDURE [dbo].[GET_StockItems]  
(  
     @StockCheckId INT = NULL,  
     @UserId INT = NULL  
)  
AS  
BEGIN  
  
SET NOCOUNT ON;  
  
DECLARE @isRegional INT;  
DECLARE @isTotal INT;  
DECLARE @StockCheckDate DateTime;  
DECLARE @DivisionId INT = NULL;  
DECLARE @DealerGroupId INT = NULL;  
DECLARE @SCId INT = NULL;  
  
  
IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END

  
SELECT   
@isRegional = IsRegional,   
@isTotal = IsTotal,   
@StockCheckDate = Date   
FROM StockChecks    
WHERE StockChecks.Id = @StockCheckId  
  
  

  
 IF @isRegional = 0 AND @isTotal = 0  
  
  BEGIN  
  
  SET @SCId = @StockCheckId;  
  
  END  
  
 ELSE IF @isRegional = 1 AND @isTotal = 0  
  
  BEGIN  
    
  SET @DivisionId = (SELECT DivisionId FROM StockChecks  
       INNER JOIN Sites ON Sites.Id=StockChecks.SiteId  
       INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id  
       WHERE StockChecks.Id = @StockCheckId AND StockChecks.IsRegional = 1)  
    
  
  END  
  
 ELSE IF @isRegional = 0 AND @isTotal = 1  
  
  BEGIN  
  
    
  SET @DealerGroupId = (SELECT DealerGroupId FROM StockChecks  
       INNER JOIN Sites ON Sites.Id=StockChecks.SiteId  
       INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id  
       WHERE StockChecks.Id = @StockCheckId)  
  
  
  END;  
  

  
  SELECT   
  SI.Id as StockItemId,
  SI.Reg,
  SI.Vin,
  SI.[Description],
  SI.DIS,
  SI.GroupDIS,
  si.Branch,
  si.StockType,
  si.Comment,
  si.Reference,
  si.StockValue,
  si.Flooring,   
  Sites.Description AS SiteName,
  SI.IsAgencyStock,
  CASE
	WHEN si.IsDuplicate = 1 THEN 'Duplicate'
	WHEN sca.Id IS NOT NULL AND sca.StockCheckId <> sca.StockCheckId THEN 'MatchedToOtherSite'
	WHEN sca.Id IS NOT NULL AND sca.StockCheckId = sca.StockCheckId THEN  'MatchedToStockOrScan'
	WHEN si.ReconcilingItemId IS NOT NULL THEN 'MatchedToReport'
	WHEN mr.Id IS NOT NULL THEN 'Resolved'
	ELSE 'OutstandingIssue'
END as [State],
rt.Description as ResolutionTypeDescription,
fi.FileName,
fi.FileDate,
fi.LoadDate,
u.Name As UserName

  FROM [dbo].[StockItems] AS SI   
  INNER JOIN StockChecks AS SC ON SC.Id=SI.StockCheckId   
  INNER JOIN Sites ON Sites.Id=SC.SiteId  
  INNER JOIN Divisions AS D ON D.Id=Sites.DivisionId  
  INNER JOIN DealerGroup AS DG ON D.DealerGroupId=DG.Id  
  LEFT JOIN Scans sca on sca.Id = si.ScanId
  LEFT JOIN MissingResolutions mr on mr.id = si.MissingResolutionId
  LEFT JOIN ResolutionTypes rt on rt.id = mr.ResolutionTypeId
  LEFT JOIN [import].[FileImports] fi ON fi.Id = SI.FileImportId
  LEFT JOIN Users u ON u.Id = fi.LoadedByUserId
  WHERE SC.Id = ISNULL(@SCId, SC.Id)   
  AND SC.Date = @StockCheckDate  
  AND D.Id = ISNULL(@DivisionId, D.Id)  
  AND DG.Id = ISNULL(@DealerGroupId, DG.Id)  
    
  
END  

GO


  
  