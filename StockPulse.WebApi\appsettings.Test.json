{

  "ConnectionStrings": {
    "DefaultConnection": "Server=tcp:cphi.database.windows.net,1433; Initial Catalog=stockpulseTest; Persist Security Info=False; User ID=StockPulseApiUser; Password=***************; MultipleActiveResultSets=False; Encrypt=True; TrustServerCertificate=False; Connection Timeout=30;"
  },


  "BlobStorage": {
    "StorageAccount": "stockpulseimages",
    "StorageAccountKey": "****************************************************************************************",

    //Test
    "FilePath": "**********************************************************/",
    "ReadOnlyKey": "sp=r&st=2023-05-24T09:01:09Z&se=2053-02-01T18:01:09Z&spr=https&sv=2022-11-02&sr=c&sig=CmvraZF9dOTVaPNo7%2Fks1mJUK7ChQHHhgILXRh5Q2zs%3D"

  },


  "WebApp": {
    "URL": "http://localhost:4200",
    "Env": "TEST", // LOCAL, DEV, TEST, PROD
    "Country": "UK" //UK, US
  }


}
