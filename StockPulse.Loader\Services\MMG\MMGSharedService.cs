﻿using StockPulse.Model;
using System;
using System.Linq;

namespace StockPulse.Loader.Services.MMG
{


    public class MMGSharedService
    {
        private LogMessage logMessage;



        //constructor
        public MMGSharedService(LogMessage logMessageIn)
        {
            logMessage = logMessageIn;
        }


        // Certain Sites we are not interested in so don't need to trigger errors
        public bool IsSiteSkippable(string siteName)
        {

            string[] dealerships = new string[]
            {
                "Ford Bury",
                "Audi Wimbledon (Service)",
                "Volvo Leeds",
                "Audi Barnstaple",
                "VW Tunbridge Wells", // This site was removed
                "Mercedes Van Croydon",
                "Lexus Canterbury",
                "Citroen Cambridge", // Site is closed
                "Toyota Bristol South",
                "Mercedes South Lakes"
            };

            return dealerships.Contains(siteName.Trim());
        }




    }
}
