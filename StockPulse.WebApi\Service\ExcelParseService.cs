﻿
using ExcelDataReader;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Data.OleDb;
using System.Runtime.CompilerServices;

namespace StockPulse.WebApi.Service
{
    public interface IExcelParseService
    {
        HeadersAndRows GetExcelFileContents(Stream fs, int headerSkip, int headerRowCount, int rowSkip);
        HeadersAndRows GetBinaryExcelFileContents(Stream fs, int headerSkip, int headerRowCount, int rowSkip);
        DataRowCollection GetRowsExcelNew(Stream fs);
    }


    public class ExcelParseService : IExcelParseService
    {

        public ExcelParseService()
        {
        }


        public HeadersAndRows GetExcelFileContents(Stream fs, int headerSkip, int headerRowCount, int rowSkip)
        {

            List<DataRow> rowsIn = GetRowsExcelNew(fs).OfType<DataRow>().ToList();

            //get headers
            List<List<string>> headers = new List<List<string>>();
            foreach (var headerRow in rowsIn.Skip(headerSkip).Take(headerRowCount).ToList())
            {
                headers.Add(headerRow.ItemArray.ToList().Select(x => x.ToString().Trim()).ToList());
            }

            //get rows
            List<List<string>> rows = new List<List<string>>();

            int i = 0;
            int n = rowsIn.Count();

            foreach (var rowIn in rowsIn.Skip(rowSkip))
            {
                rows.Add(rowIn.ItemArray.ToList().Select(x => x.ToString()).ToList());

                i++;
                ImportLogger(i + " of " + n);
            }

            return new HeadersAndRows() { headers = headers, rows = rows };
        }


        public HeadersAndRows GetBinaryExcelFileContents(Stream fs, int headerSkip, int headerRowCount, int rowSkip)
        {

            List<DataRow> rowsIn = GetRowsBinaryExcel(fs).OfType<DataRow>().ToList();

            //get headers
            List<List<string>> headers = new List<List<string>>();
            foreach (var headerRow in rowsIn.Skip(headerSkip).Take(headerRowCount).ToList())
            {
                headers.Add(headerRow.ItemArray.ToList().Select(x => x.ToString().Trim()).ToList());
            }

            //get rows
            List<List<string>> rows = new List<List<string>>();
            foreach (var rowIn in rowsIn.Skip(rowSkip))
            {
                rows.Add(rowIn.ItemArray.ToList().Select(x => x.ToString()).ToList());
            }

            return new HeadersAndRows() { headers = headers, rows = rows };
        }


        public DataRowCollection GetRowsExcelNew(Stream fs)
        {
            System.Text.Encoding.RegisterProvider(System.Text.CodePagesEncodingProvider.Instance);
            // Auto-detect format, supports:
            //  - Binary Excel files (2.0-2003 format; *.xls)
            //  - OpenXml Excel files (2007 format; *.xlsx)
            using (var reader = ExcelReaderFactory.CreateReader(fs))
            {
                // Choose one of either 1 or 2:

                // 1. Use the reader methods
                do
                {
                    while (reader.Read())
                    {
                        // reader.GetDouble(0);
                    }
                } while (reader.NextResult());

                // 2. Use the AsDataSet extension method
                // The result of each spreadsheet is in result.Tables
                var result = reader.AsDataSet(new ExcelDataSetConfiguration()
                {
                    ConfigureDataTable = (tableReader) => new ExcelDataTableConfiguration()
                    {
                        FilterRow = (rowReader) =>
                        {
                            int progress = (int)Math.Ceiling((decimal)rowReader.Depth / (decimal)rowReader.RowCount * (decimal)100);
                            // progress is in the range 0..100
                            return true;
                        }
                    }
                });

                var rows = result.Tables[0].Rows;

                return rows;

            }

        }

        public DataRowCollection GetRowsBinaryExcel(Stream fs)
        {
            
            var guid = Guid.NewGuid();
            string tempFilePath = $"{Directory.GetCurrentDirectory()}\\tempFiles\\{guid.ToString()}.xlsb";

            var fileStream = File.Create(tempFilePath);
            fs.Seek(0, SeekOrigin.Begin);
            fs.CopyTo(fileStream);
            fileStream.Close();



            string sheetName;
            string ConnectionString = "Provider = Microsoft.ACE.OLEDB.12.0; Data Source = " + tempFilePath + "; Extended Properties = 'Excel 8.0;HDR=Yes'";
            DataSet ds = new DataSet();
            try
            {
                //using (OleDbConnection con = new OleDbConnection(ConnectionString))
                //{
                //    using (OleDbCommand cmd = new OleDbCommand())
                //    {
                //        using (OleDbDataAdapter oda = new OleDbDataAdapter())
                //        {
                //            cmd.Connection = con;
                //            con.Open();
                //            DataTable dtExcelSchema = con.GetOleDbSchemaTable(OleDbSchemaGuid.Tables, null);
                //            for (int i = 0; i < dtExcelSchema.Rows.Count; i++)
                //            {
                //                sheetName = dtExcelSchema.Rows[i]["TABLE_NAME"].ToString();
                //                DataTable dt = new DataTable(sheetName);
                //                cmd.Connection = con;
                //                cmd.CommandText = "SELECT * FROM [" + sheetName + "]";
                //                oda.SelectCommand = cmd;
                //                oda.Fill(dt);
                //                dt.TableName = sheetName;
                //                ds.Tables.Add(dt);
                //            }
                //        }
                //    }
                //}
            }
            catch(Exception ex)
            {
                throw ex; 
            }
            finally
            {
                File.Delete(tempFilePath);
            }


            var headersRowValues = ds.Tables[0].Columns.Cast<DataColumn>().Select(x =>  x.ColumnName as object).ToArray();

            var newDataTable = new DataTable();
            
            foreach(string headersRowValue in headersRowValues)
            {
                newDataTable.Columns.Add(headersRowValue);
            }
            var newHeaderRow = newDataTable.NewRow();
            newHeaderRow.ItemArray = headersRowValues;
            newDataTable.Rows.Add(newHeaderRow);
            foreach (DataRow rowValue in ds.Tables[0].Rows )
            {
                newDataTable.ImportRow(rowValue);
            }


            return newDataTable.Rows;

        }


        internal static void ImportLogger(string message,
        [CallerFilePath] string file = null,
        [CallerLineNumber] int line = 0)
        {
            Console.WriteLine("{0} ({1}): {2} " + DateTime.Now.ToLongTimeString(), Path.GetFileName(file), line, message);
        }


    }
}

