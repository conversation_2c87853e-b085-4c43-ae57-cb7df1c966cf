SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE dbo.UPDATE_UserPreference
    @UserId INT,
    @PreferenceName NVARCHAR(50),
    @Preference NVARCHAR(50)
AS
BEGIN
    SET NOCOUNT ON;

    -- Check if the record exists
    IF EXISTS (
        SELECT 1 
        FROM UserPreferences
        WHERE Person_Id = @UserId AND PreferenceName = @PreferenceName
    )
    BEGIN
        -- Update the existing record
        UPDATE UserPreferences
        SET Preference = @Preference
        WHERE Person_Id = @UserId AND PreferenceName = @PreferenceName;
    END
    ELSE
    BEGIN
        -- Insert a new record
        INSERT INTO UserPreferences (Person_Id, PreferenceName, Preference)
        VALUES (@UserId, @PreferenceName, @Preference);
    END
END

GO
