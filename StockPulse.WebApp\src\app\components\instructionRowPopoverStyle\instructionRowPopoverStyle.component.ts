import { Component, ContentChild, Input, OnInit, TemplateRef } from '@angular/core';
import { IconService } from 'src/app/services/icon.service';




@Component({
  selector: 'instructionRowPopoverStyle',
  templateUrl: './instructionRowPopoverStyle.component.html',
  styleUrls: ['./instructionRowPopoverStyle.component.scss']
})
export class InstructionRowPopoverStyleComponent  {
 @Input() fullMessage:string ;
//  @Input() briefMessage:string ;
 @Input() isDanger:boolean;

 @ContentChild(TemplateRef) public inputElement: TemplateRef<any>;

  constructor(
    public icon: IconService
  ) {
  }


}
