<div class="mainContentHolder">
    <!--Pic-->
    <div class="imageHolder">
        <!-- IMAGE GOES HERE -->
        <img [src]="item.regImageThumbnailUrl">
        <div *ngIf="!item.scanId" class="doNotPrint imgSmallPlaceholder">Loading...</div>
    </div>

    <!-- Main Details-->
    <table>
        <thead>
            <tr>
                <th>Scanned by: {{item.scannerName}}</th>
                <th></th>
                <th>Per Stock List</th>
                <th>Per Scan</th>
                <th>Matches?</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>Scanned: {{item.scanDateTime|cph:'day':0}} {{item.scanDateTime|cph:'time':0}}</td>
                <td>Reg</td>
                <td class="center">
                    <div class="doNotPrint regPlate">
                        {{item.regPerStockList|cph:'numberPlate':0}}
                    </div>
                </td>
                <td class="center">
                    <div class="doNotPrint regPlate">
                        {{item.regPerScan|cph:'numberPlate':0}}</div>
                    </td>
                    <td class="center">
                        <span *ngIf="item.regPerStockList && item.regPerStockList !== item.regPerScan">
                            <fa-icon [icon]="icon.faExclamation"></fa-icon>
                        </span>
                        <span *ngIf="!item.regPerStockList || item.regPerStockList == item.regPerScan">
                            <fa-icon class="good" [icon]="icon.faCheckCircle"></fa-icon>
                        </span>
                    </td>
                </tr>
                <tr>
                <td>Scanned at: {{item.locationDescription}}</td>
                <td>VIN</td>
                <td class="center">
                    <div class="doNotPrint chassis">{{item.vinPerStockList}}</div>
                </td>
                <td class="center">
                    <div class="doNotPrint chassis">{{item.vinPerScan}}</div>
                </td>
                <td class="center">
                    <span *ngIf="item.vinPerStockList !== item.vinPerScan">
                        <fa-icon [icon]="icon.faExclamation"></fa-icon>
                    </span>
                    <span *ngIf="item.vinPerStockList == item.vinPerScan">
                        <fa-icon class="good" [icon]="icon.faCheckCircle"></fa-icon>
                    </span>
                </td>
            </tr>
        </tbody>
    </table>
</div>