﻿/****** Object:  StoredProcedure [dbo].[GET_GlobalParams]    Script Date: 10/3/21 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].GET_GlobalParams
(
    @UserId INT = NULL
)
AS
BEGIN

SET NOCOUNT ON

SELECT Id,DealerGroupId,Name,StringValue,BoolValue,DateValue,NumberValue 
FROM GlobalParams 
WHERE DealerGroupId = (select DealergroupId from Users where Id = @UserId)
	
	

END

GO




--To use this run 
--exec [GET_GlobalParams] @UserId = 1