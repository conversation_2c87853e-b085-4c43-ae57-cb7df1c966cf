﻿using System.Collections.Generic;

namespace StockPulse.WebApi.ViewModel
{
    public class UpdateSiteNameLookupsParams
    {
        public List<SoteNameLookupsToUpdate> changes { get; set; }
    }

    public class SoteNameLookupsToUpdate
    {
        public int id { get; set; }
        public string newValue { get; set; }
        public bool isPrimarySiteId { get; set; }
        public int siteId { get; set; }
    }
}
