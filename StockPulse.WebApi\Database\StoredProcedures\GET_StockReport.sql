

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].[GET_StockReport]  
(  
    @StockCheckId INT = NULL,  
    @UserId INT = NULL  
)  
AS  
BEGIN  
  
SET NOCOUNT ON;  
  
DECLARE @isRegional INT;  
DECLARE @isTotal INT;  
DECLARE @StockCheckDate DateTime;  
DECLARE @DivisionId INT = NULL;  
DECLARE @DealerGroupId INT = NULL;  
DECLARE @SCId INT = NULL;  
  
  
IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END

  
SELECT   
@isRegional = IsRegional,   
@isTotal = IsTotal,   
@StockCheckDate = Date   
FROM StockChecks    
WHERE StockChecks.Id = @StockCheckId  
  
  

  
 IF @isRegional = 0 AND @isTotal = 0  
  
  BEGIN  
  
  SET @SCId = @StockCheckId;  
  
  END  
  
 ELSE IF @isRegional = 1 AND @isTotal = 0  
  
  BEGIN  
    
  SET @DivisionId = (SELECT DivisionId FROM StockChecks  
       INNER JOIN Sites ON Sites.Id=StockChecks.SiteId  
       INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id  
       WHERE StockChecks.Id = @StockCheckId AND StockChecks.IsRegional = 1)  
    
  
  END  
  
 ELSE IF @isRegional = 0 AND @isTotal = 1  
  
  BEGIN  
  
    
  SET @DealerGroupId = (SELECT DealerGroupId FROM StockChecks  
       INNER JOIN Sites ON Sites.Id=StockChecks.SiteId  
       INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id  
       WHERE StockChecks.Id = @StockCheckId)  
  
  
  END;  
  
    SELECT SourceReports.[Id], SourceReports.[Filename], COUNT(SI.Id) AS Units, SUM(SI.StockValue) AS Balance  
    FROM SourceReports  
    INNER JOIN StockItems AS SI ON SI.SourceReportId=SourceReports.Id  
 INNER JOIN StockChecks AS SC ON SC.Id=SI.StockCheckId   
 INNER JOIN Sites ON Sites.Id=SC.SiteId  
 INNER JOIN Divisions AS D ON D.Id=Sites.DivisionId  
 INNER JOIN DealerGroup AS DG ON D.DealerGroupId=DG.Id  
    WHERE   
 SC.Id = ISNULL(@SCId, SC.Id)   
  AND SC.Date = @StockCheckDate  
  AND D.Id = ISNULL(@DivisionId, D.Id)  
  AND DG.Id = ISNULL(@DealerGroupId, DG.Id)  
    
 --SI.StockCheckId = @StockCheckId   
    GROUP BY SourceReports.[Id], SourceReports.[Filename]  
  
  
  
  END  
  


  GO
  
  