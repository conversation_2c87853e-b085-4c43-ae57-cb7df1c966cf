﻿
/****** Object:  StoredProcedure [dbo].[GET_StockItemWithScan]    Script Date: 16/3/21 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].GET_StockItemWithScan
(
    @ScanId INT = NULL	,
	@UserId INT = NULL
)
AS
BEGIN

SET NOCOUNT ON


IF dbo.[AuthenticateUser](@UserId, (select StockCheckId from dbo.Scans where Id = @ScanId)) = 0
BEGIN 
    RETURN
END



	
SELECT 
st.[Id], st.<PERSON>,st.Vin,st.Description, st.DIS,st.GroupDIS, st.Branch, st.StockType, st.Comment, st.Reference, st.StockValue,
sc.Description as 'ScanDescription', 
usrs.Name as 'ScannedBy',
sc.ScanDateTime,
locns.Description as 'LocationDescription',
sc.Id as 'ScanId'

  from StockItems   st 
  inner join Scans sc on sc.Id = st.ScanId 
  inner join Users usrs on usrs.Id = sc.UserId
  inner join Locations locns on locns.Id = sc.LocationId

  WHERE st.ScanId = @ScanId

  

END

GO


--To use this run 
--exec [GET_StockItemWithScan] @StockCheckId = 1