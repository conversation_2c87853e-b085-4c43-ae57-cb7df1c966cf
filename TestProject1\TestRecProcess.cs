using NUnit.Framework;
using StockPulse.WebApi.Service;
using StockPulse.WebApi.ViewModel;
using System.Collections.Generic;

namespace TestProject1
{
    public class Tests
    {
        [SetUp]
        public void Setup()
        {
        }

        [Test]
        public void TestRecProcessIfRequireRegAndVinMatch()
        {
            //-------------------------------------------
            //create some sample data
            //-------------------------------------------
            StockcheckDataToReconcile testData = GenerateTestDataForTestingRecProcess();

            //-------------------------------------------
            //work out what we'd expect
            //-------------------------------------------
            ReconciliationResults expectedResult = new ReconciliationResults()
            {
                duplicatedScans = new List<MatchedItem>() {
                    new MatchedItem(){ItemId = 2, MatchItemId = null}
                },
                scansMatchedToStockItems = new List<MatchedItem>() {
                    new MatchedItem(){ItemId = 3, MatchItemId = 101},
                    //new MatchedItem(){ItemId = 6, MatchItemId = 106}
                },
                scansMatchedToOtherSiteStockItems = new List<MatchedItem>() {
                    new MatchedItem(){ItemId = 5, MatchItemId = 501}
                },
                scansMatchedToRecItems = new List<MatchedItem>() {
                    new MatchedItem(){ItemId = 4, MatchItemId = 301}
                },
                remainingUnmatchedScans = new List<MatchedItem>() {
                    new MatchedItem(){ItemId = 1, MatchItemId = null},
                    new MatchedItem(){ItemId = 6, MatchItemId = null},
                    new MatchedItem(){ItemId = 7, MatchItemId = null},
                    new MatchedItem(){ItemId = 8, MatchItemId = null},
                },
                duplicatedStockItems = new List<MatchedItem>() {
                    new MatchedItem(){ItemId = 103, MatchItemId = null}
                },
                stockItemsMatchedToScans = new List<MatchedItem>() {
                    new MatchedItem(){ItemId = 101, MatchItemId = 3},
                    //new MatchedItem(){ItemId = 106, MatchItemId = 6},
                },
                stockItemsMatchedToOtherSiteScans = new List<MatchedItem>() {
                    new MatchedItem(){ItemId = 105, MatchItemId = 401}
                },
                stockItemsMatchedToRecItems = new List<MatchedItem>() {
                    new MatchedItem(){ItemId = 104, MatchItemId = 201}
                },
                remainingUnmatchedStockItems = new List<MatchedItem>() {
                    new MatchedItem(){ItemId = 102, MatchItemId = null},
                    new MatchedItem(){ItemId = 106, MatchItemId = null},
                    new MatchedItem(){ItemId = 107, MatchItemId = null},
                    new MatchedItem(){ItemId = 108, MatchItemId = null},
                },
            };



            //-------------------------------------------
            // Run results
            //-------------------------------------------
            ReconciliationResults results = StockCheckReconciliationService.DoReconcileStockCheck(testData, true);


            //-------------------------------------------
            // Test
            //-------------------------------------------
            TestEquality(expectedResult.duplicatedScans, results.duplicatedScans);
            TestEquality(expectedResult.scansMatchedToStockItems, results.scansMatchedToStockItems);
            TestEquality(expectedResult.scansMatchedToOtherSiteStockItems, results.scansMatchedToOtherSiteStockItems);
            TestEquality(expectedResult.scansMatchedToRecItems, results.scansMatchedToRecItems);
            TestEquality(expectedResult.remainingUnmatchedScans, results.remainingUnmatchedScans);
            TestEquality(expectedResult.duplicatedStockItems, results.duplicatedStockItems);
            TestEquality(expectedResult.stockItemsMatchedToScans, results.stockItemsMatchedToScans);
            TestEquality(expectedResult.stockItemsMatchedToOtherSiteScans, results.stockItemsMatchedToOtherSiteScans);
            TestEquality(expectedResult.stockItemsMatchedToRecItems, results.stockItemsMatchedToRecItems);
            TestEquality(expectedResult.remainingUnmatchedStockItems, results.remainingUnmatchedStockItems);
        }













        [Test]
        public void TestRecProcessIfRequireRegOrVinMatch()
        {
            //-------------------------------------------
            //create some sample data
            //-------------------------------------------
            StockcheckDataToReconcile testData = GenerateTestDataForTestingRecProcess();



            //-------------------------------------------
            //work out what we'd expect
            //-------------------------------------------
            ReconciliationResults expectedResult = new ReconciliationResults()
            {
                duplicatedScans = new List<MatchedItem>() {
                    new MatchedItem(){ItemId = 2, MatchItemId = null},
                    new MatchedItem(){ItemId = 7, MatchItemId = null},
                    new MatchedItem(){ItemId = 8, MatchItemId = null}
                },
                scansMatchedToStockItems = new List<MatchedItem>() {
                    new MatchedItem(){ItemId = 3, MatchItemId = 101},
                    new MatchedItem(){ItemId = 6, MatchItemId = 106}
                },
                scansMatchedToOtherSiteStockItems = new List<MatchedItem>() {
                    new MatchedItem(){ItemId = 5, MatchItemId = 501}
                },
                scansMatchedToRecItems = new List<MatchedItem>() {
                    new MatchedItem(){ItemId = 4, MatchItemId = 301}
                },
                remainingUnmatchedScans = new List<MatchedItem>() {
                    new MatchedItem(){ItemId = 1, MatchItemId = null},
                },
                duplicatedStockItems = new List<MatchedItem>() {
                    new MatchedItem(){ItemId = 103, MatchItemId = null},
                    new MatchedItem(){ItemId = 107, MatchItemId = null},
                    new MatchedItem(){ItemId = 108, MatchItemId = null}
                },
                stockItemsMatchedToScans = new List<MatchedItem>() {
                    new MatchedItem(){ItemId = 101, MatchItemId = 3},
                    new MatchedItem(){ItemId = 106, MatchItemId = 6},
                },
                stockItemsMatchedToOtherSiteScans = new List<MatchedItem>() {
                    new MatchedItem(){ItemId = 105, MatchItemId = 401}
                },
                stockItemsMatchedToRecItems = new List<MatchedItem>() {
                    new MatchedItem(){ItemId = 104, MatchItemId = 201}
                },
                remainingUnmatchedStockItems = new List<MatchedItem>() {
                    new MatchedItem(){ItemId = 102, MatchItemId = null},
                },
            };



            //-------------------------------------------
            // Run results
            //-------------------------------------------
            ReconciliationResults results = StockCheckReconciliationService.DoReconcileStockCheck(testData, false);


            //-------------------------------------------
            // Test
            //-------------------------------------------
            TestEquality(expectedResult.duplicatedScans, results.duplicatedScans);
            TestEquality(expectedResult.scansMatchedToStockItems, results.scansMatchedToStockItems);
            TestEquality(expectedResult.scansMatchedToOtherSiteStockItems, results.scansMatchedToOtherSiteStockItems);
            TestEquality(expectedResult.scansMatchedToRecItems, results.scansMatchedToRecItems);
            TestEquality(expectedResult.remainingUnmatchedScans, results.remainingUnmatchedScans);
            TestEquality(expectedResult.duplicatedStockItems, results.duplicatedStockItems);
            TestEquality(expectedResult.stockItemsMatchedToScans, results.stockItemsMatchedToScans);
            TestEquality(expectedResult.stockItemsMatchedToOtherSiteScans, results.stockItemsMatchedToOtherSiteScans);
            TestEquality(expectedResult.stockItemsMatchedToRecItems, results.stockItemsMatchedToRecItems);
            TestEquality(expectedResult.remainingUnmatchedStockItems, results.remainingUnmatchedStockItems);
        }



        [Test]
        public void TestDeDupeProcessIfDealerGroupRequiresRegANDVinMatch()
        {

            //-------------------------------------------
            //create some sample data
            //-------------------------------------------
            var testData = new StockcheckDataToReconcile()
            {
                OurScans = new List<MatchItem>() {
                    new MatchItem() { ItemId = 1, Reg = "UNK1", Vin = "VINUNK1" },  //unknown item
                    new MatchItem() { ItemId = 2, Reg = "UNK1", Vin = "VINUNK1" },  //is duplicate scan.  both same
                    new MatchItem() { ItemId = 3, Reg = "UNK1", Vin = "ABC" },  //not duplicate scan.   Although reg matches, vin is different and we are needing both to match
                    new MatchItem() { ItemId = 4, Reg = "", Vin = "VINUNK1" },  //is duplicate scan.   No reg, and vin matches, so ok
                    new MatchItem() { ItemId = 5, Reg = "UNK2", Vin = "VINUNK1" },  //not duplicate scan.   Vin matches but reg different, and require both matches
                }
            };


            //-------------------------------------------
            //work out what we'd expect
            //-------------------------------------------
            ReconciliationResults expectedResult = new ReconciliationResults()
            {
                duplicatedScans = new List<MatchedItem>() {
                    new MatchedItem(){ItemId = 2, MatchItemId = null},
                    new MatchedItem(){ItemId = 4, MatchItemId = null}
                },
            };


            //-------------------------------------------
            // Run results
            //-------------------------------------------
            ReconciliationResults results = StockCheckReconciliationService.DoReconcileStockCheck(testData,true);  //true indicates need both reg and vin match

            //-------------------------------------------
            // Test
            //-------------------------------------------
            TestEquality(expectedResult.duplicatedScans, results.duplicatedScans);
        }

        [Test]
        public void TestDeDupeProcessIfDealergroupRequiresRegOrVinMatch()
        {

            //-------------------------------------------
            //create some sample data
            //-------------------------------------------
            var testData = new StockcheckDataToReconcile()
            {
                OurScans = new List<MatchItem>() {
                      new MatchItem() { ItemId = 1, Reg = "UNK1", Vin = "VINUNK1" },  //unknown item
                    new MatchItem() { ItemId = 2, Reg = "UNK1", Vin = "VINUNK1" },  //is duplicate scan.  both same
                    new MatchItem() { ItemId = 3, Reg = "UNK1", Vin = "ABC" },  //is duplicate scan.   reg matches, we only need 1 match
                    new MatchItem() { ItemId = 4, Reg = "", Vin = "VINUNK1" },  //is duplicate scan.   vin matches, no reg
                    new MatchItem() { ItemId = 5, Reg = "UNK2", Vin = "VINUNK1" },  //is duplicate scan.   vin matches, we only need 1 match
                }
            };


            //-------------------------------------------
            //work out what we'd expect
            //-------------------------------------------
            ReconciliationResults expectedResult = new ReconciliationResults()
            {
                duplicatedScans = new List<MatchedItem>() {
                    new MatchedItem(){ItemId = 2, MatchItemId = null},
                    new MatchedItem(){ItemId = 3, MatchItemId = null},
                    new MatchedItem(){ItemId = 4, MatchItemId = null},
                    new MatchedItem(){ItemId = 5, MatchItemId = null}
                },
            };


            //-------------------------------------------
            // Run results
            //-------------------------------------------
            ReconciliationResults results = StockCheckReconciliationService.DoReconcileStockCheck(testData,false);

            //-------------------------------------------
            // Test
            //-------------------------------------------
            TestEquality(expectedResult.duplicatedScans, results.duplicatedScans);
        }



        private void TestEquality(List<MatchedItem> expectedList, List<MatchedItem> actualList)
        {
            bool result = true;
            int i = 0;
            Assert.That(actualList.Count, Is.EqualTo(expectedList.Count), "Lists are not same length");

            while (result && i < expectedList.Count && expectedList.Count == actualList.Count)
            {
                Assert.That(actualList[i].ItemId, Is.EqualTo(expectedList[i].ItemId));
                Assert.That(actualList[i].MatchItemId, Is.EqualTo(expectedList[i].MatchItemId));
                i++;
            }
        }

        private static StockcheckDataToReconcile GenerateTestDataForTestingRecProcess()
        {
            var testData = new StockcheckDataToReconcile()
            {
                OurScans = new List<MatchItem>() {
                    new MatchItem() { ItemId = 1, Reg = "UNK1", Vin = "VINUNK1" },  //unknown item
                    new MatchItem() { ItemId = 2, Reg = "UNK1", Vin = "VINUNK1" },  //duplicate scan
                    new MatchItem() { ItemId = 3, Reg = "REG2", Vin = "VIN2" },  //matched to stock
                    new MatchItem(){ItemId = 4, Reg = "RECUNK1", Vin = "VINRECUNK1"}, //matched to unknown list
                    new MatchItem(){ItemId = 5, Reg = "OTHRST1", Vin = "VINOTHRST1"}, //matched to other site stock
                    new MatchItem(){ItemId = 6, Reg = "REGONLY", Vin = "NEW1"}, //will match depending on if need reg AND vin match or not
                    new MatchItem() { ItemId = 7, Reg = "UNK1", Vin = "VINNOTDUP1" },  //will be a dupe, depending on if need reg AND vin match or not
                    new MatchItem() { ItemId = 8, Reg = "UNK1", Vin = "VINNOTDUP2" },  //will be a dupe, depending on if need reg AND vin match or not
                },
                OurStockItems = new List<MatchItem>()
                {
                    new MatchItem(){ItemId = 101, Reg = "REG2", Vin = "VIN2"},  //matched to scan
                    new MatchItem(){ItemId = 102, Reg = "MISS1", Vin = "VINMISS1"}, //missing item
                    new MatchItem(){ItemId = 103, Reg = "MISS1", Vin = "VINMISS1"}, //duplicate stock item
                    new MatchItem(){ItemId = 104, Reg = "RECMISS1", Vin = "VINRECMISS1"},  //matched to missing list
                    new MatchItem(){ItemId = 105, Reg = "OTHRSC1", Vin = "VINOTHRSC1"}, //matched to other site scan
                    new MatchItem(){ItemId = 106, Reg = "REGONLY", Vin = "NEW2"}, //will match depending on if need reg AND vin match or not
                    new MatchItem(){ItemId = 107, Reg = "MISS1", Vin = "ABC1"},  //will be a dupe, depending on if need reg AND vin match or not
                    new MatchItem(){ItemId = 108, Reg = "MISS1", Vin = "ABC2"},  //will be a dupe, depending on if need reg AND vin match or not
                },

                OurReconcilingItemsForMissing = new List<MatchItem>()
                {
                    new MatchItem(){ItemId = 201, Reg = "RECMISS1", Vin = "VINRECMISS1"}  //our missing rec list
                },

                OurReconcilingItemsForUnknown = new List<MatchItem>()
                {
                    new MatchItem(){ItemId = 301, Reg = "RECUNK1", Vin = "VINRECUNK1"}  //our unknown list
                },
                OtherSitesUnMatchedScans = new List<MatchItem>()
                {
                    new MatchItem(){ItemId = 401, Reg = "OTHRSC1", Vin = "VINOTHRSC1"}  // other site unmatched scans
                },
                OtherSiteUnmatchedStockItems = new List<MatchItem>()
                {
                    new MatchItem(){ItemId = 501, Reg = "OTHRST1", Vin = "VINOTHRST1"}  //other site unmatched stockitem
                },
            };
            return testData;
        }
    }
}