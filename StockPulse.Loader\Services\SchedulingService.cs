﻿using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Quartz;
using Quartz.Impl;
using StockPulse.Loader.Jobs;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace StockPulse.Loader.Services
{
    public class SchedulingService : BackgroundService
    {
        private readonly ILogger<SchedulingService> _logger;
        private StdSchedulerFactory _schedulerFactory;
        private CancellationToken _stopppingToken;
        private IScheduler _scheduler;
        int hourlyFilesDelay;
        bool isDev;


        public SchedulingService(ILogger<SchedulingService> logger)
        {
            _logger = logger;
            //isDev = ConfigService.incomingRoot.ToLower().Contains("dev");
            //hourlyFilesDelay = isDev ? 120 : 60;  //every 2 mins for dev plus standard wait.  
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            await StartJobs();
            _stopppingToken = stoppingToken;
            while (!stoppingToken.IsCancellationRequested)
            {
                await Task.Delay(1000, stoppingToken);
            }
            await _scheduler.Shutdown();
        }



        private async Task StartJobs()
        {
            _schedulerFactory = new StdSchedulerFactory();

            _scheduler = await _schedulerFactory.GetScheduler();
            await _scheduler.Start();

         if (ConfigService.overrideRunJobNow != "")
         {
            string hours = DateTime.Now.AddSeconds(5).Hour.ToString();
            string minutes = DateTime.Now.AddSeconds(5).Minute.ToString();
            string seconds = DateTime.Now.AddSeconds(5).Second.ToString();
            string runIn5SecondsSchedule = $"CUSTOM {seconds} {minutes} {hours} ? * * *";

            Type job = Type.GetType($"StockPulse.Loader.Jobs.{ConfigService.overrideRunJobNow}");
            await scheduleJob(JobBuilder.Create(job).Build(), runIn5SecondsSchedule);
         }
         else
         {
            await scheduleJob(JobBuilder.Create<GenericLoaderJob>().Build(), ConfigService.stockPulseLoaderJob);
            if (ConfigService.isUS)
            {
               await scheduleJob(JobBuilder.Create<LithiaFTPFileFetchJob>().Build(), ConfigService.lihiaFTPFileFetchJob);
            }
         }

            //await scheduleJob(JobBuilder.Create<JardineWIPLoaderJob>().Build(), ConfigService.stockPulseJardineWIPJob);
            //await scheduleJob(JobBuilder.Create<JardineTBLoaderJob>().Build(), ConfigService.stockPulseJardineTBJob);
            //await scheduleJob(JobBuilder.Create<MMGTBLoaderJob>().Build(), ConfigService.stockPulseMMGTBJob);
            //await scheduleJob(JobBuilder.Create<MMGWIPLoaderJob>().Build(), ConfigService.stockPulseMMGWIPJob);
            //await scheduleJob(JobBuilder.Create<MMGStockLoaderJob>().Build(), ConfigService.stockPulseMMGStockJob);
            //await scheduleJob(JobBuilder.Create<MMGStockOnLoanLoaderJob>().Build(), ConfigService.stockPulseMMGStockOnLoanJob);
            //await scheduleJob(JobBuilder.Create<MMGAtAuctionLoaderJob>().Build(), ConfigService.stockPulseMMGAtAuctionJob);

        }




        private async Task scheduleJob(IJobDetail job, string requestedSchedule)
        {
            try
            {

                if (requestedSchedule == "DO NOT RUN") { return; }


                //if we want to run regularly
                if (requestedSchedule.StartsWith("REPEAT EVERY"))
                {
                    string cronSchedule = "0 10 * ? * * *"; //set to 10mins by default, should never use this
                    if (requestedSchedule.Contains("MINUTES"))
                    {
                        int minutes = int.Parse(new string(requestedSchedule.Where(char.IsDigit).ToArray()));
                        cronSchedule = $"0 0/{minutes} * ? * * *";
                    }
                    else if (requestedSchedule.Contains("SECONDS"))
                    {
                        int seconds = int.Parse(new string(requestedSchedule.Where(char.IsDigit).ToArray()));
                        cronSchedule = $"0/{seconds} * * ? * * *";
                    }
                    await _scheduler.ScheduleJob(job, TriggerBuilder.Create().WithCronSchedule(cronSchedule).Build(), _stopppingToken);
                    return;
                }

                //if we chose a certain time
                if (requestedSchedule.StartsWith("RUN AT"))
                {
                    string timeChosen = requestedSchedule.Substring(7, requestedSchedule.Length - 7);
                    string hours = timeChosen.Split(':')[0];
                    string minutes = timeChosen.Split(':')[1];
                    string cronSchedule = $"0 {minutes} {hours} ? * * *";
                    await _scheduler.ScheduleJob(job, TriggerBuilder.Create().WithCronSchedule(cronSchedule).Build(), _stopppingToken);
                    return;
                }

                if (requestedSchedule.StartsWith("CUSTOM "))
                {
                    await _scheduler.ScheduleJob(job, TriggerBuilder.Create().WithCronSchedule(requestedSchedule.Replace("CUSTOM ", "")).Build(), _stopppingToken);
                    return;
                }

            }
            catch
            {
                { }
            }
        }

    }


}
