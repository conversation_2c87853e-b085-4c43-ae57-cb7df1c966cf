<div class="statusAndBarContainer">
    <div *ngIf="showStatusPicker" class="d-flex align-items-center">
        <div ngbDropdown container="body" placement="bottom-right" class="d-inline-block me-2">
            <button class="btn btn-primary" ngbDropdownToggle
                [disabled]="(selectionsService.stockCheck.statusId > 3 && !selectionsService.userIsApprover) || selectionsService.userIsReadOnly">
                <span *ngIf="chosenNewStatus && chosenNewStatus.description">
                    {{ chosenNewStatus.description }}
                </span>
                <span *ngIf="!chosenNewStatus || !chosenNewStatus.description">
                    {{ selectionsService.stockCheck.status }}
                </span>
            </button>
            <div ngbDropdownMenu class="dropdown-menu-left" aria-labelledby="dropdownBasic1">
                <button *ngFor="let status of constantsService.Statuses" ngbDropdownItem
                    (click)="chooseNewStatus(status)" [disabled]="constantsService.disableStatus(status)"
                    [ngbPopover]="
                      status.id === 5 && selectionsService.stockCheck.statusId < 4
                        ? 'Stock check must be set to complete before it can be approved'
                        : null
                    "
                    placement="end"
                    triggers="mouseenter:mouseleave">
                    {{ status.description }}
                </button>
            </div>
        </div>
        <div *ngIf="chosenNewStatus && selectionsService.stockCheck.statusId !== chosenNewStatus.id && !hideSaveCancelButtons"
            class="d-flex justify-content-end me-2">
            <button class="btn btn-success" (click)="saveNewStatus()">
                Confirm
            </button>
            <button class="btn btn-primary" (click)="cancelNewStatus()">
                Cancel
            </button>
        </div>
    </div>
    <div class="barHolder">
        <div class="barActual" [ngClass]="getBarClass()"
            [ngStyle]="{ 'width.%': getBarWidth() }">
        </div>
        <div class="barLabel">{{ getPercentageComplete() | cph:'percent':0 }}</div>
        <div *ngIf="selectionsService.stockCheck.isTotal || selectionsService.stockCheck.isRegional"
            class="regionalTotalLabel">
            {{ selectionsService.stockCheck.percentageCompleteExtra }}
        </div>
    </div>
</div>

<ng-template #confirmModal let-modal>
    <div class="modal-header">
        <h4 class="modal-title" id="modal-basic-title">
            Are you sure?
        </h4>
        <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <div class="modal-body lowHeight">
        <div class="warningNote">
            If you move the stock check status to {{ statusRequested?.description }}, only an approver can move it back.
            Continue?
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-success" (click)="modal.close('Ok')">OK</button>
        <button type="button" class="btn btn-primary" (click)="modal.dismiss('Cancelled')">Cancel</button>
    </div>
</ng-template>