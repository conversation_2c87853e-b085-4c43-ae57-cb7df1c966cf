﻿<log4net>
	<appender name="ConsoleAppender" type="log4net.Appender.ColoredConsoleAppender">
		<layout type="log4net.Layout.PatternLayout">
			<conversionPattern value="%date %level [%thread] %logger{1} %username - %message%newline" />
		</layout>
		<mapping>
			<level value="WARN" />
			<foreColor value="Yellow, HighIntensity" />
		</mapping>
		<mapping>
			<level value="ERROR" />
			<foreColor value="Red, HighIntensity" />
		</mapping>
	</appender>
	<appender name="RollingFile" type="log4net.Appender.RollingFileAppender">
		<file value="./logs/log.log" />
		<rollingStyle value="Date" />
		<appendToFile value="true" />
		<lockingModel type="log4net.Appender.FileAppender+MinimalLock" />
		<datePattern value="yyyyMMdd" />
		<layout type="log4net.Layout.PatternLayout">
			<conversionPattern value="%date %level [%thread] %logger{1} - %message%newline" />
		</layout>
	</appender>
	<root>
		<level value="INFO" />
		<appender-ref ref="ConsoleAppender" />
		<appender-ref ref="RollingFile" />
	</root>
</log4net>