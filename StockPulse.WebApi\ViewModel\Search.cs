﻿using System;
using System.Collections.Generic;
using static StockPulse.WebApi.ViewModel.SearchResult;

namespace StockPulse.WebApi.ViewModel
{

    //public class SearchResultVM
    //{
    //    //public ResultType ResultType { get; set; }
    //    //public ScanFullDetail ScanFullDetail { get; set; }
    //    //public StockItemFullDetail StockItemFullDetail { get; set; }
    //    public ItemFullDetail Item { get; set; }
    //    public string StockCheckSiteName { get; set; }
    //    public DateTime StockCheckDate { get; set; }

    //}

    public class SearchResult
    {
        
        public IEnumerable<ScanResult> scanResults { get; set; }
        public IEnumerable<StockItemResult> stockItemResults { get; set; }

        public class ScanResult
        {
            public int ScanId { get; set; }
            public int? StockitemId { get; set; }
            public string StockCheckSiteName { get; set; }
            public int StockCheckId { get; set; }
            public DateTime StockcheckDate { get; set; }

        }

        public class StockItemResult
        {
            public int StockitemId { get; set; }
            public int? ScanId { get; set; }
            public string StockCheckSiteName { get; set; }
            public int StockCheckId { get; set; }
            public DateTime StockcheckDate { get; set; }
        }

        //public enum ResultType
        //{
        //    ScannedAndInStock = 0,
        //    UnknownResolution = 1,
        //    MissingResolution = 2
        //}



    }

    
}
