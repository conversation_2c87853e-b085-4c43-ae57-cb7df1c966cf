﻿using StockPulse.WebApi.ViewModel;
using System.Collections.Generic;
using System.Linq;

namespace PlateRecognizer
{
    public class RegResult
    {
        public RegResult(PlateReaderResult resultIn)
        {
            var firstResult = resultIn.Results.FirstOrDefault();
            if (firstResult != null)
            {
                Box = new PlateResultBox()
                {
                    Xmax = firstResult.Box.Xmax,
                    Xmin = firstResult.Box.Xmin,
                    Ymin = firstResult.Box.Ymin,
                    Ymax = firstResult.Box.Ymax,
                };


                SeenBeforeMessage = resultIn.SeenBefore;

                ScoreAndPlateItems = new List<ScoreAndPlateItem>();
                foreach (var match in firstResult.ScoresAndPlates.OrderByDescending(x=>x.Score).Take(4))
                {
                    ScoreAndPlateItems.Add(new ScoreAndPlateItem() { Plate = match.Plate, Score = match.Score });
                }
            }
            else
            {
                //no results so
                SeenBeforeMessage = null;
                ScoreAndPlateItems = new List<ScoreAndPlateItem>();
                Box = new PlateResultBox() { Xmax = 0, Xmin = 0, Ymin = 0, Ymax = 0 };
            }
        }
        public ScanSeenBefore SeenBeforeMessage { get; set; }
        public List<ScoreAndPlateItem> ScoreAndPlateItems { get; set; }
        public PlateResultBox Box { get; set; }
    }




}