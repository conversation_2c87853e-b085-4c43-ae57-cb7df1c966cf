
import { Injectable } from '@angular/core';
import { ConstantsService } from './constants.service'

//excel 
import * as excelJS from 'exceljs';
import * as fs from 'file-saver';
import { SheetToExtract } from "../model/SheetToExtract";
import { LogoService } from './logo.service';
import { CphPipe } from '../cph.pipe'
import { GetDataService } from './getData.service';

@Injectable({
  providedIn: 'root'
})
export class ExcelExportService {

  constructor(
    public constants: ConstantsService,
    public logo: LogoService,
    public cphPipe: CphPipe,
    public data: GetDataService,

  ) { }




  exportSheetsToExcel(sheets: SheetToExtract[],rowHeight:number, isUnknowns?:boolean,includeLinkColumnOnEnd?:boolean) {

    //this.currentUrlDomain = window.location.href.replace('/dev/','').replace('/live/','');

    let workbook = new excelJS.Workbook();

    var cphLogo = workbook.addImage({
      base64: this.logo.provideStockPulseLogo(),
      extension: 'png'
    });

    sheets.forEach(sheet => {
      try {

        let workbookImages: any[] = []
        sheet.vehicleImages.forEach(image => {
          if (image){
            workbookImages.push(workbook.addImage({
            base64: image,
            extension: 'jpeg',
          }))
        }
        else{
          workbookImages.push(null);
        }
        })

        //define worksheet
        let worksheet = workbook.addWorksheet(sheet.tableName)

        //generic stuff for worksheet
        worksheet.views = [
          { state: 'frozen', xSplit: 1, ySplit: 3 + sheet.headers.length, zoomScale: 85 }
        ];

        //columns things
        let columns = []
        sheet.columnWidths.forEach(w => {
          columns.push({ width: w })
        })
        worksheet.columns = columns;


        //rows
        let titleRow = worksheet.addRow([sheet.tableName + ' (' + this.constants.pluralise(sheet.rows.length, 'vehicle', 'vehicles') + ')'])//title
        titleRow.font = { name: 'Calibri', family: 4, size: 16, bold: true, color: 'white', fill: { bgColor: { rgb: '220,230,241' } } };

        worksheet.getCell('A2').value = 'extracted ' + this.cphPipe.transform(new Date(), 'dateMed', 0) + ' ' + this.cphPipe.transform(new Date(), 'time', 0);
        worksheet.getCell('A2').font = { italic: true }
        worksheet.addRow([]);//blank

        //the table headerRow   
        let rowsSoFar = worksheet.rowCount + 1

        sheet.headers.forEach((headerRow, i) => {

          let row = worksheet.getRow(rowsSoFar + i)

          headerRow.forEach((cellValue, j) => {

            let cell = row.getCell(j + 1)

            cell.value = cellValue;
            cell.alignment = { vertical: 'middle', horizontal: 'right' };
            cell.font = { name: 'Calibri', family: 4, size: 11, color: { argb: 'FFFFFFFF' } }
            cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF252424' } }

            let format = sheet.columnTypes[j]

            switch (format) {
              case ('numberSimple'):
              case ('number'):
              case ('currency'):
              case ('percent'):
              case ('number1dp'):
              case ('percentNoRenderer'):
                cell.alignment = { horizontal: 'right' }
                break;
              default: {
                cell.alignment = { horizontal: 'center' }
              }
            }

          })
        })


        //add the data rows
        rowsSoFar = worksheet.rowCount + 1
        sheet.rows.forEach((rowData, i) => {

          let row = worksheet.getRow(rowsSoFar + i)
          //loop through each cell
          for (let j = 0; j < sheet.columnTypes.length; j++) { //loop through the columnTypes which effectively lets you walk across the columns

            let cell = row.getCell(j + 1)

            //format and value
            let format = sheet.columnTypes[j] //pick the format based on the columnType
            switch (format) {
              case ('numberSimple'):
              case ('number'): {
                cell.value = parseFloat(rowData[j])
                cell.numFmt = '#,##0;-#,##0;-';
                cell.alignment = { horizontal: 'right' }
                break;
              }
              case ('number1dp'): {
                cell.value = parseFloat(rowData[j])
                cell.numFmt = '#,##0.0;-#,##0.0;-';
                cell.alignment = { horizontal: 'right' }
                break;
              }
              case ('currency'): {
                cell.value = parseFloat(rowData[j])
                cell.numFmt = '\£#,##0;-\£#,##0;-';
                cell.alignment = { horizontal: 'right' }
                break;
              }
              case ('percent'):
              case ('percentNoRenderer'): {
                cell.value = parseFloat(rowData[j])
                cell.numFmt = '0.0%';
                cell.alignment = { horizontal: 'right' }
                break;
              } //style the cell
              default: {
                cell.value = rowData[j]
                cell.alignment = { horizontal: 'center' }
              }
            }


          }

          row.height = rowHeight ;
          row.alignment = { horizontal: 'center', vertical: 'middle' }

          let reasonTypeColNo = 10;
          let isResolvedColNo = 12;
          let linkColNo = 13;
          let setWrapCols = [7,8,9,11]
          let dirtyYellowCols = [10,11,12]

          if(!isUnknowns){
             reasonTypeColNo = 11;
             isResolvedColNo = 13;
             linkColNo = 14;
             setWrapCols = [2,3,6,8]
             dirtyYellowCols = [11,12,13]
          }
          
          //set options for reasons
          let reasons = isUnknowns ? this.constants.ResolutionTypesActive.filter(x=>!x.explainsMissingVehicle).sort((a, b) => a.description.localeCompare(b.description)).map(x=>x.description) : this.constants.ResolutionTypesActive.filter(x=>x.explainsMissingVehicle).sort((a, b) => a.description.localeCompare(b.description)).map(x=>x.description);
 
          for (let i = 0; i <= reasons.length + 8; i++) {
            worksheet.getCell(`BB${i+5}`).value = reasons[i];
          }

          row.getCell(reasonTypeColNo).dataValidation = {
            type: 'list',
            allowBlank: true,
            formulae: [`$BB$4:$BB$${reasons.length+6}`]
          };

          row.getCell(isResolvedColNo).dataValidation = {
            type: 'list',
            allowBlank: true,
            formulae: ['"True,False"']
          };

          // link page
          // if(includeLinkColumnOnEnd){
          //   row.getCell(linkColNo).value = worksheet.addImage(workbookImages[i], {
          //     tl: { col: 8, row: 4 + i }, //tl = top left
          //     //br: {col: 13, row: 2},
          //     ext: { width: 180, height: 180 },
          //     editAs: 'absolute'
          //   });
          // }

          //colour dirty yellow
          dirtyYellowCols.forEach(colNumber => {
            row.getCell(colNumber).fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFFFFCC' } }
          });

          //set wrap
          setWrapCols.forEach(n=>{
            row.getCell(n).alignment = {wrapText:true, vertical: 'middle', horizontal: 'center' };
          })



        })



        //images

        sheet.vehicleImages.forEach((image, i) => {
          if (image){
          worksheet.addImage(workbookImages[i], {
            tl: { col: 8, row: 4 + i }, //tl = top left
            //br: {col: 13, row: 2},
            ext: { width: 180, height: 180 },
            editAs: 'absolute'
          });
        }
        })







        //same for footers
        rowsSoFar = worksheet.rowCount + 1
        sheet.footers.forEach((footer, i) => {
          let row = worksheet.getRow(rowsSoFar + i)
          //loop through each cell
          for (let j = 0; j < sheet.columnTypes.length; j++) { //loop through the columnTypes which effectively lets you walk across the columns
            let cell = row.getCell(j + 1)

            cell.font = { name: 'Calibri', family: 4, size: 11, color: { argb: 'FFFFFFFF' } }
            cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF252424' } }
            //format
            let format = sheet.columnTypes[j] //pick the format based on the columnType
            switch (format) {
              case ('numberSimple'):
              case ('number'): {
                cell.value = parseFloat(footer[j])
                cell.numFmt = '#,##0;-#,##0;-';
                cell.alignment = { horizontal: 'right', vertical: 'middle' }
                break;
              }
              case ('number1dp'): {
                cell.value = parseFloat(footer[j])
                cell.numFmt = '#,##0.0;-#,##0.0;-';
                cell.alignment = { horizontal: 'right', vertical: 'middle' }
                break;
              }
              case ('currency'): {
                cell.value = parseFloat(footer[j])
                cell.numFmt = '\£#,##0;-\£#,##0;-';
                cell.alignment = { horizontal: 'right', vertical: 'middle' }
                break;
              }
              case ('percent'):
              case ('percentNoRenderer'): {
                cell.value = parseFloat(footer[j])
                cell.numFmt = '0.0%';
                cell.alignment = { horizontal: 'right', vertical: 'middle' }
                break;
              }
              default: { cell.value = footer[j] }
                cell.alignment = { horizontal: 'center', vertical: 'middle' }
            }


            row.height = 30;
            row.alignment = { vertical: 'middle' }

          }

        })



        //loop through the initial nth cols of each row and colour
        let rowCount = worksheet.rowCount + 1;

        for (let i = 4; i < rowCount; i++) {
          let row = worksheet.getRow(i)
          for (let j = 0; j < sheet.initialColumnsToHighlight; j++) {
            let cell = row.getCell(j + 1)
            cell.font = { name: 'Calibri', family: 4, size: 11, color: { argb: 'FFFFFFFF' } }
            cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF252424' } }
            cell.alignment = { horizontal: 'left', vertical: 'middle' }

          }

        }

        //images
        worksheet.addImage(cphLogo, {
          tl: { col: 11, row: 0 }, //tl = top left
          //br: {col: 13, row: 2},
          ext: { width: 180, height: 36 },
          editAs: 'absolute'
        });

        //special totals
        let rowsCount = sheet.rows.length + 4
        worksheet.getCell("F1").value = 'UnResolved'
        worksheet.getCell("F2").value = 'Resolved'
        if(isUnknowns){
          worksheet.getCell("G1").value = { formula: 'COUNTIF(L5:L' + rowsCount + ',false)', result: 10 }
          worksheet.getCell("G2").value = { formula: 'COUNTIF(L5:L' + rowsCount + ',true)', result: 0 };
        }else{
          worksheet.getCell("G1").value = { formula: 'COUNTIF(M5:M' + rowsCount + ',false)', result: 10 }
          worksheet.getCell("G2").value = { formula: 'COUNTIF(M5:M' + rowsCount + ',true)', result: 0 };

        }

        ['F1', 'F2', 'G1', 'G2'].forEach(ref => {
          let cell = worksheet.getCell(ref)
          cell.alignment = { horizontal: 'right', vertical: 'middle' }
          cell.font = { bold: true, size: 11 }
        })




      }
      catch (e) {
        //carry on?
      }


    })


    //let workbookName = 'StockPulse ' + new Date().getDate() + new Date().toLocaleString('en-gb', { month: 'short' }) + new Date().getFullYear();
    let workbookName = sheets[0].tableName;
    workbook.xlsx.writeBuffer().then((data) => {
      let blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      fs.saveAs(blob, workbookName + '.xlsx');
    });



    //XLSX.writeFile(workbook, 'my_file.xls', { bookType: 'xls', type: 'buffer' });
  }


  public workoutColWidths(tableData: any[]) {
    let colWidths: number[] = Object.keys(tableData[0]).map(x => x ? x.length : 0);
    tableData.forEach(item => {
      Object.values(item).forEach((value, i) => {
        let val = (value as string);
        let len = 0;
        if (typeof (val) === 'string') {
          len = val.trim().length;
        }
        if (len > colWidths[i]) { colWidths[i] = len; }
      });
    });
    colWidths.forEach((x, i, a) => a[i] = x + 3); //add a bit of padding on the end
    return colWidths;
  }





}
