﻿using Microsoft.EntityFrameworkCore;
using StockPulse.Loader.Stockpulse.ViewModel;
using StockPulse.Loader.ViewModel;
using StockPulse.Model;
using StockPulse.Model.Import;
using StockPulse.Repository.Database;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;

namespace StockPulse.Loader.Services.Lithia
{


    public class LithiaBookedAndPendingLoaderService : GenericLoaderJobServiceParams
    {

        public LithiaBookedAndPendingLoaderService()
        {

        }

        public JobParams GetMatchingFilesAndImportParams(string incomingRoot)
        {
            string customer = "lithia";
            string filePattern = "*Sales_BookedFinalized*.csv";

            // As we are processing different stock types dependent on file
            // the StockTypes property will be amended in the interpret file
            JobParams parms = new JobParams()
            {
                jobType = LoaderJob.LithiaBookedAndPending,
                customerFolder = customer,
                filename = filePattern,
                importSPName = null,
                loadingTableName = "ReconcilingItems",
                jobName = "LithiaBookedAndPendingJob",
                pulse = PulsesService.STK_LithiaBookedAndPending,
                fileType = FileType.csv,
                regexPattern = "((?<=\")[^\"]*(?=\"(,|$)+)|(?<=,|^)[^,\"]*(?=,|$))",
                headerFailColumn = null,
                errorCount = 0,
                dealerGroupId = 10,
                headerDefinitions = BuildHeaderDictionary(),
                allMatchingFiles = Directory.GetFiles(incomingRoot.Replace("{customer}", customer), filePattern),
                reconcilingItemTypeIdsToInclude = "107,102",
                isUS = true
            };

            return parms;
        }


        public DataTable InterpretFile(JobParams parms, RowsAndHeadersResult rowsAndHeaders, LogMessage logMessage)
        {
            List<Model.Input.ReconcilingItem> incomingLines = new List<Model.Input.ReconcilingItem>();// ExtractIncoming(parms, ref parms.errorCount, logMessage, allRows, headerLookup);

            int incomingProcessCount = 0;
            incomingLines = new List<Model.Input.ReconcilingItem>(10000);  //preset the list size (slightly quicker than growing it each time)

            Dictionary<string, int> missingSitesDictionary = new Dictionary<string, int>();

            using (var db = new StockpulseContext(true))
            {
                List<SiteDescriptionDictionary> siteDescriptionDictionary = 
                        db.SiteDescriptionDictionary.Where(x => x.IsPrimarySiteId && x.DealerGroupId == parms.dealerGroupId).AsNoTracking().ToList();

                int total = rowsAndHeaders.rowsAndCells.Count;

                // Each line here will contain at least one item
                // But also potentially up to two more
                foreach (var rowCols in rowsAndHeaders.rowsAndCells)
                {
                    incomingProcessCount++;

                    System.Console.WriteLine($"Count {incomingProcessCount} / {total}");

                    // FOR TESTING - COMMENT OUT
                    // if(incomingProcessCount > 2500) { continue; }

                    try
                    {
                        string siteName = rowCols[rowsAndHeaders.headerLookup["StockCheckSite"]].Trim();

                        if (SharedLoaderService.SkipSiteForLithiaUS(siteName))
                        {
                            continue;
                        }

                        SiteDescriptionDictionary siteDictionary = siteDescriptionDictionary.Where(x => x.Description == siteName).FirstOrDefault();

                        // Unable to find Site
                        if (siteDictionary == null)
                        {
                           parms.errorCount++;

                           if (siteName == "" || siteName == null)
                           {
                              siteName = "[BLANK NAME]";
                           }

                           if (!missingSitesDictionary.ContainsKey(siteName))
                           {
                              missingSitesDictionary[siteName] = 1;
                           }
                           else
                           {
                              missingSitesDictionary[siteName] += 1;
                           }

                           continue;
                        }

                        // Found site in dictionary - get SiteId (will go to catch if not found)
                        int siteId = siteDictionary.SiteId;
                        
                        // First item - this will always be there
                        Model.Input.ReconcilingItem incomingNewStock = new Model.Input.ReconcilingItem()
                        {
                            SiteId = siteId,
                            Vin = rowCols[rowsAndHeaders.headerLookup["Trade0VIN"]].ToString().Trim(),
                            Description = rowCols[rowsAndHeaders.headerLookup["Description"]].ToString().Trim(),
                            Reference = rowCols[rowsAndHeaders.headerLookup["Reference"]].ToString().Trim(),
                            FileImportId = parms.fileImportId,
                            DealerGroupId = parms.dealerGroupId,
                            ReconcilingItemTypeId = 102,
                            SourceReportId = 1
                        };

                        incomingLines.Add(incomingNewStock);

                        // FIRST PART EXCHANGE IF APPLICABLE
                        if (rowCols[rowsAndHeaders.headerLookup["Trade1VIN"]].ToString().Length > 0)
                        {
                            Model.Input.ReconcilingItem incomingNewStock1 = new Model.Input.ReconcilingItem()
                            {
                                SiteId = siteId,
                                Vin = rowCols[rowsAndHeaders.headerLookup["Trade1VIN"]].ToString().Trim(),
                                Description = rowCols[rowsAndHeaders.headerLookup["Description"]].ToString().Trim(),
                                Reference = rowCols[rowsAndHeaders.headerLookup["Reference"]].ToString().Trim(),
                                Comment = rowCols[rowsAndHeaders.headerLookup["Comment"]].ToString().Trim(),
                                FileImportId = parms.fileImportId,
                                DealerGroupId = parms.dealerGroupId,
                                ReconcilingItemTypeId = 107,
                                SourceReportId = 1
                            };

                            incomingLines.Add(incomingNewStock1);
                        }

                        // SECOND PART EXCHANGE IF APPLICABLE 
                        if (rowCols[rowsAndHeaders.headerLookup["Trade2VIN"]].ToString().Length > 0)
                        {
                            Model.Input.ReconcilingItem incomingNewStock2 = new Model.Input.ReconcilingItem()
                            {
                                SiteId = siteId,
                                Vin = rowCols[rowsAndHeaders.headerLookup["Trade2VIN"]].ToString().Trim(),
                                Description = rowCols[rowsAndHeaders.headerLookup["Description"]].ToString().Trim(),
                                Reference = rowCols[rowsAndHeaders.headerLookup["Reference"]].ToString().Trim(),
                                Comment = rowCols[rowsAndHeaders.headerLookup["Comment"]].ToString().Trim(),
                                FileImportId = parms.fileImportId,
                                DealerGroupId = parms.dealerGroupId,
                                ReconcilingItemTypeId = 107,
                                SourceReportId = 1
                            };

                            incomingLines.Add(incomingNewStock2);
                        }

                    }

                    catch (Exception err)
                    {
                        logMessage.FailNotes = logMessage.FailNotes + $" failed on adding item, Item: {incomingProcessCount} {err.ToString()} <br>";
                        parms.errorCount++;
                        continue;
                    }
                }
            }

            missingSitesDictionary = missingSitesDictionary
             .OrderBy(kvp => kvp.Key) // Sort by siteName
             .ToDictionary(kvp => kvp.Key, kvp => kvp.Value);

            foreach (var item in missingSitesDictionary)
            {
               logMessage.FailNotes += $"Unable to find site: {item.Key} ({item.Value}) <br>";
            }

            DataTable result = incomingLines.ToDataTable();
            result.Columns.Remove("Sites");
            result.Columns.Remove("FileImport");
            result.Columns.Remove("DealerGroup");
            result.Columns.Remove("ReconcilingItemType");
            result.Columns.Remove("SourceReports");
            return result;
        }

        private Dictionary<string, string> BuildHeaderDictionary()
        {
            Dictionary<string, string> headerDefinitions = new Dictionary<string, string>()
                {
                        { "StockCheckSite",  "COMPANY_NAME"}, // Same for all x3
                        { "Reference", "STOCK_NUMBER" },
                        { "Description", "DEAL_NUMBER" },
                        { "Trade0VIN", "VIN" },
                        { "Trade1VIN", "TRADE1_VIN" },
                        { "Trade2VIN", "TRADE2_VIN" },
                        { "Comment", "VIN" }, // Only for part exchanges
                };

            return headerDefinitions;
        }

        private decimal GetDecimal(string raw)
        {
            try
            {
                return decimal.Parse(raw);
            }
            catch
            {
                return 0;
            };
        }

        private int? GetNullableInt(string raw)
        {
            try
            {
                return int.Parse(raw);
            }
            catch
            {
                return null;
            };
        }

        private string GetVinLastEightChars(string raw)
        {
            try
            {
                string vinRaw = raw.ToString().Trim();
                int length = vinRaw.Length;
                return vinRaw.Substring(length - 8);
            }
            catch
            {
                return null;
            }

        }




    }
}
