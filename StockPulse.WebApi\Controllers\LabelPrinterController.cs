﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using static StockPulse.WebApi.Attribute.UserLevelAccessAttribute;
using StockPulse.WebApi.Attribute;
using StockPulse.WebApi.Service;
using System;
using System.Threading.Tasks;
using StockPulse.WebApi.ViewModel;

namespace StockPulse.WebApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    [UserLevelAccess(new[] { UserRole.SysAdministrator, UserRole.Reconciler, UserRole.Resolver, UserRole.Approver, UserRole.GeneralManager, UserRole.ReadOnly, UserRole.ScanAndPrint, UserRole.ScanAndView })]

    public class LabelPrinterController : ControllerBase, IAttributeValueProvider
    {
        private readonly ILabelPrinterService labelPrinterService;
        private readonly int userId;
        private readonly string userRole;

        public UserRole attributeUserRole => GetUserRole();
        private UserRole GetUserRole()
        {
            return (UserRole)Enum.Parse(typeof(UserRole), userRole);
        }

        public LabelPrinterController (ILabelPrinterService labelPrinterService, IUserService userService)
        {
            this.labelPrinterService = labelPrinterService;
            userId = userService.GetUserId();
            userRole = userService.GetUserRole();
        }

        [HttpPost]
        [Route("SavePrintLog")]
        [UserLevelAccess(new[] { UserRole.SysAdministrator, UserRole.Reconciler, UserRole.Resolver, UserRole.Approver })]
        public async Task SavePrintLog(SavePrintLogParams parms)
        {
            await labelPrinterService.SavePrintLog(parms.vin, userId);
        }
    }
}
