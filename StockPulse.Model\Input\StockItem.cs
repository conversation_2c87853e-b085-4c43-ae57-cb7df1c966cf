﻿using StockPulse.Model.Import;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace StockPulse.Model.Input
{

    // modelBuilder.Entity<WebApi.Model.Input.StockItem>().HasIndex(p => new { p.<PERSON>, p.<PERSON>, p.SiteId }).IsUnique();
    [Table("StockItems", Schema = "input")]
    public class StockItem
    {
        [Key]
        public int Id { get; set; }

        public int SourceReportId { get; set; }
        [ForeignKey("SourceReportId")]
        public virtual SourceReport SourceReports { get; set; }

        public string Reg { get; set; }

        public string Vin { get; set; }

        public string Description { get; set; }

        public int? DIS { get; set; }

        public int? GroupDIS { get; set; }

        public string Branch { get; set; }

        public string Comment { get; set; }

        public string StockType { get; set; }

        public string Reference { get; set; }

        public decimal StockValue { get; set; }

        public int SiteId { get; set; }
        [ForeignKey("SiteId")]
        public virtual Site Sites { get; set; }


        public int? FileImportId { get; set; }
        [ForeignKey("FileImportId")]
        public virtual FileImport FileImport { get; set; }


        public int DealerGroupId { get; set; }
        [ForeignKey("DealerGroupId")]
        public virtual DealerGroup DealerGroup { get; set; }


        public decimal Flooring { get; set; }

    }

}