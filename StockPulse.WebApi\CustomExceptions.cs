﻿using System;
using System.Globalization;

namespace StockPulse.WebApi
{

    public class InvalidWebVersionException : Exception
    {
        public InvalidWebVersionException() : base() { }

        public InvalidWebVersionException(string message) : base(message) { }

        public InvalidWebVersionException(string message, params object[] args)
            : base(String.Format(CultureInfo.CurrentCulture, message, args))
        {
        }
    }

    public class InvalidMobileVersionException : Exception
    {
        public InvalidMobileVersionException() : base() { }

        public InvalidMobileVersionException(string message) : base(message) { }

        public InvalidMobileVersionException(string message, params object[] args)
            : base(String.Format(CultureInfo.CurrentCulture, message, args))
        {
        }
    }
}
