﻿--1. Find the dependent SPs for this type via SSMS Object Explorer (right click -> View dependencies)
--2. Drop the dependent SPs
--3. Drop the type
--4. Recreate the type
--5. Recreate the dependent SPs


--Deleting Dependent SPs
DROP PROC [dbo].[INSERT_FinancialLine]
GO

--Deleting Type
DROP TYPE [dbo].[FinancialLineType]
GO


--Creating Type
CREATE TYPE [dbo].[FinancialLineType] AS TABLE(
      [Code] [nvarchar](50)
      ,[AccountDescription] [nvarchar](250) NULL
      ,[Notes] [nvarchar](250) NULL
      ,[IsExplanation] [bit]
      ,[Balance] [decimal](15,2)
      ,[StockCheckId] [int]
      ,[FileImportId] [int]
)
GO

--Create the dependent SPs 
--(best would be to run all_sps.sql)