﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System;

namespace StockPulse.Model
{
    public class SourceReport
    {
        [Key]
        public int Id { get; set; }


        public int UserId { get; set; }
        [ForeignKey("UserId")]
        public virtual User User { get; set; }


        public string Filename { get; set; }

        [Column(TypeName = "datetime")]
        public DateTime DateTime { get; set; }
    }

}