<div class="accountPage">
  <div class="inner">
    <div class="imgHolder">
      <img src="assets/imgs/stockpulseLogoBlack.svg">
    </div>

    <h2>Choose New Password</h2>
    <div *ngIf="resetSuccess">
      Your password has been reset successfully.<br>
      Click the button below to login with your new details.
    </div>
    <div *ngIf="resetFailed" class="validation-summary-errors text-danger">Failed to reset password.</div>
    <div *ngIf="passwordMatchFailed" class="validation-summary-errors text-danger">Your password and confirm password do
      not match.</div>
    <div *ngIf="usernameCriteriaFailed" class="validation-summary-errors text-danger">Please enter your email - e.g.
      <EMAIL></div>

    <section id="loginForm">
      <form [formGroup]="passwordResetFormGroup" (ngSubmit)="Submit()">
        <div class="form-group">

          <table>
            <tbody>

              <ng-container *ngIf="!resetSuccess">
                <tr>
                  <td>
                    <div class="inputAndIcon">
                      <input class="form-control" formControlName="userName" placeholder="Email" type="text" value=""
                        (focusout)="validateUsername($event)">
                      <fa-icon [icon]="icon.faUser" [fixedWidth]="true"></fa-icon>
                    </div>
                  </td>
                </tr>


                <tr>
                  <td>

                    <div class="inputAndIcon">
                      <input class="form-control" formControlName="password"
                        data-val-length="The Password must be at least 12 characters long." data-val-length-max="100"
                        data-val-length-min="6" data-val-required="The Password field is required."
                        placeholder="Password" type="password">
                      <fa-icon [icon]="icon.faLock" [fixedWidth]="true"></fa-icon>
                    </div>
                  </td>
                </tr>

                <tr>
                  <td>
                    <div class="validation-summary-errors" [ngClass]="{ 'text-danger': passwordcriteriaFailed }"
                      *ngIf="!resetSuccess">
                      Password must contain
                      <ul>
                        <li>At least 1 lowercase letter [a-z]</li>
                        <li>At least 1 uppercase letter [A-Z]</li>
                        <li>At least 12 characters</li>
                      </ul>
                    </div>
                  </td>
                </tr>


                <tr>
                  <td>
                    <div class="inputAndIcon">
                      <input class="form-control" formControlName="confirmPassword"
                        data-val-equalto="The password and confirmation password do not match."
                        data-val-equalto-other="*.Password" placeholder="Confirm password" type="password">
                      <fa-icon [icon]="icon.faLock" [fixedWidth]="true"></fa-icon>
                    </div>
                  </td>
                </tr>


              </ng-container>

              <tr>
                <td>
                  <button *ngIf="!resetSuccess" class="btn btn-primary" type="button" id="resetButton"
                    [disabled]="passwordResetFormGroup.pristine || passwordResetFormGroup.invalid || usernameCriteriaFailed"
                    (click)="Submit()">
                    Reset
                  </button>
                  <button *ngIf="resetSuccess" class="btn btn-primary" id="loginButton"
                    [disabled]="passwordResetFormGroup.pristine || passwordResetFormGroup.invalid || usernameCriteriaFailed"
                    (click)="RedirectToLogin()">
                    Login
                  </button>
                </td>
              </tr>
            </tbody>
          </table>


        </div>
        <!-- Password -->
        <div class="form-group" *ngIf="!resetSuccess">

        </div>



        <!-- ConfirmPassword -->
        <div class="form-group" *ngIf="!resetSuccess">

        </div>

      </form>
    </section>

  </div>




</div>