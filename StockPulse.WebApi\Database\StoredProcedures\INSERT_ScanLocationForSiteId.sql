

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE OR ALTER PROCEDURE [dbo].[INSERT_ScanLocationForSiteId]
(
    @SiteId INT = NULL,
	@NewLocation NVARCHAR(MAX) = NULL
)
AS
BEGIN

DECLARE @LocationId INT;

SELECT @LocationId = Id FROM Locations WHERE Description = @NewLocation;

IF @LocationId IS NULL
BEGIN
    INSERT INTO Locations (Description)
    VALUES (@NewLocation);

    SELECT @LocationId = SCOPE_IDENTITY();
END

SET NOCOUNT ON

INSERT INTO SiteLocations (SiteId, LocationId)
VALUES (@SiteId, @LocationId)

END

GO

