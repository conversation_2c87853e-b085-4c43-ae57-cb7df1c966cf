﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace StockPulse.Model
{
    public class MissingResolutionImage
    {
        [Key]
        public int Id { get; set; }

        public int MissingResolutionId { get; set; }
        [ForeignKey("MissingResolutionId")]
        public virtual MissingResolution MissingResolution { get; set; }
        public string FileName { get; set; }

    }
}