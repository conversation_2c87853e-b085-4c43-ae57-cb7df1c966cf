﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO




CREATE OR ALTER PROCEDURE [dbo].[GET_GlobalSearchResults]
(
--@StockCheckId INT = NULL,
    @UserId INT = NULL,
	@<PERSON> nvarchar(10) ,
	@<PERSON> nvarchar(10) ,
	@RequireAndMatch bit
)
AS
BEGIN


DECLARE @DealerGroupId INT;
SET @DealerGroupId = (SELECT DealerGroupId FROM Users WHERE Id = @UserId)

IF @Reg = '' AND @Vin = '' 
BEGIN 
	SELECT NULL
END

ELSE

	BEGIN

	--Find Scans matching
	SELECT 
	sca.Id As ScanId, 
	sca.StockItemId, 
	si.Description As StockCheckSiteName, 
	SC.Date As StockCheckDate
	FROM Scans AS sca
	INNER JOIN StockChecks AS SC ON sca.StockCheckId = SC.Id
	INNER JOIN Sites AS si ON si.Id = SC.SiteId
	INNER JOIN Users AS U ON sca.UserId = U.Id
	INNER JOIN Locations AS L ON sca.LocationId = L.Id
	WHERE 
	(
		(
			@RequireAndMatch = 1 AND
			(sca.Reg <> '' AND sca.Reg IS NOT NULL AND sca.Reg = @Reg ) AND
				(sca.Vin <> '' AND sca.Vin IS NOT NULL AND sca.Vin = @Vin )
		)
		OR
		(
			@RequireAndMatch = 0 AND
			(
				(sca.Reg <> '' AND sca.Reg IS NOT NULL AND sca.Reg = @Reg ) OR
				(sca.Vin <> '' AND sca.Vin IS NOT NULL AND sca.Vin = @Vin )
			)
		)
	)

	AND sca.StockItemId IS NULL --to prevent us finding same item twice, as the query below will find this
	AND U.DealerGroupId = @DealerGroupId


	--Find StockItems matching
	SELECT 
	SI.Id As StockItemId, 
	SI.ScanId, 
	ST.Description As StockCheckSiteName, 
	SC.Date As StockCheckDate
	FROM StockItems AS SI
	INNER JOIN StockChecks AS SC ON SI.StockCheckId = SC.Id
	INNER JOIN Sites AS ST ON ST.Id = SC.SiteId
	INNER JOIN Divisions AS D ON ST.DivisionId = D.Id
	INNER JOIN DealerGroup AS DG ON d.DealerGroupId = DG.Id
	WHERE 
	(
		(
			@RequireAndMatch = 1 AND
			(si.Reg <> '' AND si.Reg IS NOT NULL AND si.Reg = @Reg ) AND
				(si.Vin <> '' AND si.Vin IS NOT NULL AND si.Vin = @Vin )
		)
		OR
		(
			@RequireAndMatch = 0 AND
			(
				(si.Reg <> '' AND si.Reg IS NOT NULL AND si.Reg = @Reg ) OR
				(si.Vin <> '' AND si.Vin IS NOT NULL AND si.Vin = @Vin )
			)
		)
	)
	
	AND DG.Id = @DealerGroupId

	END

END

GO


