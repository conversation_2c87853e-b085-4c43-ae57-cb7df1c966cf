.printOnly {
    display: none;
}

.mainContentHolder {
    display: flex;
    align-items: center;
}

.imageHolder {
    margin-right: 2em;
}

.mainContentHolder .imageHolder img,
.mainContentHolder .imageHolder .imgSmallPlaceholder {
    width: 10em;
}

.mainContentHolder .imageHolder .imgSmallPlaceholder {
    min-height: 120px;
    min-width: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
}

table {
    width: 100%;

    td:nth-of-type(1){width: 33%;}
    td:nth-of-type(2){width: 7%;text-align: right;}
    td:nth-of-type(3){width: 25%;}
    td:nth-of-type(4){width: 25%;}
    td:nth-of-type(5){width: 10%;}
}

table th {
    text-align: center;
    font-weight: 400!important;
}
table th:nth-of-type(1){text-align: left!important;}

table td.center {
    text-align: center !important;
}



.chassis,
.regPlate {
    width: 75%;
    margin: 0 auto;
}


fa-icon {
    color: var(--danger) !important
}

fa-icon.good {
    color: var(--success) !important
}



