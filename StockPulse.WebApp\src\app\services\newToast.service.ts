import { Injectable } from '@angular/core';
import { CreateHotToastRef, HotToastService } from '@ngneat/hot-toast';
import { ConstantsService } from './constants.service';

@Injectable({
    providedIn: 'root'
})
export class ToastService {
    //toastReference: CreateHotToastRef<any>;

    constructor(
        private hotToastService: HotToastService,
        private constants: ConstantsService
    ) { }

    successToast(message: string) {
        return this.hotToastService.success(message, { theme: 'snackbar', iconTheme: { primary: '#28A745' } });
    }

    errorToast(message: string) {
        this.destroyToast(); //Clear all toasts, this helps in clearing all stale, specially Loading... toasts.
        this.hotToastService.error(message, { theme: 'snackbar' });
    }

    userNotFoundToast(message: string) {
        this.hotToastService.error(message, { theme: 'snackbar', autoClose: false,style: {
            maxWidth: '800px'
        }}); //email addresses are long 
    }

    loadingToast(message: string = "Loading...", backdrop?: boolean): CreateHotToastRef<any> {
        if (backdrop) {this.constants.spinnerBackdrop = true;}
        return this.hotToastService.loading(message, { theme: 'snackbar', autoClose: false });
        //return this.toastReference;
    }

    destroyToast() {
        this.constants.spinnerBackdrop = false;
        this.hotToastService.close();
        // if (this.toastReference) {
        //     this.toastReference = null;
        // }
    }

    closeToast(toast){
        if(toast){
            toast.close();
        }
    }
    closeToastInAWhile(toast){
        setTimeout(()=>{
            toast.close()
        },2000)
    }
}
