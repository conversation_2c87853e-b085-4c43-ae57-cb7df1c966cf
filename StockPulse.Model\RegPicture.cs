﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace StockPulse
{
    public class RegPicture
    {
        [Key]
        public int Id { get; set; }
        //public string Image { get; set; }
        [MaxLength(99999)]
        [Column(TypeName = "varbinary(max)")]
        public byte[] ImageBytes { get; set; }

        [Column(TypeName = "datetime")]
        public DateTime RequestDate { get; set; }
        public int Priority { get; set; }
        public bool IsDone { get; set; }
        
        public string Result { get; set; }
    }


}