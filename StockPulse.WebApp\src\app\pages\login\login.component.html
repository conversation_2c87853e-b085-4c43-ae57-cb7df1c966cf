<div id="backgroundImage"></div>
<div class="accountPage" *ngIf="!isRefByAuthRedirect">
  <div class="inner">
    
    <div class="imgHolder">
      <img src="assets/imgs/stockpulseLogoBlack.svg">
    </div>
    
    <section id="loginForm">
      <form [formGroup]="loginFormGroup">
        <!-- <div *ngIf="loginError || incorrectAccessRights" class="validation-summary-errors text-danger">
        </div> -->
          

        <table>
          <tbody>


            <tr *ngIf="loginService.incorrectAccessRights" > 
              <td >
                Your account level does not permit you to use the StockPulse web app.
                Please log in to the iOS or Android mobile app.
              </td>
            </tr>



            <!-- Email -->
            <tr>
              <td>
                <div class="inputAndIcon">
                  <fa-icon [icon]="icon.faUser" [fixedWidth]="true"></fa-icon>
                  <input id="email" class="form-control" formControlName="email"
                    placeholder="email, e.g. <EMAIL>" type="text" value="">
                </div>
              </td>
              <td>
                <fa-icon  class="error animated bounceIn"
                  *ngIf="loginFormGroup.controls.email?.status==='INVALID' && loginFormGroup.controls?.email?.touched"
                  [icon]="icon.faCircleExclamation" [fixedWidth]="true"></fa-icon>
              </td>
            </tr>

            <!-- Password -->
            <tr>
              <td>
                <div class="inputAndIcon">
                <fa-icon [icon]="icon.faLock" [fixedWidth]="true"></fa-icon>
                <input class="form-control"  formControlName="password" placeholder="Password" type="password">
              </div>
              </td>
              <td>
                <fa-icon  *ngIf="loginFormGroup.controls.password?.status==='INVALID' && loginFormGroup.controls?.password?.touched"
                class="error animated bounceIn" [icon]="icon.faCircleExclamation" [fixedWidth]="true"></fa-icon>
              </td>
            </tr>


            <!-- Log in button -->
            <tr>
              <td >
                <button id="loginButton" (click)="submit()"
                  [disabled]="loginFormGroup.pristine || loginFormGroup.invalid" class="btn btn-primary">
                  <span *ngIf="!haveSubmittedForm">Log in</span>
                   <fa-icon *ngIf="haveSubmittedForm" [icon]="icon.faCircleNotch" class="spinAndColour"> </fa-icon>
                
                </button>
              </td>
            </tr>

           

            <!-- Forgot password button -->
            <tr>
              <td >
                <button id="forgotPasswordButton" (click)="redirectToForgotPassword()" class="btn btn-danger">
                  Forgot Password
                </button>
              </td>
            </tr>

            <tr>
              <td>
                <div id="orMessage">
                  or
                </div>
              </td>
            </tr>


            <tr>
              <td>
                <button id = "microsoftButton" class="btn btn-primary" (click)="loginWithAzureAD()">
                  <img id="microsoftImage" src="assets/imgs/Microsoft.png"> Sign in with Microsoft
                </button>
              </td>
            </tr>

          </tbody>
        </table>


      </form>


     

    </section>



    <!-- Azure login -->
    <section>
     
    </section>
  </div>
</div>