﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace StockPulse.Repository.Migrations
{
    /// <inheritdoc />
    public partial class removeconstraintsfrominputtable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_StockItems_Reg_Vin_SiteId",
                schema: "input",
                table: "StockItems");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateIndex(
                name: "IX_StockItems_Reg_Vin_SiteId",
                schema: "input",
                table: "StockItems",
                columns: new[] { "Reg", "Vin", "SiteId" },
                unique: true,
                filter: "[Reg] IS NOT NULL AND [Vin] IS NOT NULL");
        }
    }
}
