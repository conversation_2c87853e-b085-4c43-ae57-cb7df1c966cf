﻿using Microsoft.Extensions.Configuration;
using StockPulse.WebApi.Controllers;
using StockPulse.WebApi.DataAccess;
using StockPulse.Model;
using StockPulse.WebApi.ViewModel;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Collections.Concurrent;

namespace StockPulse.WebApi.Service
{
   public interface IStockCheckService
   {
      Task<IEnumerable<StockCheckVM>> GetStockChecks(int userId, bool isActive, DateTime? fromDate, DateTime? toDate, int? stockcheckId);

      Task CreateStockChecksForAllSites(DateTime selectedDate, int userId, int dealerGroupId);
      Task<IEnumerable<ReconciliationBucket>> GetReconciliationBuckets(int stockcheckId, int userId);
      Task<IEnumerable<ResolutionBucket>> GetResolutionBuckets(int stockcheckId, int userId);
      Task CreateStockCheck(CreateStockCheckParams parms, int userId);
      Task UpdateStatus(StatusUpdate updateStatus, int userId);
      Task<bool> AddSignoffPicture(SignoffPictureSubmission pictureDetails, int userId);
      Task<IEnumerable<StockCheckVM>> GetStockChecksForSite(int stockCheckId, int userId);
      Task<StockCheckMobileApp> GetStockCheckMobileApp(int stockCheckId, int userId);
      Task<IEnumerable<PhotoMapItemGroup>> GetPhotoMapItemGroups(int stockcheckId, PhotoMapReportType reportType, int userId);
      Task<IEnumerable<WaterfallBarDetailItem>> GetWaterfallBarDetail(WaterfallBarDetailParams parms, int userId);
      Task<IEnumerable<WaterfallBarSet>> GetAllWaterfallBarDetail(int stockcheckId, int userId);
      Task Archive(List<int> stockCheckIds, int userId);
      Task UnArchive(List<int> stockCheckIds, int userId);
      Task<string> Delete(List<int> stockCheckIds, int userId);
      Task<IEnumerable<int>> CheckIfAlreadyExists(string stockCheckDate, List<int> siteIds);
      Task<IEnumerable<int>> GetStockCheckIdsForUserDealerGroup(int userId);
      Task ImportLatestData(ImportLatestDataParams parms, int userId, int dealerGroupId);
      Task<DateTime> GetLatestDataRecievedDate(int dealerGroupId);
   }

   public class StockCheckService : IStockCheckService
   {
      //properties of the service
      private readonly IStockCheckDataAccess stockCheckDataAccess;
      private readonly IImageService imageService;
      private readonly IConfiguration _config;
      private readonly IStockItemService stockItemService;
      private readonly ISiteService siteService;
      private readonly IReconcilingItemService reconcilingItemService;
      private readonly IScanService scanService;
      private readonly IScanDataAccess scanDataAccess;
      private readonly IStockCheckReconciliationService stockCheckReconciliationService;
      private string Connectionstring = "DefaultConnection";
      private readonly IStatusChangeLogItemsDataAccess StatusChangeLogItemsDataAccess;

      public StockCheckService(
          IStockCheckDataAccess stockCheckDataAccess,
          IImageService imageService,
          IConfiguration config,
          IStockItemService stockItemService,
          ISiteService siteService,
          IReconcilingItemService reconcilingItemService,
          IScanService scanService,
          IScanDataAccess scanDataAccess,
          IStatusChangeLogItemsDataAccess StatusChangeLogItemsDataAccess
          // IStockCheckReconciliationService stockCheckReconciliationService
          )
      {
         this.stockCheckDataAccess = stockCheckDataAccess;
         this.imageService = imageService;
         _config = config;
         this.stockItemService = stockItemService;
         this.siteService = siteService;
         this.reconcilingItemService = reconcilingItemService;
         this.scanService = scanService;
         this.scanDataAccess = scanDataAccess;
         this.StatusChangeLogItemsDataAccess = StatusChangeLogItemsDataAccess;
         // this.stockCheckReconciliationService = stockCheckReconciliationService;
      }

      public async Task ImportLatestData(ImportLatestDataParams parms, int userId, int dealerGroupId)
      {

         IEnumerable<int> listOfStockCheckIdsToReconcile = [];

         // Marshalls
         if (dealerGroupId == 11)
         {
            listOfStockCheckIdsToReconcile = await stockCheckDataAccess.ImportLatestDataMMG(parms, userId);
         }
         // Lithia US
         else if (dealerGroupId == 10)
         {
            listOfStockCheckIdsToReconcile = await stockCheckDataAccess.ImportLatestDataLithia(parms, userId);
         }
         // Jardines (also Lithia UK)
         else if (dealerGroupId == 7)
         {
            listOfStockCheckIdsToReconcile = await stockCheckDataAccess.ImportLatestDataJardine(parms, userId);
         }


         //Reconcile other impacted StockChecks
         foreach (var stockcheckId in listOfStockCheckIdsToReconcile)
         {
            await stockCheckReconciliationService.ReconcileStockCheck(stockcheckId, userId);
         }

      }

      public async Task<DateTime> GetLatestDataRecievedDate(int dealerGroupId)
      {
         return await stockCheckDataAccess.GetLatestDataRecievedDate(dealerGroupId);
      }

      public async Task CreateStockCheck(CreateStockCheckParams parms, int userId)
      {
         await stockCheckDataAccess.CreateStockCheck(parms, userId);
      }

      public async Task CreateStockChecksForAllSites(DateTime selectedDate, int userId, int dealerGroupId)
      {
         await stockCheckDataAccess.CreateStockChecksForAllSites(selectedDate, userId, dealerGroupId);
      }

      public async Task<IEnumerable<WaterfallBarSet>> GetAllWaterfallBarDetail(int stockcheckId, int userId)
      {
         List<WaterfallBarSet> results = new List<WaterfallBarSet>();

         //get all reconciliationIds for this stockCheckId
         IEnumerable<ReconcilingItemTypeStat> reconcilationTypes = await reconcilingItemService.GetReconcilingItemTypeStats(stockcheckId, userId);

         //all stockItems
         results.Add(await CreateBarSet(stockcheckId, userId, ReconciliationState.AllItems, false, null));

         foreach (var type in reconcilationTypes.Where(x => x.ExplainsMissing))
         {
            bool isScan = type.Description.Contains("Scanned") ? true : false;
            results.Add(await CreateBarSet(stockcheckId, userId, ReconciliationState.MatchedToReport, isScan, type.Description));
         }
         ;

         results.Add(await CreateBarSet(stockcheckId, userId, ReconciliationState.Duplicate, false, null));
         results.Add(await CreateBarSet(stockcheckId, userId, ReconciliationState.OutstandingIssue, false, null));
         results.Add(await CreateBarSet(stockcheckId, userId, ReconciliationState.Resolved, false, null));
         results.Add(await CreateBarSet(stockcheckId, userId, ReconciliationState.MatchedToOtherSite, false, null));
         results.Add(await CreateBarSet(stockcheckId, userId, ReconciliationState.MatchedToStockOrScan, false, null));

         //all scans
         results.Add(await CreateBarSet(stockcheckId, userId, ReconciliationState.AllItems, true, null));

         foreach (var type in reconcilationTypes.Where(x => !x.ExplainsMissing))
         {
            bool isScan = type.Description.Contains("Scanned") ? true : false;
            results.Add(await CreateBarSet(stockcheckId, userId, ReconciliationState.MatchedToReport, isScan, type.Description));
         }
         ;

         results.Add(await CreateBarSet(stockcheckId, userId, ReconciliationState.OutstandingIssue, true, null));
         results.Add(await CreateBarSet(stockcheckId, userId, ReconciliationState.Resolved, true, null));
         results.Add(await CreateBarSet(stockcheckId, userId, ReconciliationState.MatchedToOtherSite, true, null));
         results.Add(await CreateBarSet(stockcheckId, userId, ReconciliationState.Duplicate, true, null));
         results.Add(await CreateBarSet(stockcheckId, userId, ReconciliationState.MatchedToStockOrScan, true, null));


         return results;
      }

      private async Task<WaterfallBarSet> CreateBarSet(int stockcheckId, int userId, ReconciliationState state, bool isScan, string recTypeDesc)
      {
         if (recTypeDesc != null)
         {
            IEnumerable<WaterfallBarDetailItem> items = await GetWaterfallBarDetail(new WaterfallBarDetailParams { IsScan = isScan, State = state, StockcheckId = stockcheckId }, userId);

            return new WaterfallBarSet
            {
               State = state,
               IsScan = isScan,
               Items = items.Where(x => x.MatchingItemTypeDesc == recTypeDesc).ToList(),
               ReconcilingItemTypeDescription = recTypeDesc,
            };
         }
         else
         {
            return new WaterfallBarSet
            {
               State = state,
               IsScan = isScan,
               Items = await GetWaterfallBarDetail(new WaterfallBarDetailParams { IsScan = isScan, State = state, StockcheckId = stockcheckId }, userId),
               ReconcilingItemTypeDescription = recTypeDesc,
            };
         }

      }

      public async Task<IEnumerable<WaterfallBarDetailItem>> GetWaterfallBarDetail(WaterfallBarDetailParams parms, int userId)
      {
         // ---------------------------------
         // Scans
         // ---------------------------------
         if (parms.IsScan)
         {
            return await GetScanWaterfallBarDetail(parms, parms.StockcheckId, userId);
         }

         // ---------------------------------
         // StockItems
         // ---------------------------------
         else
         {
            return await GetStockItemWaterfallBarDetail(parms, parms.StockcheckId, userId);
         }


         throw new NotImplementedException();
      }

      private async Task<IEnumerable<WaterfallBarDetailItem>> GetStockItemWaterfallBarDetail(WaterfallBarDetailParams parms, int stockCheckId, int userId)
      {
         // StockItems
         switch (parms.State)
         {
            case ReconciliationState.AllItems:
               {
                  //we want all the stockItems
                  IEnumerable<ViewModel.StockItem> stockItems = await stockItemService.GetStockItems(stockCheckId, userId);
                  return stockItems.ToList().ConvertAll(x => new WaterfallBarDetailItem(x));
               }
            case ReconciliationState.OutstandingIssue:
               {
                  IEnumerable<StockItemWithResolution> stockItems = await stockItemService.GetStockItemsWithResolution(stockCheckId, userId);
                  return stockItems.Where(x => !x.IsResolved).ToList().ConvertAll(x => new WaterfallBarDetailItem(x));
               }
            case ReconciliationState.Resolved:
               {
                  IEnumerable<StockItemWithResolution> stockItems = await stockItemService.GetStockItemsWithResolution(stockCheckId, userId);

                  foreach (var item in stockItems)
                  {
                     BuildOutImageURLsStock(item);
                  }

                  return stockItems.Where(x => x.IsResolved).ToList().ConvertAll(x => new WaterfallBarDetailItem(x));
               }
            case ReconciliationState.MatchedToReport:
               {
                  IEnumerable<StockItemMatchedToRecItem> stockItems = await stockItemService.GetStockItemsMatchedToRecItem(stockCheckId, parms.ReconcilingItemTypeId, userId);
                  return stockItems.ToList().ConvertAll(x => new WaterfallBarDetailItem(x));
               }
            case ReconciliationState.MatchedToOtherSite:
               {
                  IEnumerable<StockItemMatchedToOtherSiteScan> stockItems = await stockItemService.GetStockItemMatchedToOtherSiteScan(stockCheckId, userId);
                  return stockItems.ToList().ConvertAll(x => new WaterfallBarDetailItem(x));
               }
            case ReconciliationState.Duplicate:
               {
                  IEnumerable<ViewModel.StockItemThatIsADuplicate> stockItems = await stockItemService.GetDuplicateStockItems(stockCheckId, userId);
                  return stockItems.ToList().ConvertAll(x => new WaterfallBarDetailItem(x));
               }
            case ReconciliationState.MatchedToStockOrScan:
               {
                  IEnumerable<StockItemWithScan> items = await stockItemService.GetStockItemsWithScan(stockCheckId, userId);
                  return items.ToList().ConvertAll(x => new WaterfallBarDetailItem(x));
               }
            default:
               throw new NotImplementedException();
         }
      }

      private void BuildOutImageURLsStock(StockItemWithResolution item)
      {
         //now fill out the urls for the missing resolutions
         item.ResolutionImages = new List<ImageToUpdate>();
         if (item.ResolutionImageIds != null)
         {

            foreach (var resolutionImage in item.ResolutionImageIds.Split("::"))
            {
               if (resolutionImage != "|FileName")
               {
                  var resolutionId = resolutionImage.Split("|")[0];
                  var resolutionImageName = resolutionImage.Split("|")[1];
                  string imageUrl = imageService.GetMissingImageURL(resolutionId);
                  item.ResolutionImages.Add(new ImageToUpdate() { Id = int.Parse(resolutionId), Status = "BLOB", FileBase64 = "", Url = imageUrl, FileName = resolutionImageName });
               }
            }
         }
      }

      private async Task<IEnumerable<WaterfallBarDetailItem>> GetScanWaterfallBarDetail(WaterfallBarDetailParams parms, int stockCheckId, int userId)
      {
         //Scans
         switch (parms.State)
         {
            case ReconciliationState.AllItems:
               {
                  //we want all the scans
                  string stockcheckName = await stockCheckDataAccess.GetStockCheckSiteName(stockCheckId, userId);
                  IEnumerable<ViewModel.Scan> scans = await scanDataAccess.GetScans(stockCheckId, userId);
                  return scans.ToList().ConvertAll(x => new WaterfallBarDetailItem(x, stockcheckName));
               }

            case ReconciliationState.OutstandingIssue:
               {
                  string stockcheckName = await stockCheckDataAccess.GetStockCheckSiteName(stockCheckId, userId);
                  IEnumerable<ScanWithResolution> scansWithRes = await scanService.GetScansWithResolution(stockCheckId, userId);
                  return scansWithRes.Where(x => !x.IsResolved).ToList().ConvertAll(x => new WaterfallBarDetailItem(x, stockcheckName));
               }

            case ReconciliationState.Resolved:
               {
                  string stockcheckName = await stockCheckDataAccess.GetStockCheckSiteName(stockCheckId, userId);
                  IEnumerable<ScanWithResolution> scansWithRes = await scanService.GetScansWithResolution(stockCheckId, userId);

                  foreach (var item in scansWithRes)
                  {
                     BuildOutImageURLsScan(item);
                  }

                  return scansWithRes.Where(x => x.IsResolved).ToList().ConvertAll(x => new WaterfallBarDetailItem(x, stockcheckName));
               }
            case ReconciliationState.MatchedToReport:
               {
                  string stockcheckName = await stockCheckDataAccess.GetStockCheckSiteName(stockCheckId, userId);
                  IEnumerable<ScanMatchedToRecItem> scans = await scanService.GetScansMatchedToRecItem(stockCheckId, parms.ReconcilingItemTypeId, userId);
                  return scans.ToList().ConvertAll(x => new WaterfallBarDetailItem(x, stockcheckName));
               }
            case ReconciliationState.MatchedToOtherSite:
               {
                  string stockcheckName = await stockCheckDataAccess.GetStockCheckSiteName(stockCheckId, userId);
                  IEnumerable<ScanMatchedToOtherSiteStockItem> scans = await scanService.GetScanMatchedToOtherSiteStockItem(stockCheckId, userId);
                  return scans.ToList().ConvertAll(x => new WaterfallBarDetailItem(x, stockcheckName));
               }
            case ReconciliationState.Duplicate:
               {
                  string stockcheckName = await stockCheckDataAccess.GetStockCheckSiteName(stockCheckId, userId);
                  IEnumerable<ScanThatIsADuplicate> scans = await scanService.GetDuplicateScans(stockCheckId, userId);
                  return scans.ToList().ConvertAll(x => new WaterfallBarDetailItem(x, stockcheckName));
               }
            case ReconciliationState.MatchedToStockOrScan:
               {
                  IEnumerable<StockItemWithScan> items = await stockItemService.GetStockItemsWithScan(stockCheckId, userId);
                  return items.ToList().ConvertAll(x => new WaterfallBarDetailItem(x));
               }
            default:
               throw new NotImplementedException();
         }
      }

      private void BuildOutImageURLsScan(ScanWithResolution item)
      {
         //now fill out the urls for the missing resolutions
         item.ResolutionImages = new List<ImageToUpdate>();
         if (item.ResolutionImageIds != null)
         {

            foreach (var resolutionImage in item.ResolutionImageIds.Split("::"))
            {
               if (resolutionImage != "|FileName")
               {
                  var resolutionId = resolutionImage.Split("|")[0];
                  var resolutionImageName = resolutionImage.Split("|")[1];
                  string imageUrl = imageService.GetUnknownImageURL(resolutionId);
                  item.ResolutionImages.Add(new ImageToUpdate() { Id = int.Parse(resolutionId), Status = "BLOB", FileBase64 = "", Url = imageUrl, FileName = resolutionImageName });
               }
            }
         }
      }


      //methods of the service
      public async Task<IEnumerable<StockCheckVM>> GetStockChecks(int userId, bool isActive, DateTime? fromDate, DateTime? toDate, int? stockcheckId)
      {
         var res = await stockCheckDataAccess.GetStockChecksOverview(userId, isActive, fromDate, toDate, stockcheckId);
         return res;
      }

      public async Task<StockCheckMobileApp> GetStockCheckMobileApp(int stockCheckId, int userId)
      {
         StockCheckMobileApp res = await stockCheckDataAccess.GetStockCheck(userId, stockCheckId);

         res.StockItems = await stockItemService.GetStockItems(stockCheckId, userId);
         res.Locations = await siteService.GetLocationsForStockCheck(stockCheckId, userId);
         res.ReconcilingItems = await reconcilingItemService.GetReconcilingItemsWithType(stockCheckId, userId);
         res.Scans = (await scanService.GetScans(stockCheckId, userId)).OrderByDescending(x => x.ScanDateTime);

         return res;
      }

      public async Task<IEnumerable<PhotoMapItemGroup>> GetPhotoMapItemGroups(int stockcheckId, PhotoMapReportType reportType, int userId)
      {

         List<PhotoMapItem> items = new List<PhotoMapItem>();

         var customPriorityOrder = new Dictionary<ReconciliationState, int>
            {
                { ReconciliationState.Duplicate, 1 },
                { ReconciliationState.MatchedToStockOrScan, 2 },
                { ReconciliationState.MatchedToOtherSite, 3 },
                { ReconciliationState.MatchedToReport, 4 },
                { ReconciliationState.Resolved, 5 },
                { ReconciliationState.OutstandingIssue, 6 }
            };


         //allScans
         if (reportType == PhotoMapReportType.allScans)
         {
            IEnumerable<ViewModel.Scan> scans = await scanService.GetScans(stockcheckId, userId);
            items.AddRange(scans.ToList().ConvertAll(x => new PhotoMapItem(x, x.LocationDescription)).OrderBy(x => customPriorityOrder[x.ReconciliationState]));
            return ConvertItemsToGroupedList(items);
         }

         //unknownsByLocation
         if (reportType == PhotoMapReportType.unknownsByLocation)
         {
            IEnumerable<ViewModel.Scan> scans = await scanService.GetScans(stockcheckId, userId);
            items.AddRange(scans.Where(x => x.ScanState == ReconciliationState.OutstandingIssue || x.ScanState == ReconciliationState.Resolved)
                                .ToList().ConvertAll(x => new PhotoMapItem(x, x.LocationDescription)).OrderBy(x => customPriorityOrder[x.ReconciliationState]));
            return ConvertItemsToGroupedList(items);
         }

         //unknownsByResolutionType
         if (reportType == PhotoMapReportType.unknownsByResolutionType)
         {
            IEnumerable<ViewModel.ScanWithResolution> scans = await scanService.GetScansWithResolution(stockcheckId, userId);
            foreach (var scan in scans)
            {
               if (scan.ResolutionTypeDescription == null) { scan.ResolutionTypeDescription = "Not resolved"; }
               scan.ScanState = scan.IsResolved ? ReconciliationState.Resolved : ReconciliationState.OutstandingIssue;
            }
            items.AddRange(scans.ToList().ConvertAll(x => new PhotoMapItem(x, x.ResolutionTypeDescription)).OrderBy(x => x.GroupingLabel));
            return ConvertItemsToGroupedList(items);
         }

         //missingsByStockType
         if (reportType == PhotoMapReportType.missingsByStockType)
         {
            IEnumerable<ViewModel.StockItem> stockItems = await stockItemService.GetStockItems(stockcheckId, userId);
            items.AddRange(stockItems.Where(x => x.State == ReconciliationState.OutstandingIssue || x.State == ReconciliationState.Resolved)
                                .ToList().ConvertAll(x => new PhotoMapItem(x, x.StockType)).OrderBy(x => customPriorityOrder[x.ReconciliationState]));
            return ConvertItemsToGroupedList(items);
         }

         //missingsByResolutionType
         if (reportType == PhotoMapReportType.missingsByResolutionType)
         {
            IEnumerable<ViewModel.StockItemWithResolution> stockItems = await stockItemService.GetStockWithResolution(stockcheckId, userId);
            foreach (var stockItem in stockItems)
            {
               if (stockItem.ResolutionTypeDescription == null) { stockItem.ResolutionTypeDescription = "Not resolved"; }
               stockItem.State = stockItem.IsResolved ? ReconciliationState.Resolved : ReconciliationState.OutstandingIssue;
            }
            items.AddRange(stockItems.ToList().ConvertAll(x => new PhotoMapItem(x, x.ResolutionTypeDescription)).OrderBy(x => customPriorityOrder[x.ReconciliationState]));
            return ConvertItemsToGroupedList(items);
         }

         //allScansByScanner
         if (reportType == PhotoMapReportType.allScansByScanner)
         {
            IEnumerable<ViewModel.Scan> scans = await scanService.GetScans(stockcheckId, userId);
            items.AddRange(scans.ToList().ConvertAll(x => new PhotoMapItem(x, x.ScannerName)).OrderBy(x => customPriorityOrder[x.ReconciliationState]));
            return ConvertItemsToGroupedList(items);
         }

         throw new Exception("Unknown report type");
      }

      private static List<PhotoMapItemGroup> ConvertItemsToGroupedList(List<PhotoMapItem> items)
      {
         List<PhotoMapItemGroup> res = new List<PhotoMapItemGroup>();
         foreach (var grouping in items.ToLookup(x => x.GroupingLabel))
         {
            res.Add(new PhotoMapItemGroup() { Label = grouping.Key, Items = grouping.ToList() });
         }

         return res.OrderBy(x => x.Label).ToList();
      }

      public async Task<IEnumerable<ReconciliationBucket>> GetReconciliationBuckets(int stockcheckId, int userId)
      {
         IEnumerable<ReconciliationBucket> result = await stockCheckDataAccess.GetReconciliationBuckets(stockcheckId, userId);

         return RandomiseVehicleCounts(result.ToList());
      }

      public List<ReconciliationBucket> RandomiseVehicleCounts(List<ReconciliationBucket> buckets)
      {
         var random = new Random();

         foreach (var bucket in buckets)
         {
            bucket.VehicleCount = random.Next(0, 901); // 0 to 900 inclusive
         }

         return buckets;
      }

      public async Task<IEnumerable<ResolutionBucket>> GetResolutionBuckets(int stockcheckId, int userId)
      {
         return await stockCheckDataAccess.GetResolutionBuckets(stockcheckId, userId);
      }


      public async Task UpdateStatus(StatusUpdate statusUpdate, int userId)
      {

         using IDbConnection db = new SqlConnection(_config.GetConnectionString(Connectionstring));
         try
         {
            if (db.State == ConnectionState.Closed)
            {
               db.Open();
            }

            using var tran = db.BeginTransaction();
            try
            {
               //should go ahead and do reconcile first
               //await stockCheckReconciliationService.ReconcileStockCheck(statusUpdate.StockCheckId, userId);

               await stockCheckDataAccess.UpdateStatus(statusUpdate.StockCheckId, statusUpdate.StatusId, userId, db, tran);

               if (statusUpdate.Images != null)
               {
                  var image = statusUpdate.Images.Find(i => i.Status.ToUpper() == "DELETE");
                  if (image != null)
                  {
                     await stockCheckDataAccess.UpdateSignoffImageFlag(statusUpdate.StockCheckId, 0, userId, db, tran);

                     var result = await imageService.DeleteSignOffImage(statusUpdate.StockCheckId);
                     if (result.Equals(false))
                     {
                        throw new Exception("Delete Failed");
                     }
                  }

                  image = statusUpdate.Images.Find(i => i.Status.ToUpper() == "ADD");
                  if (image != null)
                  {
                     await stockCheckDataAccess.UpdateSignoffImageFlag(statusUpdate.StockCheckId, 1, userId, db, tran);

                     using (Stream stream = imageService.convertBase64ToStream(image.FileBase64))
                     {
                        var result = await imageService.UploadSignOffImage(stream, statusUpdate.StockCheckId, true);
                        if (result.Equals(false))
                        {
                           throw new Exception("Upload Failed");
                        }
                     }
                  }
               }

               tran.Commit();

               StatusChangeLogItemParams parms = new StatusChangeLogItemParams();
               parms.StockCheckId = statusUpdate.StockCheckId;
               parms.UserId = userId;
               parms.StatusId = statusUpdate.StatusId;

               await StatusChangeLogItemsDataAccess.AddStatusChangeLogItem(parms);
            }
            catch (Exception ex)
            {
               tran.Rollback();
               throw new Exception(ex.Message, ex);
            }
         }
         catch (Exception ex)
         {
            throw new Exception(ex.Message, ex);
         }
         finally
         {
            if (db.State == ConnectionState.Open)
               db.Close();
         }

      }

      public async Task<bool> AddSignoffPicture(SignoffPictureSubmission pictureDetails, int userId)
      {
         //need
         //to do something here to 1. Check user is ok to see this stockcheck.  2. Check stockcheck must be at status 4 (complete)  3.  If so, replace any existing stockcheck image with this one
         int StockChekcStatusId = await stockCheckDataAccess.GetStockCheckStatus(pictureDetails, userId);


         if (StockChekcStatusId == 4)
         {
            await stockCheckDataAccess.UpdateSignoffImageFlag(pictureDetails.StockCheckId, 1, userId);

            using (Stream stream = imageService.convertBase64ToStream(pictureDetails.PictureString))
            {
               var result = await imageService.UploadSignOffImage(stream, pictureDetails.StockCheckId, true);
               if (result.Equals(false))
               {
                  throw new Exception("Upload Failed");
               }
            }

            return true;
         }
         else
         {
            return false;
         }
      }

      public async Task<IEnumerable<StockCheckVM>> GetStockChecksForSite(int stockCheckId, int userId)
      {
         ConcurrentBag<StockCheckVM> stockCheckVMs = new ConcurrentBag<StockCheckVM>();
         await GetStockCheckAndAddToList(stockCheckVMs, stockCheckId, userId);

         var last4 = await stockCheckDataAccess.GetLastFourStockCheckIds(stockCheckId, userId);

         List<Task> tasks = new List<Task>();
         foreach (var id in last4)
         {
            tasks.Add(GetStockCheckAndAddToList(stockCheckVMs, id, userId));
         }

         await Task.WhenAll(tasks);

         return stockCheckVMs.ToList();
      }

      private async Task GetStockCheckAndAddToList(ConcurrentBag<StockCheckVM> stockCheckVMs, int stockCheckId, int userId)
      {
         stockCheckVMs.Add(await stockCheckDataAccess.GetStockChecks(stockCheckId, userId));
      }

      public async Task Archive(List<int> stockCheckIds, int userId)
      {
         await stockCheckDataAccess.Archive(stockCheckIds, userId);
      }

      public async Task UnArchive(List<int> stockCheckIds, int userId)
      {
         await stockCheckDataAccess.UnArchive(stockCheckIds, userId);
      }

      public async Task<string> Delete(List<int> stockCheckIds, int userId)
      {
         int totalScanCount = await scanDataAccess.GetTotalScansCount(stockCheckIds, userId);
         if (totalScanCount > 0)
         {
            return "Error";
         }

         int totalStockItemCount = await stockItemService.GetTotalStockItemsCount(stockCheckIds, userId);
         if (totalStockItemCount > 0)
         {
            return "Error";
         }

         await stockCheckDataAccess.Delete(stockCheckIds, userId);
         return string.Empty;

      }

      public async Task<IEnumerable<int>> CheckIfAlreadyExists(string stockCheckDate, List<int> siteIds)
      {
         return await stockCheckDataAccess.CheckIfAlreadyExists(stockCheckDate, siteIds);
      }

      public async Task<IEnumerable<int>> GetStockCheckIdsForUserDealerGroup(int userId)
      {
         return await stockCheckDataAccess.GetStockCheckIdsForUserDealerGroup(userId);
      }
   }
}
