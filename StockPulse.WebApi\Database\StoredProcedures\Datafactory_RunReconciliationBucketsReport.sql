﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].[Datafactory_RunReconciliationBucketsReport]  
  
AS  
BEGIN  
  
DECLARE @StockCheckId INT  
  
--Clear todays report data  
DELETE FROM [ReconciliationBucketsReport] WHERE ReportDate = Convert(date, GETDATE())  
  
   
DECLARE load_cursor CURSOR FOR   
    SELECT SC.Id FROM StockChecks AS SC   
 WHERE SC.IsActive = 1  
  
   
OPEN load_cursor   
FETCH NEXT FROM load_cursor INTO @StockCheckId  
   
WHILE @@FETCH_STATUS = 0   
BEGIN   
  
 EXEC [dbo].[Datafactory_ReconciliationBucketsReport] @StockCheckId  
  
   
    FETCH NEXT FROM load_cursor INTO @StockCheckId  
END   
   
CLOSE load_cursor   
DEALLOCATE load_cursor   
  
  
  
  
END