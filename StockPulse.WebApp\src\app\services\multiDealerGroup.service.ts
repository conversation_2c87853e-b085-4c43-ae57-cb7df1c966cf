import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, forkJoin } from 'rxjs';


import { ConstantsService } from './constants.service'
import { SelectionsService } from './selections.service'

import { Router } from '@angular/router';
import { ApiAccessService } from './apiAccess.service';
import { BaseURLVM } from '../model/BaseURLVM';



@Injectable({
  providedIn: 'root'
})
export class MultiDealerGroupService {


  constructor(
    public http: HttpClient,
    public constants: ConstantsService,
    public selections: SelectionsService,
    public apiAccess: ApiAccessService,
    public router: Router,
  ) {


  }

  createRequests(email: string) {
    //add more country calls here
    let requests = [];
    requests.push(this.apiAccess.getTextForCountry('UK', 'AppStart', `GetBaseURLNew/${email}`));
    requests.push(this.apiAccess.getTextForCountry('US', 'AppStart', `GetBaseURLNew/${email}`));

    return requests;
  }

  checkandSetBaseURL(baseURLUK: BaseURLVM, baseURLUS: BaseURLVM) {
    
    let baseURL = '';
    let dealerGroup = '';
    if (baseURLUK?.baseURL?.startsWith('https://')) {
      baseURL = baseURLUK.baseURL;
      dealerGroup = baseURLUK.dealerGroupVMs[0].description;
    } else if (baseURLUS?.baseURL?.startsWith('https://')) {
      baseURL = baseURLUS.baseURL;
      dealerGroup = baseURLUS.dealerGroupVMs[0].description;
    }

    const totalDealerGroups = baseURLUK.dealerGroupVMs?.length + baseURLUS.dealerGroupVMs?.length;

    //add more dealer group checks here 
    if (totalDealerGroups > 1) {
      this.constants.isMultiDealerGroupAccount = true;
      this.constants.dealerGroupSelectionModal.baseURLVMs = [];
      const baseURLVMs = [];
      baseURLVMs.push(baseURLUK);
      baseURLVMs.push(baseURLUS);
      this.constants.dealerGroupSelectionModal.baseURLVMs = baseURLVMs
    }

    if (!this.constants.isMultiDealerGroupAccount) {

      if (baseURL.startsWith('https://')) {
        localStorage.setItem('baseURL', baseURL);
        localStorage.setItem('selectedDealerGroup', dealerGroup);
      }

    }

  }

  setBaseURLByCountryAndResetPassword(country: string, email: string){
    let requests = [];

    if (!(country == 'UK' || country == 'US')){
      country = 'UK' //setting default country
    }

    switch(country){
      case 'UK':
        requests.push(this.apiAccess.getTextForCountry('UK', 'AppStart', `GetBaseURLNew/${email}`));
      case 'US':
        requests.push(this.apiAccess.getTextForCountry('US', 'AppStart', `GetBaseURLNew/${email}`));
    }

    return forkJoin(requests);


  }




}
