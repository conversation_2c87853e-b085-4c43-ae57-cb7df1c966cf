﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using StockPulse.WebApi.ViewModel;
using StockPulse.WebApi.Service;
using System.Collections.Generic;
using System.Threading.Tasks;
using static StockPulse.WebApi.Attribute.UserLevelAccessAttribute;
using StockPulse.WebApi.Attribute;
using System;
using System.Linq;
using Newtonsoft.Json;

namespace StockPulse.WebApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    [UserLevelAccess(new[] { UserRole.SysAdministrator, UserRole.Reconciler, UserRole.Resolver, UserRole.Approver, UserRole.GeneralManager, UserRole.ReadOnly, UserRole.ScanAndView })]
    public class FinancialLinesController : ControllerBase, IAttributeValueProvider
    {

        private readonly IFinancialLineService financialLinesService;
        private readonly int userId;
        private readonly string userRole;

        public UserRole attributeUserRole => GetUserRole();
        private UserRole GetUserRole()
        {
            return (UserRole)Enum.Parse(typeof(UserRole), userRole);
        }

        public FinancialLinesController(IFinancialLineService financialLinesService, IUserService userService)
        {
            this.financialLinesService = financialLinesService;
            userId =  userService.GetUserId();
            userRole = userService.GetUserRole();
        }

        [HttpGet]
        [Route("{stockcheckId}")]
        public async Task<IEnumerable<FinancialLineVM>> GetFinancialLines(int stockcheckId)
        {
            return await financialLinesService.GetFinancialLines(stockcheckId, userId);
        }

        [HttpGet]
        [Route("TotalItems/{stockcheckId}")]
        public async Task<int> GetFinancialLinesTotalItems(int stockcheckId)
        {
            return (await financialLinesService.GetFinancialLines(stockcheckId, userId)).Count();
        }

        [HttpPost]
        [Route("FinancialLines")]
        [UserLevelAccess(new[] { UserRole.SysAdministrator, UserRole.Reconciler, UserRole.Resolver, UserRole.Approver })]
        public async Task<IActionResult> PostFinancialLines(PostFinancialLines payload)
        {
            //string json = System.Text.Json.JsonSerializer.Serialize(payload);
            //var obj = System.Text.Json.JsonSerializer.Deserialize<PostFinancialLines>(json);
            try
            {
                await financialLinesService.PostFinancialLines(payload.financialLines, payload.stockCheckId, userId, payload.fileImportId);
                return Ok();
            }
            catch (Exception e)
            {
                return BadRequest(e.Message);
            }
        }


        [HttpGet]
        [Route("UpdateLine")]
        [UserLevelAccess(new[] { UserRole.SysAdministrator, UserRole.Reconciler, UserRole.Resolver, UserRole.Approver })]
        public async Task UpdateLine(int stockCheckId, int financialLineId, string description, string notes, decimal balance)
        {
            await financialLinesService.UpdateLine(stockCheckId, financialLineId, description,notes,balance, userId);
        }

        [HttpDelete]
        [Route("FinancialLine")]
        [UserLevelAccess(new[] { UserRole.SysAdministrator, UserRole.Reconciler, UserRole.Resolver, UserRole.Approver })]
        public async Task DeleteLine(int id, int stockCheckId)
        {
            await financialLinesService.DeleteLine(id,stockCheckId,userId);
        }

        [HttpGet]
        [Route("FinancialLine/All")]
        [UserLevelAccess(new[] { UserRole.SysAdministrator, UserRole.Reconciler, UserRole.Resolver, UserRole.Approver })]
        public async Task DeleteAllLines(int stockCheckId)
        {
            await financialLinesService.DeleteAllLines(stockCheckId, userId);
        }
    }
}
