﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace StockPulse.Model
{
    public class StatusChangeLogItem
    {
        [Key]
        public int Id { get; set; }
        public int StockCheckId { get; set; }
        [ForeignKey("StockCheckId")]
        public virtual StockCheck StockCheck { get; set; }
        public int UserId { get; set; }
        [ForeignKey("UserId")]
        public virtual User User { get; set; }
        public int StatusId { get; set; }
        [ForeignKey("StatusId")]
        public virtual Status Status { get; set; }
        public DateTime Date { get; set; }
    }
}
