﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].[DataFactory_UpdateFinancialLines]

AS
BEGIN
    -- SET NOCOUNT ON added to prevent extra result sets from
    -- interfering with SELECT statements.
    SET NOCOUNT ON

    -- Insert statements for procedure here
    BEGIN TRAN



	--------------------------------------------------------------------------------------------------
	----Backup the data---

	DECLARE @UniqueID UNIQUEIDENTIFIER
	SET @UniqueID  = NEWID()

	DECLARE @BackupDate DateTime
	SET @BackupDate = GETUTCDATE()

	INSERT INTO [dbo].[stockpulse_dealership_totals_backup]
           ([UniqueId]
           ,[BackupDate]
           ,[location_name]
           ,[expense_code]
           ,[nl_balance]
           ,[fingerprint]
           ,[updated])
	SELECT  @UniqueId
           ,@BackupDate
           ,[location_name]
           ,[expense_code]
           ,[nl_balance]
           ,[fingerprint]
           ,[updated]
	FROM [stockpulse_dealership_totals]

	--DELETE Older backups. Delete backups older than 15days.
	DELETE FROM [stockpulse_dealership_totals_backup] WHERE BackupDate < GETUTCDATE() - 15
	--------------------------------------------------------------------------------------------------

	






MERGE FinancialLines AS TARGET
USING 
(SELECT DT.expense_code As Code, CONCAT(S.Description, ' ',DT.expense_code)  as AccountDescription, NULL As Notes, 0 As IsExplanation,DT.nl_balance As Balance, ISNULL(SC.Id,SCForMap.Id) AS StockCheckId
 FROM [dbo].[stockpulse_dealership_totals] As DT
 LEFT JOIN [dbo].[Sites] AS S ON S.Description = DT.location_name AND S.IsActive = 1
 LEFT JOIN [dbo].[stockchecks] AS SC ON S.Id = SC.SiteId AND SC.IsActive = 1
 LEFT JOIN [import].[SiteDescriptionDictionary] AS BN ON DT.location_name = BN.Description AND BN.IsPrimarySiteId = 1 AND BN.DealerGroupId = 1
 LEFT JOIN [dbo].[Sites] AS SForMap ON SForMap.Id = BN.SiteId 
 LEFT JOIN [dbo].[stockchecks] AS SCForMap ON SForMap.Id = SCForMap.SiteId AND SCForMap.IsActive = 1
 WHERE ISNULL(SC.Id,SCForMap.Id) IS NOT NULL) 
  
 AS SOURCE
 ON TARGET.Code = SOURCE.CODE AND TARGET.StockCheckId = SOURCE.StockCheckId 

 WHEN MATCHED AND TARGET.IsExplanation = 0
 THEN UPDATE SET TARGET.Balance = SOURCE.Balance

 WHEN NOT MATCHED BY TARGET 
 THEN INSERT ([Code]
           ,[AccountDescription]
           ,[Notes]
           ,[IsExplanation]
           ,[Balance]
           ,[StockCheckId]) VALUES 
		   (SOURCE.[Code]
           ,SOURCE.[AccountDescription]
           ,SOURCE.[Notes]
           ,SOURCE.[IsExplanation]
           ,SOURCE.[Balance]
           ,SOURCE.[StockCheckId])

WHEN NOT MATCHED BY SOURCE AND TARGET.StockCheckId IN (
SELECT SC.Id FROM StockChecks AS SC 
INNER JOIN sites as s on sc.SiteId = s.Id and s.IsActive = 1
INNER JOIN Divisions as d on s.DivisionId = d.Id
WHERE d.DealerGroupId = 1 AND SC.IsActive = 1
)
THEN DELETE


OUTPUT $action,
INSERTED.*,
DELETED.*;
--DELETED.Code AS Code, 
--DELETED.AccountDescription AS AccountDescription, 
--DELETED.IsExplanation AS IsExplanation, 
--DELETED.Balance AS Balance, 
--DELETED.StockCheckId AS StockCheckId, 
--INSERTED.Code AS Code, 
--INSERTED.AccountDescription AS AccountDescription, 
--INSERTED.IsExplanation AS IsExplanation, 
--INSERTED.Balance AS Balance, 
--INSERTED.StockCheckId AS StockCheckId;
--INTO @Outputs;



--INSERT INTO [stockpulse_dealership_totals_logs]()
--SELECT * FROM @outputs

SELECT @@ROWCOUNT;


--SELECT * FROM [stockpulse_dealership_totals_logs]

COMMIT TRAN


END
GO
