﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].[CREATE_ReconcilingItems]
(
    @Items  as ReconcilingItemSaveType readonly,
	@UserId int
)
AS
BEGIN

SET NOCOUNT ON

DECLARE @StockCheckId INT 

SET @StockCheckId = (SELECT TOP 1 StockCheckId FROM @Items)

IF (dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0 OR dbo.[CanSaveScans](@StockCheckId) = 0)
BEGIN 
    RETURN
END

INSERT 
	INTO ReconcilingItems (ReconcilingItemTypeId,Reg,Vin,Description,Comment,Reference,SourceReportId,StockCheckId,FileImportId)
	(select ReconcilingItemTypeId,Reg,Vin,Description,Comment,Reference,SourceReportId,StockCheckId,FileImportId from @Items)
	

	EXEC [dbo].[UPDATE_StockCheckLastUpdate] @StockCheckId, @UserId
 
END

GO


