﻿/****** Object:  StoredProcedure [dbo].[GET_ResolutionBuckets]    Script Date: 11/08/2021 11:56:01 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].[GET_ResolutionBuckets]
(
    @StockCheckId INT = NULL,
	@UserId INT = NULL
)
AS
BEGIN

SET NOCOUNT ON;


DECLARE @isRegional INT;  
DECLARE @isTotal INT;  
DECLARE @StockCheckDate DateTime;
DECLARE @DivisionId INT = NULL;
DECLARE @DealerGroupId INT = NULL;
DECLARE @SCId INT = NULL;


	
IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END


 SET @DealerGroupId = (SELECT DealerGroupId FROM StockChecks  
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId  
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id  
                        WHERE StockChecks.Id = @StockCheckId)  

--SET VARIABLES BASED ON THE CHOSEN STOCKCHECK
SELECT 
@isRegional = IsRegional, 
@isTotal = IsTotal, 
@StockCheckDate = Date 
FROM StockChecks  
WHERE StockChecks.Id = @StockCheckId
 

--IF SITE
IF @isRegional = 0 AND @isTotal = 0  
  
    BEGIN  
	SET @SCId = @StockCheckId;
    END  

--REGIONAL  
ELSE IF @isRegional = 1 AND @isTotal = 0  
  
    BEGIN  
    SET @DivisionId = (SELECT DivisionId FROM StockChecks  
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId  
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id  
                        WHERE StockChecks.Id = @StockCheckId AND StockChecks.IsRegional = 1)  
    END  

--TOTAL  
ELSE IF @isRegional = 0 AND @isTotal = 1  
  
    BEGIN  
    SET @DealerGroupId = (SELECT DealerGroupId FROM StockChecks  
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId  
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id  
                        WHERE StockChecks.Id = @StockCheckId)  
    END 




	--find all the unknowns that have been resolved
	DECLARE @unknownsresolved table(resTypeId int, vehicleCount int)
	INSERT INTO @unknownsresolved (resTypeId,vehicleCount) 
	( 
		SELECT rt.Id, 
		Count(sc.Id) as vehicleCount
		FROM SCANS sc
		INNER JOIN UnknownResolutions ur on ur.id = sc.UnknownResolutionId
		INNER join ResolutionTypes rt on rt.Id = ur.ResolutionTypeId
		INNER JOIN Stockchecks ON Stockchecks.Id=sc.StockCheckId 
		INNER JOIN Sites ON Sites.Id=StockChecks.SiteId
		INNER JOIN Divisions AS D ON D.Id=Sites.DivisionId
		INNER JOIN DealerGroup AS DG ON D.DealerGroupId=DG.Id
	
		WHERE 	UnknownResolutionId is not null 
		AND ur.IsResolved = 1 
		AND ReconcilingItemId IS NULL 
		AND sc.IsDuplicate = 0 
		AND sc.StockItemId IS NULL
		AND Stockchecks.Id = ISNULL(@SCId, Stockchecks.Id)
		AND Stockchecks.Date = @StockCheckDate
		AND D.Id = ISNULL(@DivisionId, D.Id)
		AND DG.Id = ISNULL(@DealerGroupId, DG.Id)
		GROUP BY rt.Id
	)


	--find all the missings that have been resolved
	DECLARE @missingsresolved table(resTypeId int, vehicleCount int, inStockValue float)
	INSERT INTO @missingsresolved (resTypeId,vehicleCount,inStockValue) 
	( 
		SELECT rt.Id, Count(st.Id), ROUND(SUM(st.StockValue), 2) as inStockValue 
		FROM StockItems st
		INNER JOIN MissingResolutions mr on mr.id = st.MissingResolutionId
		INNER JOIN ResolutionTypes rt on rt.Id = mr.ResolutionTypeId
		INNER JOIN Stockchecks ON Stockchecks.Id=st.StockCheckId 
		INNER JOIN Sites ON Sites.Id=StockChecks.SiteId
		INNER JOIN Divisions AS D ON D.Id=Sites.DivisionId
		INNER JOIN DealerGroup AS DG ON D.DealerGroupId=DG.Id
		WHERE MissingResolutionId is not null AND ReconcilingItemId IS NULL and mr.IsResolved =1 and st.ScanId is null 
		AND Stockchecks.Id = ISNULL(@SCId, Stockchecks.Id)
		AND Stockchecks.Date = @StockCheckDate
		AND D.Id = ISNULL(@DivisionId, D.Id)
		AND DG.Id = ISNULL(@DealerGroupId, DG.Id)
		GROUP BY rt.Id
	)


	--find the total count of unknowns not resolved  
	 DECLARE @unknownsnotresolved table(vehicleCount int)
	 INSERT INTO @unknownsnotresolved (vehicleCount)  
	 SELECT
	 (
		SELECT count(S.Id) 
		FROM Scans as S
		INNER JOIN Stockchecks ON Stockchecks.Id=S.StockCheckId 
		INNER JOIN Sites ON Sites.Id=StockChecks.SiteId
		INNER JOIN Divisions AS D ON D.Id=Sites.DivisionId
		INNER JOIN DealerGroup AS DG ON D.DealerGroupId=DG.Id
		WHERE  Stockchecks.Id = ISNULL(@SCId, Stockchecks.Id)
		AND Stockchecks.Date = @StockCheckDate
		AND D.Id = ISNULL(@DivisionId, D.Id)
		AND DG.Id = ISNULL(@DealerGroupId, DG.Id)
		AND StockItemId is null 
		AND ReconcilingItemId is null 
		AND UnknownResolutionId is null 
		AND IsDuplicate = 0 
	 )  

	 +   

	 (
		SELECT count(sc.Id) 
		FROM Scans sc 
		INNER JOIN UnknownResolutions mr on mr.Id = sc.UnknownResolutionId 
		INNER JOIN Stockchecks ON Stockchecks.Id=sc.StockCheckId 
		INNER JOIN Sites ON Sites.Id=StockChecks.SiteId
		INNER JOIN Divisions AS D ON D.Id=Sites.DivisionId
		INNER JOIN DealerGroup AS DG ON D.DealerGroupId=DG.Id
		WHERE mr.IsResolved = 0 
		AND StockItemId IS NULL 
		AND ReconcilingItemId IS NULL 
		AND IsDuplicate = 0
		AND  Stockchecks.Id = ISNULL(@SCId, Stockchecks.Id)
		AND Stockchecks.Date = @StockCheckDate
		AND D.Id = ISNULL(@DivisionId, D.Id)
		AND DG.Id = ISNULL(@DealerGroupId, DG.Id)
	  )    
  
	 
	 
	 --find the total count of missings not resolved  
	 declare @missingsnotresolved table (vehicleCount int)
	 INSERT INTO @missingsnotresolved (vehicleCount)
	 SELECT
	 (
		SELECT count(SI.Id) 
		FROM stockitems AS SI
		INNER JOIN Stockchecks ON Stockchecks.Id=SI.StockCheckId 
		INNER JOIN Sites ON Sites.Id=StockChecks.SiteId
		INNER JOIN Divisions AS D ON D.Id=Sites.DivisionId
		INNER JOIN DealerGroup AS DG ON D.DealerGroupId=DG.Id
		WHERE ScanId is null 
		AND ReconcilingItemId is null 
		AND MissingResolutionId is null 
		AND IsDuplicate = 0 
		AND  Stockchecks.Id = ISNULL(@SCId, Stockchecks.Id)
		AND Stockchecks.Date = @StockCheckDate
		AND D.Id = ISNULL(@DivisionId, D.Id)
		AND DG.Id = ISNULL(@DealerGroupId, DG.Id)
	 ) 

	 +   

	 (
		SELECT count(SI.Id) 
		FROM StockItems SI 
		INNER JOIN MissingResolutions mr on mr.Id = SI.MissingResolutionId 
		INNER JOIN Stockchecks ON Stockchecks.Id=SI.StockCheckId 
		INNER JOIN Sites ON Sites.Id=StockChecks.SiteId
		INNER JOIN Divisions AS D ON D.Id=Sites.DivisionId
		INNER JOIN DealerGroup AS DG ON D.DealerGroupId=DG.Id
		WHERE 	 mr.IsResolved = 0 
		AND SI.ScanId is null 
		AND IsDuplicate = 0 
		AND SI.ReconcilingItemId is null
		AND  Stockchecks.Id = ISNULL(@SCId, Stockchecks.Id)
		AND Stockchecks.Date = @StockCheckDate
		AND D.Id = ISNULL(@DivisionId, D.Id)
		AND DG.Id = ISNULL(@DealerGroupId, DG.Id)
	 )  
  
  

	 SELECT 
	 resTypes.Description, 
	 ISNULL(ur.vehicleCount,0) as 'VehicleCount', 
	 0 as InStockValue,
	 0 as 'ExplainsMissing'  
	 FROM ResolutionTypes resTypes  
	 LEFT JOIN @unknownsresolved ur on ur.resTypeId = resTypes.Id 
	 WHERE resTypes.ExplainsMissingVehicle = 0 
	 AND resTypes.DealerGroupId = @DealerGroupId


	UNION ALL

	SELECT 'Unknowns not resolved' as Description, 
	vehicleCount as VehicleCount, 
	0 as InStockValue,
	0 as ExplainsMissing
	FROM @unknownsnotresolved

	UNION ALL

	SELECT resTypes.Description, 
	ISNULL(mr.vehicleCount,0) as 'VehicleCount', 
	ISNULL(mr.inStockValue,0) as 'InStockValue',
	1 as 'ExplainsMissing'
	FROM ResolutionTypes resTypes
	LEFT JOIN @missingsresolved mr on mr.resTypeId = resTypes.Id
	WHERE resTypes.ExplainsMissingVehicle = 1 and resTypes.DealerGroupId = @DealerGroupId

	UNION ALL

	SELECT 'Missings not resolved' as Description, 
	vehicleCount as VehicleCount, 
	0 as InStockValue,
	1 as ExplainsMissing
	FROM @missingsnotresolved




END

