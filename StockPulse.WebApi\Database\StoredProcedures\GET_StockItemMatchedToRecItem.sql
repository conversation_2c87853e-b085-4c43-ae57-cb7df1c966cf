

CREATE OR ALTER PROCEDURE [dbo].[GET_StockItemMatchedToRecItem]    
(    
    @StockCheckId INT = NULL,    
    @UserId INT = NULL    
)    
AS    
BEGIN    
    
SET NOCOUNT ON;    
    
DECLARE @isRegional INT;    
DECLARE @isTotal INT;    
DECLARE @StockCheckDate DateTime;  
DECLARE @DivisionId INT = NULL;  
DECLARE @DealerGroupId INT = NULL;  
DECLARE @SCId INT = NULL;  
  

IF dbo.[AuthenticateUser](@UserId, @StockCheckId) = 0
BEGIN 
    RETURN
END


  
SELECT   
@isRegional = IsRegional,   
@isTotal = IsTotal,   
@StockCheckDate = Date   
FROM StockChecks    
WHERE StockChecks.Id = @StockCheckId  
    

  
    
IF @isRegional = 0 AND @isTotal = 0    
    
    BEGIN    
  
 SET @SCId = @StockCheckId;  
    
  
    END    
    
ELSE IF @isRegional = 1 AND @isTotal = 0    
    
    BEGIN    
    
    
    SET @DivisionId = (SELECT DivisionId FROM StockChecks    
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId    
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id    
                        WHERE StockChecks.Id = @StockCheckId AND StockChecks.IsRegional = 1)    
    
    END    
    
ELSE IF @isRegional = 0 AND @isTotal = 1    
    
    BEGIN    
    
        
    SET @DealerGroupId = (SELECT DealerGroupId FROM StockChecks    
                        INNER JOIN Sites ON Sites.Id=StockChecks.SiteId    
                        INNER JOIN Divisions ON Sites.DivisionId=Divisions.Id    
                        WHERE StockChecks.Id = @StockCheckId)    
    
    
    END   
  
  
  SELECT     
 si.Id as StockItemId,    
 si.Reg,    
 si.Vin,    
 si.[Description],    
 si.DIS,    
 si.GroupDIS,    
 si.Branch,    
 si.StockType,    
 si.Comment,    
 si.Reference,    
 si.StockValue,
 si.Flooring,
    sites.Description as SiteName,
 si.ReconcilingItemId,    
 rit.Id as 'ReconcilingItemTypeId',    
 rit.[Description] as 'ReconcilingItemTypeDescription',    
 recitems.[Description] AS MatchingDesc,    
 recitems.[Comment] AS MatchingComment,    
 recitems.Reference AS 'MatchingRef'    
 FROM StockItems si    
 INNER JOIN ReconcilingItems recitems ON recitems.Id = si.ReconcilingItemId    
 INNER JOIN ReconcilingItemTypes rit ON rit.Id = recitems.ReconcilingItemTypeId    
 INNER JOIN StockChecks AS SC ON SC.Id=si.StockCheckId    
 INNER JOIN Sites ON Sites.Id=SC.SiteId    
 INNER JOIN Divisions AS D ON D.Id=Sites.DivisionId  
 INNER JOIN DealerGroup AS DG ON D.DealerGroupId=DG.Id  
 WHERE si.ReconcilingItemId IS NOT NULL    
    --AND si.StockCheckId = @StockCheckId     
 AND si.ScanId IS NULL --Added si.ScanId IS NULL to fix (vindis - skoda vw cambridge) issue  
 AND SC.Id = ISNULL(@SCId, SC.Id)  
 AND SC.Date = @StockCheckDate  
 AND D.Id = ISNULL(@DivisionId, D.Id)  
 AND DG.Id = ISNULL(@DealerGroupId, DG.Id)  

UNION ALL

SELECT 
si.Id,    
si.Reg,    
si.Vin,    
si.[Description],    
si.DIS,    
si.GroupDIS,    
si.Branch,    
si.StockType,    
si.Comment,    
si.Reference,    
si.StockValue,
si.Flooring,
sites.Description as SiteName,
si.ReconcilingItemId, 
NULL as 'ReconcilingItemTypeId',    
'Scanned at another site' as 'ReconcilingItemTypeDescription',    
NULL AS MatchingDesc,    
NULL AS MatchingComment,    
NULL AS 'MatchingRef'    
FROM StockItems si     
INNER JOIN Scans sc on sc.Id = si.ScanId    
--INNER JOIN ReconcilingItems recitems ON recitems.Id = si.ReconcilingItemId    
--INNER JOIN ReconcilingItemTypes rit ON rit.Id = recitems.ReconcilingItemTypeId    
INNER JOIN StockChecks AS sck ON sck.Id=si.StockCheckId    
INNER JOIN Sites ON Sites.Id=sck.SiteId    
INNER JOIN Divisions AS D ON D.Id=Sites.DivisionId  
INNER JOIN DealerGroup AS DG ON D.DealerGroupId=DG.Id 
WHERE sc.StockCheckId <> @StockCheckId     
AND ScanId IS NOT NULL
AND sc.IsDuplicate = 0    
AND si.IsDuplicate = 0    
AND si.StockCheckId = @StockCheckId
    
   
END  


GO
